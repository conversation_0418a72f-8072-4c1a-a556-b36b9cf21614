/* Author:              <EMAIL>
 * Last Modified:       April 10, 2018
 * Class description:   Apex class for invoking Web service callouts
 *                      to the DLL server for creating Keys
 *
 *                      Key Generation process:
 *                      1 This Apex Callout will be invoked via Custom Button
 *                      2 Custom But<PERSON> will call a method to check if Key Exists
 *                      3 Otherwise the webservice is invoked
 *
 *                      Key Email process: pending
 *                      endpoint URL: https://licensing.mirror.microstrategy.com/KeyGeneratorAPI/API/KeyGen/GetCDKey
 *                      https://cdkeydev.microstrategy.com/KeyGeneratorAPI/API/KeyGen/GetCDKey
 */

global with sharing class KeyGeneratorAPICallout {


    global static String generateKeyfromJSON(String JSONPayload, String endpoint, String username, String password){

        //Query Record where Id = recordID

        //Build JSON
        JSONGenerator JSONGen = JSON.createGenerator(true);
            JSONGen.writeStartObject();
              //JSONGen.writeStringField();     //start input parameters
            JSONGen.writeEndObject();
            //JSONPayload=
            //String JSONParameters = JSONGen.getAsString();
            String JSONParameters = JSONPayload.replace('KstDate','Date');
        System.debug('JSON Input Parameters: ' + JSONParameters);



    //Set HTTPRequest Method
        //String endpoint = 'https://cdkeydev.microstrategy.com/KeyGeneratorAPI/API/KeyGen/GetCDKey';
        HttpRequest request = new HttpRequest();
        request.setEndpoint(endpoint);
        request.setMethod('POST');
        //String username = 'MSTRKeyGenAPI' ;
        //String password = '8..aR^A[4@fvP';

        //String password = 'M$tR12#4';

        Blob headerValue = Blob.valueOf(username + ':' + password);
        String authorizationHeader = 'BASIC ' + EncodingUtil.base64Encode(headerValue);
        request.setHeader('Authorization', authorizationHeader);
        request.setHeader('Accept', 'application/json');
        request.setHeader('Content-Type', 'application/json');
        request.setTimeout(120000);

        request.setBody(JSONParameters);

        Http http = new Http();
        HTTPResponse response = http.send(request);
        
        
        
        //Deserialize result
        //Map < String, Object > results = (Map < String, Object > ) JSON.deserializeUntyped(response.getBody());
        system.debug(response);
        system.debug(response.getBody());
        system.debug(response.getStatusCode());
        for(Integer i = 0; i<3 && response.getStatusCode() != 200; i++){
            response = http.send(request);
        }
        if (response.getStatusCode() != 200){
            return 'Error '+response.getBody();
        }
        else

    /*
    JSONParser parser = JSON.createParser(response.getBody());
          String key='';
          while (parser.nextToken() != null) {
              if ((parser.getCurrentToken() == JSONToken.FIELD_NAME) &&
                  (parser.getText() == 'encryptKeys')) {
                  // Get the value.
                  parser.nextToken();
                  // Compute the grand total price for all invoices.
                  key+= parser.getText();
              }
          }
          system.debug('Key=' + key);
*/
          return response.getBody();
    //create New Key Record with the appropriate details


    }

    global static String generateKeyfromJSONGeo(Map<String,String> payload, String endpoint, String username, String password){
        

        //Query Record where Id = recordID

        //Build JSON

        String qparams ='?';


        for(String param : payload.keySet()){
          String paramBody = param=='orgName'?EncodingUtil.urlEncode(payload.get(param), 'UTF-8'):payload.get(param);
          qparams+= param+'='+paramBody+'&';

        }
        qparams=qparams.substring(0,qparams.length()-1);


        endpoint+=qparams;
        system.debug(endpoint);


    //Set HTTPRequest Method


        HTTPRequest r = new HTTPRequest();

		r.setEndpoint(endpoint);
		r.setMethod('GET');
    //r.setBody('');

		HttpClient client = new HttpClient('corp\\'+username, password,true);

		HTTPResponse response = client.send(r);


        //String username = 'MSTRKeyGenAPI' ;
        //String password = '8..aR^A[4@fvP';

        //String password = 'M$tR12#4';

        //Blob headerValue = Blob.valueOf(username + ':' + password);
        //String authorizationHeader = 'NTLM '+EncodingUtil.base64Encode(headerValue);
        //request.setHeader('Authorization', authorizationHeader);
        //SaveResultrequest.setHeader('Accept', 'application/json');
        //request.setHeader('Content-Type', 'application/json');

        //request.setBody(JSONParameters);

    //Http http = new Http();
        //HTTPResponse response = http.send(request);



    //Deserialize result
    //Map < String, Object > results = (Map < String, Object > ) JSON.deserializeUntyped(response.getBody());
        system.debug(response);
        system.debug(response.getBody());
        system.debug(response.getStatusCode());
        for(Integer i = 0; i<3 && response.getStatusCode() != 200; i++){
            response = client.send(r);
        }
        if (response.getStatusCode() != 200){
            return 'Error '+response.getBody();
        }
        else

    /*
    JSONParser parser = JSON.createParser(response.getBody());
          String key='';
          while (parser.nextToken() != null) {
              if ((parser.getCurrentToken() == JSONToken.FIELD_NAME) &&
                  (parser.getText() == 'encryptKeys')) {
                  // Get the value.
                  parser.nextToken();
                  // Compute the grand total price for all invoices.
                  key+= parser.getText();
              }
          }
          system.debug('Key=' + key);
*/
          return response.getBody();
    //create New Key Record with the appropriate details


    }
}