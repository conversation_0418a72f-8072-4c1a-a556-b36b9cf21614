/************************************* MODIFICATION LOG ********************************************************************************************
*                --------------- updateEngagementScore  --------------
* if  event record type is "BDR Qualified meeting"  launch recalculation of the lead/event (whoID) engagement Score which is based on the agregete of qualified 
* CampaignMembers + Tasks + Events  - caps 100 if greater than that

*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Alla Kiyashko                 04/14/2016                        -- Original Version 
*/

public class EventTriggerHelper{
  public static void updateEngagementScore(List<Event> evtList, Map<Id,Event> oldEventMap, Boolean isUpdate, Boolean isInsert, Boolean isDelete){
        Set<Id> allEventIds = new Set<Id>();  
     
        if (FeatureSwitchManager.isSwitchDisabled('updateEngagementScore') && !Test.isRunningTest()) {
            System.debug(' disabled');
            return;
        }
       
       
        id rtEvent =  Schema.SObjectType.Event.getRecordTypeInfosByName().get('BDR Qualified Meeting').getRecordTypeId() ;
     
        for(Event eRc : evtList){
            if(eRc.WhoId!= null && eRc.RecordTypeId == rtEvent ){
                if(isInsert ||isDelete && eRc.Engagement_Score__c != 0){
                    allEventIds.add(eRc.WhoId); 

                }else if(isUpdate && ( eRc.Engagement_Score__c != oldEventMap.get(eRc.Id).Engagement_Score__c)){
                    allEventIds.add(eRc.WhoId); 
                    allEventIds.add(oldEventMap.get(eRc.Id).WhoId); 
                }
            }
           
            system.debug('###_--all items for Engagement calculation-'+ allEventIds);       
        }
        
        if(!allEventIds.isEmpty()){
            EngagementCalculate.CalculateOneSet(new List<id>(allEventIds));
        }
    }
    
   public static void updateEventFields (List<Event> newEvents, Map<Id, Event> oldEventMap, Boolean isInsert, Boolean isUpdate) {
        
        Map<String,String> prefixVsSObj = New Map<String,String>();
        Map<String,List<Id>> sObjVsSObjIds = New Map<String,List<Id>>();
        Map<String,Map<Id,sObject>> sObjVsSObjMap = New Map<String,Map<Id,sObject>>();
        Map<String,String> sObjVsSObjNameField = New Map<String,String>{'Case' => 'CaseNumber', 'ProcessException' => 'ProcessExceptionNumber', 
            'AssetRelationship' => 'AssetRelationshipNumber', 'Solution' => 'SolutionNumber'};
                String sObjName;
        String nameField;
        
        for (Schema.SObjectType sObjtype : Schema.getGlobalDescribe().values()) {
            Schema.DescribeSObjectResult result = sObjtype.getDescribe();
            prefixVsSObj.put(result.getKeyPrefix(),result.getName());
        }
        
        for (Event evt : newEvents) {
            if ((isInsert && evt.WhoId != null) || (isUpdate && TriggersHelper.valueChangedNotBlank(oldEventMap.get(evt.Id), evt, Event.WhoId))) {
                sObjName = prefixVsSObj.get(String.valueOf(evt.WhoId).substring(0,3));
                if (!sObjVsSObjIds.containsKey(sObjName))
                    sObjVsSObjIds.put(sObjName, New List<Id>());
                sObjVsSObjIds.get(sObjName).add(evt.WhoId);
            }
            if ((isInsert && evt.WhatId != null) || (isUpdate && TriggersHelper.valueChangedNotBlank(oldEventMap.get(evt.Id), evt, Event.WhatId))) {
                sObjName = prefixVsSObj.get(String.valueOf(evt.WhatId).substring(0,3));
                if (!sObjVsSObjIds.containsKey(sObjName))
                    sObjVsSObjIds.put(sObjName, New List<Id>());
                sObjVsSObjIds.get(sObjName).add(evt.WhatId);
            }
        }
        
        for (String sObj : sObjVsSObjIds.keySet()) {
            List<Id> sObjIds = sObjVsSObjIds.get(sObj);
            nameField = sObjVsSObjNameField.containsKey(sObj) ? sObjVsSObjNameField.get(sObjName) : 'Name';
            Map<Id,sObject> sObjList = new Map<Id,sObject>(Database.query('SELECT '+nameField+' FROM '+sObj+' WHERE Id IN :sObjIds'));
            sObjVsSObjMap.put(sObj,sObjList);
        }
        
        for (Event evt : newEvents) {
            if ((isInsert && evt.WhoId != null) || (isUpdate && TriggersHelper.valueChangedNotBlank(oldEventMap.get(evt.Id), evt, Event.WhoId))) {
                sObjName = prefixVsSObj.get(String.valueOf(evt.WhoId).substring(0,3));
                nameField = sObjVsSObjNameField.containsKey(sObjName) ? sObjVsSObjNameField.get(sObjName) : 'Name';
                evt.Who_Name__c = String.valueOf(sObjVsSObjMap.get(sObjName).get(evt.WhoId).get(nameField));
            }
            if ((isInsert && evt.WhatId != null) || (isUpdate && TriggersHelper.valueChangedNotBlank(oldEventMap.get(evt.Id), evt, Event.WhatId))) {
                sObjName = prefixVsSObj.get(String.valueOf(evt.WhatId).substring(0,3));
                nameField = sObjVsSObjNameField.containsKey(sObjName) ? sObjVsSObjNameField.get(sObjName) : 'Name';
                evt.What_Name__c = String.valueOf(sObjVsSObjMap.get(sObjName).get(evt.WhatId).get(nameField));
            }
        }
    }
}
