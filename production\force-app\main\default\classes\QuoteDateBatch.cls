/************************************ MODIFICATION LOG ********************************************************************************************
        * QuoteDateBatch
        *
        *---------------------------------------------------------------------------------------------------------------------------------------------------
        * DEVELOPER                     DATE                                REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Priyank                       04/16/2021                          Case 462501 - CPQ - Fix Terms to be correctly populated on the QLs of executed quote upon Ship Date
* Priyank                       06/25/2021                          Case 481641 - Service Cloud - Cloud Onboarding Cases should not be overwritten by Term Key Request Assignment
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Test Class: QuoteDateBatchTest
*/

global with sharing class QuoteDateBatch implements Database.Batchable<SObject>, Database.AllowsCallouts {
    public static Set<Id> renewalOppIds {
        get {
            if (renewalOppIds == null) {
                renewalOppIds = new Set<Id>{
                        Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Renewal Opportunity (New)').getRecordTypeId(),
                        Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Renewal Opportunity').getRecordTypeId()
                };
            }
            return renewalOppIds;
        }
        set;
    }

    public static Set<String> renewalTypes = new Set<String>{
            'Term Renewal', 'Cloud Renewal', 'Maintenance Renewal', 'Support Renewal'
    };

    global Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator([
                SELECT Id, SBCF_Ship_to_Contact__c, SBCF_Ship_to_Contact__r.Full_Name__c, SBCF_Ship_to_Contact__r.Name,
                        SBQQ__Opportunity2__r.DSI__c, SBQQ__Opportunity2__r.DSI__r.Platform__c, SBQQ__Opportunity2__r.DSI__r.ServiceNow_Id__c,
                        SBQQ__PrimaryContact__r.ServiceNow_Id__c, SBQQ__Opportunity2__r.AccountId, SBQQ__StartDate__c,
                        SBQQ__Opportunity2__r.Ship_Date__c, SBQQ__Opportunity2__r.CloseDate, SBQQ__Opportunity2__r.Name,
                        SBQQ__Opportunity2__r.Cloud_Opportunity__c,SBQQ__Opportunity2__r.SBQQ__PrimaryQuote__r.Platform__c, SBQQ__Opportunity2__r.RecordTypeId, SBQQ__Opportunity2__r.Term_Opportunity__c,
                        SBCF_Sum_of_Tech_Support_Extension__c, SBQQ__Opportunity2__r.Type, SBQQ__Opportunity2__c,
                        SBQQ__Opportunity2__r.Account.Theatre_Name__c, SBQQ__Opportunity2__r.Account.ServiceNow_ID__c,
                        SBQQ__Opportunity2__r.Account.District__r.Name, SBCF_Ship_to_Contact__r.ServiceNow_Id__c, MYC__c, MinStDt__c
                FROM SBQQ__Quote__c
                WHERE SBQQ__Primary__c = TRUE
                AND Date_Sync__c = FALSE
                AND SBQQ__Type__c != 'Amendment'
                AND SBQQ__Opportunity2__r.StageName = 'S6 - Closed Won'
                AND SBQQ__Status__c = 'Q09 EXE'
                AND SBQQ__Opportunity2__r.CloseDate >= LAST_MONTH
                AND Order__r.Status != 'Integration' AND Order__r.Status != 'Activated' AND Order__r.Status != 'Cancelled'
                AND ((SBQQ__Opportunity2__r.RecordTypeId IN :renewalOppIds AND SBQQ__Opportunity2__r.Type IN :renewalTypes) OR (
                        (SBQQ__Opportunity2__r.RecordTypeId NOT IN :renewalOppIds OR SBQQ__Opportunity2__r.Type = 'Term Migration') AND
                        (SBQQ__Opportunity2__r.SBQQ__PrimaryQuote__r.Platform__c = 'MCG' OR SBQQ__Opportunity2__r.Cloud_Opportunity__c = TRUE OR SBQQ__Opportunity2__r.Ship_Date__c != NULL OR SBCF_Sum_of_Tech_Support_Extension__c > 0 OR SBQQ__Opportunity2__r.Agreement_Description__c = 'Maintenance; ')
                ))
        ]);
    }

    global void execute(Database.BatchableContext BC, List<SBQQ__Quote__c> scope) {
        List<User> mstrSFUser = [SELECT Id FROM User WHERE Name = 'MSTR Salesforce'];

        //we always run job for 1 quote per batch, that is why we take element at position 0
        SBQQ__Quote__c quote = scope[0];
        System.debug('Quote Id: ' + quote.Id);

        if ((!renewalOppIds.contains(quote.SBQQ__Opportunity2__r.RecordTypeId) || quote.SBQQ__Opportunity2__r.Type == 'Term Migration') &&
                (quote.SBQQ__Opportunity2__r.SBQQ__PrimaryQuote__r.Platform__c == 'MCG' || quote.SBQQ__Opportunity2__r.Cloud_Opportunity__c || quote.SBQQ__Opportunity2__r.Ship_Date__c != null || quote.SBCF_Sum_of_Tech_Support_Extension__c > 0)
        ) {
            Date startDateVal = (quote.SBQQ__Opportunity2__r.SBQQ__PrimaryQuote__r.Platform__c == 'MCG' || quote.SBQQ__Opportunity2__r.Cloud_Opportunity__c || quote.SBCF_Sum_of_Tech_Support_Extension__c > 0) ?
                    quote.SBQQ__Opportunity2__r.CloseDate :
                    quote.SBQQ__Opportunity2__r.Ship_Date__c;

            List<SBQQ__QuoteLine__c> quoteLines = [
                    SELECT Id, SBQQ__EndDate__c, SBQQ__Quote__r.SBQQ__Opportunity2__r.CloseDate,
                            SBQQ__Quote__r.SBQQ__Opportunity2__r.Cloud_Opportunity__c,SBQQ__Quote__r.SBQQ__Opportunity2__r.SBQQ__PrimaryQuote__r.Platform__c, Opportunity__c,
                            SBCF_DSI__c, SBCF_DSI__r.Name, SBCF_DSI__r.Account__r.District__r.Business_Hours__c,
                            Interval__c, SBQQ__StartDate__c, SBQQ__ProrateMultiplier__c, SBQQ__Group__c
                    FROM SBQQ__QuoteLine__c
                    WHERE SBQQ__Quote__c = :quote.Id
                    AND (SBCF_Product_Filter__c LIKE '%Support%'
                    OR (SBQQ__StartDate__c != NULL AND SBQQ__EndDate__c != NULL))
            ];
            Map<Id,OpportunityLineItem> OLIList = new Map<Id,OpportunityLineItem>();

            List<SBQQ__QuoteLineGroup__c> quoteLineGroups = [
                    SELECT Id, SBQQ__StartDate__c, SBQQ__EndDate__c
                    FROM SBQQ__QuoteLineGroup__c
                    WHERE SBQQ__Quote__c = :quote.Id
            ];

            List<SBQQ__Subscription__c> subscriptions = [
                    SELECT Id, SBQQ__SubscriptionStartDate__c, SBQQ__SubscriptionEndDate__c, SBQQ__QuoteLine__r.SBQQ__Group__c
                    FROM SBQQ__Subscription__c
                    WHERE SBQQ__QuoteLine__r.SBQQ__Quote__c = :quote.Id
            ];

            List<Contract> contracts = [
                    SELECT Id, StartDate, SBQQ__RenewalOpportunity__c, SBQQ__RenewalOpportunity__r.StageName, EndDate
                    FROM Contract
                    WHERE SBQQ__Quote__c = :quote.Id
            ];

            List<Order> orders = [
                    SELECT Id, EffectiveDate, EndDate
                    FROM Order
                    WHERE SBQQ__Quote__c = :quote.Id
            ];
            List<OrderItem> orderItems = [
                    SELECT Id, ServiceDate, EndDate, Quote_Line_Group__c
                    FROM OrderItem
                    WHERE SBQQ__QuoteLine__c IN :quoteLines
            ];


            Date startDateVrb;
            Date endDateVrb;
            Date orderEndDate;
            Integer termInDays;

            if (quote.MinStDt__c <= startDateVal) {
                if (quote.MYC__c && !quoteLineGroups.isEmpty()) {
                    for (SBQQ__QuoteLineGroup__c quoteLineGroup : quoteLineGroups) {
                        startDateVrb = startDateVrb == null ? startDateVal : startDateVrb;
                        quoteLineGroup.SBQQ__StartDate__c = startDateVrb;


                        for (SBQQ__QuoteLine__c quoteLine : quoteLines) {
                            if (quoteLine.SBQQ__Group__c == quoteLineGroup.Id) {
                                termInDays = quoteLine.SBQQ__StartDate__c.daysBetween(quoteLine.SBQQ__EndDate__c);
                                endDateVrb = startDateVrb.addDays(termInDays);
                                if (orderEndDate == null || orderEndDate < endDateVrb) {
                                    orderEndDate = endDateVrb;
                                }
                                quoteLine.SBQQ__StartDate__c = startDateVrb;
                                quoteLine.SBQQ__EndDate__c = endDateVrb;
                            }
                        }

                        quoteLineGroup.SBQQ__EndDate__c = endDateVrb;

                        for (OrderItem orderItem : orderItems) {
                            if (orderItem.Quote_Line_Group__c == quoteLineGroup.Id) {
                                orderItem.ServiceDate = startDateVrb;
                                orderItem.EndDate = endDateVrb;
                            }
                        }

                        for (SBQQ__Subscription__c subscription : subscriptions) {
                            if (subscription.SBQQ__QuoteLine__r.SBQQ__Group__c == quoteLineGroup.Id) {
                                subscription.SBQQ__SubscriptionStartDate__c = startDateVrb;
                                subscription.SBQQ__SubscriptionEndDate__c = endDateVrb;
                            }
                        }
                        startDateVrb = endDateVrb.addDays(1);
                    }
                } else {
                    for (SBQQ__QuoteLine__c quoteLine : quoteLines) {
                        if (quote != null) {
                            startDateVrb = startDateVrb == null ? startDateVal : startDateVrb;
                            termInDays = quoteLine.SBQQ__StartDate__c.daysBetween(quoteLine.SBQQ__EndDate__c);
                            endDateVrb = startDateVrb.addDays(termInDays);
                            if (orderEndDate == null || orderEndDate < endDateVrb) {
                                orderEndDate = endDateVrb;
                            }
                            quoteLine.SBQQ__StartDate__c = startDateVrb;
                            quoteLine.SBQQ__EndDate__c = endDateVrb;
                        }
                    }
                    for (OrderItem orderItem : orderItems) {
                        orderItem.ServiceDate = startDateVrb;
                        orderItem.EndDate = endDateVrb;
                    }

                    for (SBQQ__Subscription__c subscription : subscriptions) {
                        subscription.SBQQ__SubscriptionStartDate__c = startDateVrb;
                        subscription.SBQQ__SubscriptionEndDate__c = endDateVrb;
                    }

                }
                for (Order order : orders) {
                    order.EndDate = orderEndDate;
                }
                for (Contract c : contracts) {
                    c.EndDate = orderEndDate;
                }
                if (!contracts.isEmpty()) {
                    update contracts;
                }
                if (!orders.isEmpty()) {
                    SBQQ.TriggerControl.disable();
                    update orders;
                    SBQQ.TriggerControl.enable();
                }
                for (Order order : orders) {
                    order.EffectiveDate = startDateVal;
                }
                List<Opportunity> oppList = new List<Opportunity>();
                for (Contract c : contracts) {
                    c.StartDate = startDateVal;

                    System.debug(c.SBQQ__RenewalOpportunity__c + ' ' + c.SBQQ__RenewalOpportunity__r.StageName);
                    if ((c.SBQQ__RenewalOpportunity__c != null && c.SBQQ__RenewalOpportunity__r.StageName != 'S6 - Closed Won') || Test.isRunningTest()) {
                        Opportunity opp = new Opportunity(Id = c.SBQQ__RenewalOpportunity__c);
                        opp.CloseDate = endDateVrb;
                        opp.Up_for_Renewal_Date__c = endDateVrb.addDays(1);
                        oppList.add(opp);
                    }
                }
                for(SBQQ__QuoteLine__c quoteLine : quoteLines){
                    for(OpportunityLineItem OLI: quoteLine.SBQQ__OpportunityProducts__r){
                        if(OLI.Start_Date__c != quoteLine.SBQQ__StartDate__c || OLI.End_Date__c != quoteLine.SBQQ__EndDate__c){
                            OLI.Start_Date__c = quoteLine.SBQQ__StartDate__c;
                            OLI.End_Date__c = quoteLine.SBQQ__EndDate__c;
                            OLIList.put(OLI.Id, OLI);
                        }
                    }
                }
                if (!quoteLines.isEmpty()) {
                    SBQQ.TriggerControl.disable();
                    update quoteLines;
                    if(!OLIList.values().isEmpty()){
                        update OLIList.values();
                    }
                    SBQQ.TriggerControl.enable();
                }

                if (!quoteLineGroups.isEmpty()) {
                    update quoteLineGroups;
                }
                if (!contracts.isEmpty()) {
                    update contracts;
                }
                if (oppList.size() > 0 && !Test.isRunningTest()) {
                    update oppList;
                }
                if (!orderItems.isEmpty()) {
                    SBQQ.TriggerControl.disable();
                    update orderItems;
                    SBQQ.TriggerControl.enable();
                }
                if (!orders.isEmpty()) {
                    SBQQ.TriggerControl.disable();
                    update orders;
                    SBQQ.TriggerControl.enable();
                }
                if (!subscriptions.isEmpty()) {
                    update subscriptions;
                }
            } else if (quote.MinStDt__c > startDateVal) {
                if (quote.MYC__c && !quoteLineGroups.isEmpty()) {
                    for (SBQQ__QuoteLineGroup__c quoteLineGroup : quoteLineGroups) {
                        startDateVrb = startDateVrb == null ? startDateVal : startDateVrb;
                        quoteLineGroup.SBQQ__StartDate__c = startDateVrb;


                        for (SBQQ__QuoteLine__c quoteLine : quoteLines) {
                            if (quoteLine.SBQQ__Group__c == quoteLineGroup.Id) {
                                termInDays = quoteLine.SBQQ__StartDate__c.daysBetween(quoteLine.SBQQ__EndDate__c);
                                endDateVrb = startDateVrb.addDays(termInDays);
                                if (orderEndDate == null || orderEndDate < endDateVrb) {
                                    orderEndDate = endDateVrb;
                                }
                                quoteLine.SBQQ__StartDate__c = startDateVrb;
                                quoteLine.SBQQ__EndDate__c = endDateVrb;
                            }
                        }

                        quoteLineGroup.SBQQ__EndDate__c = endDateVrb;

                        for (OrderItem orderItem : orderItems) {
                            if (orderItem.Quote_Line_Group__c == quoteLineGroup.Id) {
                                orderItem.ServiceDate = startDateVrb;
                                orderItem.EndDate = endDateVrb;
                            }
                        }
                        for (SBQQ__Subscription__c subscription : subscriptions) {
                            if (subscription.SBQQ__QuoteLine__r.SBQQ__Group__c == quoteLineGroup.Id) {
                                subscription.SBQQ__SubscriptionStartDate__c = startDateVrb;
                                subscription.SBQQ__SubscriptionEndDate__c = endDateVrb;
                            }
                        }
                        startDateVrb = endDateVrb.addDays(1);
                    }
                } else {
                    for (SBQQ__QuoteLine__c quoteLine : quoteLines) {
                        if (quote != null) {
                            startDateVrb = startDateVrb == null ? startDateVal : startDateVrb;
                            termInDays = quoteLine.SBQQ__StartDate__c.daysBetween(quoteLine.SBQQ__EndDate__c);
                            endDateVrb = startDateVrb.addDays(termInDays);
                            if (orderEndDate == null || orderEndDate < endDateVrb) {
                                orderEndDate = endDateVrb;

                            }
                            quoteLine.SBQQ__StartDate__c = startDateVrb;
                            quoteLine.SBQQ__EndDate__c = endDateVrb;

                        }
                    }

                    for (OrderItem orderItem : orderItems) {
                        orderItem.ServiceDate = startDateVrb;
                        orderItem.EndDate = endDateVrb;
                    }

                    for (SBQQ__Subscription__c subscription : subscriptions) {
                        subscription.SBQQ__SubscriptionStartDate__c = startDateVrb;
                        subscription.SBQQ__SubscriptionEndDate__c = endDateVrb;
                    }

                }
                for (Order order : orders) {
                    order.EffectiveDate = startDateVal;
                }
                List<Opportunity> oppList = new List<Opportunity>();
                for (Contract c : contracts) {
                    c.StartDate = startDateVal;
                    System.debug(c.SBQQ__RenewalOpportunity__c + ' ' + c.SBQQ__RenewalOpportunity__r.StageName);
                    if ((c.SBQQ__RenewalOpportunity__c != null && c.SBQQ__RenewalOpportunity__r.StageName != 'S6 - Closed Won') || Test.isRunningTest()) {
                        Opportunity opp = new Opportunity(Id = c.SBQQ__RenewalOpportunity__c);
                        opp.CloseDate = orderEndDate;
                        opp.Up_for_Renewal_Date__c = orderEndDate.addDays(1);
                        oppList.add(opp);
                    }
                }
                for(SBQQ__QuoteLine__c quoteLine : quoteLines){
                    for(OpportunityLineItem OLI: quoteLine.SBQQ__OpportunityProducts__r){
                        if(OLI.Start_Date__c != quoteLine.SBQQ__StartDate__c || OLI.End_Date__c != quoteLine.SBQQ__EndDate__c){
                            OLI.Start_Date__c = quoteLine.SBQQ__StartDate__c;
                            OLI.End_Date__c = quoteLine.SBQQ__EndDate__c;
                            OLIList.put(OLI.Id, OLI);
                        }
                    }
                }

                if (!contracts.isEmpty()) {
                    update contracts;
                }
                if (!orders.isEmpty()) {
                    SBQQ.TriggerControl.disable();
                    update orders;
                    SBQQ.TriggerControl.enable();
                }
                if (!orderItems.isEmpty()) {
                    SBQQ.TriggerControl.disable();
                    update orderItems;
                    SBQQ.TriggerControl.enable();

                }
                if (oppList.size() > 0 && !Test.isRunningTest()) {
                    update oppList;
                }
                if (!quoteLines.isEmpty()) {
                    SBQQ.TriggerControl.disable();
                    update quoteLines;
                    if(!OLIList.values().isEmpty()){
                        update OLIList.values();
                    }
                    SBQQ.TriggerControl.enable();
                }

                if (!quoteLineGroups.isEmpty()) {
                    update quoteLineGroups;
                }
                for (Order order : orders) {
                    order.EndDate = orderEndDate;
                }
                for (Contract c : contracts) {
                    c.EndDate = orderEndDate;
                }
                if (!orders.isEmpty() && !Test.isRunningTest()){
                    SBQQ.TriggerControl.disable();
                    update orders;
                    SBQQ.TriggerControl.enable();
                }
                if (!contracts.isEmpty()) {
                    update contracts;
                }

                if (!subscriptions.isEmpty()) {
                    update subscriptions;
                }
            } else if (quote.MinStDt__c == null) {
                Boolean updateForward = true;
                startDateVrb = startDateVrb == null ? startDateVal : startDateVrb;
                endDateVrb = startDateVrb.addMonths(12).addDays(-1);
                for (SBQQ__QuoteLine__c quoteLine : quoteLines) {
                    if (quote != null) {
                        quoteLine.SBQQ__StartDate__c = startDateVrb;
                        quoteLine.SBQQ__EndDate__c = endDateVrb;
                    }
                }

                for (SBQQ__Subscription__c subscription : subscriptions) {
                    subscription.SBQQ__SubscriptionStartDate__c = startDateVrb;
                    subscription.SBQQ__SubscriptionEndDate__c = endDateVrb;
                }
                for (Order order : orders) {
                    if (order.EffectiveDate > startDateVal)
                        updateForward = false;
                    order.EffectiveDate = startDateVal;
                }
                List<Opportunity> oppList = new List<Opportunity>();
                for (Contract c : contracts) {
                    c.StartDate = startDateVal;
                    c.EndDate = endDateVrb;
                    System.debug(c.SBQQ__RenewalOpportunity__c + ' ' + c.SBQQ__RenewalOpportunity__r.StageName);
                    if ((c.SBQQ__RenewalOpportunity__c != null && c.SBQQ__RenewalOpportunity__r.StageName != 'S6 - Closed Won') || Test.isRunningTest()) {
                        Opportunity opp = new Opportunity(Id = c.SBQQ__RenewalOpportunity__c);
                        opp.CloseDate = endDateVrb;
                        opp.Up_for_Renewal_Date__c = endDateVrb.addDays(1);
                        oppList.add(opp);
                    }
                }
                for(SBQQ__QuoteLine__c quoteLine : quoteLines){
                    for(OpportunityLineItem OLI: quoteLine.SBQQ__OpportunityProducts__r){
                        if(OLI.Start_Date__c != quoteLine.SBQQ__StartDate__c || OLI.End_Date__c != quoteLine.SBQQ__EndDate__c){
                            OLI.Start_Date__c = quoteLine.SBQQ__StartDate__c;
                            OLI.End_Date__c = quoteLine.SBQQ__EndDate__c;
                            OLIList.put(OLI.Id, OLI);
                        }
                    }
                }
                if (updateForward) {
                    if (!orders.isEmpty()) {
                        update orders;
                    }
                    if (!contracts.isEmpty()) {
                        update contracts;
                    }
                } else {
                    if (!contracts.isEmpty()) {
                        update contracts;
                    }
                    if (!orders.isEmpty()) {
                        update orders;
                    }
                }
                if (oppList.size() > 0 && !Test.isRunningTest()) {
                    update oppList;
                }
                if (!quoteLines.isEmpty()) {
                    SBQQ.TriggerControl.disable();
                    update quoteLines;
                    if(!OLIList.values().isEmpty()){
                        update OLIList.values();
                    }
                    SBQQ.TriggerControl.enable();
                }
                if (!quoteLineGroups.isEmpty()) {
                    update quoteLineGroups;
                }
                if (!subscriptions.isEmpty()) {
                    update subscriptions;
                }
            }

        } else if (renewalOppIds.contains(quote.SBQQ__Opportunity2__r.RecordTypeId) && renewalTypes.contains(quote.SBQQ__Opportunity2__r.Type)) {

            Date startDateVal = (quote.SBQQ__Opportunity2__r.SBQQ__PrimaryQuote__r.Platform__c == 'MCG' || quote.SBQQ__Opportunity2__r.Cloud_Opportunity__c || quote.SBCF_Sum_of_Tech_Support_Extension__c > 0) ?
                    quote.SBQQ__Opportunity2__r.CloseDate :
                    quote.SBQQ__Opportunity2__r.Ship_Date__c;

            List<Contract> contracts = [
                    SELECT Id, StartDate, SBQQ__RenewalOpportunity__c, SBQQ__RenewalOpportunity__r.StageName, EndDate, ContractTerm
                    FROM Contract
                    WHERE SBQQ__Quote__c = :quote.Id
            ];

            List<Opportunity> oppList = new List<Opportunity>();
            for (Contract c : contracts) {
                if (startDateVal != null) {
                    c.StartDate = startDateVal;

                    if ((c.SBQQ__RenewalOpportunity__c != null && c.SBQQ__RenewalOpportunity__r.StageName != 'S6 - Closed Won') || Test.isRunningTest()) {
                        Opportunity opp = new Opportunity(Id = c.SBQQ__RenewalOpportunity__c);
                        opp.CloseDate = startDateVal.addMonths(c.ContractTerm).addDays(-1);
                        opp.Up_for_Renewal_Date__c = startDateVal.addMonths(c.ContractTerm);
                        oppList.add(opp);
                    }
                }
            }

            if ((quote.SBQQ__Opportunity2__r.Type == 'Term Renewal' ||
                    quote.SBQQ__Opportunity2__r.Type == 'Cloud Renewal' ||
                    quote.SBQQ__Opportunity2__r.Type == 'Term Migration')
                    && quote.SBQQ__Opportunity2__r.DSI__r.Platform__c != 'MCE' && quote.SBQQ__Opportunity2__r.DSI__r.Platform__c != 'MCG' && quote.SBQQ__Opportunity2__r.DSI__r.Platform__c != 'CMC'
                    && quote.SBQQ__Opportunity2__r.DSI__r.Platform__c != 'MCS' && !TriggersHelper.runningInASandbox
            ) {
                OLICreateDSILineItemsHelper.createServiceNowCase(quote);
            }

            if (oppList.size() > 0 && !Test.isRunningTest()) {
                update oppList;
            }
        }

        quote.Date_Sync__c = true;
        update quote;

        OLICreateDSILineItemsHelper.createEntitlements(mstrSFUser, scope);
    }

    global void finish(Database.BatchableContext BC) {
    }
}