/************************************* MODIFICATION LOG ********************************************************************************************
* TaskTrigger 
*
* DESCRIPTION : Trigger for TaskTriggerHelper class with casecommunication and taskmilestone logics
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Alla Kiyashko            5/20/2018                       -- Original Version                                          
*/

trigger EventTrigger on Event (before insert, before update, after insert, after update, after delete) {

    if (Trigger.isBefore && (Trigger.isInsert || Trigger.isUpdate)) {
        EventTriggerHelper.updateEventFields(Trigger.new, Trigger.oldMap, Trigger.isInsert, Trigger.isUpdate);
    }
    
    if(Trigger.isAfter && (Trigger.isInsert || Trigger.isDelete || Trigger.isUpdate)){
              
        //update engagement score on qualified leads and contacts
        EventTriggerHelper.updateEngagementScore( (Trigger.isInsert || Trigger.isUpdate ? Trigger.New : Trigger.Old), Trigger.OldMap, Trigger.isUpdate, Trigger.isInsert, Trigger.isDelete);  
    }
    
 }