/************************************* MODIFICATION LOG ********************************************************************************************
* Account<PERSON>rigger<PERSON>andler
*
* DESCRIPTION : Handles all trigger logic related to account
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Amritha Buddharaju           01/19/2020                         -- Original Version Case 375915
* Anton Katyrenchuk            03/17/2020                         - added restriction for downgrading Account Category
* Amritha Buddharaju           03/30/2020                         - Updated Account Category picklist values to match multi picklist values(Case 390362)
* Priyank					   03/04/2021						  - Case 458877 - Salesforce - Account - adjust code to exclude MicroStrategy and Education Placeholder accounts from Account Category
* Archit Bansal				   01/02/2023						  - Case 520272 - add "Support Exception" flag
* Archit Bansal				   23/03/2023						  - Case 521033 - Create an end date field for the Expired Support Exception
*/

public without sharing class AccountTriggerHandler {

    public static void updateAccountCategory (List<Account> accList) {

        for (Account acc : accList) {
            if (acc.AccountCategory__c != null) {
                List<String> lst = acc.AccountCategory__c.split(';');
                Map<String, String> strMap = new Map<String, String>();
                String shortStr = '';

                if (lst.size() == 1) {
                    acc.Account_Category__c = acc.AccountCategory__c;
                    acc.AccountCategoryShort__c = lst[0].substring(0, 2);
                } else if (lst.size() > 1) {
                    for (String str : lst) {
                        strMap.put(str.substring(0, 1), str);
                        shortStr += str.substring(0, 2) + ',';
                    }
                    acc.AccountCategoryShort__c = shortStr.substring(0, shortStr.length() - 1);

                    if (strMap.containsKey('C')) {
                        acc.Account_Category__c = strMap.get('C');
                    } else if (strMap.containsKey('R') && !strMap.containsKey('C')) {
                        acc.Account_Category__c = strMap.get('R');
                    } else if (strMap.containsKey('I') && !strMap.containsKey('C') && !strMap.containsKey('R')) {
                        acc.Account_Category__c = strMap.get('I');
                    } else if (strMap.containsKey('P') && !strMap.containsKey('C') && !strMap.containsKey('R') && !strMap.containsKey('I')) {
                        acc.Account_Category__c = strMap.get('P');
                    } else if (strMap.containsKey('V') && !strMap.containsKey('C') && !strMap.containsKey('R') && !strMap.containsKey('I') && !strMap.containsKey('P')) {
                        acc.Account_Category__c = strMap.get('V');
                    }
                }
            } else {
                acc.Account_Category__c = null;
                acc.AccountCategoryShort__c = null;
            }
        }
    }

    public static void restrictDowngrading (Map<Id, Account> oldAccountsByIds, Map<Id, Account> newAccountsByIds) {
        Map<Id, Account> updatedAccounts = new Map<Id, Account>();
        for (Account newAccount : newAccountsByIds.values()) {
            Account oldAccount = oldAccountsByIds.get(newAccount.Id);
            if (oldAccount.AccountCategory__c != newAccount.AccountCategory__c &&
                (newAccount.AccountCategory__c == null || newAccount.AccountCategory__c.contains('P1 - Prospect'))
                ) {
                updatedAccounts.put(newAccount.Id, newAccount);
            }
        }
        
        if (!updatedAccounts.isEmpty()) {
            
            //Case 458877 - Start
            List<MSTR_Global_Configuation__mdt> globalConfigList = [SELECT Id, Value__c 
                                                                   FROM MSTR_Global_Configuation__mdt 
                                                                   WHERE DeveloperName IN ('MicroStrategy_Accounts')];
            
            if(globalConfigList.size() > 0) {
                List<String> accountIdsToBeSkipped = new List<String> {TriggersHelper.unmappedAccountId};
                for(MSTR_Global_Configuation__mdt globalConfig : globalConfigList) {
                    if(String.isNotBlank(globalConfig.Value__c)) {
                        accountIdsToBeSkipped.addAll(globalConfig.Value__c.split(';'));
                    }
                }
                
                if(accountIdsToBeSkipped.size() > 0) {
                    for(String accId : accountIdsToBeSkipped) {
                        updatedAccounts.remove(accId);
                    }
                }
            }
            //Case 458877 - End
            
            List<String> activeStages = new List<String> {
                'S1 - Recognize Needs', 'S2 - Define Requirements', 'S3 - Evaluate Options/Proposals',
                'S4 - Resolve Concerns', 'S5 - Negotiate/Purchase Solution'
            };
            List<Opportunity> activeAOpportunities = [
                SELECT Id, StageName, AccountId
                FROM Opportunity
                WHERE AccountId IN :updatedAccounts.keySet()
                    AND StageName IN :activeStages
                    AND RecordType.Name <> 'Archived Renewals'
            ];

            if (!activeAOpportunities.isEmpty()) {
                Set<Id> accountIdsToRestrict = new Set<Id>();
                for (Opportunity opp : activeAOpportunities) {
                    accountIdsToRestrict.add(opp.AccountId);
                }

                for (Id accountId : accountIdsToRestrict) {
                    updatedAccounts.get(accountId).addError(
                        'An Account with Active Opportunities must have an Account Category of P2 - Major Prospect or higher.'
                    );
                }
            }
        }
    }

    public static void stampABBillingModeAndPOCategory(Map<Id, Account> oldAccounts, Map<Id, Account> newAccounts) {
        Set<Id> changedAccounts = new Set<Id>();
        for (Account acc : newAccounts.values()) {
            if (acc.Billing_Mode__c != oldAccounts.get(acc.Id).Billing_Mode__c) {
                changedAccounts.add(acc.Id);
            }
            if (acc.PO_required__c != oldAccounts.get(acc.Id).PO_required__c) {
                changedAccounts.add(acc.Id);
            }
        }
        if (changedAccounts.isEmpty()) {
            return;
        }
        List<FIN_AddressBook__c> relatedAB = [
            SELECT Id,Billing_Mode__c,Account__c,Billing_Mode_Override__c,PO_Category__c
            FROM FIN_AddressBook__c
            WHERE Account__c IN :changedAccounts
        ];
        List<FIN_AddressBook__c> booksForUpdate = new List<FIN_AddressBook__c>();
        for (FIN_AddressBook__c book : relatedAB) {
            Account account = newAccounts.get(book.Account__c);
            Boolean updateNeeded = false;
            if (!book.Billing_Mode_Override__c && (book.Billing_Mode__c != account.Billing_Mode__c)) {
                book.Billing_Mode__c = account.Billing_Mode__c;
                updateNeeded = true;
            }
            if (book.PO_Category__c != account.PO_required__c) {
                book.PO_Category__c = account.PO_required__c;
                updateNeeded = true;
            }
            if (updateNeeded) {
                booksForUpdate.add(book);
            }
        }
        if (!booksForUpdate.isEmpty()) {
            update booksForUpdate;
        }
    }
    
    public static void populateClearExpiredSupportExceptionApprovedByField(Map<Id, Account> oldAccounts, Map<Id, Account> newAccounts){
        for (Account newAccount : newAccounts.values()) {
            Account oldAccount = oldAccounts.get(newAccount.Id);
            //Prevent setting Expired Support Exception End Date field if Expired Support Exception field is not checked
            if (newAccount.Expired_Support_Exception_End_Date__c != null && newAccount.Expired_Support_Exception__c != true && oldAccount.Expired_Support_Exception__c != true){
                newAccount.Expired_Support_Exception__c.addError('Please check Expired Support Exception field first');
            }
            // If Expired Support Exception field is already checked, prevent changing Expired Support Exception End Date field to a date more than 6 months (180 days) from the current date or a date in the past
            if (oldAccount.Expired_Support_Exception_End_Date__c != newAccount.Expired_Support_Exception_End_Date__c && newAccount.Expired_Support_Exception__c){
                Date currentDate = Date.today();
                Integer daysBetween = currentDate.daysBetween(newAccount.Expired_Support_Exception_End_Date__c);
                if(daysBetween > 180){
                    newAccount.Expired_Support_Exception_End_Date__c.addError('End date cannot be more than 6 months from the current date, please select an earlier date');
                }
                else if (daysBetween < 0){
				    newAccount.Expired_Support_Exception_End_Date__c.addError('End date cannot be in the past, please select a future date');                            
                }
            }
            if (oldAccount.Expired_Support_Exception__c != newAccount.Expired_Support_Exception__c){
                if(newAccount.Expired_Support_Exception__c){
                    //If End Date is not populated, throw error
                    if(newAccount.Expired_Support_Exception_End_Date__c == null){
                        newAccount.Expired_Support_Exception_End_Date__c.addError('An end date is required for Expired Support Exception, please select a date');
                    }
                    //If End Date is populated, validate its not more than 6 months (180 days) from the current date and a past date is not selected
                    else{
                        Date currentDate = Date.today();
                        Integer daysBetween = currentDate.daysBetween(newAccount.Expired_Support_Exception_End_Date__c);
                        if(daysBetween > 180){
                            newAccount.Expired_Support_Exception_End_Date__c.addError('End date cannot be more than 6 months from the current date, please select an earlier date');
                        }
                        else if (daysBetween < 0){
							newAccount.Expired_Support_Exception_End_Date__c.addError('End date cannot be in the past, please select a future date');                            
                        }
                    }
                    //Populate Expired Support Exception Approved By field with user name and EST timestamp 
                    newAccount.Expired_Support_Exception_Approved_By__c = UserInfo.getName() + ' - ' + DateTime.now().format('yyyy-MM-dd HH:mm:ss', 
                                                                                                                             'EST') + ' EST ';
                }
                //Clear Expired Support Exception Approved By and Expired Support Exception End Date fields when Expired Support Exception is unchecked
                else{
                    newAccount.Expired_Support_Exception_Approved_By__c = null;
                    newAccount.Expired_Support_Exception_End_Date__c = null;
                }
            }
        }  
    }

    //beforeInsert
    public static void populatePlan(Map<Id, Account> newAccounts) {
        for (Account acc : newAccounts.values()) {
            if (acc.Plan_Long__c != null) {
                acc.Plan__c = acc.Plan_Long__c.length() > 255 ? acc.Plan_Long__c.substring(0, 255) : acc.Plan_Long__c;
            }
        }
    }

    //beforeUpdate
    public static void populatePlan(Map<Id, Account> oldAccounts, Map<Id, Account> newAccounts) {
        for (Account newAcc : newAccounts.values()) {
            if(newAcc.Plan_Long__c != oldAccounts.get(newAcc.Id).Plan_Long__c){
                newAcc.Plan__c = newAcc.Plan_Long__c.length() > 255 ? newAcc.Plan_Long__c.substring(0, 255) : newAcc.Plan_Long__c;
            }
        }
    }

    public static void populateChurnDate(Map<Id, Account> oldAccounts, Map<Id, Account> newAccounts) {
        Set<Id> churnedAccountIds = new Set<Id>();
        for (Account newAccount : newAccounts.values()) {
            Account oldAccount = oldAccounts.get(newAccount.Id);
            if (newAccount.Account_Health__c == 'A0 - Churn' && newAccount.Account_Health__c != oldAccount.Account_Health__c) {
                churnedAccountIds.add(newAccount.Id);
            } else if(oldAccount.Account_Health__c == 'A0 - Churn' && oldAccount.Account_Health__c != newAccount.Account_Health__c) {
                newAccount.Churn_Date__c = null;
            }
        }

        if (!churnedAccountIds.isEmpty()) {
            Map<Id, Date> latestSubscriptionEndDate = new Map<Id, Date>();
            List<AggregateResult> results = [
                SELECT SBQQ__Account__c, MAX(SBQQ__EndDate__c) maxEndDate
                FROM SBQQ__Subscription__c
                WHERE SBQQ__Account__c IN :churnedAccountIds
                GROUP BY SBQQ__Account__c
            ];

            if(!results.isEmpty()) {
                for (AggregateResult ar : results) {
                    latestSubscriptionEndDate.put((Id)ar.get('SBQQ__Account__c'), (Date)ar.get('maxEndDate'));
                }

                for (Account account : newAccounts.values()) {
                    if (latestSubscriptionEndDate.containsKey(account.Id)) {
                        account.Churn_Date__c = latestSubscriptionEndDate.get(account.Id);
                    }
                }
            }
        }
    }
    
    public static void criticalAccountEscalation(Map<Id, Account> oldAccounts, Map<Id, Account> newAccounts) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        Id orgWideEmailAddressId;
        EmailTemplate emailTemplate;
        Id CAEDefaultTAM;
        Id CAEDefaultCSM_NAM;
        Id CAEDefaultCSM;
        Set<String> CAERecipients = new Set<String>{};
        Set<String> CAERecipientsNAM = new Set<String>{};
        Set<String> CAERecipientsIN = new Set<String>{};
        Set<Id> accountUsersIds = new Set<Id>{};
        Map<Id, User> accountUsers = new Map<Id, User>();
        List<Account> escalatedAccounts = new List<Account>();
        List<String> addresses = new List<String>();
        String htmlBody;
        String plainBody;
        String subjectLine;
        
        for(Account newAccount : newAccounts.values()) {
            if(newAccount.Critical_Account_Escalation__c) { 
                if(String.isBlank(newAccount.Business_Justification__c)){
                    newAccount.Business_Justification__c.addError('Business Justification is required for Critical Account Escalation');   
                }
                else if (oldAccounts.get(newAccount.Id) == null || newAccount.Critical_Account_Escalation__c != oldAccounts.get(newAccount.Id).Critical_Account_Escalation__c){
                    newAccount.Escalation_Date__c = Datetime.now();
                    escalatedAccounts.add(newAccount);
                    accountUsersIds.add(newAccount.Technical_Account_Manager__c);
                    accountUsersIds.add(newAccount.Customer_Success_Manager__c);
                }
            }
            else{
                newAccount.Business_Justification__c = '';
                newAccount.Escalation_Date__c = null;
            }
        }
        if(!escalatedAccounts.isEmpty()) {
            orgWideEmailAddressId = [SELECT Id FROM OrgWideEmailAddress WHERE Address = '<EMAIL>'].Id;
            emailTemplate = [SELECT Id, Subject, HtmlValue, Body FROM EmailTemplate WHERE DeveloperName = 'Critical_Account_Escalation'];
            CAEDefaultTAM = [SELECT Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'CAE_Default_TAM'].Value__c;
            CAEDefaultCSM_NAM = [SELECT Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'CAE_Default_CSM_NAM'].Value__c;
            CAEDefaultCSM = [SELECT Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'CAE_Default_CSM'].Value__c;
            CAERecipients = new Set<String>([SELECT Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'CAE_Recipients'].Value__c.split(';'));
            CAERecipientsNAM = new Set<String>([SELECT Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'CAE_Recipients_NAM'].Value__c.split(';'));
            CAERecipientsIN = new Set<String>([SELECT Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'CAE_Recipients_IN'].Value__c.split(';'));
			accountUsersIds.add(CAEDefaultTAM);
            accountUsersIds.add(CAEDefaultCSM);
            accountUsersIds.add(CAEDefaultCSM_NAM);
                
            accountUsers = new Map<Id, User>([SELECT Id, Name, Email, Manager.Email, isActive FROM User WHERE Id IN: accountUsersIds]);     
        } 
        for(Account escAccount: escalatedAccounts){
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            mail.setOrgWideEmailAddressId(orgWideEmailAddressId);
            subjectLine = emailTemplate.Subject;
            htmlBody = emailTemplate.HtmlValue;
            addresses = new List<String>();
            User TAM = accountUsers.get(escAccount.Technical_Account_Manager__c) != null && accountUsers.get(escAccount.Technical_Account_Manager__c).isActive? accountUsers.get(escAccount.Technical_Account_Manager__c): accountUsers.get(CAEDefaultTAM);
            User CSM = accountUsers.get(escAccount.Customer_Success_Manager__c) != null && accountUsers.get(escAccount.Customer_Success_Manager__c).isActive? accountUsers.get(escAccount.Customer_Success_Manager__c): (escAccount.Theatre_Name__c != null && escAccount.Theatre_Name__c == 'NAM')? accountUsers.get(CAEDefaultCSM_NAM): accountUsers.get(CAEDefaultCSM);
            List<User> usersToAddress = new List<User>{TAM, CSM};
            for(User u: usersToAddress){
            	addresses.add(u.Email);
                if(u.Id != CAEDefaultTAM && u.Id != CAEDefaultCSM && u.Id != CAEDefaultCSM_NAM){
            		addresses.add(u.Manager.Email);
                }
            }
            addresses.addAll(CAERecipients);
            if(escAccount.Theatre_Name__c != null && escAccount.Theatre_Name__c == 'NAM'){
            	addresses.addAll(CAERecipientsNAM);
            }
            else{
            	addresses.addAll(CAERecipientsIN);
            }
            mail.setToAddresses(addresses);
            subjectLine = subjectLine.replace('[Account Name]', escAccount.Name);
            if(TAM.Id == CSM.Id){
            	htmlBody = htmlBody.replace('[TAM] and [CSM]', TAM.Name); 
                htmlBody = htmlBody.replace('[CSM]:', ''); 
            }
            htmlBody = htmlBody.replace('[TAM]', TAM.Name);
            htmlBody = htmlBody.replace('[CSM]', CSM.Name);
            htmlBody = htmlBody.replace('[Date]', Datetime.now().format('MM/dd/yyyy'));
            htmlBody = htmlBody.replace('[Business Justification]', escAccount.Business_Justification__c);
            htmlBody = htmlBody.replace('[Account Name]', escAccount.Name);
            htmlBody = htmlBody.replace('[Account Link]', '<a href=' + Url.getOrgDomainUrl().toExternalForm() + '/' + escAccount.Id + '>' + escAccount.Name + '</a>');
            mail.setSubject(subjectLine);
            mail.setHtmlBody(htmlBody);
            emails.add(mail);
        }
        if(!emails.isEmpty() && !Test.isRunningTest() && !TriggersHelper.runningInASandbox){
        	Messaging.sendEmail(emails);
        }
    }
}