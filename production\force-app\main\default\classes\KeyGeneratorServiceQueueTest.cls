/************************************* MODIFICATION LOG ********************************************************************************************
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                 MODIFICATION DATE				REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Priyank					06/17/2015						Case 381435 - CPQ - Renewals - Enable the translation of DSIs by the SOP team
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Class - KeyGeneratorController
*/

@IsTest(SeeAllData = true)
public class KeyGeneratorServiceQueueTest {

    public static testMethod void keyGenExceptionTest() {


        //Create Product
        Product2 product = new Product2();
        product.IsActive = true;
        product.Software_Type_ID__c = 3;
        product.Name = 'Test';
        product.RMS_Product_ID__c = 608;
        product.Version_ID__c = 104;
        product.ProductCode = '12323';
        insert product;

        String rtIdP = [
            SELECT Id
            FROM RecordType
            WHERE Name = 'Product'
            AND SobjectType = 'Product2'
        ].Id;


        Product2 product2 = new Product2();
        product2.IsActive = true;
        product2.Software_Type_ID__c = 3;
        product2.Name = 'Test';
        product2.RMS_Product_ID__c = 608;
        product2.Version_ID__c = 104;
        product2.ProductCode = '**********';
        product2.Previous_SKU__c = product.Id;
        product2.RecordTypeId = rtIdP;
        product2.Interval__c = 'Term';
        insert product2;

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
//        acc.Theatre__c = [SELECT Id FROM Theatre__c WHERE Name = 'International' LIMIT 1].Id;
        acc.District__c = [SELECT Id FROM District__c WHERE Theatre__c = 'International' AND Business_Unit__r.Name = 'Geo-Coding' LIMIT 1].Id;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;


        // Add Assets
        Asset asset1 = new Asset();
        asset1.Name = 'Test Asset';
        asset1.AccountId = acc.Id;
        asset1.Quantity = 1;
        asset1.SBCF_DSI__c = dsi.Id;
        asset1.Product2Id = product.Id;
        asset1.Key_Group_multi__c = 'Prod Key 1';
        //asset1.Key_Group_ID__c=1;
        insert asset1;

        Asset asset2 = new Asset();
        asset2.Name = 'Test Asset';
        asset2.Quantity = 1;
        asset2.SBCF_DSI__c = dsi.Id;
        asset2.AccountId = acc.Id;
        asset2.Product2Id = product.Id;
        asset2.Key_Group_multi__c = 'Prod Key 2';
        //asset2.Key_Group_ID__c=2;
        insert asset2;

        Asset asset3 = new Asset();
        asset3.Name = 'Test Asset';
        asset3.Quantity = 1;
        asset3.SBCF_DSI__c = dsi.Id;
        asset3.AccountId = acc.Id;
        asset3.Product2Id = product.Id;
        asset3.Key_Group_multi__c = 'Prod Key 1';

        insert asset3;

        // Add Subscriptions
        SBQQ__Subscription__c subs1 = new SBQQ__Subscription__c();
        subs1.SBQQ__Account__c = acc.Id;
        subs1.SBQQ__Product__c = product2.Id;
        subs1.SBQQ__Quantity__c = 1;
        subs1.SBCF_DSI__c = dsi.Id;
        subs1.Key_Group_multi__c = 'Prod Key 1';
        insert subs1;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '10.4.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);

        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenGeospatialExceptionTest() {


        //Create Product
        Product2 product = new Product2();
        product.IsActive = true;
        product.Software_Type_ID__c = 3;
        product.Name = 'Test';
        product.RMS_Product_ID__c = 608;
        product.Version_ID__c = 104;
        product.ProductCode = '12323';
        insert product;

        String rtIdP = [
            SELECT Id
            FROM RecordType
            WHERE Name = 'Product'
            AND SobjectType = 'Product2'
        ].Id;


        Product2 product2 = new Product2();
        product2.IsActive = true;
        product2.Software_Type_ID__c = 3;
        product2.Name = 'Test';
        product2.RMS_Product_ID__c = 608;
        product2.Version_ID__c = 104;
        product2.ProductCode = '**********';
        product2.Previous_SKU__c = product.Id;
        product2.RecordTypeId = rtIdP;
        product2.Interval__c = 'Term';
        insert product2;

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
//        acc.Theatre__c = [SELECT Id FROM Theatre__c WHERE Name = 'International' LIMIT 1].Id;
        acc.District__c = [SELECT Id FROM District__c WHERE Theatre__c = 'International' AND Business_Unit__r.Name = 'Geo-Coding' LIMIT 1].Id;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;


        // Add Assets
        Asset asset1 = new Asset();
        asset1.Name = 'Test Asset';
        asset1.AccountId = acc.Id;
        asset1.Quantity = 1;
        asset1.SBCF_DSI__c = dsi.Id;
        asset1.Product2Id = product.Id;
        asset1.Key_Group_multi__c = 'Prod Key 1';
        //asset1.Key_Group_ID__c=1;
        insert asset1;

        Asset asset2 = new Asset();
        asset2.Name = 'Test Asset';
        asset2.Quantity = 1;
        asset2.SBCF_DSI__c = dsi.Id;
        asset2.AccountId = acc.Id;
        asset2.Product2Id = product.Id;
        asset2.Key_Group_multi__c = 'Prod Key 2';
        //asset2.Key_Group_ID__c=2;
        insert asset2;

        Asset asset3 = new Asset();
        asset3.Name = 'Test Asset';
        asset3.Quantity = 1;
        asset3.SBCF_DSI__c = dsi.Id;
        asset3.AccountId = acc.Id;
        asset3.Product2Id = product.Id;
        asset3.Key_Group_multi__c = 'Prod Key 1';

        insert asset3;

        // Add Subscriptions
        SBQQ__Subscription__c subs1 = new SBQQ__Subscription__c();
        subs1.SBQQ__Account__c = acc.Id;
        subs1.SBQQ__Product__c = product2.Id;
        subs1.SBQQ__Quantity__c = 1;
        subs1.SBCF_DSI__c = dsi.Id;
        subs1.Key_Group_multi__c = 'Prod Key 1';
        insert subs1;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
//        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '10.4.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.Type__c = 'Mapbox Key';
        tempRequest.QuoteID__c = 104;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);

        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void esriKeyTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '11.0.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        tempRequest.Employee_Key__c = true;
        tempRequest.Map_To_Previous_SKU__c = false;
        tempRequest.Type__c = 'ESRI Key 2021 update 4 and earlier';

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('ESRIKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void geospatialKeyTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '11.0.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        tempRequest.Employee_Key__c = true;
        tempRequest.Map_To_Previous_SKU__c = false;
        tempRequest.Type__c = 'Mapbox Key';

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('GeospatialKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenExceptionTestDowngrade() {


        //Create Product
        Product2 prodct = new Product2();
        prodct.IsActive = true;
        prodct.Software_Type_ID__c = 3;
        prodct.Name = 'Test';
        prodct.RMS_Product_ID__c = 608;
        prodct.Version_ID__c = 104;
        prodct.ProductCode = '12323';
        insert prodct;

        String rtIdP = [
            SELECT Id
            FROM RecordType
            WHERE Name = 'Product'
            AND SobjectType = 'Product2'
        ].Id;


        Product2 prodct2 = new Product2();
        prodct2.IsActive = true;
        prodct2.Software_Type_ID__c = 3;
        prodct2.Name = 'Test';
        prodct2.RMS_Product_ID__c = 608;
        prodct2.Version_ID__c = 104;
        prodct2.ProductCode = '**********';
        prodct2.Previous_SKU__c = prodct.Id;
        prodct2.RecordTypeId = rtIdP;
        prodct2.Interval__c = 'Term';
        insert prodct2;

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        acc.District__c = [SELECT Id FROM District__c WHERE Theatre__c = 'International' AND Business_Unit__r.Name = 'Geo-Coding' LIMIT 1].Id;

        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;


        // Add Assets
        Asset asset1 = new Asset();
        asset1.Name = 'Test Asset';
        asset1.AccountId = acc.Id;
        asset1.Quantity = 1;
        asset1.SBCF_DSI__c = dsi.Id;
        asset1.Product2Id = prodct.Id;
        asset1.Key_Group_multi__c = 'Prod Key 1';
        //asset1.Key_Group_ID__c=1;
        insert asset1;

        Asset asset2 = new Asset();
        asset2.Name = 'Test Asset';
        asset2.Quantity = 1;
        asset2.SBCF_DSI__c = dsi.Id;
        asset2.AccountId = acc.Id;
        asset2.Product2Id = prodct.Id;
        asset2.Key_Group_multi__c = 'Prod Key 2';
        //asset2.Key_Group_ID__c=2;
        insert asset2;

        Asset asset3 = new Asset();
        asset3.Name = 'Test Asset';
        asset3.Quantity = 1;
        asset3.SBCF_DSI__c = dsi.Id;
        asset3.AccountId = acc.Id;
        asset3.Product2Id = prodct.Id;
        asset3.Key_Group_multi__c = 'Prod Key 1';

        insert asset3;

        // Add Subscriptions
        SBQQ__Subscription__c subs1 = new SBQQ__Subscription__c();
        subs1.SBQQ__Account__c = acc.Id;
        subs1.SBQQ__Product__c = prodct2.Id;
        subs1.SBQQ__Quantity__c = 1;
        subs1.SBCF_DSI__c = dsi.Id;
        subs1.Key_Group_multi__c = 'Prod Key 1';
        insert subs1;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '10.1.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);

        try {
            Test.startTest();


            System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

            Test.stopTest();
        } catch (Exception e) {
            System.assert(e.getMessage().contains('This organization'));
        }

    }

    public static testMethod void keyGenExceptionMapToPreviousTest() {
        String rtcon = [
            SELECT Id
            FROM RecordType
            WHERE SobjectType = 'Contact'
            AND Name = 'Sales Contact'
        ].Id;

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;

        Contact con1 = new Contact();
        con1.RecordTypeId = rtcon;
        con1.FirstName = 'Test';
        con1.LastName = 'Name';
        con1.Email = '<EMAIL>';
        con1.AccountId = acc.Id;
        con1.MailingCountry = 'United States';
        // con1.District__c='a1XE0000000FoWF';
        insert con1;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82457', '80447', '105080', '82381', '82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;

        //Product_Key_Request__c tempRequest2 = new Product_Key_Request__c();

        Opportunity opp = [SELECT Id FROM Opportunity LIMIT 1];


        //Opportunity opp1 = TestHelper.createOpportunity(false, false, false);


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '10.4.8';
        tempRequest.Ship_Contact__c = con1.Id;
        tempRequest.Opportunity__c = opp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        tempRequest.Map_To_Previous_SKU__c = true;
        tempRequest.Expiration_Date__c = Date.today().addDays(40);

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);

        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();


    }

    public static testMethod void keyGenExceptionLockedKey1048Test() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82262 ', '82260', '82192', ' 82259') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82262 ', '82260', '82192', ' 82259') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '10.4.8';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        tempRequest.Map_To_Previous_SKU__c = true;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenCollaborationTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82262 ', '82260', '82192', ' 82259', '82371', '82387') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '2019';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;


        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenArchitectBundleTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('84050 ', '82260', '82192', ' 82259', '82371', '82387', '82445') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '2019';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;


        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenArchitectBundleTestNoDrivers() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('84050', '82260', '82192', '82371') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '2019';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;


        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenTestClientArchitectGeospatial() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('89105', '89605', '89565', '89485') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '2019';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;


        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenTestClientMobileException() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('89225') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '2019';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;


        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);


        try {
            Test.startTest();


            System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

            Test.stopTest();
        } catch (Exception e) {
            System.assert(e.getMessage().contains('violates'));
        }

    }

    public static testMethod void keyGenTestClientArchitectGeospatial2019U2() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('89105', '89605', '89565', '89485') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '2019 Update 2';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;


        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenTestClientHyperMobile() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('84116', '82309', '82365', '84000') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '2019';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;


        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }


    public static testMethod void keyGenTestClientArchitectGeospatial1048() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('89105', '89605', '89565', '89485') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '10.4.8';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;


        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGen110EmployeeTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82262 ', '82260', '82192', ' 82259') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        //tempRequest.DSI__c = dsi.id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '11.0.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        tempRequest.Employee_Key__c = true;
        tempRequest.Map_To_Previous_SKU__c = false;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenExceptionLockedKeyTest11() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82444 ', '82364', '82365', '82370', '82311') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82444 ', '82364', '82365') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '11.1.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        //tempRequest.Map_To_Previous_SKU__c=TRUE;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenExceptionLockedKeyEmailView() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82444 ', '82364', '82365', '82370', '82311') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82444 ', '82364', '82365') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;


        KeyGeneratorController.generateEmailView(dsi.Id, '10.4.0', false);
        KeyGeneratorController.generateEmailView(dsi.Id, '9.4.1', true);
        KeyGeneratorController.generateEmailView(dsi.Id, '106', true);

    }


    public static testMethod void keyGenExceptionLockedKeyTranslationEventViewOnlyStandard() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82192 ', '82260', '82262') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;


        KeyGeneratorController.generateTranslationEvent(true, dsi.Id, false, false, new List<String>(), null);


    }

    public static testMethod void keyGenExceptionLockedKeyTranslationEventViewOnlyEnhanced() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82192 ', '82260', '82262') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;

        List<String> enhancedProds = new List<String>();
        enhancedProds.add('Drivers - OLAP');
        enhancedProds.add('Drivers - Big Data');
        enhancedProds.add('Gateway - EMM - Mobile Iron');

        KeyGeneratorController.generateTranslationEvent(true, dsi.Id, true, false, enhancedProds, null);


    }

    public static testMethod void keyGenExceptionLockedKeyTranslationEventViewOnlyStdToEnhanced() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82365 ', '82307', '82309') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asset1.Translated__c = true;
            asstInsert.add(asset1);
        }


        insert asstInsert;

        List<String> enhancedProds = new List<String>();
        enhancedProds.add('Drivers - OLAP');
        enhancedProds.add('Drivers - Big Data');
        enhancedProds.add('Gateway - EMM - Mobile Iron');

        KeyGeneratorController.generateTranslationEvent(true, dsi.Id, false, true, enhancedProds, null);


    }

    public static testMethod void keyGenExceptionLockedKeyTranslationEventStandard() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82192 ', '82260', '82262') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('30089 ') AND SBCF_Renewal_Category__c = 'Maintenance 1st Year' AND Name = 'Standard Support'
            AND IsActive = TRUE
        ];
        insert asstInsert;

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();

        List < SBQQ__SubscribedAsset__c > subsAsstInsert = new List < SBQQ__SubscribedAsset__c > ();


        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 1;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);

        }


        //insert asstInsert;
        insert subsInsert;

        for (SBQQ__Subscription__c sub : subsInsert) {
            for (Asset asst : asstInsert) {
                SBQQ__SubscribedAsset__c subsAsset = new SBQQ__SubscribedAsset__c();
                subsAsset.SBQQ__Asset__c = asst.Id;
                subsAsset.SBQQ__Subscription__c = sub.Id;
                subsAsset.SBQQ__Active__c = true;
                subsAsstInsert.add(subsAsset);

            }
        }


        insert subsAsstInsert;


        KeyGeneratorController.generateTranslationEvent(false, dsi.Id, false, false, new List<String>(), null);


    }


    public static testMethod void keyGenExceptionLockedKeyTranslationEventEnhanced() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82192 ', '82260', '82262') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('30089 ') AND SBCF_Renewal_Category__c = 'Maintenance 1st Year' AND Name = 'Standard Support'
            AND IsActive = TRUE
        ];
        insert asstInsert;

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();

        List < SBQQ__SubscribedAsset__c > subsAsstInsert = new List < SBQQ__SubscribedAsset__c > ();


        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 1;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);

        }


        //insert asstInsert;
        insert subsInsert;

        for (SBQQ__Subscription__c sub : subsInsert) {
            for (Asset asst : asstInsert) {
                SBQQ__SubscribedAsset__c subsAsset = new SBQQ__SubscribedAsset__c();
                subsAsset.SBQQ__Asset__c = asst.Id;
                subsAsset.SBQQ__Subscription__c = sub.Id;
                subsAsset.SBQQ__Active__c = true;
                subsAsstInsert.add(subsAsset);

            }
        }


        insert subsAsstInsert;

        List<String> enhancedProds = new List<String>();
        enhancedProds.add('Drivers - OLAP');
        enhancedProds.add('Drivers - Big Data');
        enhancedProds.add('Gateway - EMM - Mobile Iron');

        KeyGeneratorController.generateTranslationEvent(false, dsi.Id, true, false, enhancedProds, null);


    }


    public static testMethod void keyGenExceptionLockedKeyTranslationEventStdToEnhanced() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82192 ', '82260', '82262') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('30089 ') AND SBCF_Renewal_Category__c = 'Maintenance 1st Year' AND Name = 'Standard Support'
            AND IsActive = TRUE
        ];
        insert asstInsert;

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();

        List < SBQQ__SubscribedAsset__c > subsAsstInsert = new List < SBQQ__SubscribedAsset__c > ();


        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 1;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);

        }


        //insert asstInsert;
        insert subsInsert;

        for (SBQQ__Subscription__c sub : subsInsert) {
            for (Asset asst : asstInsert) {
                SBQQ__SubscribedAsset__c subsAsset = new SBQQ__SubscribedAsset__c();
                subsAsset.SBQQ__Asset__c = asst.Id;
                subsAsset.SBQQ__Subscription__c = sub.Id;
                subsAsset.SBQQ__Active__c = true;
                subsAsstInsert.add(subsAsset);

            }
        }


        insert subsAsstInsert;


        KeyGeneratorController.generateTranslationEvent(false, dsi.Id, false, false, new List<String>(), null);

        List<String> enhancedProds = new List<String>();
        enhancedProds.add('Drivers - OLAP');
        enhancedProds.add('Drivers - Big Data');
        enhancedProds.add('Gateway - EMM - Mobile Iron');

        KeyGeneratorController.generateTranslationEvent(false, dsi.Id, false, true, enhancedProds, null);


    }


    public static testMethod void keyGenExceptionLockedKeyTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82262 ', '82260', '82192', ' 82259') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82262 ', '82260', '82192', ' 82259') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '10.4.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        //tempRequest.Map_To_Previous_SKU__c=TRUE;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenExceptionLockedKeyTestCPUmhzrated() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82261 ', '82263') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82261 ', '82263') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '10.4.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        //tempRequest.Map_To_Previous_SKU__c=TRUE;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenExceptionLockedKeyTestCPUunrated() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [SELECT Id, Name FROM Product2 WHERE ProductCode IN('105063 ', '105069')];


        prodsToAdd = [SELECT Id, Name FROM Product2 WHERE ProductCode IN('105063 ', '105069')];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            subsInsert.add(asset1);
        }


        //insert asstInsert;
        insert subsInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '10.4.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        //tempRequest.Map_To_Previous_SKU__c=TRUE;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenExceptionLockedKeyOld() {
        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [SELECT Id, Name FROM Product2 WHERE ProductCode IN('74197')];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }


        insert asstInsert;
        //insert subsInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '8.1.2';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        //tempRequest.Map_To_Previous_SKU__c=TRUE;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenExceptionEmptyProducts() {
        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [SELECT Id, Name FROM Product2 WHERE ProductCode IN('74197')];


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = true;
        tempRequest.Version_List__c = '8.1.2';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        //tempRequest.Map_To_Previous_SKU__c=TRUE;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        try {
            Test.startTest();


            System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

            Test.stopTest();
        } catch (Exception e) {
            System.assert(e.getMessage().contains('products'));
        }

    }

    public static testMethod void keyGenExceptionLockedKey1048AutoTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        acc.District__c = [SELECT Id FROM District__c WHERE Theatre__c = 'International' AND Business_Unit__r.Name = 'Geo-Coding' LIMIT 1].Id;

        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('81659', '105080', '82381', '82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '10.4.8';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        tempRequest.Map_To_Previous_SKU__c = true;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }


    public static testMethod void keyGenExceptionLockedKey1048AutoPreshippedTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82457', '80447', '105080', '82381', '82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;

        Product_Key_Request__c tempRequest2 = new Product_Key_Request__c();

        tempRequest2.Account__c = acc.Id;
        tempRequest2.DSI__c = dsi.Id;
        tempRequest2.Manual_Key__c = false;
        tempRequest2.Version_List__c = '10.4.0';
        tempRequest2.Version_ID__c = 104;
        tempRequest2.isUniversal__c = true;
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest2.QuoteID__c = 104;
        tempRequest2.LockedKey__c = false;
        //tempRequest2.Map_To_Previous_SKU__c=TRUE;
        tempRequest2.Delivery_Date__c = Date.today().addDays(20);
        tempRequest2.New_Key_Delivered__c = true;
        insert tempRequest2;

        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '10.4.0';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = false;
        tempRequest.Map_To_Previous_SKU__c = false;

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }

    public static testMethod void keyGenExceptionLockedKey1048AutoTermTest() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        acc.District__c = [SELECT Id FROM District__c WHERE Theatre__c = 'International' AND Business_Unit__r.Name = 'Geo-Coding' LIMIT 1].Id;

        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82457', '80447', '105080', '82381', '82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;

        Product_Key_Request__c tempRequest2 = new Product_Key_Request__c();

        tempRequest2.Account__c = acc.Id;
        tempRequest2.DSI__c = dsi.Id;
        tempRequest2.Manual_Key__c = false;
        tempRequest2.Version_List__c = '10.4.8';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest2.QuoteID__c = 104;
        tempRequest2.LockedKey__c = true;
        tempRequest2.Map_To_Previous_SKU__c = true;
        tempRequest2.Delivery_Date__c = Date.today().addDays(1);
        tempRequest2.New_Key_Delivered__c = true;
        insert tempRequest2;

        Opportunity opp1 = TestHelper.createOpportunity(false, false, false);


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '10.4.8';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        tempRequest.Opportunity__c = opp1.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        tempRequest.Map_To_Previous_SKU__c = true;
        tempRequest.Expiration_Date__c = Date.today().addDays(40);

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);
        
        Key_Generator_Combo_Products__mdt comboProductRule = new Key_Generator_Combo_Products__mdt();
        comboProductRule.Label = '89412';
        comboProductRule.DeveloperName = 'X89412'; // API Name, must be unique within the MDT
        comboProductRule.Child_Product_Codes__c = '89408;';
        comboProductRule.Named_User__c = 2;
        comboProductRule.Quantity__c = 2;

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        Test.startTest();

        System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

        Test.stopTest();

    }


    public static testMethod void keyGenExceptionLockedKey1048AutoTermTestException() {

        //Create Account
        Account acc = TestHelper.createAccount();
        acc.Compass_ID__c = 1234987;
        acc.Maintenance_AR_90__c = true;
        acc.District__c = [SELECT Id FROM District__c WHERE Theatre__c = 'International' AND Business_Unit__r.Name = 'Geo-Coding' LIMIT 1].Id;

        update acc;
        // Create DSI
        DSI__c dsi = new DSI__c();
        dsi.Name = 'Test DSI Test';
        dsi.Account__c = acc.Id;
        insert dsi;

        List < Product2 > prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82457', '80447', '105080', '82381', '82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Perpetual'
            AND IsActive = TRUE
        ];
        List < Asset > asstInsert = new List < Asset > ();
        for (Product2 prod : prodsToAdd) {

            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prod.Id;
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            asset1.Key_Group_multi__c = 'Manual Key 1';
            asstInsert.add(asset1);
        }

        prodsToAdd = [
            SELECT Id, Name
            FROM Product2
            WHERE ProductCode IN('82261', '82262', '82260', '82192', ' 82259') AND Interval__c = 'Term'
            AND IsActive = TRUE
        ];

        List < SBQQ__Subscription__c > subsInsert = new List < SBQQ__Subscription__c > ();
        for (Product2 prod : prodsToAdd) {

            SBQQ__Subscription__c asset1 = new SBQQ__Subscription__c();
            //asset1.Name='Test Asset';
            asset1.SBQQ__Account__c = acc.Id;
            asset1.SBQQ__Quantity__c = 10;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.SBQQ__Product__c = prod.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            asset1.SBQQ__SubscriptionStartDate__c = Date.today();
            asset1.SBQQ__SubscriptionEndDate__c = Date.today().addDays(20);
            subsInsert.add(asset1);
        }


        insert asstInsert;
        insert subsInsert;

        Product_Key_Request__c tempRequest2 = new Product_Key_Request__c();

        tempRequest2.Account__c = acc.Id;
        tempRequest2.DSI__c = dsi.Id;
        tempRequest2.Manual_Key__c = false;
        tempRequest2.Version_List__c = '10.4.8';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        //tempRequest.Opportunity__c=temp.Id;
        tempRequest2.QuoteID__c = 104;
        tempRequest2.LockedKey__c = true;
        tempRequest2.Map_To_Previous_SKU__c = true;
        tempRequest2.Delivery_Date__c = Date.today().addDays(1);
        tempRequest2.New_Key_Delivered__c = true;
        insert tempRequest2;

        Opportunity opp1 = TestHelper.createOpportunity(false, false, false);


        Product_Key_Request__c tempRequest = new Product_Key_Request__c();

        tempRequest.Account__c = acc.Id;
        tempRequest.DSI__c = dsi.Id;
        tempRequest.Manual_Key__c = false;
        tempRequest.Version_List__c = '10.4.8';
        //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
        tempRequest.Opportunity__c = opp1.Id;
        tempRequest.QuoteID__c = 104;
        tempRequest.LockedKey__c = true;
        tempRequest.Map_To_Previous_SKU__c = true;
        tempRequest.Expiration_Date__c = Date.today().addDays(40);

        insert tempRequest;

        List < Id > requestIds = new List < Id > ();
        requestIds.add(tempRequest.Id);

        StaticResourceCalloutMock mock = new StaticResourceCalloutMock();
        mock.setStaticResource('CDKeyMock');
        mock.setStatusCode(200);
        mock.setHeader('Content-Type', 'application/json');
        Test.setMock(HttpCalloutMock.class, mock);
        try {
            Test.startTest();


            System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));

            Test.stopTest();
        } catch (Exception e) {
            System.assert(e.getMessage().contains('This organization'));
        }


    }
	
    public static testMethod void createTestUser() {
        Test.startTest();
        KeyGeneratorController.createCommunityUser([SELECT Id from Contact LIMIT 1].Id);
        KeyGeneratorController.normalizeKeyGroups(new List<sObject>());
        Test.stopTest();
    }

}