<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>SBQQ__Field__c</fullName>
    <deprecated>false</deprecated>
    <description>Field on Quote Line or Product Option object that is evaluated by this condition.</description>
    <inlineHelpText>Choose the field that should contain the value you enter in the Value field.</inlineHelpText>
    <label>Field</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Product Code</fullName>
                <default>false</default>
                <label>Product Code</label>
            </value>
            <value>
                <fullName>SBCF_DSI__c</fullName>
                <default>false</default>
                <label>SBCF_DSI__c</label>
            </value>
            <value>
                <fullName>SBQQ__ProductName__c</fullName>
                <default>false</default>
                <label>SBQQ__ProductName__c</label>
            </value>
            <value>
                <fullName>SBQQ__RenewedSubscription__c</fullName>
                <default>false</default>
                <label>SBQQ__RenewedSubscription__c</label>
            </value>
            <value>
                <fullName>SBQQ__Discount__c</fullName>
                <default>false</default>
                <label>SBQQ__Discount__c</label>
            </value>
            <value>
                <fullName>SBQQ__AdditionalDiscountAmount__c</fullName>
                <default>false</default>
                <label>SBQQ__AdditionalDiscountAmount__c</label>
            </value>
            <value>
                <fullName>SBQQ__EffectiveQuantity__c</fullName>
                <default>false</default>
                <label>SBQQ__EffectiveQuantity__c</label>
            </value>
            <value>
                <fullName>SBCF_Legacy__c</fullName>
                <default>false</default>
                <label>SBCF_Legacy__c</label>
            </value>
            <value>
                <fullName>Enterprise_Support__c</fullName>
                <default>false</default>
                <label>Enterprise_Support__c</label>
            </value>
            <value>
                <fullName>SBQQ__DefaultSubscriptionTerm__c</fullName>
                <default>false</default>
                <label>SBQQ__DefaultSubscriptionTerm__c</label>
            </value>
            <value>
                <fullName>SBQQ__SubscriptionTerm__c</fullName>
                <default>false</default>
                <label>SBQQ__SubscriptionTerm__c</label>
            </value>
            <value>
                <fullName>ES_Credit__c</fullName>
                <default>false</default>
                <label>ES_Credit__c</label>
            </value>
            <value>
                <fullName>ES_Rate__c</fullName>
                <default>false</default>
                <label>ES_Rate__c</label>
            </value>
            <value>
                <fullName>SBCF_Document_Language__c</fullName>
                <default>false</default>
                <label>SBCF_Document_Language__c</label>
            </value>
            <value>
                <fullName>EduOnly__c</fullName>
                <default>false</default>
                <label>EduOnly__c</label>
            </value>
            <value>
                <fullName>CreatedDate</fullName>
                <default>false</default>
                <label>CreatedDate</label>
            </value>
            <value>
                <fullName>Quote_Approved__c</fullName>
                <default>false</default>
                <label>Quote_Approved__c</label>
            </value>
            <value>
                <fullName>PriceLock__c</fullName>
                <default>false</default>
                <label>PriceLock__c</label>
            </value>
            <value>
                <fullName>SBQQ__ListPrice__c</fullName>
                <default>false</default>
                <label>SBQQ__ListPrice__c</label>
            </value>
            <value>
                <fullName>SBQQ__Quantity__c</fullName>
                <default>false</default>
                <label>SBQQ__Quantity__c</label>
            </value>
            <value>
                <fullName>SBQQ__ProductFamily__c</fullName>
                <default>false</default>
                <label>SBQQ__ProductFamily__c</label>
            </value>
            <value>
                <fullName>SBCF_Text_Interval__c</fullName>
                <default>false</default>
                <label>SBCF_Text_Interval__c</label>
            </value>
            <value>
                <fullName>SBCF_Approval_Status__c</fullName>
                <default>false</default>
                <label>SBCF_Approval_Status__c</label>
            </value>
            <value>
                <fullName>Rlty__c</fullName>
                <default>false</default>
                <label>Rlty__c</label>
            </value>
            <value>
                <fullName>Auto_Execute__c</fullName>
                <default>false</default>
                <label>Auto_Execute__c</label>
            </value>
            <value>
                <fullName>Is_BC_Quote__c</fullName>
                <default>false</default>
                <label>Is_BC_Quote__c</label>
            </value>
            <value>
                <fullName>Gross_Term__c</fullName>
                <default>false</default>
                <label>Gross_Term__c</label>
            </value>
            <value>
                <fullName>License_Type_Lock__c</fullName>
                <default>false</default>
                <label>License_Type_Lock__c</label>
            </value>
            <value>
                <fullName>Opportunity_Type__c</fullName>
                <default>false</default>
                <label>Opportunity_Type__c</label>
            </value>
            <value>
                <fullName>Opportunity_Record_Type__c</fullName>
                <default>false</default>
                <label>Opportunity_Record_Type__c</label>
            </value>
            <value>
                <fullName>SBQQ__StartDate__c</fullName>
                <default>false</default>
                <label>SBQQ__StartDate__c</label>
            </value>
            <value>
                <fullName>SBQQ__Type__c</fullName>
                <default>false</default>
                <label>SBQQ__Type__c</label>
            </value>
            <value>
                <fullName>Quote_Offer_Type__c</fullName>
                <default>false</default>
                <label>Quote_Offer_Type__c</label>
            </value>
            <value>
                <fullName>Renewal_Sync__c</fullName>
                <default>false</default>
                <label>Renewal_Sync__c</label>
            </value>
            <value>
                <fullName>SBQQ__EndDate__c</fullName>
                <default>false</default>
                <label>SBQQ__EndDate__c</label>
            </value>
            <value>
                <fullName>SBCF_CPI_Uplift__c</fullName>
                <default>false</default>
                <label>SBCF_CPI_Uplift__c</label>
            </value>
            <value>
                <fullName>SBQQ__ProductCode__c</fullName>
                <default>false</default>
                <label>SBQQ__ProductCode__c</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
