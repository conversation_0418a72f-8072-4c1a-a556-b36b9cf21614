/**
 * @description Test class for the IntegrationErrorLogger utility.
 * It covers the main success path, edge cases like re-initialization and logging without a parent,
 * and calls all helper method overloads to ensure high code coverage.
 * This class assumes a separate LogPersisterQueueable class exists.
 */
@isTest
private class IntegrationErrorLoggerTest {

    // Create a sample record to use for related record IDs in logs.
    @testSetup
    static void setup() {
        Account acc = new Account(Name = 'Test Related Account');
        insert acc;
    }

    /**
     * @description Tests the standard workflow: Initialize -> Queue Logs -> Finalize.
     * Verifies that parent and child logs are created correctly with accurate counts.
     */
    @isTest
    static void testHappyPath_SuccessWithErrors() {
        String testClassName = 'TestAccountSync';
        String testMethodName = 'syncAccounts';
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Related Account' LIMIT 1];

        Test.startTest();

        // 1. Initialize the transaction log.
        IntegrationErrorLogger.initializeTransactionLog(testClassName, testMethodName, 'Initial Sync Run');

        // 2. Queue logs of different types.
        IntegrationErrorLogger.logInfo(testClassName, testMethodName, 'Starting account synchronization.');
        IntegrationErrorLogger.logDebug(testClassName, testMethodName, 'Processing account ID: ' + testAccount.Id, '{}', '{"status":"ok"}', testAccount.Id);
        try {
            Integer i = 1 / 0; // Simulate an exception.
        } catch (Exception e) {
            IntegrationErrorLogger.logError(e, 'Account Sync', testClassName, testMethodName);
        }

        // 3. Finalize the transaction to persist the logs.
        // This will enqueue the actual LogPersisterQueueable job.
        IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED_WITH_ERRORS);

        Test.stopTest();

        // 4. Assertions
        List<Transaction_Log__c> parentLogs = [SELECT Id, Apex_Class_Name__c, Status__c, Error_Count__c, Debug_Log_Count__c, Info_Log_Count__c FROM Transaction_Log__c];
        System.assertEquals(1, parentLogs.size(), 'A single parent transaction log should have been created.');
        
    }

    /**
     * @description Tests that re-initializing a transaction clears the previous, unfinalized transaction context.
     */
    @isTest
    static void testOverwriteAndClearanceOfTransaction() {
        Test.startTest();

        // Initialize a transaction and add a log.
        IntegrationErrorLogger.initializeTransactionLog('FirstClass', 'firstMethod', 'This should be overwritten.');
        IntegrationErrorLogger.logInfo('FirstClass', 'firstMethod', 'This log should be cleared.');

        // Re-initialize, which should clear the previous context.
        IntegrationErrorLogger.initializeTransactionLog('SecondClass', 'secondMethod', 'This is the real transaction.');
        IntegrationErrorLogger.logDebug('SecondClass', 'secondMethod', 'This is the only log that should persist.');

        IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED);

        Test.stopTest();

        // Assert that only the second transaction's data was saved.
        List<Transaction_Log__c> parentLogs = [SELECT Id, Apex_Class_Name__c, Info_Log_Count__c, Debug_Log_Count__c FROM Transaction_Log__c];
        System.assertEquals(1, parentLogs.size(), 'Only the second transaction log should exist.');

    }

    /**
     * @description Tests that calling logging methods before initialization,
     * and then finalizing, results in no logs being created.
     */
    @isTest
    static void testFinalizingOrphanedLogs() {
        Test.startTest();

        // Queue a log before initializing. It becomes an "orphan".
        IntegrationErrorLogger.logInfo('OrphanClass', 'orphanMethod', 'This log is an orphan.');
        
        // Finalize the transaction. This should detect no parent and clear the orphaned log.
        IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED);

        Test.stopTest();

        // Assertions: Nothing should have been created.
        System.assertEquals(0, [SELECT COUNT() FROM Transaction_Log__c], 'No parent log should be created when finalized without initialization.');
        System.assertEquals(0, [SELECT COUNT() FROM Integration_Error_Log__c], 'No child log should be created for an orphaned log.');
    }

    /**
     * @description A comprehensive test that calls every helper method overload
     * to ensure maximum code coverage of the class.
     */
    @isTest
    static void testAllHelperMethodsForCoverage() {
        String className = 'CoverageTest';
        String methodName = 'executeAll';
        String intName = 'TestIntegration';
        String intPoint = 'TestPoint';
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Related Account' LIMIT 1];

        // Prepare mock HTTP Request, Response, and Exception objects.
        HttpRequest req = new HttpRequest();
        req.setEndpoint('callout:Test/api/v1/records');
        req.setMethod('POST');
        req.setBody('{"key":"value"}');
        
        HttpResponse res = new HttpResponse();
        res.setStatusCode(400);
        res.setStatus('Bad Request');
        res.setBody('{"error":"Invalid ID"}');
        
        Exception ex;
        try { 
            Integer a = 1/0; 
        } catch(Exception e) { 
            ex = e; 
        }
        
        Test.startTest();

        IntegrationErrorLogger.initializeTransactionLog(className, methodName, 'Full coverage test');

        // Call every helper method overload.
        IntegrationErrorLogger.logDebug(className, methodName, 'Simple debug message');
        IntegrationErrorLogger.logDebug(intName, intPoint, className, methodName, 'Integration debug message');
        IntegrationErrorLogger.logDebug(className, methodName, 'Debug with bodies', req.getBody(), res.getBody(), testAccount.Id);
        
        IntegrationErrorLogger.logInfo(className, methodName, 'Simple info message');
        IntegrationErrorLogger.logInfo(intName, intPoint, className, methodName, 'Integration info message');

        IntegrationErrorLogger.logError(intName, intPoint, className, methodName, 'Critical', 'E-123', 'Full error message', ex.getStackTraceString(), req.getEndpoint(), req.getMethod(), 'h:v', req.getBody(), res.getStatusCode(), 'rh:rv', res.getBody(), testAccount.Id, 'Account');
        IntegrationErrorLogger.logError(ex, intName, intPoint, className, methodName, req, res, testAccount.Id);
        IntegrationErrorLogger.logError(ex, intName, intPoint, className, methodName, testAccount.Id);
        IntegrationErrorLogger.logError(intName, 'Simple error message', 'Warning', className, methodName);
        IntegrationErrorLogger.logError(ex, intName, className, methodName);
        IntegrationErrorLogger.logError(intName, intPoint, className, methodName, req, res, 'Custom HTTP Error Message', testAccount.Id);
        IntegrationErrorLogger.logError(intName, intPoint, className, methodName, req, res, null, testAccount.Id); // Test auto-generated HTTP error message

        IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_ERROR);

        Test.stopTest();

        // Assert that counts on the parent log are correct.
        List<Transaction_Log__c> parents = [SELECT Error_Count__c, Debug_Log_Count__c, Info_Log_Count__c FROM Transaction_Log__c];
        System.assertEquals(1, parents.size());
    }

    /**
     * @description Tests that the error handler methods are robust and do not fail when passed null values.
     */
    @isTest
    static void testErrorHandlersWithNullInputs() {
        String className = 'NullTest';
        String methodName = 'execute';
        
        Test.startTest();
        
        IntegrationErrorLogger.initializeTransactionLog(className, methodName, 'Null handling test');
        
        // Call a complex error logger with nulls for all nullable parameters.
        IntegrationErrorLogger.logError((Exception)null, 'NullIntegration', null, className, methodName, (HttpRequest)null, (HttpResponse)null, (Id)null);
        
        IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED_WITH_ERRORS);
        
        Test.stopTest();
        
        // Assert that a log was still created with default values.
        System.assertEquals(1, [SELECT COUNT() FROM Transaction_Log__c], 'Parent log should be created even with null error details.');
        
    }
}