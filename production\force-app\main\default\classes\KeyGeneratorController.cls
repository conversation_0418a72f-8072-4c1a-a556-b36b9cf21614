/************************************* MODIFICATION LOG ********************************************************************************************
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                 MODIFICATION DATE               REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Priyank                   06/17/2020                      Case 381435 - CPQ - Renewals - Enable the translation of DSIs by the SOP team
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Test Class - KeyGeneratorServiceQueueTest
*/

public without sharing class KeyGeneratorController {

    public class KeyGeneratorException extends Exception {
    }
    public static Map<String, String> technicalSupportQueuesByTheatres = new Map<String, String>{
        'NAM' => 'Technical_Support_North_America',
        'North America' => 'Technical_Support_North_America',
        'MEA' => 'Technical_Support_EMEA',
        'Europe' => 'Technical_Support_EMEA',
        'APAC' => 'Technical_Support_APAC',
        'LatAm' => 'Technical_Support_LATAM'
    };

    //method to be called from flows and processes
    @InvocableMethod
    public static void generateDSIKeyInvocable(List < Id > requests) {
        System.debug('#generateDSIKeyInvocable');

        Product_Key_Request__c request = [
            SELECT Id,
                Bypass_CPU_Checking__c,
                CloudBI__c,
                Disable_Registration__c,
                DSI__c,
                DSI__r.DSI_ID__c,
                DSI__r.Elastic_Cloud_Console__c,
                DSI__r.Operating_System__c,
                DSI__r.Account__r.Compass_Id_F__c,
                //DSI__r.DSIID__c,
                Expiration_Date__c,
                isAWS__c,
                isUniversal__c,
                QuoteID__c,
                Version__c,
                Version_ID__c,
                Version_List__c,
                Account__c,
                Account__r.Name,
                Account__r.Theatre_Name__c,
                Account__r.District__r.Name,
                Ship_Contact__r.Name,
                Ship_Contact__r.FirstName,
                Ship_Contact__r.LastName,
                Ship_Contact__r.Email,
                Manual_Key__c,
                New_Key_Delivered__c,
                Existing_Request__c,
                Delivery_Date__c,
                Ship_Contact__c,
                Opportunity__c,
                Opportunity_Term__c,
                Opportunity_Max_End_Date__c,
                Opportunity_Min_Start_Date__c,
                isAuditingDisabled__c,
                LockedKey__c,
                Map_To_Previous_SKU__c,
                Ship_Contact_Account__c,
                Employee_Key__c,
                Employee_Number__c,
                Ship_Contact_Name__c,
                Type__c,
                Account__r.Compass_Id_F__c,
                Cats_Id__c

            FROM Product_Key_Request__c
            WHERE Id = :requests
        ];
        try {
            System.debug(request);
            if (request.Type__c != 'Product Key') {
                generateGeospatialDSIKey(request);
            } else {
                generateDSIKey(request);
            }
        } catch (Exception e) {
            request.Error__c = '[' + e.getLineNumber() + '] ' + e.getMessage() + '\r\n' + e.getStackTraceString();
            update request;

            if (!request.Manual_Key__c && !request.Employee_Key__c && request.Opportunity__c == null) {
                //create a Case
                Case csa = new Case();
                csa.Origin = 'Web';
                csa.Version__c = request.Version_List__c;
                csa.Reason = 'Account Administration';
                csa.Subreason__c = 'License Key';
                csa.ContactId = request.Ship_Contact__c;
                csa.AccountId = request.Account__c;
                csa.DSI__c = request.DSI__c;
                csa.Status = 'C1 - New';

                csa.Internal_Comments__c = 'Failed Request \n' + request.Error__c + ' \nRequest ID ' + request.Id;

                if (request.Type__c != 'Product Key') {
                    Group techSupportQueue;

                    if (request.Account__r.Theatre_Name__c != null) {
                        if (technicalSupportQueuesByTheatres.get(request.Account__r.Theatre_Name__c) != null) {
                            techSupportQueue = [SELECT Id,Name FROM Group WHERE Type = 'Queue' AND DeveloperName = :technicalSupportQueuesByTheatres.get(request.Account__r.Theatre_Name__c) LIMIT 1];
                        } else if (request.Account__r.Theatre_Name__c == 'International') {
                            techSupportQueue = [
                                SELECT Id,Name
                                FROM Group
                                WHERE Type = 'Queue' AND DeveloperName = 'Technical_Support_North_America'
                                LIMIT 1
                            ];

                        }
                    }
                    System.debug('#techSupportQueue ' + techSupportQueue);
                    csa.Subject = request.Type__c + ' License Request for ' + request.Version_List__c + ' - ' + request.Ship_Account_Name__c + ' for ' + request.Ship_Contact_Name__c;
                    csa.Description = request.Type__c + 'License Request for ' + request.Version_List__c + ' - ' + request.Ship_Account_Name__c + ' for ' + request.Ship_Contact_Name__c;

                    csa.OwnerId = techSupportQueue.Id;

                } else {
                    AssignmentRule AR = new AssignmentRule();
                    AR = [
                        SELECT Id
                        FROM AssignmentRule
                        WHERE SobjectType = 'Case'
                        AND Active = TRUE
                        LIMIT 1
                    ];
                    Database.DMLOptions dmlOpts = new Database.DMLOptions();
                    dmlOpts.assignmentRuleHeader.assignmentRuleId = AR.Id;
                    csa.setOptions(dmlOpts);
                    csa.Subject = 'License Key Request for ' + request.Version_List__c + ' - ' + request.Ship_Account_Name__c + ' for ' + request.Ship_Contact_Name__c;
                    csa.Description = 'License Key Request for ' + request.Version_List__c + ' - ' + request.Ship_Account_Name__c + ' for ' + request.Ship_Contact_Name__c;

                }


                insert csa;

            }
        }

    }

    public static void generateGeospatialDSIKey(Product_Key_Request__c request) {
        //Run business rules
        request.Ship_Account_Name__c = request.Account__r.Name;
        request.Ship_Contact_Name__c = request.Ship_Contact__r.Name;
        request.Ship_Contact_Email__c = request.Ship_Contact__r.Email;
        request.Account_Compass_ID__c = request.DSI__r.Account__r.Compass_Id_F__c;


        GeospatialRequest geoRequest = new GeospatialRequest();
        geoRequest.orgId = Integer.valueOf(request.Account__r.Compass_Id_F__c);
        geoRequest.dsiId = request.DSI__c != null ? Integer.valueOf(request.DSI__r.DSI_ID__c) : 1;
        geoRequest.orgName = request.Account__r.Name;
        geoRequest.userId = 1;
        geoRequest.isEmployee = request.Employee_Key__c;


        //String JSONKeyRequest = JSON.serialize(geoRequest, true);
        String endpointURL;
        Map < String, String > payload = new Map < String, String > ();

        if (request.Type__c.startsWith('ESRI Key')) {
            endpointURL = KeyGeneratorConstants.KEYENDPOINT_ESRIKEY;
            payload.put('newVersion', String.valueOf(request.Type__c =='ESRI Key 2021 update 5 and later'));
        } else {
            endpointURL = KeyGeneratorConstants.KEYENDPOINT_GEOSPATIALKEY;
        }
        payload.put('orgId', String.valueOf(geoRequest.orgId));
        payload.put('dsiId', String.valueOf(geoRequest.dsiId));
        payload.put('orgName', geoRequest.orgName);
        payload.put('userId', String.valueOf(geoRequest.userId));
        payload.put('isEmployee', geoRequest.isEmployee ? 'TRUE' : 'FALSE');
        //send API request to Webservice.
        String keyResponse = KeyGeneratorAPICallout.generateKeyfromJSONGeo(payload, endpointURL, 'ISMarketingAuto_Prod', 'z*8UmS_ymT9Qpr@');
        GeospatialRequest resultKey = mapGeospatialJSONToKeys(keyResponse);
        //GeospatialRequest resultKey = new GeospatialRequest();
        //resultKey.EncryptedKey='<key><![CDATA[0000000001c284ab5c1d03f36c5d1ee23237c470a04322ea46869ca3d342184cb3052102a37a6ee997c3e9e0dc79306e77c12bd1e8f19aba9b63910a92b093fc3254f177c12c34a0e90e633fdb398a98a2b06d1e6c5fbd1ccf1487cde8af93bcde54ee7dc9113eb753abe3778b2c31269eb9b6f1dfcb1ada53f7865fed930d6056d4bea07009b46a9b6ad0a213c1b5]]></key>';
        Product_Key__c keyObj = new Product_Key__c();
        keyObj.Product_Key_Request__c = request.Id;
        keyObj.Key__c = resultKey.EncryptedKey;
        keyObj.DSI__c = request.DSI__c;
        keyObj.Ship_Contact__c = request.Ship_Contact__c;
        keyObj.Delivery_Date__c = Datetime.now();
        keyObj.Ship_Account_Name__c = request.Account__r.Name;
        keyObj.Ship_Contact_Name__c = request.Ship_Contact__r.Name;
        keyObj.Type__c = request.Type__c;

        //update request;
        update request;
        insert keyObj;

        System.debug(resultKey);
    }
    public static void generateDSIKey(Product_Key_Request__c request) {

        setCatsId(request); // to set cats id on product key request based on the account it is mapped with.

        //Run business rules
        request.Ship_Account_Name__c = request.Account__r.Name;
        request.Ship_Contact_Name__c = request.Ship_Contact__r.Name;
        request.Ship_Contact_Email__c = request.Ship_Contact__r.Email;
        request.Account_Compass_ID__c = request.DSI__r.Account__r.Compass_Id_F__c;

        //Account level Exceptions not dependent on DSI config
        KeyGeneratorHelper.validateExceptionRules(request);

        //Calculates Expiration Date based on Key Request
        request.Expiration_Date__c = KeyGeneratorHelper.getExpirationDate(request);

        List < Version_Map__mdt > versionMap = [SELECT Version_ID__c, Map_To_Previous_SKU__c, LockedKey__c, BlockCustomerKey__c, Employee_Key__c FROM Version_Map__mdt WHERE Label = :request.Version_List__c LIMIT 1];
        request.Version_ID__c = versionMap.size() > 0 ? (Integer) versionMap[0].Version_ID__c : 104; //default to a 104 key
        request.Map_To_Previous_SKU__c = versionMap.size() > 0 ? versionMap[0].Map_To_Previous_SKU__c : false;


        //figure out if its a universal key
        request.isUniversal__c = !request.LockedKey__c ? isUniversalKey(request.DSI__c, request.Version_List__c) : !request.LockedKey__c;

        System.debug('---' + request);


        //check if DSI configuration has changed and if its valid to generate new Keys
        CDKeyRequest keyRequest = new CDKeyRequest();

        keyRequest.ReleaseVersion = (Integer) request.Version_ID__c;
        keyRequest.employee = request.Employee_Key__c; //CHANGE
        //keyRequest.employee = false; //ENABLE Above when Webservice is fixed.


        System.debug(request.Version_ID__c);
        //skip all this if there is key already
        if (!request.Manual_Key__c && !request.Employee_Key__c && versionMap.size() > 0 && versionMap[0].BlockCustomerKey__c) {
            throw new KeyGeneratorException('The Requested Key Version: ' + request.Version_List__c + ' is not allowed for Self Serve Key Generation');
        }
        //getAll Assets to generate list of keyGroups
        if (request.Employee_Key__c && versionMap.size() > 0 && versionMap[0].Employee_Key__c != null) {
            //send direct key

            Product_Key__c keyObj = new Product_Key__c();
            keyObj.Product_Key_Request__c = request.Id;
            keyObj.Key__c = versionMap[0].Employee_Key__c;
            keyObj.Ship_Contact__c = request.Ship_Contact__c;
            keyObj.isUniversal__c = request.isUniversal__c;


            keyObj.Version__c = request.Version_List__c;
            keyObj.Delivery_Date__c = Datetime.now();
            keyObj.Key_Group__c = '1';
            //keyObj.Expiration_Date__c = key.ExpirationDate!=null?key.ExpirationDate.Date().adddays(2):null;
            //keyObj.CartItems__c= convertCartItemstoJSON(dsiConsolidatedCart.get(key.kGrpKey));
            keyObj.Key_Group_Desc__c = 'Prod Key 1';
            keyObj.KeyGroupIndex__c = 'Prod Key 1-Perpetual';

            insert keyObj;

            request.Success__c = true;
            request.Delivery_Date__c = Datetime.now();
            request.Key_Shipped__c = true;
            update request;

            return;
        }

        //HERE Prepare Virtual Carts, separate Assets from Subscriptions. Break Assets by Key Group and Subscriptions by Key Group. Prepare list of Cart Items to run
        List < Asset > dsiAssets = DSIAssetHelper.getDSIAssets(request.DSI__c, false, false);
/*
        if(!request.Manual_Key__c&&request.Expiration_Date__c==null){
            dsiAssets = KeyGeneratorHelper.removeTermAssets(dsiAssets);
        }
*/
        //getAll Subscriptions to generate list of keyGroups
        List <SBQQ__Subscription__c> dsiSubscriptions = DSIAssetHelper.getSubscriptions(request.DSI__c);
        System.debug('YC Debug-dsiAssets-' +dsiAssets.size());
        System.debug('YC Debug-dsiSubscriptions-' +dsiSubscriptions.size());
        Map <String, List <KeyGeneratorCartItem>> dsiShoppingCart = convertDSIToCartItems(request, dsiAssets, dsiSubscriptions, false, false);

        System.debug('YC Debug-dsiShoppingCart-' +JSON.serialize(dsiShoppingCart));

        //Integer quoteID = DSIAssetHelper.getAssetQuoteID(dsiAssets);

        Integer quoteID = 104;

        request.QuoteID__c = request.QuoteID__c == null ? quoteID : request.QuoteID__c;

        if (!request.Employee_Key__c && dsiShoppingCart.size() == 0) {
            throw new KeyGeneratorException('This DSI does not contain assets or subscriptions.');
        }

        validateVersion(dsiShoppingCart, request);

        if (!request.Manual_Key__c && !request.Employee_Key__c) {
            if (request.Opportunity__c != null || !String.isBlank(request.Opportunity__c)) {

                //exception validation for dsiShoppingCart
                for (List < KeyGeneratorCartItem > shopCarts : dsiShoppingCart.values()) {
                    if (!shopCarts.isEmpty()) {
                        for (KeyGeneratorCartItem dsi : shopCarts) {
                            if (dsi.isLegacyCatalog == true) {
                                throw new KeyGeneratorException('Cannot generate key, this DSI should contains Only 2017 and up products');
                            }
                            if (dsi.environment != 'Named User') {
                                throw new KeyGeneratorException('Cannot generate key, this DSI should contains Only Named Users Assets');
                            }
                            if (dsi.interval == 'Term') {
                                throw new KeyGeneratorException('Cannot generate Key for DSI with term interval');
                            }
                        }
                    }
                }

            }
            //check auto key generation
            //KeyGeneratorHelper.validateVersion(dsiAssets, dsiSubscriptions, request);

            //Validate Manual key group exception
            //KeyGeneratorHelper.validateKeyGroups(dsiAssets, request);
            validateKeyGroups(dsiShoppingCart, request);
            //Validate Manual key group exception
            //KeyGeneratorHelper.validateKeyGroupsSubs(dsiSubscriptions, request);

            //Validate Management and Term Exception
            //KeyGeneratorHelper.validateExceptions(dsiAssets, request);
            //Validate Management and Term Exception
            //KeyGeneratorHelper.validateExceptionsSubs(dsiSubscriptions, request);
            validateExceptions(dsiShoppingCart, request);

        }


        //All checks passed, lets generate or reuse key
        //DateTime assetModDate = DSIAssetHelper.getLatestModDate(dsiAssets);

        //DateTime subsModDate = DSIAssetHelper.getLatestModDate(dsiAssets);
        //DateTime subsModDate = DSIAssetHelper.getLatestModDate(dsiAssets);

        request = KeyGeneratorHelper.validateNewKey(getLatestModDate(dsiShoppingCart), request);

        if (!request.New_Key_Delivered__c) {

            request.Success__c = true;
            request.Delivery_Date__c = Datetime.now();
            request.Key_Shipped__c = true;

            if (request.Id == null) {
                insert request;
            } else {
                update request;
            }

            return;

        }

        System.debug('YC Debug-dsiShoppingCart123-' +JSON.serialize(dsiShoppingCart));

        //generateJSONString
        if (keyRequest.ReleaseVersion == 106 || keyRequest.ReleaseVersion == 107 || keyRequest.ReleaseVersion == 108) {
            System.debug('YC Debug-1 - here');
            keyRequest.LicenseClassInfo11 = KeyGeneratorController.generateJSONKeyRequest11(request, request.DSI__c, (Integer) request.Version_ID__c, dsiShoppingCart);
            if (keyRequest.employee) {
                CDKeyMod11 tempcd = new CDKeyMod11();
                tempcd.LKG = 1;
                tempcd.kGrpKey = 'Prod Key 1-Perpetual';
                keyRequest.LicenseClassInfo11.add(tempcd);
            }

        } else {
            System.debug('YC Debug-2 - here');
            keyRequest.LicenseClassInfo = KeyGeneratorController.generateJSONKeyRequest(request, request.DSI__c, (Integer) request.Version_ID__c, dsiShoppingCart);
            if (keyRequest.employee) {
                CDKeyMod tempcd = new CDKeyMod();
                tempcd.LKG = 1;
                tempcd.kGrpKey = 'Prod Key 1-Perpetual';
                keyRequest.LicenseClassInfo.add(tempcd);
            }
        }

        System.debug(keyRequest);
        //Map<string, List<KeyGeneratorCartItem>> dsiConsolidatedCart = generateConsolidatedMap(keyRequest);

        keyRequest.employeeID = request.Employee_Key__c ? Integer.valueOf(request.Employee_Number__c) : null;
        keyRequest.IsUniversal = request.Employee_Key__c ? true : null;

        String JSONKeyRequest = JSON.serialize(keyRequest, true);
        //compile keyRequest adding all keyGroups
        //String JSONParameters = JSONKeyRequest.replace('KstDate', 'Date');
        JSONKeyRequest = JSONKeyRequest.replace('KstDate', 'Date');

        //JSONKeyRequest.replace('"LicenseClassInfo":[]', '"LicenseClassInfo":[{}]');

        if (keyRequest.ReleaseVersion == 106 || keyRequest.ReleaseVersion == 107 || keyRequest.ReleaseVersion == 108) {
            JSONKeyRequest = JSONKeyRequest.replace('LicenseClassInfo11', 'LicenseClassInfo');
            if (keyRequest.ReleaseVersion == 106) {
                JSONKeyRequest = JSONKeyRequest.replace('HyperWeb', 'ServerHyperIntelligence');
            }
            //JSONKeyRequest.replace('"LicenseClassInfo":[]', '"LicenseClassInfo":[{}]');
        }
        System.debug(JSONKeyRequest);
        request.JSON_Payload__c = JSONKeyRequest; //AK Temp
        //send API request to Webservice.
        String keyResponse = KeyGeneratorAPICallout.generateKeyfromJSON(JSONKeyRequest, KeyGeneratorConstants.KEYENDPOINT_PRODUCTKEY, 'MSTRKeyGenAPI', '8..aR^A[4@fvP');

        if (keyRequest.ReleaseVersion == 106 || keyRequest.ReleaseVersion == 107 || keyRequest.ReleaseVersion == 108) {
            keyResponse = keyResponse.replace('EnablePOC', 'EnablePOC2');
            //JSONKeyRequest.replace('"LicenseClassInfo":[]', '"LicenseClassInfo":[{}]');
        }
        //wait for Response from service
        if (keyResponse.startsWithIgnoreCase('Error')) {
            request.Error__c = keyResponse;
            throw new KeyGeneratorException(keyResponse);
        }
        CDKeyRequest responseMod = mapJSONToKeys(keyResponse);
        List<CDKeyMod> licenseClassInfo = responseMod.LicenseClassInfo;


        request.JSON_Payload__c = keyResponse;
        request.Success__c = true;
        request.Delivery_Date__c = Datetime.now();

        if (request.Id == null) {
            insert request;
        } else {
            update request;
        }

        System.debug(responseMod);

        List < Product_Key__c > finalKeys = new List < Product_Key__c > ();

        for (CDKeyMod key : responseMod.LicenseClassInfo) {
            if (key != null) {

                Product_Key__c keyObj = new Product_Key__c();
                keyObj.Product_Key_Request__c = request.Id;
                keyObj.Key__c = key.encryptKey;
                keyObj.DSI__c = request.DSI__c;
                keyObj.Ship_Contact__c = request.Ship_Contact__c;
                keyObj.CloudBI__c = request.CloudBI__c;
                keyObj.Bypass_CPU_Checking__c = request.Bypass_CPU_Checking__c;
                keyObj.Disable_Registration__c = request.Disable_Registration__c;
                keyObj.isAWS__c = request.isAWS__c;
                keyObj.isUniversal__c = request.isUniversal__c;
                keyObj.Quote_ID__c = request.QuoteID__c;
                //Version_Map__mdt versionT = [SELECT MasterLabel FROM Version_Map__mdt WHERE Version_ID__c =: request.Version_ID__c LIMIT 1];

                keyObj.Version__c = request.Version_List__c;
                keyObj.Delivery_Date__c = Datetime.now();
                keyObj.Key_Group__c = key.LKG.format();
                keyObj.Expiration_Date__c = key.ExpirationDate != null ? key.ExpirationDate.date().addDays(2) : null;
                //keyObj.CartItems__c= convertCartItemstoJSON(dsiConsolidatedCart.get(key.kGrpKey));
                keyObj.Key_Group_Desc__c = key.kGrpKey.left(key.kGrpKey.indexOf('-'));
                keyObj.KeyGroupIndex__c = key.kGrpKey;
                keyObj.DSI_Payload__c = KeyGeneratorController.generateDSIPayload (key.kGrpKey, dsiShoppingCart);
                keyObj.Ship_Account_Name__c = request.Account__r.Name;
                keyObj.Ship_Contact_Name__c = request.Ship_Contact__r.Name;
                keyObj.Type__c = 'Product Key';
                finalKeys.add(keyObj);


            }
        }
        System.debug(finalKeys);
        insert finalKeys;

        request.Key_Shipped__c = true;
        update request;

        List < Opportunity > shippedOpps = [SELECT Id, Ship_Date__c, key_auto_generated__c, SBQQ__PrimaryQuote__c, Software__c FROM Opportunity WHERE Id = :request.Opportunity__c LIMIT 1];

        if (!shippedOpps.isEmpty()) {
            Opportunity shippedOpp = shippedOpps[0];
            shippedOpp.Ship_Date__c = Date.today();
            shippedOpp.key_auto_generated__c = true;
            update shippedOpp;

            //lets check and grant Download Site access
            List < User > usrs = [SELECT Id FROM User WHERE ContactId = :request.Ship_Contact__c AND IsActive = TRUE];
            if (usrs.isEmpty()) {
                createCommunityUser(request.Ship_Contact__c);
            }


        }
        /*
if(responseMod!=null){

request.JSON_Payload__c=JSONKeyRequest;
update request;

Product_Key__c keyObj = new Product_Key__c();
keyObj.Product_Key_Request__c=request.ID;
keyObj.Key__c=key;
keyObj.DSI__c=request.DSI__c;
keyObj.Quote_Id__c=request.QuoteId__c;
keyObj.version__c=request.Version_ID_T__c;
keyObj.Key_Group__c='Production';

insert keyObj;

}*/

        //write back key to Key field on Product Key Object

        //write back some parameters to the Product Key Request Object

        //maybe return something for the interface?

        //END
    }

    public static String generateDSIPayload(String keyGroup, Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart) {

        List<DSIPayloadItem> dsiContentJson = new List<DSIPayloadItem>();
        if (!dsiShoppingCart.isEmpty()) {
            for (KeyGeneratorCartItem cartItem : dsiShoppingCart.get(keyGroup)) {
                DSIPayloadItem temp = new DSIPayloadItem();
                temp.sku = Integer.valueOf(cartItem.productCode);
                temp.productName = cartItem.prodName;
                dsiContentJson.add(temp);
            }
        }
        return JSON.serialize(dsiContentJson);

    }

    /**
@AuraEnabled
public static DSI__c getDsiTranslationStatus(string dsiId){
//return dsi translated to determine which buttons to disable
return [Select id, TranslationType__c FROM DSI__c WHERE id =: dsiId LIMIT 1];
}
**/

    /*
@AuraEnabled
public static List < Cart > generateTranslationView(String dsiId, Boolean byDSI, Boolean isEnhancedTranslation, Boolean isStandardToEnh, List < String > enhancedList) {

Product_Key_Request__c request = new Product_Key_Request__c();
request.DSI__c = dsiId;

List < Asset > dsiAssets = DSIAssetHelper.getDSIAssets(request.DSI__c, isStandardToEnh);
List < SBQQ__Subscription__c > dsiSubscriptions = DSIAssetHelper.getSubscriptions(request.DSI__c);

Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart = convertDSIToCartItems(request, dsiAssets, dsiSubscriptions, byDSI);
List < Cart > resultItems = new List < Cart > ();

for (String kgrp: dsiShoppingCart.keyset()) {

List < KeyGeneratorCartItem > cartItems = dsiShoppingCart.get(kgrp);

Cart keyCart = new Cart(request, 1);
keyCart.keyGroupDesc = kgrp.left(kgrp.indexOf('-'));

keyCart = unbundleSkusUpgrade(keyCart, cartItems, false, false, isEnhancedTranslation, isStandardToEnh, enhancedList);


system.debug(keyCart.listUnbundled);

//keyCart.ListConsolidated = groupSKUs11(keyCart.isDowngrade, keyCart);
keyCart.ListConsolidated = keyCart.listUnbundled;
system.debug(keyCart.ListConsolidated);

resultItems.add(keyCart);
}

return resultItems;

}*/

    // Commented by Prateek Jain as Aura Enabled method overloading is not allowed - Case-554375
    /*@AuraEnabled
    public static List < Cart > generateTranslationEvent(Boolean isViewOnly, String dsiId, Boolean isEnhancedTranslation, Boolean isStandardToEnh, List < String > enhancedList) {
        //Start - P Translate
        return generateTranslationEvent(isViewOnly, dsiId, isEnhancedTranslation, isStandardToEnh, enhancedList, null);
    }*/

    @AuraEnabled
    public static List < Cart > generateTranslationEvent(Boolean isViewOnly, String dsiId, Boolean isEnhancedTranslation, Boolean isStandardToEnh, List < String > enhancedList, List<Cart> cartList) {
        //End - P  Translate
        Product_Key_Request__c request = new Product_Key_Request__c();
        request.DSI__c = dsiId;

        List < Asset > dsiAssets = DSIAssetHelper.getDSIAssets(request.DSI__c, isStandardToEnh, true);

        //TODO DSIAssetHelper - exclude assets that have exception_multi__c as Translated to Agnostic

        List < SBQQ__Subscription__c > dsiSubscriptions = DSIAssetHelper.getSubscriptions(request.DSI__c);

        Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart = convertDSIToCartItems(request, dsiAssets, dsiSubscriptions, false, true);

        List < Cart > resultItems = new List < Cart > ();


        for (String kgrp : dsiShoppingCart.keySet()) {

            List < KeyGeneratorCartItem > cartItems = dsiShoppingCart.get(kgrp);

            Cart keyCart = new Cart(request, 1);
            keyCart.keyGroupDesc = kgrp.left(kgrp.indexOf('-'));
            keyCart = unbundleSkusUpgrade(keyCart, cartItems, false, false, isEnhancedTranslation, isStandardToEnh, enhancedList, true);

            keyCart.ListConsolidated = keyCart.listUnbundled;
            //keyCart.ListConsolidated=keyCart.listUnbundled;
            System.debug(keyCart.ListConsolidated);

            if (!isViewOnly) {
                //Start - P  Translate
                List < KeyGeneratorCartItem > listconsolidated = keyCart.ListConsolidated;
                System.debug('cartList ' + cartList);
                if (cartList != null && cartList.size() > 0 && cartList[0].ListConsolidated.size() > 0) {
                    listconsolidated = cartList[0].ListConsolidated;
                }
                System.debug('listconsolidated' + listconsolidated);
                List < SObject > dsiContents = convertCartItemtoSObject(listconsolidated, dsiId);
                //End - P  Translate
                insert dsiContents;

                if (!isStandardToEnh) {
                    decrecateOldAssets(dsiAssets, dsiContents);
                }

                reSubscribeAssets(dsiAssets, dsiContents);

                DSI__c dsi_update = new DSI__c();
                dsi_update.Id = dsiId;
                dsi_update.TranslationType__c = (isEnhancedTranslation || isStandardToEnh) ? 'Enhanced' : 'Standard';
                update dsi_update;
            }

            else {
                List < KeyGeneratorCartItem > helperItems = new List < KeyGeneratorCartItem >();
                //Start - P  Translate

                //Deduction - Start - don't use reduce count logic for now
                /*
                Integer transactionCount = 0;

                for(KeyGeneratorCartItem item: keyCart.ListConsolidated){
                    system.debug('item ' + item.prodName + ' ' + item.insertOnTranslation + item.masterUniqueID);
                    if(item.insertOnTranslation && item.translationInheritanceType == 'Decrease Count on Translation'){
                        transactionCount += (item.License.Type == 1 ? item.namedUsersInternal : item.quantityInternal);
                    }
                }

                system.debug('transactionCount ' + transactionCount);
                */
                //end

                Integer rowCounter = 0;

                //List<String> deduectionItemList = new List<String>(); //Deduction
                for (KeyGeneratorCartItem item : keyCart.ListConsolidated) {
                    System.debug('item ' + item.prodName + ' ' + item.insertOnTranslation + item.masterUniqueID + ' ' + item.subscriptionName + ' ' + item.subscriptionContractNumber);
                    if (item.insertOnTranslation) {
                        //Deduction - start
                        /*if(item.License.Type == 1) {

                            Boolean decCount = item.translationInheritanceType != 'Decrease Count on Translation' && item.namedUsersInternal > transactionCount;
                            item.decCount = decCount;

                            if(decCount && deduectionItemList.contains(item.prodName) == false) {
                                item.namedUsersInternal = item.namedUsersInternal - transactionCount;
                                deduectionItemList.add(item.prodName);
                            }
                        }*/
                        //end
                        /*else {

                        Boolean decCount = item.translationInheritanceType != 'Decrease Count on Translation' && item.quantityInternal > transactionCount;
                        item.decCount = decCount;
                        item.quantityInternal = decCount ? item.quantityInternal - transactionCount : item.quantityInternal;
                        }*/

                        item.rowUniqueID = 'row-' + (rowCounter++);
                        helperItems.add(item);
                        //End - P  Translate
                    }
                }

                //Deduction - start
                /*for(KeyGeneratorCartItem item: keyCart.ListConsolidated) {
                    if(deduectionItemList.contains(item.prodName)) {
                        item.decCount = true;
                    }
                }*/
                //end

                keyCart.ListConsolidated = helperItems;

                resultItems.add(keyCart);

            }


            //unsubscribeAssets(dsiAssets,dsiContents);
            //subscribeNewAssets

            //lets mark old assets to Translated.


        }

        return resultItems;

    }
    public static void decrecateOldAssets(List < Asset > assets, List < SObject > dsiContents) {

        List<Asset> assetsToRetire = new List<Asset>();

        for (SObject obj : dsiContents) {
            for (Asset asst : assets) {
                if (asst.Id == obj.get('ParentId') && asst.Id != obj.get('Id')) {
                    if (!assetsToRetire.contains(asst)) {
                        assetsToRetire.add(asst);
                    }
                }
            }
        }

        for (Asset asst : assets) {
            if (asst.Product2.Environment__c == 'CPU-NonProduction' && !assetsToRetire.contains(asst)) {
                //Since non-prod no longer translates into anything, retire if the asset was returned to be translated.
                assetsToRetire.add(asst);
            }
        }

        for (Asset sku : assetsToRetire) {

            sku.Exception_multi__c = sku.Exception_multi__c == null ? 'Retired' : 'Retired;' + sku.Exception_multi__c;
            sku.Aggregation_Comments__c = sku.Aggregation_Comments__c == null ? 'Asset translated on: ' + Datetime.now() : 'Asset translated on: ' + Datetime.now() + '; ' + sku.Aggregation_Comments__c;
            sku.Key_Group_multi__c = 'N/A'; // Remove Key Groups
            sku.SBQQ__SubscriptionEndDate__c = null;
            sku.SBQQ__SubscriptionStartDate__c = null;
        }
        update assetsToRetire;
    }
    /*
@AuraEnabled
public static List<String> getDSIListToTranslate(String contractID){
List<String> resultDSIs = new List<String>();
for(SBQQ__subscribedAsset__c asst: [SELECT Id,SBQQ__Asset__c,SBQQ__Asset__r.SBCF_DSI__c,SBQQ__Asset__r.SBCF_DSI__r.TranslationType__c FROM SBQQ__subscribedAsset__c WHERE  SBQQ__Subscription__r.SBQQ__Contract__c=:contractID]){
if(!resultDSIs.contains(asst.SBQQ__Asset__r.SBCF_DSI__c)&&asst.SBQQ__Asset__r.SBCF_DSI__r.TranslationType__c==null){
resultDSIs.add(asst.SBQQ__Asset__r.SBCF_DSI__c);
}
}
return resultDSIs;
}
@AuraEnabled
public static String quoteContract(String contractID){
List< Contract> contractsToUpdate = new List<Contract>();

for(Contract contr: [SELECT Id,SBQQ__RenewalQuoted__c FROM Contract WHERE  Id=:contractID]){
if(!contr.SBQQ__RenewalQuoted__c){
contr.SBQQ__RenewalQuoted__c=true;
contractsToUpdate.add(contr);
}
}

if(!contractsToUpdate.isempty()){
update contractsToUpdate;
return 'Contracts Updated';
}
return 'No Contracts to Update';
}
*/


    public static void reSubscribeAssets(List < Asset > assets, List < SObject > dsiContents) {
        List < String > currSubs = new List < String > ();
        List < SObject > objectsToUpdate = new List<SObject>();
        Set <Id> updateObjectIdSet = new Set<Id>();

        for (SObject obj : dsiContents) {
            for (Asset asst : assets) {
                if (asst.Id == obj.get('ParentId') && asst.Id != obj.get('Id')) {

                    currSubs.add(asst.SBQQ__CurrentSubscription__c);
                    asst.SBQQ__CurrentSubscription__c = null;
                    if (!updateObjectIdSet.contains(asst.Id)) {
                        objectsToUpdate.add(asst);
                        updateObjectIdSet.add(asst.Id);
                    }
                }
            }

        }

        List < SBQQ__SubscribedAsset__c > subAssets = [SELECT Id, SBQQ__Asset__c, SBQQ__Subscription__c,CurrencyIsoCode,SBQQ__Active__c FROM SBQQ__SubscribedAsset__c WHERE SBQQ__Asset__c IN:assets AND SBQQ__Subscription__c IN:currSubs];

        List < SBQQ__SubscribedAsset__c > subsToClone = new List < SBQQ__SubscribedAsset__c > ();

        for (SObject obj : dsiContents) {
            for (Asset asst : assets) {
                if (asst.Id == obj.get('ParentId') && asst.Id != obj.get('Id')) {
                    for (SBQQ__SubscribedAsset__c subsAsst : subAssets) {
                        if (subsAsst.SBQQ__Asset__c == asst.Id) {
                            SBQQ__SubscribedAsset__c temp = new SBQQ__SubscribedAsset__c();
                            temp.SBQQ__Active__c = subsAsst.SBQQ__Active__c;
                            temp.CurrencyIsoCode = subsAsst.CurrencyIsoCode;
                            temp.SBQQ__Asset__c = obj.Id;
                            temp.SBQQ__Subscription__c = subsAsst.SBQQ__Subscription__c;
                            subsToClone.add(temp);
                            obj.put('SBQQ__CurrentSubscription__c', subsAsst.SBQQ__Subscription__c);

                        }
                    }
                }
            }

        }

        List <SBQQ__Subscription__c> subsToTouch = [SELECT Id,SBQQ__RenewalPrice__c,SBQQ__NetPrice__c,SBQQ__RootId__c,SBQQ__QuoteLine__c,SBQQ__OriginalQuoteLine__c,SBQQ__Contract__c FROM SBQQ__Subscription__c WHERE Id IN:currSubs];

        List <String> contractsTouched = new List<String>();
        for (SBQQ__Subscription__c sub : subsToTouch) {
            if (!contractsTouched.contains(sub.SBQQ__Contract__c)) {
                contractsTouched.add(sub.SBQQ__Contract__c);
            }
        }

        for (SBQQ__Subscription__c sub : subsToTouch) {
            sub.SBQQ__QuoteLine__c = null;
            sub.SBQQ__Number__c = null;
            sub.SBCF_Legacy__c = true;
            sub.SBQQ__RenewalPrice__c = sub.SBQQ__RenewalPrice__c == null ? sub.SBQQ__RegularPrice__c : sub.SBQQ__RenewalPrice__c;
            if (!updateObjectIdSet.contains(sub.Id)) {
                objectsToUpdate.add(sub);
                updateObjectIdSet.add(sub.Id);
            }

        }

        List <SBQQ__Subscription__c> subsToTouchViaContract = [SELECT Id,SBQQ__RenewalPrice__c,SBQQ__RegularPrice__c,SBQQ__RootId__c,SBQQ__QuoteLine__c,SBQQ__OriginalQuoteLine__c FROM SBQQ__Subscription__c WHERE SBQQ__Contract__c IN:contractsTouched];

        for (SBQQ__Subscription__c sub : subsToTouchViaContract) {
            sub.SBQQ__QuoteLine__c = null;
            sub.SBQQ__Number__c = null;
            if (!updateObjectIdSet.contains(sub.Id)) {
                objectsToUpdate.add(sub);
                updateObjectIdSet.add(sub.Id);
            }
        }

        objectsToUpdate.addAll(dsiContents);

        insert subsToClone;

        delete subAssets;

        //update assets;

        update objectsToUpdate;

    }

    public static List < SObject > convertCartItemtoSObject(List < KeyGeneratorCartItem > cartItems, String dsiID) {

        List < SObject > resultObjects = new List < SObject > ();
        List < DSI__c > accs = [SELECT Account__c FROM DSI__c WHERE Id = :dsiID LIMIT 1];

        for (KeyGeneratorCartItem item : cartItems) {
            System.debug('item.insertOnTranslation ' + item.insertOnTranslation);
            if (!item.insertOnTranslation) {
                //do not insert assets if not marked as needed for translation
                continue;
            }

            SObject obj = Schema.getGlobalDescribe().get(item.itemIdentifier).newSObject();
            String prodPrefix = item.itemIdentifier == 'Asset' ? 'Product2Id' : 'SBQQ__Product__c';
            String accountIdentifier = item.itemIdentifier == 'Asset' ? 'AccountId' : 'SBQQ__Account__c';
            String quantityIdentifier = item.itemIdentifier == 'Asset' ? 'Quantity' : 'SBQQ__Quantity__c';
            //String endDateIdentifier = item.itemIdentifier=='Asset'?'SBQQ__SubscriptionEndDate__c':'SBQQ__EndDate__c';
            //String startDateIdentifier = item.itemIdentifier=='Asset'?'SBQQ__SubscriptionStartDate__c':'SBQQ__EndDate__c';

            //Clone fields
            obj.put(prodPrefix, item.productSFID);
            obj.put('Key_Group_multi__c', item.keygroups);
            obj.put('Exception_multi__c', item.exceptions);
            obj.put('Aggregation_Comments__c', item.aggregationComments);
            //Start - P  Translate
            //obj.put(quantityIdentifier, item.License.getGenericCount());
            obj.put(quantityIdentifier, item.quantityProcessed);

            obj.put('SBCF_DSI__c', dsiID);
            obj.put(accountIdentifier, accs.isEmpty() ? '' : accs.get(0).Account__c);


            if (item.itemIdentifier == 'Asset')
                obj.put('Name', 'XXXX'); //Doesn't matter
            //obj.put('Translated__c',true);
            if (item.itemIdentifier == 'Asset') {
                obj.put('Price', 0);
                obj.put('Translated__c', true);
                obj.put('SBQQ__SubscriptionEndDate__c', item.subsEndDate);
                obj.put('SBQQ__SubscriptionStartDate__c', item.subsStartDate);
                obj.put('SBQQ__CurrentSubscription__c', item.currentSubscriptionId);
                //End - P  Translate
                String temp = getTopParent(item);
                System.debug(temp);
                if (temp != null && temp != '0') {
                    obj.put('ParentId', getTopParent(item));
                }
            }


            resultObjects.add(obj);
        }
        return resultObjects;

    }

    public static String getTopParent(KeyGeneratorCartItem item) {
        if (item.BundleParent != null)
            return getTopParent(item.BundleParent);

        return item.uniqueID;
    }

    @AuraEnabled
    public static Map < String, List < KeyGeneratorCartItem >> generateEmailView(String dsiId, String version, Boolean lockedKey) {

        Product_Key_Request__c request = new Product_Key_Request__c();
        request.DSI__c = dsiId;

        List < Asset > dsiAssets = DSIAssetHelper.getDSIAssets(request.DSI__c, false, false);
        List < SBQQ__Subscription__c > dsiSubscriptions = DSIAssetHelper.getSubscriptions(request.DSI__c);

        //generate Cart items by Key Group
        Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart = convertDSIToCartItems(request, dsiAssets, dsiSubscriptions, false, false);

        Map < String, List < KeyGeneratorCartItem >> resultItems = new Map < String, List < KeyGeneratorCartItem >> ();

        for (String kgrp : dsiShoppingCart.keySet()) {

            List < KeyGeneratorCartItem > cartItems = dsiShoppingCart.get(kgrp);

            Cart keyCart = new Cart(request, 1);

            String kgrpDesc = kgrp;

            switch on version {
                when '106', '107', '108' {

                    keyCart.listUnbundled = cartItems;

                    System.debug(keyCart.listUnbundled);

                    keyCart.ListConsolidated = groupSKUs11(keyCart.isDowngrade, keyCart, true);

                    System.debug(keyCart.ListConsolidated);

                    resultItems.put(kgrpDesc, keyCart.ListConsolidated);
                }

                when
                    else {
                    keyCart.listUnbundled = cartItems;

                    keyCart.ListConsolidated = groupSKUs(keyCart.isDowngrade, keyCart, true);
                    System.debug(keyCart.ListConsolidated);

                    resultItems.put(kgrpDesc, keyCart.ListConsolidated);
                }
            }


        }

        return resultItems;

    }
    /*
public static Map<String,List<KeyGeneratorCartItem>> generateConsolidatedMap(CDKeyRequest request){
Map<String,List<KeyGeneratorCartItem>> dsiConsolidatedCart = new Map<String,List<KeyGeneratorCartItem>>();

if(request.LicenseClassInfo!=null){
for(CDKeyMod lcs: request.LicenseClassInfo){
dsiConsolidatedCart.put(lcs.kGrpKey,lcs.contents);
lcs.contents=null;
}
}

if(request.LicenseClassInfo11!=null){
for(CDKeyMod11 lcs: request.LicenseClassInfo11){
dsiConsolidatedCart.put(lcs.kGrpKey,lcs.contents);
lcs.contents=null;
}
}

return dsiConsolidatedCart;
}


public static String convertCartItemstoJSON (List<KeyGeneratorCartItem> items){

return JSON.serialize(items, true);

}*/

    public static Map < String, List < KeyGeneratorCartItem >> convertDSIToCartItems(Product_Key_Request__c request, List < Asset > dsiAssets, List < SBQQ__Subscription__c > dsiSubscriptions, Boolean removeKeyGroups, Boolean ignoreKeyGroups) {

        /*Rules:
Term KeyGeneratorLicense = Term + Perpetual KeyGeneratorLicense in same Key Group
Perpetual = Only perpetual assets
Both Term and Perpetual blocks are broken by Key Group. Each Key Group in each block generates a key
Do not include licenses on NA or Empty Key Groups
*/

        //Split by Key Groups
        //helper to group by keygroup
        List < SObject > dsiContents = new List < SObject > ();
        dsiContents.addAll(dsiAssets);
        dsiContents.addAll(dsiSubscriptions);
        dsiContents = removeKeyGroups ? normalizeKeyGroups(dsiContents) : dsiContents;
        Map <String, List < SObject >> helperContents = new Map <String, List < SObject >> ();
        helperContents.put('keygroupIgnored', dsiContents);
        Map < String, List < SObject >> dsiContentsByKeyGroup = ignoreKeyGroups ? helperContents : KeyGeneratorHelper.groupSObjectsbyKeyGroup(dsiContents);
        Map < String, List < KeyGeneratorCartItem >> cartItemsByKeyGroup = new Map < String, List < KeyGeneratorCartItem >> ();
        System.debug(dsiContentsByKeyGroup);

        //convert to cart Items

        for (String kgrp : dsiContentsByKeyGroup.keySet()) {
            List < KeyGeneratorCartItem > itemsByKeyGroup = transformsObjectToCart(dsiContentsByKeyGroup.get(kgrp), false, request.Map_To_Previous_SKU__c);
            cartItemsByKeyGroup.put(kgrp, itemsByKeyGroup);
        }


        return cartItemsByKeyGroup;

    }

    public static List < SObject > normalizeKeyGroups(List < SObject > contents) {
        for (SObject obj : contents) {
            obj.put('Key_Group_multi__c', 'Prod Key 1');
        }
        return contents;
    }

    @Future
    public static void createCommunityUser(String contactId) {

        Contact cntc = [SELECT Id, FirstName, LastName, Email FROM Contact WHERE Id = :contactId];

        if (cntc != null) {
            User newusr = new User();
            newusr.ContactId = cntc.Id;
            newusr.FirstName = cntc.FirstName;
            newusr.LastName = cntc.LastName;
            newusr.Email = cntc.Email;
            newusr.Alias = cntc.FirstName;
            newusr.Username = cntc.Email + '.mstr';
            newusr.ProfileId = '00e44000000j9BvAAI';
            newusr.IsActive = true;
            newusr.CommunityNickname = cntc.FirstName;
            newusr.TimeZoneSidKey = 'America/New_York';
            newusr.CurrencyIsoCode = 'USD';
            newusr.LanguageLocaleKey = 'en_US';
            newusr.LocaleSidKey = 'en_US';
            //newusr.IsPortalEnabled=TRUE;
            newusr.EmailEncodingKey = 'UTF-8';
            newusr.Employee_Group__c = 'Community';

            insert newusr;
        }

    }

    public static CDKeyRequest mapJSONToKeys(String JSONResponse) {
        JSONParser parser = JSON.createParser(JSONResponse);
        CDKeyRequest cdkeyMod = null;
        while (parser.nextToken() != null) {
            // Start at the array of invoices.

            if (parser.getCurrentToken() == JSONToken.START_OBJECT) {
                // Read entire CDKEYMOD object,
                cdkeyMod = (CDKeyRequest) parser.readValueAs(CDKeyRequest.class);
                System.debug('CDKEYMod: ' + cdkeyMod);

            }

        }
        return cdkeyMod;

    }

    public static GeospatialRequest mapGeospatialJSONToKeys(String JSONResponse) {
        JSONParser parser = JSON.createParser(JSONResponse);
        GeospatialRequest cdkeyMod = null;
        while (parser.nextToken() != null) {
            // Start at the array of invoices.

            if (parser.getCurrentToken() == JSONToken.START_OBJECT) {
                // Read entire CDKEYMOD object,
                cdkeyMod = (GeospatialRequest) parser.readValueAs(GeospatialRequest.class);
                System.debug('GeospatialRequest: ' + cdkeyMod);

            }

        }
        return cdkeyMod;

    }
    public static Boolean isUniversalKey(String dsiID, String version) {

        //check custom settings first
        List < Version_Map__mdt > versionMap = [SELECT Version_ID__c, Map_To_Previous_SKU__c, LockedKey__c FROM Version_Map__mdt WHERE Label = :version LIMIT 1];
        //check DSI level exceptions
        return versionMap.size() > 0 ? !versionMap[0].LockedKey__c : true;
        //true for now, work in progress

    }

    public static List < CDKeyMod > generateJSONKeyRequest(Product_Key_Request__c request, String dsiID, Integer version, Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart) {

        List < Product2 > allProducts;

        if (request.isUniversal__c) {
            //get all products from the catalog
            //figure out version formats
            allProducts = DSIAssetHelper.getAllProductsByVersion(version);
            System.debug(allProducts);

        }

        List < CDKeyMod > keyInputs = new List < CDKeyMod > ();
        System.debug(dsiShoppingCart);

        for (String kgrp : dsiShoppingCart.keySet()) {
            if (kgrp.contains('N/A') || kgrp.contains('Flat MYC') || kgrp.contains('MYC On Hold')) {
                //ignore the N/A key group
                continue;
            }
            List < KeyGeneratorCartItem > currCart = dsiShoppingCart.get(kgrp);
            keyInputs.add(generateKeyParameters(allProducts, request, dsiID, version, '1', currCart, kgrp));
        }


        return keyInputs;


    }

    public static List < CDKeyMod11 > generateJSONKeyRequest11(Product_Key_Request__c request, String dsiID, Integer version, Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart) {

        List < Product2 > allProducts;

        if (request.isUniversal__c) {
            //get all products from the catalog
            //figure out version formats
            allProducts = DSIAssetHelper.getAllProductsByVersion(version);
            System.debug(allProducts);

        }

        List < CDKeyMod11 > keyInputs = new List < CDKeyMod11 > ();
        System.debug(dsiShoppingCart);

        for (String kgrp : dsiShoppingCart.keySet()) {
            if (kgrp.contains('N/A') || kgrp.contains('Flat MYC') || kgrp.contains('MYC On Hold')) {
                //remove n/a keygroup
                continue;
            }
            List < KeyGeneratorCartItem > currCart = dsiShoppingCart.get(kgrp);
            keyInputs.add(generateKeyParameters11(allProducts, request, dsiID, version, '1', currCart, kgrp));
        }

        return keyInputs;

    }

    public static CDKeyMod generateKeyParameters(List < Product2 > allProducts, Product_Key_Request__c request, String dsiID, Integer version, String keyGroup, List < KeyGeneratorCartItem > currCart, String keyGroupKey) {
        //Integer versionAssets = !assets.isEmpty()?DSIAssetHelper.getAssetVersion(assets):104;
        //ActionversionAssets = subscriptions.size()>0?DSIAssetHelper.getSubsVersion(subscriptions):versionAssets;


        if (currCart.size() == 0 && !request.Employee_Key__c) {
            throw new KeyGeneratorException('Key Request has no Assets');
        }
        /*
if (versionAssets > Integer.valueof(version)) {
///error you can't downgrade
throw new KeyGeneratorException('Key Request cannot be downgraded');
}*/


        //generate List of Cart items based on universality
        List < KeyGeneratorCartItem > cartItems = new List < KeyGeneratorCartItem > ();
        if (request.isUniversal__c) {
            System.debug('isUniversal');
            cartItems = transformProductsToCart(allProducts);

        } else {

            System.debug('---SZ Manual Key: Assets');

            //is Term
            cartItems = currCart;
            /*List < KeyGeneratorCartItem > cartItemsAsst = transformAssetsToCart(assets, false,request.Map_To_Previous_SKU__c);

List < KeyGeneratorCartItem > cartItemsSubs = transformSubsToCart(subscriptions, false,request.Map_To_Previous_SKU__c);

/*if(!cartItemsAsst.isEmpty()){
cartItemsAsst.addAll(cartItemsSubs);
cartItems= cartItemsAsst;
}
else if(!cartItemsSubs.isEmpty()){
cartItemsSubs.addAll(cartItemsAsst);
cartItems= cartItemsSubs;
}
else{
cartItems=new List<KeyGeneratorCartItem>();
}*/
            /*
cartItemsAsst.addAll(cartItemsSubs);
cartItems= cartItemsAsst;
system.debug(cartItems);*/


        }

        //validate before aggregation, need a good way to check combinations of products
        //Integer tempgroup = subscriptions.size()>0? (Integer)subscriptions[0].Key_Group_ID__c: (Integer)assets[0].Key_Group_ID__c;

        Cart keyCart = new Cart(request, getKeyGroupID(keyGroupKey));

        //add expiration date at the key level or allow override at the request level
        keyCart.ExpirationDate = checkExpirationDate(request, keyCart, currCart, keyGroupKey);

        keyCart = unbundleSkus(keyCart, cartItems, false, false);

        keyCart.IsAuditingDisabled = request.isAuditingDisabled__c;

        validateBeforeAggregation(keyCart.isModular, keyCart.listUnbundled);

        System.debug(keyCart.listUnbundled);

        keyCart.ListConsolidated = groupSKUs(keyCart.isDowngrade, keyCart, false);
        for (KeyGeneratorCartItem sku : keyCart.ListConsolidated) {
            System.debug(sku.SKUID + ' ' + sku.License + ' ' + sku.ProductID);
        }

        System.debug(keyCart.ListConsolidated);

        ValidateBusinessRules(keyCart, keyCart.ListConsolidated);

        CDKeyMod keyGen = SetCommonParameters(new CDKeyMod(), keyCart);

        if (keyCart.isDowngrade && keyCart.CurrentVersionID >= KeyGeneratorConstants.VERSIONID_V930 &&
            keyCart.VersionID < KeyGeneratorConstants.VERSIONID_V930) {
            keyGen.SDK = true;
        }

        keyGen = BuildModular(keyCart, keyGen);
        //keyGen = CheckWebMHz(keYgen);
        keyGen.kGrpKey = keyGroupKey;
        //keyGen.contents = keyCart.ListConsolidated;
        keyGen.UsherAnalyticsNamedUsers = keyCart.UsherAnalyticsNamedusers;
        keyGen.UsherMobileNamedUsers = keyCart.UsherMobileNamedUsers;
        keyGen.UsherProfessionalNamedUsers = keyCart.UsherProfessionalNamedUsers;
        keyGen.HasUsherProfessional = keyCart.hasUsherProfessional;
        keyGen.HasUsherMobile = keyCart.hasUsherMobile;
        keyGen.HasUsherAnalytics = keyCart.hasUsherAnalytics;

        return keyGen;


    }

    public static Integer getKeyGroupID(String keyGroupKey) {
        String currKgrp = keyGroupKey.left(keyGroupKey.indexOf('-'));
        Integer currInteger = KeyGeneratorConstants.keyGroupMap.get(currKgrp);
        //system.debug(currKgrp);
        //system.debug(currInteger);
        return currInteger == null ? -1 : currInteger;
    }

    public static CDKeyMod11 generateKeyParameters11(List < Product2 > allProducts, Product_Key_Request__c request, String dsiID, Integer version, String keyGroup, List < KeyGeneratorCartItem > currCart, String keyGroupKey) {
        /*
        Integer versionAssets = !assets.isEmpty()?DSIAssetHelper.getAssetVersion(assets):104;
        versionAssets = subscriptions.size()>0?DSIAssetHelper.getSubsVersion(subscriptions):versionAssets;
        */

        if (currCart.size() == 0 && !request.Employee_Key__c) {
            throw new KeyGeneratorException('Key Request has no Assets');
        }
        /*
        if (versionAssets > Integer.valueof(version)) {
            ///error you can't downgrade
            throw new KeyGeneratorException('Key Request cannot be downgraded');
        }
        */

        //generate List of Cart items based on universality
        List < KeyGeneratorCartItem > cartItems = new List < KeyGeneratorCartItem > ();
        if (request.isUniversal__c) {
            System.debug('isUniversal');
            cartItems = transformProductsToCart(allProducts);
        } else {
            System.debug('---SZ Manual Key: Assets');
            //is Term
            cartItems = currCart;
        }
        //validate before aggregation, need a good way to check combinations of products
        //Integer tempgroup = subscriptions.size()>0? (Integer)subscriptions[0].Key_Group_ID__c: (Integer)assets[0].Key_Group_ID__c;
        Cart keyCart = new Cart(request, getKeyGroupID(keyGroupKey));

        //add expiration date at the key level or allow override at the request level
        keyCart.ExpirationDate = checkExpirationDate(request, keyCart, currCart, keyGroupKey);

        keyCart = unbundleSkusUpgrade(keyCart, cartItems, false, false, false, false, new List < String > (), false);
        //keyCart.listUnbundled= cartItems;

        keyCart.IsAuditingDisabled = request.isAuditingDisabled__c;

        //validateBeforeAggregation(keyCart.isModular, keyCart.listUnbundled);
        System.debug(keyCart.listUnbundled);

        //Start - P Case 356154
        //validateBlacklistedProducts(keyCart, version);
        RemoveUnsupportedProducts(keyCart, version);
        //End - P Case 356154


        keyCart.ListConsolidated = groupSKUs11(keyCart.isDowngrade, keyCart, false);
        
        keyCart.ListConsolidated = matchPurchasedBundleCounts(keyCart.ListConsolidated);

        System.debug(keyCart.ListConsolidated);

        ValidateBusinessRules(keyCart, keyCart.ListConsolidated);
        validateLicenseModel(keyCart);

        CDKeyMod11 keyGen = SetCommonParameters11(new CDKeyMod11(), keyCart);

        /*
        if (keyCart.IsDowngrade && keyCart.CurrentVersionID >= KeyGeneratorConstants.VERSIONID_V930 &&
            keyCart.VersionID < KeyGeneratorConstants.VERSIONID_V930) {
                keyGen.SDK = true;
        }
        */
        keyGen.kGrpKey = keyGroupKey;
        //keyGen.contents = keyCart.ListConsolidated;
        keyGen = BuildModular11_1(keyCart, keyGen);
        //keyGen = CheckWebMHz(keYgen);

/*
        keyGen.UsherAnalyticsNamedUsers = keyCart.UsherAnalyticsNamedusers;
        keyGen.UsherMobileNamedUsers = keyCart.UsherMobileNamedUsers;
        KeyGen.UsherProfessionalNamedUsers=keyCart.UsherProfessionalNamedUsers;
        keyGen.hasUsherProfessional = keyCart.hasUsherProfessional;
        keyGen.HasUsherMobile = keyCart.hasUsherMobile;
        keyGen.HasUsherAnalytics = keyCart.hasUsherAnalytics;
*/

        return keyGen;

    }
    //Start -P 356154
    /*public static void validateBlacklistedProducts(Cart keyCart, integer version) {

Map < String, Key_Generator_Exception__mdt > exceptionMap = new Map < String, Key_Generator_Exception__mdt > ();
string curVersionFilter = '%'+version+'%';
system.debug('Blacklist Filter '+ curVersionFilter );
for (Key_Generator_Exception__mdt kg: [SELECT Label, Exception_Entity__c, Applicable_Versions__c, Exception_Type__c FROM Key_Generator_Exception__mdt WHERE Type__c = 'Blacklisted Product'
AND Inactive__c = false and Applicable_Versions__c like :curVersionFilter
]) {
exceptionMap.put(kg.Label, kg);
system.debug('Found applicable filter '+kg.Label +' - '+kg);
}
for (KeyGeneratorCartItem sku: keyCart.listUnbundled) {
if (exceptionMap.containsKey(String.valueOf(sku.SKUID)))
throw new KeyGeneratorException('Cannot generate key, this DSI contains a blacklisted product\r\n SKU: ' + sku.SKUID + ' Product Name: ' + sku.prodName);
}
}*/

    public static void RemoveUnsupportedProducts(Cart keyCart, Integer version) {

        Map < String, Key_Generator_Exception__mdt > exceptionMap = new Map < String, Key_Generator_Exception__mdt > ();
        String curVersionFilter = '%' + version + '%';
        System.debug('Blacklist Filter ' + curVersionFilter);
        for (Key_Generator_Exception__mdt kg : [
            SELECT Label, Exception_Entity__c, Applicable_Versions__c, Exception_Type__c
            FROM Key_Generator_Exception__mdt
            WHERE Type__c = 'Blacklisted Product'
            AND Inactive__c = FALSE AND Applicable_Versions__c LIKE :curVersionFilter
        ]) {
            exceptionMap.put(kg.Label, kg);
            System.debug('Found applicable filter ' + kg.Label + ' - ' + kg);
        }

        List<KeyGeneratorCartItem> finalListUnbundled = new List<KeyGeneratorCartItem>();
        for (KeyGeneratorCartItem sku : keyCart.listUnbundled) {
            if (exceptionMap.containsKey(String.valueOf(sku.SKUID)) == false) {
                finalListUnbundled.add(sku);
            }
        }
        if (finalListUnbundled.size() > 0) {
            keyCart.listUnbundled = finalListUnbundled;
        }
    }
    //End - P 356154

    public static List < KeyGeneratorCartItem > matchPurchasedBundleCounts(List < KeyGeneratorCartItem > cartItems) {

        for (KeyGeneratorCartItem item : cartItems) {
            System.debug(item.keyFeature);
            System.debug(item.LicenseInheritanceType);
            System.debug(item.SKUID);
            System.debug(item.BundleParent);
            if (item.BundleParent != null) {
                System.debug(item.BundleParent.SKUID);
                System.debug(item.BundleParent.keyFeature);
                System.debug(item.BundleParent.LicenseInheritanceType);
            }
            if (item.LicenseInheritanceType == 'If Purchased Only Match Base Servers'
                || (item.BundleParent != null && item.BundleParent.LicenseInheritanceType == 'If Purchased Only Match Base Servers')) {
                System.debug(item);
                item.quantity = item.quantity != null ? item.quantity + getBaseServerDifference(item.keyFeature, cartItems, 'quantity') : null;
                item.namedUsers = item.namedUsers != null ? item.namedUsers + getBaseServerDifference(item.keyFeature, cartItems, 'namedUsers') : null;
                item.cpuCount = item.cpuCount != null ? item.cpuCount + getBaseServerDifference(item.keyFeature, cartItems, 'CpuCount') : null;
            }
            //Hyper Mobile and Client Application SKUs that require Mobile and Reporter respectively
            if (item.LicenseInheritanceType == 'Match Base Feature'
                || (item.BundleParent != null && item.BundleParent.LicenseInheritanceType == 'Match Base Feature')) {
                System.debug(item);
                item.quantity = item.quantity != null ? item.quantity + getBaseFeatureDifference(item.keyFeature, cartItems, 'quantity') : null;
                item.namedUsers = item.namedUsers != null ? item.namedUsers + getBaseFeatureDifference(item.keyFeature, cartItems, 'namedUsers') : null;
                item.cpuCount = item.cpuCount != null ? item.cpuCount + getBaseFeatureDifference(item.keyFeature, cartItems, 'CpuCount') : null;
            }

        }

        return cartItems;
    }

    public static Integer getBaseServerDifference(String keyFeature, List < KeyGeneratorCartItem > cartItems, String attribute) {
        Integer baseServerCount = 0;
        Integer currentFeatureCount = 0;
        Integer helper = 0;

        //Add CPU Licenses to two dev Key groups in Custom Metadata
        List < String > baseServers = new List<String>();

        for (Key_Generator_Exception__mdt kgexp : [
            SELECT Label, Exception_Entity__c, Exception_Type__c
            FROM Key_Generator_Exception__mdt
            WHERE Type__c = 'Bundle Base Server'
            AND Inactive__c = FALSE AND Label = :keyFeature
        ]) {
            List<String> bases = kgexp.Exception_Entity__c.split(',');
            baseServers.addAll(bases);
        }

        for (KeyGeneratorCartItem item : cartItems) {
            helper = 0;
            if (item.keyFeature == null || keyFeature == null) continue;


            switch on attribute {
                when 'quantity' {
                    helper = item.quantity;
                }
                when 'namedUsers' {
                    helper = item.namedUsers;
                }
                when 'CpuCount' {
                    helper = item.cpuCount;
                }
                when else {       // default block, optional
                    // code block 4
                }
            }
            helper = helper == null ? 0 : helper;

            if (item.keyFeature == keyFeature) {
                currentFeatureCount += helper;
            } else if (baseServers.contains(item.keyFeature)) {
                baseServerCount += helper;
            }
        }
        System.debug(keyFeature);
        System.debug(baseServerCount);
        System.debug(currentFeatureCount);


        return baseServerCount > currentFeatureCount ? baseServerCount - currentFeatureCount : 0;

    }

    public static Integer getBaseFeatureDifference(String keyFeature, List < KeyGeneratorCartItem > cartItems, String attribute) {
        Integer baseServerCount = 0;
        Integer currentFeatureCount = 0;
        Integer helper = 0;

        //Add CPU Licenses to two dev Key groups in Custom Metadata
        List < String > baseServers = new List<String>();

        for (Key_Generator_Exception__mdt kgexp : [
            SELECT Label, Exception_Entity__c, Exception_Type__c
            FROM Key_Generator_Exception__mdt
            WHERE Type__c = 'Bundle Base Server'
            AND Inactive__c = FALSE AND Label = :keyFeature
        ]) {
            List<String> bases = kgexp.Exception_Entity__c.split(',');
            baseServers.addAll(bases);
        }

        List < String > baseFeature = new List<String>();

        for (Key_Generator_Exception__mdt kgexp : [
            SELECT Label, Exception_Entity__c, Exception_Type__c
            FROM Key_Generator_Exception__mdt
            WHERE Type__c = 'Base Feature'
            AND Inactive__c = FALSE AND Label = :keyFeature
        ]) {
            List<String> bases = kgexp.Exception_Entity__c.split(',');
            baseFeature.addAll(bases);
        }

        for (KeyGeneratorCartItem item : cartItems) {
            helper = 0;
            if (item.keyFeature == null || keyFeature == null) continue;


            switch on attribute {
                when 'quantity' {
                    helper = item.quantity;
                }
                when 'namedUsers' {
                    helper = item.namedUsers;
                }
                when 'CpuCount' {
                    helper = item.cpuCount;
                }
                when else {

                }
            }
            helper = helper == null ? 0 : helper;

            if (baseFeature.contains(item.keyFeature)) {
                currentFeatureCount += helper;
            } else if (baseServers.contains(item.keyFeature)) {
                baseServerCount += helper;
            }
        }
        System.debug(keyFeature);
        System.debug(baseServerCount);
        System.debug(currentFeatureCount);


        return baseServerCount > currentFeatureCount ? 0 : baseServerCount - currentFeatureCount;

    }

    /*
public static List < KeyGeneratorCartItem > transformAssetsToCart(List < Asset > assets, Boolean expired, Boolean mapSKUs) {
List < KeyGeneratorCartItem > newCart = new List < KeyGeneratorCartItem > ();
List < Product2 > newProducts = new List<Product2>();
if(mapSKUs){
List<String> prevProducts = DSIAssetHelper.getProductsFromAsset(assets);
newProducts= DSIAssetHelper.getAllProductsByPrevious(prevProducts);
system.debug(newProducts);
}
for (Asset asst: assets) {
Date ExpirationDate = null;
Integer previousMHz = -1;
Integer quantity = -1;
Integer namedUsers = -1;
Boolean isChild = false;

if (expired) {
ExpirationDate = asst.SBQQ__SubscriptionEndDate__c;
} else {
previousMHz = Integer.ValueOf(asst.Previous_MHz__c);
}

if (asst.Product2.SKU_Category_ID__c == KeyGeneratorConstants.SKUCATEGORY_NAMEDUSER) {
quantity = 1;
namedUsers = Integer.valueof(asst.quantity);
} else if (asst.Product2.SKU_Category_ID__c == KeyGeneratorConstants.SKUCATEGORY_MHZRATED) {
quantity = Integer.valueof(asst.quantity);
namedUsers = 0;
} else if (asst.Product2.SKU_Category_ID__c == KeyGeneratorConstants.SKUCATEGORY_UNRATED) {
quantity = Integer.valueof(asst.quantity);
if (asst.Product2.SKU_Type_ID__c == KeyGeneratorConstants.UNRATEDTYPE_HYBRID) {
//CHECK namedUsers=asst.NamedUsers__c;
} else {
namedUsers = 500;
}
}

if(mapSKUs){
for(Product2 prdt: newProducts){
if(prdt.Previous_SKU__r.ProductCode==asst.Product2.ProductCode){
KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
Integer.ValueOf(prdt.ProductCode),
quantity,
namedUsers,
isChild,
previousMHz,
(Integer) prdt.cpuRestriction__c,
(Integer) prdt.RMS_Product_ID__c,
(Integer) prdt.SKU_Category_ID__c,
(Integer) prdt.Version_ID__c,
(Integer) prdt.SKU_Type_ID__c,
(Integer) prdt.Variation_Type__c,
(Integer) prdt.Mhz__c,
(Integer) prdt.NU_Quantity__c,
ExpirationDate,
prdt.isBundle__c,
(Integer) prdt.Software_Type_ID__c,
prdt.clustering__c,
(Integer) prdt.BaseProductID__c,
(Integer) prdt.Variation_ID__c,
prdt.Id,
prdt.Key_Feature__c,
prdt.Id,
'',
Date.TODAY().addYears(1000),
Date.TODAY().addYears(1000),
prdt.Name,
prdt.Environment__c,
prdt.Version__c,
prdt.Interval__c);

newCart.add(tempItem);
}
}

}else{
KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
Integer.ValueOf(asst.Product2.ProductCode),
quantity,
namedUsers,
isChild,
previousMHz,
(Integer) asst.Product2.cpuRestriction__c,
(Integer) asst.Product2.RMS_Product_ID__c,
(Integer) asst.Product2.SKU_Category_ID__c,
(Integer) asst.Product2.Version_ID__c,
(Integer) asst.Product2.SKU_Type_ID__c,
(Integer) asst.Product2.Variation_Type__c,
(Integer) asst.Product2.Mhz__c,
(Integer) asst.Product2.NU_Quantity__c,
ExpirationDate,
asst.Product2.isBundle__c,
(Integer) asst.Product2.Software_Type_ID__c,
asst.Product2.clustering__c,
(Integer) asst.Product2.BaseProductID__c,
(Integer) asst.Product2.Variation_ID__c,
asst.Id,
asst.Product2.Key_Feature__c,
asst.Product2Id,
asst.Exception_multi__c,
asst.SBQQ__SubscriptionEndDate__c,
asst.LastModifiedDate,
asst.Product2.Name,
asst.Product2.Environment__c,
asst.Product2.Version__c,
asst.Product2.Interval__c);

newCart.add(tempItem);
}

}
return newCart;
}
*/

    public static Datetime checkExpirationDate(Product_Key_Request__c request, Cart keyCart, List < KeyGeneratorCartItem > cartItems, String keyGroupKey) {

        if (request.Expiration_Date__c != null) {
            return request.Expiration_Date__c;
        }

        if (keyGroupKey.contains('Perpetual')) {
            return null;
        }

        return getEarliestExpiration(cartItems);

    }

    public static Datetime getEarliestExpiration(List < KeyGeneratorCartItem > cartItems) {
        Datetime minDate = Date.newInstance(3000, 1, 1);
        for (KeyGeneratorCartItem sku : cartItems) {
            //System.debug(sku);
            if (sku.itemIdentifier == 'SBQQ__Subscription__c' && sku.ExpirationDate < minDate) {
                minDate = sku.ExpirationDate;
            }
        }

        return minDate;
    }

    public static List < KeyGeneratorCartItem > transformsObjectToCart(List < SObject > assets, Boolean expired, Boolean mapSKUs) {
        List < KeyGeneratorCartItem > newCart = new List < KeyGeneratorCartItem > ();
        List < Product2 > newProducts = new List < Product2 > ();
        Map<String, Key_Generator_Quantity_Exception__mdt> skuWithQuantity = new Map<String, Key_Generator_Quantity_Exception__mdt>();

        List<Key_Generator_Quantity_Exception__mdt> keyGenExceptionQuantity = [SELECT Id, Label, Quantity__c, Named_User__c FROM Key_Generator_Quantity_Exception__mdt];
        for(Key_Generator_Quantity_Exception__mdt kgqe : keyGenExceptionQuantity) {
            skuWithQuantity.put(kgqe.Label, kgqe);
        }
        
        Map<String, Key_Generator_Combo_Products__mdt> comboRulesMap = new Map<String, Key_Generator_Combo_Products__mdt>();
        for (Key_Generator_Combo_Products__mdt rule : [SELECT Label, Child_Product_Codes__c, Named_User__c, Quantity__c FROM Key_Generator_Combo_Products__mdt]) {
            comboRulesMap.put(rule.Label, rule);
        }

        if (mapSKUs) {
            List < String > prevProducts = DSIAssetHelper.getProductsFromSObject(assets);
            newProducts = DSIAssetHelper.getAllProductsByPrevious(prevProducts);
            System.debug(newProducts);
        }
        
        Map<Id, String> parentAssetIdToParentProductCodeMap = new Map<Id, String>();
        Set<String> allChildProductCodesToQuery = new Set<String>();

        for (SObject asst : assets) {

            String prodPrefix = asst.getSObjectType().getDescribe().getName() == 'Asset' ? 'Product2' : 'SBQQ__Product__r';
            String prodPrefixId = asst.getSObjectType().getDescribe().getName() == 'Asset' ? 'Product2Id' : 'SBQQ__Product__c';
            String quantityIdentifier = asst.getSObjectType().getDescribe().getName() == 'Asset' ? 'Quantity' : 'SBQQ__Quantity__c';
            String endDateIdentifier = asst.getSObjectType().getDescribe().getName() == 'Asset' ? 'End_Date__c' : 'SBQQ__EndDate__c';

            Date ExpirationDate = null;
            Integer previousMHz = -1;
            Integer quantity = -1;
            Integer namedUsers = -1;
            Boolean isChild = false;
            ExpirationDate = (Date) asst.get(endDateIdentifier);

            if (expired) {

            } else {
                previousMHz = Integer.valueOf(asst.get('Previous_MHz__c'));
            }

            String currentProductCode = (String) asst.getSObject(prodPrefix).get('ProductCode');

            if (Integer.valueOf(asst.getSObject(prodPrefix).get('SKU_Category_ID__c')) == KeyGeneratorConstants.SKUCATEGORY_NAMEDUSER) {
                quantity = 1;
                namedUsers = Integer.valueOf(asst.get(quantityIdentifier));
            } else if (Integer.valueOf(asst.getSObject(prodPrefix).get('SKU_Category_ID__c')) == KeyGeneratorConstants.SKUCATEGORY_MHZRATED) {
                quantity = Integer.valueOf(asst.get(quantityIdentifier));
                namedUsers = 0;
            } else if (Integer.valueOf(asst.getSObject(prodPrefix).get('SKU_Category_ID__c')) == KeyGeneratorConstants.SKUCATEGORY_UNRATED) {
                quantity = Integer.valueOf(asst.get(quantityIdentifier));
                if (Integer.valueOf(asst.getSObject(prodPrefix).get('SKU_Type_ID__c')) == KeyGeneratorConstants.UNRATEDTYPE_HYBRID) {
                    //CHECK namedUsers=asst.NamedUsers__c;
                } else {
                    namedUsers = 500;
                }
            }
            if(skuWithQuantity.containsKey(String.valueOf(asst.getSObject(prodPrefix).get('ProductCode')))) {
                quantity = Integer.valueOf(skuWithQuantity.get(String.valueOf(asst.getSObject(prodPrefix).get('ProductCode'))).Quantity__c);
                namedUsers = Integer.valueOf(skuWithQuantity.get(String.valueOf(asst.getSObject(prodPrefix).get('ProductCode'))).Named_User__c);
            }
            
            if (comboRulesMap.containsKey(currentProductCode)) {
                parentAssetIdToParentProductCodeMap.put(asst.Id, currentProductCode); 
                Key_Generator_Combo_Products__mdt rule = comboRulesMap.get(currentProductCode);
                if (String.isNotBlank(rule.Child_Product_Codes__c)) {
                    for (String childCode : rule.Child_Product_Codes__c.split(';')) {
                        if(String.isNotBlank(childCode?.trim())){
                            allChildProductCodesToQuery.add(childCode.trim());
                        }
                    }
                }
            }

            if (mapSKUs) {
                for (Product2 prdt : newProducts) {
                    if (prdt.Previous_SKU__r.ProductCode == (String) asst.getSObject(prodPrefix).get('ProductCode')) {
                        KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
                            Integer.valueOf(prdt.ProductCode),
                            quantity,
                            namedUsers,
                            isChild,
                            previousMHz,
                            (Integer) prdt.CPURestriction__c,
                            (Integer) prdt.RMS_Product_ID__c,
                            (Integer) prdt.SKU_Category_ID__c,
                            (Integer) prdt.Version_ID__c,
                            (Integer) prdt.SKU_Type_ID__c,
                            (Integer) prdt.Variation_Type__c,
                            (Integer) prdt.Mhz__c,
                            (Integer) prdt.NU_Quantity__c,
                            ExpirationDate,
                            //prdt.isBundle__c,
                            (Integer) prdt.Software_Type_ID__c,
                            prdt.Clustering__c,
                            (Integer) prdt.BaseProductID__c,
                            (Integer) prdt.Variation_ID__c,
                            prdt.Id,
                            prdt.Key_Feature__c,
                            prdt.Id,
                            (String) asst.get('Exception_multi__c'),
                            Date.today().addYears(1000),
                            Date.today().addYears(1000),
                            prdt.Name,
                            prdt.Environment__c,
                            prdt.Version__c,
                            prdt.Interval__c,
                            asst.getSObjectType().getDescribe().getName(),
                            (String) asst.get('Key_Group_multi__c'),
                            (String) asst.get('Aggregation_Comments__c'),
                            new List<Boolean>{
                                (Boolean) asst.getSObject(prodPrefix).get('isBundle__c'), (Boolean) asst.getSObject(prodPrefix).get('LegacyCatalog__c')
                            });

                        //Deduction(Change) - Start
                        if (asst != null) {
                            if (asst.getSObjectType().getDescribe().getName() == 'Asset') {
                                //system.debug(asst.getSObject('SBQQ__Currentsubscription__r'));
                                tempItem.subscriptionName = asst.getSObject('SBQQ__Currentsubscription__r') != null ? (String) asst.getSObject('SBQQ__Currentsubscription__r').get('Name') : null;
                                tempItem.subscriptionContractNumber = asst.getSObject('SBQQ__Currentsubscription__r') != null && asst.getSObject('SBQQ__Currentsubscription__r').getSObject('SBQQ__Contract__r') != null ? (String) asst.getSObject('SBQQ__Currentsubscription__r').getSObject('SBQQ__Contract__r').get('Contractnumber') : null;
                                tempItem.currentSubscriptionId = (String) asst.get('SBQQ__Currentsubscription__c');
                            } else {
                                //system.debug(asst.get('Name'));
                                tempItem.subscriptionName = (String) asst.get('Name');
                                tempItem.subscriptionContractNumber = asst.getSObject('SBQQ__Contract__r') != null ? (String) asst.getSObject('SBQQ__Contract__r').get('Contractnumber') : null;
                            }
                        }

                        //end
                        newCart.add(tempItem);
                    }
                }

                if ((String) asst.getSObject(prodPrefix).get('Environment__c') == 'CPU' || asst.getSObject(prodPrefix).get('Name') == 'Client - Architect') {
                    KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
                        Integer.valueOf((String) asst.getSObject(prodPrefix).get('ProductCode')),
                        quantity,
                        namedUsers,
                        isChild,
                        previousMHz,
                        Integer.valueOf(asst.getSObject(prodPrefix).get('cpuRestriction__c')),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('RMS_Product_ID__c')),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('SKU_Category_ID__c')),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('Version_ID__c')),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('SKU_Type_ID__c')),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('Variation_Type__c')),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('Mhz__c')),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('NU_Quantity__c')),
                        ExpirationDate,
                        //(Boolean) asst.getSObject(prodPrefix).get('isBundle__c'),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('Software_Type_ID__c')),
                        (Boolean) asst.getSObject(prodPrefix).get('clustering__c'),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('BaseProductID__c')),
                        Integer.valueOf(asst.getSObject(prodPrefix).get('Variation_ID__c')),
                        asst.Id,
                        (String) asst.getSObject(prodPrefix).get('Key_Feature__c'),
                        (Id) asst.get(prodPrefixId),
                        (String) asst.get('Exception_multi__c'),
                        (Date) asst.get('SBQQ__SubscriptionEndDate__c'),
                        (Datetime) asst.get('LastModifiedDate'),
                        (String) asst.getSObject(prodPrefix).get('Name'),
                        (String) asst.getSObject(prodPrefix).get('Environment__c'),
                        (String) asst.getSObject(prodPrefix).get('Version__c'),
                        (String) asst.getSObject(prodPrefix).get('Interval__c'),
                        asst.getSObjectType().getDescribe().getName(),
                        (String) asst.get('Key_Group_multi__c'),
                        (String) asst.get('Aggregation_Comments__c'),
                        new List<Boolean>{
                            (Boolean) asst.getSObject(prodPrefix).get('isBundle__c'), (Boolean) asst.getSObject(prodPrefix).get('LegacyCatalog__c')
                        }
                    );
                    tempItem.subsStartDate = (Date) asst.get('SBQQ__SubscriptionStartDate__c');
                    //Deduction(Change) - start
                    if (asst != null) {
                        if (asst.getSObjectType().getDescribe().getName() == 'Asset') {
                            //system.debug(asst.getSObject('SBQQ__Currentsubscription__r'));
                            tempItem.subscriptionName = asst.getSObject('SBQQ__Currentsubscription__r') != null ? (String) asst.getSObject('SBQQ__Currentsubscription__r').get('Name') : null;
                            tempItem.subscriptionContractNumber = asst.getSObject('SBQQ__Currentsubscription__r') != null && asst.getSObject('SBQQ__Currentsubscription__r').getSObject('SBQQ__Contract__r') != null ? (String) asst.getSObject('SBQQ__Currentsubscription__r').getSObject('SBQQ__Contract__r').get('Contractnumber') : null;
                            tempItem.currentSubscriptionId = (String) asst.get('SBQQ__Currentsubscription__c');
                        } else {
                            //system.debug(asst.get('Name'));
                            tempItem.subscriptionName = (String) asst.get('Name');
                            tempItem.subscriptionContractNumber = asst.getSObject('SBQQ__Contract__r') != null ? (String) asst.getSObject('SBQQ__Contract__r').get('Contractnumber') : null;
                        }
                    }//end

                    newCart.add(tempItem);
                }

            } else {
                KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
                    Integer.valueOf((String) asst.getSObject(prodPrefix).get('ProductCode')),
                    quantity,
                    namedUsers,
                    isChild,
                    previousMHz,
                    Integer.valueOf(asst.getSObject(prodPrefix).get('cpuRestriction__c')),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('RMS_Product_ID__c')),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('SKU_Category_ID__c')),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('Version_ID__c')),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('SKU_Type_ID__c')),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('Variation_Type__c')),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('Mhz__c')),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('NU_Quantity__c')),
                    ExpirationDate,
                    //(Boolean) asst.getSObject(prodPrefix).get('isBundle__c'),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('Software_Type_ID__c')),
                    (Boolean) asst.getSObject(prodPrefix).get('clustering__c'),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('BaseProductID__c')),
                    Integer.valueOf(asst.getSObject(prodPrefix).get('Variation_ID__c')),
                    asst.Id,
                    (String) asst.getSObject(prodPrefix).get('Key_Feature__c'),
                    (Id) asst.get(prodPrefixId),
                    (String) asst.get('Exception_multi__c'),
                    (Date) asst.get('SBQQ__SubscriptionEndDate__c'),
                    (Datetime) asst.get('LastModifiedDate'),
                    (String) asst.getSObject(prodPrefix).get('Name'),
                    (String) asst.getSObject(prodPrefix).get('Environment__c'),
                    (String) asst.getSObject(prodPrefix).get('Version__c'),
                    (String) asst.getSObject(prodPrefix).get('Interval__c'),
                    asst.getSObjectType().getDescribe().getName(),
                    (String) asst.get('Key_Group_multi__c'),
                    (String) asst.get('Aggregation_Comments__c'),
                    new List<Boolean>{
                        (Boolean) asst.getSObject(prodPrefix).get('isBundle__c'), (Boolean) asst.getSObject(prodPrefix).get('LegacyCatalog__c')
                    }
                );
                tempItem.subsStartDate = (Date) asst.get('SBQQ__SubscriptionStartDate__c');
                //Deduction - start
                if (asst != null) {
                    if (asst.getSObjectType().getDescribe().getName() == 'Asset') {
                        //system.debug(asst.getSObject('SBQQ__Currentsubscription__r').get('Name') + ' ' + asst.getSObject('SBQQ__Currentsubscription__r').getSObject('SBQQ__Contract__r').get('Contractnumber'));
                        tempItem.subscriptionName = asst.getSObject('SBQQ__Currentsubscription__r') != null ? (String) asst.getSObject('SBQQ__Currentsubscription__r').get('Name') : null;
                        tempItem.subscriptionContractNumber = asst.getSObject('SBQQ__Currentsubscription__r') != null && asst.getSObject('SBQQ__Currentsubscription__r').getSObject('SBQQ__Contract__r') != null ? (String) asst.getSObject('SBQQ__Currentsubscription__r').getSObject('SBQQ__Contract__r').get('Contractnumber') : null;
                        tempItem.currentSubscriptionId = (String) asst.get('SBQQ__Currentsubscription__c');
                    } else {
                        //system.debug(asst.get('Name'));
                        tempItem.subscriptionName = (String) asst.get('Name');
                        tempItem.subscriptionContractNumber = asst.getSObject('SBQQ__Contract__r') != null ? (String) asst.getSObject('SBQQ__Contract__r').get('Contractnumber') : null;
                    }
                }
                //end

                newCart.add(tempItem);
            }

        }

        Map<String, Product2> childProductDetailsMap = new Map<String, Product2>();
        if (!allChildProductCodesToQuery.isEmpty()) {
            for (Product2 p : DSIAssetHelper.getProductsByCodes(new Set<String>(allChildProductCodesToQuery))) {
                childProductDetailsMap.put(p.ProductCode, p);
            }
        }
        Map<Id, SObject> assetMap = new Map<Id, SObject>(assets);
        
        for (Id parentAssetId : parentAssetIdToParentProductCodeMap.keySet()) {
            String parentProductCode = parentAssetIdToParentProductCodeMap.get(parentAssetId);
            SObject parentAsst = assetMap.get(parentAssetId);
            Key_Generator_Combo_Products__mdt rule = comboRulesMap.get(parentProductCode);
            
            String parentKeyGroup = (String) parentAsst.get('Key_Group_multi__c'); // Key group from the parent asset
            
            if (rule != null && String.isNotBlank(rule.Child_Product_Codes__c)) {
                List<String> childProductCodeList = rule.Child_Product_Codes__c.split(';');
                for (String childCodeStr : childProductCodeList) {
                    String trimmedChildCode = childCodeStr.trim();
                    if(String.isBlank(trimmedChildCode)) continue;
                    
                    Product2 childProduct = childProductDetailsMap.get(trimmedChildCode);
                    
                    if (childProduct != null) {
                        // Use quantity and named users from the custom metadata rule
                        Integer childQuantity = rule.Quantity__c != null ? Integer.valueOf(rule.Quantity__c) : 1;
                        Integer childNamedUsers = rule.Named_User__c != null ? Integer.valueOf(rule.Named_User__c) : 1;                        
                        
                        KeyGeneratorCartItem comboChildItem = new KeyGeneratorCartItem(
                            Integer.valueOf(childProduct.ProductCode),
                            childQuantity,
                            childNamedUsers,
                            false, 
                            0,
                            childProduct.CPURestriction__c != null ? Integer.valueOf(childProduct.CPURestriction__c) : null,
                            childProduct.RMS_Product_ID__c != null ? Integer.valueOf(childProduct.RMS_Product_ID__c) : null,
                            childProduct.SKU_Category_ID__c != null ? Integer.valueOf(childProduct.SKU_Category_ID__c) : null,
                            childProduct.Version_ID__c != null ? Integer.valueOf(childProduct.Version_ID__c) : null,
                            childProduct.SKU_Type_ID__c != null ? Integer.valueOf(childProduct.SKU_Type_ID__c) : null,
                            childProduct.Variation_Type__c != null ? Integer.valueOf(childProduct.Variation_Type__c) : null,
                            childProduct.Mhz__c != null ? Integer.valueOf(childProduct.Mhz__c) : null,
                            childProduct.NU_Quantity__c != null ? Integer.valueOf(childProduct.NU_Quantity__c) : null,
                            null,
                            childProduct.Software_Type_ID__c != null ? Integer.valueOf(childProduct.Software_Type_ID__c) : null,
                            childProduct.Clustering__c,
                            childProduct.BaseProductID__c != null ? Integer.valueOf(childProduct.BaseProductID__c) : null,
                            childProduct.Variation_ID__c != null ? Integer.valueOf(childProduct.Variation_ID__c) : null,
                            childProduct.Id, 
                            childProduct.Key_Feature__c,
                            childProduct.Id, 
                            '', // exceptions multi - default empty for added items
                            null, // subsEndDate
                            Datetime.now(), // lastModDate
                            childProduct.Name,
                            childProduct.Environment__c,
                            childProduct.Version__c,
                            childProduct.Interval__c,
                            'Product', // itemIdentifier, as it's based on a Product2
                            parentKeyGroup, // Key group from the PARENT asset
                            'Added by combo rule from ' + parentProductCode, // aggregationComments
                            new List<Boolean>{childProduct.isBundle__c, childProduct.LegacyCatalog__c}
                        );
                        newCart.add(comboChildItem);
                        System.debug('Added combo child KeyGeneratorCartItem for ProductCode ' + childProduct.ProductCode +
                                     ' due to parent ' + parentProductCode + ' with KeyGroup: ' + parentKeyGroup);
                    } else {
                        System.debug('Warning: Child Product with code ' + trimmedChildCode + ' defined in combo rule for ' + parentProductCode + ' not found.');
                    }
                }
            }
        
        }
        return newCart;
    }


    /*
public static List < KeyGeneratorCartItem > transformSubsToCart(List < SBQQ__Subscription__c > assets, Boolean expired, Boolean mapSKUs) {
List < KeyGeneratorCartItem > newCart = new List < KeyGeneratorCartItem > ();
List < Product2 > newProducts = new List<Product2>();
if(mapSKUs){
List<String> prevProducts = DSIAssetHelper.getProductsFromSubscription(assets);
newProducts= DSIAssetHelper.getAllProductsByPrevious(prevProducts);
system.debug(newProducts);
}

for (SBQQ__Subscription__c asst: assets) {
Date ExpirationDate = null;
Integer previousMHz = -1;
Integer quantity = -1;
Integer namedUsers = -1;
Boolean isChild = false;

if (expired) {
ExpirationDate = asst.SBQQ__SubscriptionEndDate__c;
} else {
previousMHz = Integer.ValueOf(asst.Previous_MHz__c);
}

if (asst.SBQQ__Product__r.SKU_Category_ID__c == KeyGeneratorConstants.SKUCATEGORY_NAMEDUSER) {
quantity = 1;
namedUsers = Integer.valueof(asst.SBQQ__Quantity__c);
} else if (asst.SBQQ__Product__r.SKU_Category_ID__c == KeyGeneratorConstants.SKUCATEGORY_MHZRATED) {
quantity = Integer.valueof(asst.SBQQ__Quantity__c);
namedUsers = 0;
} else if (asst.SBQQ__Product__r.SKU_Category_ID__c == KeyGeneratorConstants.SKUCATEGORY_UNRATED) {
quantity = Integer.valueof(asst.SBQQ__Quantity__c);
if (asst.SBQQ__Product__r.SKU_Type_ID__c == KeyGeneratorConstants.UNRATEDTYPE_HYBRID) {
//CHECK namedUsers=asst.NamedUsers__c;
} else {
namedUsers = 500;
}
}

if(mapSKUs){
for(Product2 prdt: newProducts){
if(prdt.Previous_SKU__r.ProductCode==asst.SBQQ__Product__r.ProductCode){
KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
Integer.ValueOf(prdt.ProductCode),
quantity,
namedUsers,
isChild,
previousMHz,
(Integer) prdt.cpuRestriction__c,
(Integer) prdt.RMS_Product_ID__c,
(Integer) prdt.SKU_Category_ID__c,
(Integer) prdt.Version_ID__c,
(Integer) prdt.SKU_Type_ID__c,
(Integer) prdt.Variation_Type__c,
(Integer) prdt.Mhz__c,
(Integer) prdt.NU_Quantity__c,
ExpirationDate,
prdt.isBundle__c,
(Integer) prdt.Software_Type_ID__c,
prdt.clustering__c,
(Integer) prdt.BaseProductID__c,
(Integer) prdt.Variation_ID__c,
prdt.Id,
prdt.Key_Feature__c,
prdt.Id,
'',
Date.TODAY().addYears(1000),
Date.TODAY().addYears(1000),
prdt.Name,
prdt.Environment__c,
prdt.Version__c,
prdt.Interval__c);

newCart.add(tempItem);
}
}

}
else{
KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
Integer.ValueOf(asst.SBQQ__Product__r.ProductCode),
quantity,
namedUsers,
isChild,
previousMHz,
(Integer) asst.SBQQ__Product__r.cpuRestriction__c,
(Integer) asst.SBQQ__Product__r.RMS_Product_ID__c,
(Integer) asst.SBQQ__Product__r.SKU_Category_ID__c,
(Integer) asst.SBQQ__Product__r.Version_ID__c,
(Integer) asst.SBQQ__Product__r.SKU_Type_ID__c,
(Integer) asst.SBQQ__Product__r.Variation_Type__c,
(Integer) asst.SBQQ__Product__r.Mhz__c,
(Integer) asst.SBQQ__Product__r.NU_Quantity__c,
ExpirationDate,
asst.SBQQ__Product__r.isBundle__c,
(Integer) asst.SBQQ__Product__r.Software_Type_ID__c,
asst.SBQQ__Product__r.clustering__c,
(Integer) asst.SBQQ__Product__r.BaseProductID__c,
(Integer) asst.SBQQ__Product__r.Variation_ID__c,
asst.Id,
asst.SBQQ__Product__r.Key_Feature__c,
asst.SBQQ__Product__c,
asst.Exception_multi__c,
asst.SBQQ__SubscriptionEndDate__c,
asst.LastModifiedDate,
asst.SBQQ__Product__r.Name,
asst.SBQQ__Product__r.Environment__c,
asst.SBQQ__Product__r.Version__c,
asst.SBQQ__Product__r.Interval__c);

newCart.add(tempItem);
}
}
system.debug(newCart);
return newCart;
}
*/

    public static List < KeyGeneratorCartItem > transformProductsToCart(List < Product2 > products) {
        System.debug(products);
        List < KeyGeneratorCartItem > newCart = new List < KeyGeneratorCartItem > ();
        for (Product2 prod : products) {
            Date ExpirationDate = null;
            Integer previousMHz = 0;
            Integer quantity = 0;
            Integer namedUsers = 0;
            Boolean isChild = false; //false when building initial cart


            KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
                Integer.valueOf(prod.ProductCode),
                quantity,
                namedUsers,
                isChild,
                previousMHz,
                (Integer) prod.CPURestriction__c,
                (Integer) prod.RMS_Product_ID__c,
                (Integer) prod.SKU_Category_ID__c,
                (Integer) prod.Version_ID__c,
                (Integer) prod.SKU_Type_ID__c, //unratedType = Sky Type ID
                (Integer) prod.Variation_Type__c,
                (Integer) prod.Mhz__c,
                (Integer) prod.NU_Quantity__c,
                ExpirationDate,
                //prod.isBundle__c,
                (Integer) prod.Software_Type_ID__c,
                prod.Clustering__c,
                (Integer) prod.BaseProductID__c,
                (Integer) prod.Variation_ID__c,
                prod.Id,
                prod.Key_Feature__c,
                prod.Id,
                '',
                Date.today().addYears(1000),
                Date.today().addYears(1000),
                prod.Name,
                prod.Environment__c,
                prod.Version__c,
                prod.Interval__c,
                'Product',
                '',
                '',
                new List<Boolean>{
                    prod.isBundle__c, prod.LegacyCatalog__c
                }
            );

            newCart.add(tempItem);

        }
        return newCart;
    }

    public static CDKeyMod SetCommonParameters(CDKeyMod keyBase, Cart key) {
        // Retrieve key info from interface
        if (key.ExpirationDate != null && key.ExpirationDate != Date.newInstance(1700, 1, 1)) {
            keyBase.EnablePOC = true;
            keyBase.ExpirationDate = key.ExpirationDate;
        } else {
            keyBase.EnablePOC = false;
        }
        keyBase.KDate = Date.today();
        //keyBase.KstDate = keyBase.KDate.format();
        keyBase.KstDate = String.valueOf(keyBase.KDate.month()) + '/' + String.valueOf(keyBase.KDate.day()) + '/' + String.valueOf(keyBase.KDate.year());
        //need to check
        keyBase.CATSId = key.CATSId;
        keyBase.DSI = key.DSI;
        keyBase.LKG = key.LKG;
        keyBase.PreRelease = false;
        keyBase.DisableRegistration = key.DisableRegistration;
        keyBase.BypassCPUChecking = key.BypassCPUChecking;
        keyBase.CloudBI = key.CloudBI;
        keyBase.IsAWS = key.isAWS;

        return keyBase;
    }

    public static CDKeyMod11 SetCommonParameters11(CDKeyMod11 keyBase, Cart key) {
        // Retrieve key info from interface
        if (key.ExpirationDate != null && key.ExpirationDate != Date.newInstance(1700, 1, 1)) {
            KeyPOC tempPOC = new KeyPOC();
            tempPOC.ExpirationDate = key.ExpirationDate;
            keyBase.EnablePOC = tempPOC;
            keyBase.ExpirationDate = key.ExpirationDate;
        }
        keyBase.KDate = Date.today();
        //keyBase.KstDate = keyBase.KDate.format();
        keyBase.KstDate = String.valueOf(keyBase.KDate.month()) + '/' + String.valueOf(keyBase.KDate.day()) + '/' + String.valueOf(keyBase.KDate.year());
        //need to check
        keyBase.CATSId = key.CATSId;
        keyBase.DSI = key.DSI;
        keyBase.LKG = key.LKG;
        keyBase.IsVCPUs = key.IsVCPUs;
        keyBase.Platform = key.Platform;


        keyBase.PreRelease = false;
        keyBase.DisableRegistration = key.DisableRegistration;
        keyBase.BypassCPUChecking = key.BypassCPUChecking;
        keyBase.CloudBI = key.CloudBI;
        keyBase.IsAWS = key.isAWS;
        keyBase.ByPassAudit = key.IsAuditingDisabled;
        keyBase.EnableDailyReport = false;
        keyBase.KeylevelEval = true;

        return keyBase;
    }

    public static Cart unbundleSkus(Cart keyCart, List < KeyGeneratorCartItem > cartItems, Boolean excludeNewBundle, Boolean excludeUsherBundles) {

        System.debug('unbundleSkus---');
        System.debug(cartItems);
        Map < String, List < KeyGeneratorCartItem >> childMap = buildChildMap(cartItems, 'Downgrade', false, false, keyCart);
        System.debug(childMap);

        for (KeyGeneratorCartItem sku : cartItems) {
            //system.debug(sku);
            if (keyCart.isUniversalKey && sku.License.Type != KeyGeneratorConstants.LICENSETYPE_NAMEDUSER)
                continue;

            //usher business rules
            if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYUSHERANALYTICS) {
                keyCart.UsherAnalyticsNamedusers += sku.namedUsers;
                keyCart.hasUsherAnalytics = true;
            }
            if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYUSHERMOBILE) {
                keyCart.UsherMobileNamedUsers += sku.namedUsers;
                keyCart.hasUsherMobile = true;
            }
            if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_USHERSECPROF ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_USHERSECPROFTERM) {
                keyCart.UsherProfessionalNamedUsers += sku.namedUsers;
                keyCart.hasUsherProfessional = true;
                System.debug('hasusher');
            }

            //check bundles but not report bundles
            if (sku.isBundle &&
                (!excludeNewBundle ||
                    (excludeNewBundle &&
                        sku.type != KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYWEBBUNDLE &&
                        sku.type != KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYSERVERBUNDLE &&
                        sku.type != KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYMOBILEBUNDLE &&
                        sku.type != KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYARCHITECTBUNDLE &&
                        sku.type != KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYUSHERANALYTICS &&
                        sku.type != KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYUSHERMOBILE &&
                        sku.type != KeyGeneratorConstants.PRODUCTTYPE_USHERSECPROF &&
                        sku.type != KeyGeneratorConstants.PRODUCTTYPE_USHERSECPROFTERM)
                ) &&
                sku.softwareType != KeyGeneratorConstants.SOFTWARETYPE_MODULARELB) {
                // if yes, get child skus
                //for usher bundles don't add anything
                //TODO NEED TO BULKIFY THIS
                List < KeyGeneratorCartItem > bundleItems = childMap.get(sku.masterUniqueID); //TODO BUILD SUPPORT FOR RECURSIVE SEARCH
                //List < KeyGeneratorCartItem > bundleItems = recursiveSearch(childMap,sku.skuid);
                if (bundleItems == null) {
                    bundleItems = new List < KeyGeneratorCartItem > ();
                } else {
                    //propagate License
                    for (KeyGeneratorCartItem crt : bundleItems) {
                        //system.debug(crt);
                        crt.quantity = sku.quantity;
                        crt.namedUsers = sku.namedUsers;
                        crt.cpuCount = sku.cpuCount;
                    }
                }
                //else System.debug('childItems');
                keyCart.listUnbundled.addAll(bundleItems);
            } else if (!(excludeUsherBundles &&
                (sku.type == KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYUSHERANALYTICS ||
                    sku.type == KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYUSHERMOBILE ||
                    sku.type == KeyGeneratorConstants.PRODUCTTYPE_USHERSECPROF ||
                    sku.type == KeyGeneratorConstants.PRODUCTTYPE_USHERSECPROFTERM))) //to exclude usher from downgrade to below 9.5.1
            {
                // add self to final list of sku
                keyCart.listUnbundled.add(sku);
            }

        }

        for (KeyGeneratorCartItem sku2 : keyCart.listUnbundled) {
            //system.debug(sku2.SKUID+' '+sku2.License+' '+sku2.ProductID);
        }
        return keyCart;

    }

    public static Cart unbundleSkusUpgrade(Cart keyCart, List < KeyGeneratorCartItem > cartItems, Boolean excludeNewBundle, Boolean excludeUsherBundles, Boolean isEnhancedTranslation, Boolean isStandardToEnh, List < String > enhancedList, Boolean isTranslation) {
        Map < String, List < KeyGeneratorCartItem >> childMap = buildChildMap(cartItems, 'Upgrade', isEnhancedTranslation, isStandardToEnh, keyCart);

        for (KeyGeneratorCartItem sku : cartItems) {
            //check bundles but not report bundles
            // if yes, get child skus
            //for usher bundles don't add anything
            //TODO NEED TO BULKIFY THIS
            List < KeyGeneratorCartItem > bundleItems = childMap.get(sku.masterUniqueID); //TODO BUILD SUPPORT FOR RECURSIVE SEARCH
            //List < KeyGeneratorCartItem > bundleItems = recursiveSearch(childMap,sku.skuid);

            if (bundleItems == null) {
                System.debug('unbundleSkusUpgrade---No SKUs to Add, Add self');
                bundleItems = new List < KeyGeneratorCartItem > ();
                keyCart.listUnbundled.add(sku);
            } else {
                //propagate License
                Integer ind = 0;
                List < Integer > indexesToRemove = new List < Integer > ();

                for (KeyGeneratorCartItem crt : bundleItems) {
                    crt.quantity = sku.quantity;
                    crt.namedUsers = sku.namedUsers;
                    crt.cpuCount = sku.cpuCount;

                    switch on crt.LicenseInheritanceType {
                        when 'Named User Only' { // when block 1
                            crt.License.Type = KeyGeneratorConstants.LICENSETYPE_NAMEDUSER;
                        }
/*
                        when 'If Purchased Only' {
                        if (!cartContainsFeature(cartItems, crt)) {
                        indexesToRemove.add(ind);
                        }
                        }
*/
                        when 'Enhanced Translation Only' {
                            //Enhanced Translation only if checked
                            System.debug('crt.prodName ' + crt.prodName);
                            System.debug(enhancedList);
                            if ((isEnhancedTranslation || isStandardToEnh) && !enhancedList.contains(crt.prodName)) {
                                //remove product if not in enhanced selection
                                System.debug('removed: ' + crt.prodName);
                                indexesToRemove.add(ind);
                            }

                        }
                    }
                    if ((isTranslation && !crt.insertOnTranslation) && !indexesToRemove.contains(ind)) {
                        //remove non 2019 product_Hierarchy__C or originating assets
                        System.debug('removed ' + crt.prodName);
                        indexesToRemove.add(ind);
                    }
                    ind++;
                }

                if (!indexesToRemove.isEmpty()) {
                    List < KeyGeneratorCartItem > helperList = new List < KeyGeneratorCartItem > ();
                    Integer helperIndex = 0;
                    for (KeyGeneratorCartItem item : bundleItems) {
                        if (!indexesToRemove.contains(helperIndex)) {
                            helperList.add(item);
                        }
                        helperIndex++;
                    }
                    bundleItems = helperList;
                }

                for (KeyGeneratorCartItem crt : bundleItems) {
                    if (crt.keepParent) {
                        bundleItems.add(sku);
                        break;
                    }
                }
            }
            //else System.debug('childItems');
            keyCart.listUnbundled.addAll(bundleItems);
        }

        List < KeyGeneratorCartItem > finalList = new List < KeyGeneratorCartItem > ();

        for (KeyGeneratorCartItem crt : keyCart.listUnbundled) {
            switch on crt.LicenseInheritanceType {
                when 'If Purchased Only' {
                    if (cartContainsPurchasedFeature(keyCart.listUnbundled, crt)) {
                        finalList.add(crt);
                    }
                }
                when 'If Purchased Only Match Base Servers' {
                    if (cartContainsPurchasedFeature(keyCart.listUnbundled, crt)) {
                        finalList.add(crt);
                    }
                }
                when else {
                    finalList.add(crt);
                }
            }

        }
        keyCart.listUnbundled = finalList;
        return keyCart;
    }

    public static Boolean cartContainsFeature(List < KeyGeneratorCartItem > cartItems, KeyGeneratorCartItem item) {
        for (KeyGeneratorCartItem sku : cartItems) {
            if (sku.keyFeature == item.keyFeature) {
                return true;
            }
        }
        return false;
    }

    public static Boolean cartContainsPurchasedFeature(List < KeyGeneratorCartItem > cartItems, KeyGeneratorCartItem item) {
        for (KeyGeneratorCartItem sku : cartItems) {
            if (sku.keyFeature == item.keyFeature && sku.LicenseInheritanceType != 'If Purchased Only' && sku.LicenseInheritanceType != 'If Purchased Only Match Base Servers') {
                return true;
            }
        }
        return false;
    }

    public static Map < String, List < KeyGeneratorCartItem >> buildChildMap(List <KeyGeneratorCartItem> cartItems, String unbundleType, Boolean isEnhancedTranslation, Boolean isStandardToEnh, Cart keyCart) {

        List < String > cartItemClean = new List < String > ();
        List < KeyGeneratorCartItem > cartItemMap = new List < KeyGeneratorCartItem > ();
        //build uniqueIDs
        Integer h = (Integer) Math.random() * 100;
        for (KeyGeneratorCartItem sku : cartItems) {
            //sku.uniqueID= Integer.valueof(h).format().replace(',', '');
            sku.masterUniqueID = Integer.valueOf(h).format().replace(',', '');
            System.debug('sku.masterUniqueID ' + sku.masterUniqueID);
            h++;
        }

        for (KeyGeneratorCartItem sku : cartItems) {
            if (sku.isBundle || (unbundleType == 'Upgrade')) {
                //cartItemClean.add(Integer.valueof(sku.SKUID).format().replace(',', ''));
                if (unbundleType == 'Upgrade') {
                    cartItemClean.add(sku.productSFID);
                } else {
                    cartItemClean.add(String.valueOf(sku.SKUID));
                }

                //cartItemMap.put(Integer.valueof(sku.SKUID).format().replace(',', ''),sku);
                cartItemMap.add(sku);
            }
        }

        List < Version_Map__mdt > versionMap = [SELECT Version_ID__c,Label, Map_To_Previous_SKU__c, LockedKey__c, BlockCustomerKey__c, Employee_Key__c FROM Version_Map__mdt WHERE Version_ID__c = :keyCart.VersionID LIMIT 1];
        String version = versionMap.size() > 0 ? (String) versionMap[0].Label : ''; //default to empty

        return buildRecursiveList(new Map < String, List < KeyGeneratorCartItem >> (), cartItemClean, 0, cartItemMap, unbundleType, isEnhancedTranslation, isStandardToEnh, version);

    }
    //recursive method to support 10 levels of recursion
    public static Map < String, List < KeyGeneratorCartItem >> buildRecursiveList(Map < String, List < KeyGeneratorCartItem >> itemMap, List < String > skuIDs, Integer level, List < KeyGeneratorCartItem > cartItemMap, String unbundleType, Boolean isEnhancedTranslation, Boolean isStandardToEnh, String version) {
        if (level < 10) {
            List < Product_Hierarchy__c > currentChildren = DSIAssetHelper.getProductBundles(skuIDs, unbundleType, level, isEnhancedTranslation, isStandardToEnh, version);
            if (currentChildren.size() > 0) {
                //prepare map
                //prepare next iteration
                List < String > nextChildren = new List < String > ();
                List < KeyGeneratorCartItem > nextcartItemMap = new List < KeyGeneratorCartItem > ();

                List < KeyGeneratorCartItem > auxCart = transformHierarchy(currentChildren, cartItemMap);
                for (KeyGeneratorCartItem sku : auxCart) {
                    if (itemMap.containsKey(sku.masterUniqueID)) {
                        List < KeyGeneratorCartItem > tempcart = itemMap.get(sku.masterUniqueID);
                        tempcart.add(sku);
                        itemMap.put(sku.masterUniqueID, tempcart);
                    } else {
                        List < KeyGeneratorCartItem > tempcart = new List < KeyGeneratorCartItem > ();
                        tempcart.add(sku);
                        itemMap.put(sku.masterUniqueID, tempcart);
                    }

                    if (sku.isBundle || (unbundleType == 'Upgrade')) {
                        //nextChildren.add(Integer.valueof(sku.SKUID).format().replace(',', ''));
                        if (unbundleType == 'Upgrade') {
                            if (sku.LicenseInheritanceType != 'First Level Only' && sku.LicenseInheritanceType != 'Do Not Propagate') {
                                nextChildren.add(sku.productSFID);
                            }
                        } else {
                            nextChildren.add(String.valueOf(sku.SKUID));
                        }
                        nextcartItemMap.add(sku);
                    }
                }
                /*
for (Product_Hierarchy__c curr: currentChildren) {
if (curr.Child_Product__r.isBundle__c) {
nextChildren.add(Integer.valueof(curr.Child_Product__r.ProductCode).format().replace(',', ''));
}
}*/
                //call next recursion
                if (nextChildren.size() > 0) {
                    System.debug(level);
                    System.debug(nextChildren);
                    System.debug(nextcartItemMap);

                    buildRecursiveList(itemMap, nextChildren, level + 1, nextcartItemMap, unbundleType, isEnhancedTranslation, isStandardToEnh, version);
                }
            }

        }
        return itemMap;
    }
    /*
public List<KeyGeneratorCartItem> recursiveList(Integer skuID, Map<Integer,List<KeyGeneratorCartItem>> itemMap, List<KeyGeneratorCartItem> cartItems){

}*/

    /*public static List<KeyGeneratorCartItem> recursiveSearch (Map < Integer, List < KeyGeneratorCartItem >> childMap, Integer skuId){
List<KeyGeneratorCartItem> resultList = new List<KeyGeneratorCartItem>();
if(childMap.containsKey(skuID)){
resultList.addAll(childMap.get(skuID));
for(KeyGeneratorCartItem sku : childMap.get(skuID)){
resultList.addAll(recursiveSearch(childMap,sku.skuid));
}
return resultList;
}
else{
return resultList;
}
}*/
    /*
public static List < KeyGeneratorCartItem > transformHierarchy(List < Product_Hierarchy__c > products,Map<String, KeyGeneratorCartItem> cartItemMap) {
system.debug(products);
system.debug(cartItemMap);
List < KeyGeneratorCartItem > newCart = new List < KeyGeneratorCartItem > ();
for (Product_Hierarchy__c prod: products) {
system.debug(prod.Parent_Product__r.ProductCode);
system.debug(cartItemMap.get(prod.Parent_Product__r.ProductCode));
Date ExpirationDate = null;
/*
Integer previousMHz = 0;
Integer quantity = 0;
Integer namedUsers = 0;
*/
    /*
Integer previousMHz = cartItemMap.get(prod.Parent_Product__r.ProductCode).PreviousMHz;
Integer quantity = cartItemMap.get(prod.Parent_Product__r.ProductCode).quantity;
Integer namedUsers = cartItemMap.get(prod.Parent_Product__r.ProductCode).namedUsers;
Integer cpuRestriction = cartItemMap.get(prod.Parent_Product__r.ProductCode).cpuRestriction;
Boolean isChild = true; //true when building children SKUs

KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
Integer.ValueOf(prod.Child_Product__r.ProductCode),
quantity,
namedUsers,
isChild,
previousMHz,
cpuRestriction,
(Integer) prod.Child_Product__r.RMS_Product_ID__c,
(Integer) prod.Child_Product__r.SKU_Category_ID__c,
(Integer) prod.Child_Product__r.Version_ID__c,
(Integer) prod.Child_Product__r.SKU_Type_ID__c, //unratedType = Sky Type ID
(Integer) prod.Child_Product__r.Variation_Type__c,
(Integer) prod.Child_Product__r.Mhz__c,
(Integer) prod.Child_Product__r.NU_Quantity__c,
ExpirationDate,
prod.Child_Product__r.isBundle__c,
(Integer) prod.Child_Product__r.Software_Type_ID__c,
prod.Child_Product__r.clustering__c,
(Integer) prod.Child_Product__r.BaseProductID__c,
(Integer) prod.Child_Product__r.Variation_ID__c,
prod.Id);

tempItem.ParentSKUID = Integer.ValueOf(prod.Parent_Product__r.ProductCode);

//build Parent
ExpirationDate = null;
previousMHz = 0;
quantity = 0;
namedUsers = 0;
isChild = false; //false for parents


KeyGeneratorCartItem tempItem2 = new KeyGeneratorCartItem(
Integer.ValueOf(prod.Parent_Product__r.ProductCode),
quantity,
namedUsers,
isChild,
previousMHz,
(Integer) prod.Parent_Product__r.cpuRestriction__c,
(Integer) prod.Parent_Product__r.RMS_Product_ID__c,
(Integer) prod.Parent_Product__r.SKU_Category_ID__c,
(Integer) prod.Parent_Product__r.Version_ID__c,
(Integer) prod.Parent_Product__r.SKU_Type_ID__c, //unratedType = Sky Type ID
(Integer) prod.Parent_Product__r.Variation_Type__c,
(Integer) prod.Parent_Product__r.Mhz__c,
(Integer) prod.Parent_Product__r.NU_Quantity__c,
ExpirationDate,
prod.Parent_Product__r.isBundle__c,
(Integer) prod.Parent_Product__r.Software_Type_ID__c,
prod.Parent_Product__r.clustering__c,
(Integer) prod.Parent_Product__r.BaseProductID__c,
(Integer) prod.Parent_Product__r.Variation_ID__c,
prod.Id);


if ((tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITE || tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITENOCPULIMIT) && tempItem2.VersionID < KeyGeneratorConstants.VERSIONID_V930)
tempItem.NamedUsers = tempItem2.GetChildNamedUsersForReportingSuite(tempItem) * tempItem2.NamedUsers;
else if ((tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITE || tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITENOCPULIMIT) && tempItem2.VersionID >= KeyGeneratorConstants.VERSIONID_V930)
tempItem.NamedUsers = tempItem2.GetChildNamedUsersForNewReportingSuite(tempItem) * tempItem2.NamedUsers; // this is the functionality for new reporting suite introduced in 9.3
else if (tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_SOFTWARESUITE)
tempItem.NamedUsers = tempItem2.GetChildNamedUsersForMSTRSuite(tempItem) * tempItem2.NamedUsers;

//tempItem.NamedUsers = tempItem2.GetChildNamedUsersForMSTRSuite(tempItem) * tempItem2.NamedUsers;
// below line to multiply with Named user quantity was done based on Brays request.
if (tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_MOBILESUITE)
tempItem.NamedUsers = tempItem2.GetChildNamedUsersForMobileSuite(tempItem) * tempItem2.NamedUsers;

//tempItem.BundleParent = tempItem2;
tempItem.BundleParent = cartItemMap.get(prod.Parent_Product__r.ProductCode);
system.debug(tempItem);
newCart.add(tempItem);

}
return newCart;
}*/

    public static List < KeyGeneratorCartItem > transformHierarchy(List < Product_Hierarchy__c > products, List < KeyGeneratorCartItem > cartItemMap) {
        //build searchable hierarchy Map
        //Start - P
        Integer reduceCount = 0;
        //End - P
        List < KeyGeneratorCartItem > newCart = new List < KeyGeneratorCartItem > ();
        for (KeyGeneratorCartItem sku : cartItemMap) {
            for (Product_Hierarchy__c prod : products) {
                //if(prod.Parent_Product__r.ProductCode==Integer.valueof(sku.SKUID).format().replace(',', '')){
                if (sku.LicenseInheritanceType != 'Do Not Propagate' &&
                    prod.Parent_Product__r.ProductCode == String.valueOf(sku.SKUID)) {

                    Date ExpirationDate = null;
/*
                    Integer previousMHz = 0;
                    Integer quantity = 0;
                    Integer namedUsers = 0;
*/
                    Integer previousMHz = sku.PreviousMHz;
                    Integer quantity = sku.quantity;
                    Integer namedUsers = sku.namedUsers;
                    Integer cpuRestriction = sku.CPURestriction;
                    Boolean isChild = true; //true when building children SKUs
                    //Start - P
                    System.debug('prod ' + prod + ' ' + quantity);
                    System.debug('prod.Child_Product__r ' + prod.Child_Product__r);

                    if (prod != null && prod.Child_Product__r != null) {

                        KeyGeneratorCartItem tempItem = new KeyGeneratorCartItem(
                            Integer.valueOf(prod.Child_Product__r.ProductCode),
                            quantity,
                            namedUsers,
                            isChild,
                            previousMHz,
                            cpuRestriction,
                            (Integer) prod.Child_Product__r.RMS_Product_ID__c,
                            (Integer) prod.Child_Product__r.SKU_Category_ID__c,
                            (Integer) prod.Child_Product__r.Version_ID__c,
                            (Integer) prod.Child_Product__r.SKU_Type_ID__c, //unratedType = Sky Type ID
                            (Integer) prod.Child_Product__r.Variation_Type__c,
                            (Integer) prod.Child_Product__r.Mhz__c,
                            (Integer) prod.Child_Product__r.NU_Quantity__c,
                            ExpirationDate,
                            //prod.Child_Product__r.isBundle__c,
                            (Integer) prod.Child_Product__r.Software_Type_ID__c,
                            prod.Child_Product__r.Clustering__c,
                            (Integer) prod.Child_Product__r.BaseProductID__c,
                            (Integer) prod.Child_Product__r.Variation_ID__c,
                            prod.Id,
                            prod.Child_Product__r.Key_Feature__c,
                            prod.Child_Product__c,
                            sku.exceptions,
                            sku.subsEndDate,
                            Date.today().addYears(1000),
                            prod.Child_Product__r.Name,
                            prod.Child_Product__r.Environment__c,
                            prod.Child_Product__r.Version__c,
                            prod.Child_Product__r.Interval__c,
                            sku.itemIdentifier,
                            sku.keygroups,
                            sku.aggregationComments,
                            new List<Boolean>{
                                prod.Child_Product__r.isBundle__c, prod.Child_Product__r.LegacyCatalog__c
                            }
                        );

                        tempItem.keepParent = prod.Keep_Parent__c;
                        if (sku.LicenseInheritanceType == 'If Purchased Only' || sku.LicenseInheritanceType == 'If Purchased Only Match Base Servers' || sku.LicenseInheritanceType == 'Match Base Feature') {
                            tempItem.LicenseInheritanceType = sku.LicenseInheritanceType;
                        } else {
                            tempItem.LicenseInheritanceType = prod.LicenseInheritanceType__c;
                        }

                        tempItem.insertOnTranslation = prod.insertOnTranslation__c;

                        //Deduction(change) - start
                        tempItem.subscriptionName = sku.subscriptionName;
                        tempItem.subscriptionContractNumber = sku.subscriptionContractNumber;
                        tempItem.subsStartDate = sku.subsStartDate;
                        tempItem.currentSubscriptionId = sku.currentSubscriptionId;
                        //end

                        tempItem.ParentSKUID = Integer.valueOf(prod.Parent_Product__r.ProductCode);
                        tempItem.masterUniqueID = sku.masterUniqueID;
                        //build Parent
                        ExpirationDate = null;
                        previousMHz = 0;
                        quantity = 0;
                        namedUsers = 0;
                        isChild = false; //false for parents


                        /*  KeyGeneratorCartItem tempItem2 = new KeyGeneratorCartItem(
Integer.ValueOf(prod.Parent_Product__r.ProductCode),
quantity,
namedUsers,
isChild,
previousMHz,
(Integer) prod.Parent_Product__r.cpuRestriction__c,
(Integer) prod.Parent_Product__r.RMS_Product_ID__c,
(Integer) prod.Parent_Product__r.SKU_Category_ID__c,
(Integer) prod.Parent_Product__r.Version_ID__c,
(Integer) prod.Parent_Product__r.SKU_Type_ID__c, //unratedType = Sky Type ID
(Integer) prod.Parent_Product__r.Variation_Type__c,
(Integer) prod.Parent_Product__r.Mhz__c,
(Integer) prod.Parent_Product__r.NU_Quantity__c,
ExpirationDate,
prod.Parent_Product__r.isBundle__c,
(Integer) prod.Parent_Product__r.Software_Type_ID__c,
prod.Parent_Product__r.clustering__c,
(Integer) prod.Parent_Product__r.BaseProductID__c,
(Integer) prod.Parent_Product__r.Variation_ID__c,
prod.Id,
prod.Parent_Product__r.Key_Feature__c,
prod.Parent_Product__c,
'',
Date.TODAY().addYears(1000),
Date.TODAY().addYears(1000),
prod.Parent_Product__r.Name,
prod.Parent_Product__r.Environment__c,
prod.Parent_Product__r.Version__c,
prod.Parent_Product__r.Interval__c);


if ((tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITE || tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITENOCPULIMIT) && tempItem2.VersionID < KeyGeneratorConstants.VERSIONID_V930)
tempItem.NamedUsers = tempItem2.GetChildNamedUsersForReportingSuite(tempItem) * tempItem2.NamedUsers;
else if ((tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITE || tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITENOCPULIMIT) && tempItem2.VersionID >= KeyGeneratorConstants.VERSIONID_V930)
tempItem.NamedUsers = tempItem2.GetChildNamedUsersForNewReportingSuite(tempItem) * tempItem2.NamedUsers; // this is the functionality for new reporting suite introduced in 9.3
else if (tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_SOFTWARESUITE)
tempItem.NamedUsers = tempItem2.GetChildNamedUsersForMSTRSuite(tempItem) * tempItem2.NamedUsers;

//tempItem.NamedUsers = tempItem2.GetChildNamedUsersForMSTRSuite(tempItem) * tempItem2.NamedUsers;
// below line to multiply with Named user quantity was done based on Brays request.
if (tempItem2.ProductID == KeyGeneratorConstants.PRODUCTTYPE_MOBILESUITE)
tempItem.NamedUsers = tempItem2.GetChildNamedUsersForMobileSuite(tempItem) * tempItem2.NamedUsers;
*/
                        //tempItem.BundleParent = tempItem2;
                        tempItem.BundleParent = sku;

                        System.debug(tempItem.SKUID + ' ' + tempItem.License + ' ' + tempItem.ProductID + ' ' + tempItem.quantity);
                        newCart.add(tempItem);
                        //End - P
                    }
                }

            }
        }
        return newCart;
    }

    public static void validateBeforeAggregation(Boolean isModular, List < KeyGeneratorCartItem > cartItems) {
        for (KeyGeneratorCartItem item : cartItems) {
            if (item.unratedType == KeyGeneratorConstants.UNRATEDTYPE_ADDITIONAL500) {
                // check for corresponding unrated
                Boolean found = false;
                for (KeyGeneratorCartItem j : cartItems) {
                    if (j.unratedType == KeyGeneratorConstants.UNRATEDTYPE_UNRATED500 && j.type == item.type) {
                        found = true;
                        break;
                    }
                }
                if (!found) throw new KeyGeneratorException(item.toString() + ' is missing its base unrated/500 SKU \n');
            }
        }

        if (!isModular) {
            for (KeyGeneratorCartItem item : cartItems) {
                if (item.clustering && item.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_INTELLIGENCESERVER) {
                    Boolean foundIserver = false;
                    for (KeyGeneratorCartItem j : cartItems) {
                        if (j.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_INTELLIGENCESERVER && !j.clustering) {
                            foundIserver = true;
                            break;
                        }
                    }
                    if (!foundIserver) throw new KeyGeneratorException('Clustering option is present, but the corresponding IServer is not, please add it.');
                }
            }
        }
    }
    private static Boolean containsBase(List < KeyGeneratorCartItem > cartItems, Integer baseProductType) {
        for (KeyGeneratorCartItem item : cartItems)
            if (item.baseProductType == baseProductType) return true;
        return false;
    }

    public static void validateLicenseModel(Cart keyCart) {
        List < Key_Generator_Exception__mdt > exceptionList = [
            SELECT Label, Exception_Entity__c, Exception_Type__c
            FROM Key_Generator_Exception__mdt
            WHERE Type__c = 'Model Requirement'
            AND Inactive__c = FALSE
        ];
        Map < String, Key_Generator_Exception__mdt > exceptionMap = new Map < String, Key_Generator_Exception__mdt > ();

        for (Key_Generator_Exception__mdt kg : exceptionList) {
            exceptionMap.put(kg.Label, kg);
        }

        for (KeyGeneratorCartItem sku : keyCart.ListConsolidated) {
            if (exceptionMap.containsKey(sku.keyFeature)) {
                List < String > featureRequirements = exceptionMap.get(sku.keyFeature).Exception_Entity__c.split(',');
                String workException = exceptionMap.get(sku.keyFeature).Exception_Type__c;
                Integer currSKUQuantity = sku.License.getGenericCount();
                //helpers
                Integer countHelper = 0;
                Boolean hasCPUinConfig = false;
                Boolean minimumConfig = false;
                for (KeyGeneratorCartItem itemCheck : keyCart.ListConsolidated) {

                    if (!featureRequirements.contains(itemCheck.keyFeature)) continue;

                    switch on workException {
                        when 'Named or CPU Count Match' {
                            if (!sku.License.isCPU() && itemCheck.License.isCPU()) {
                                hasCPUinConfig = true;
                            }
                            if (sku.License.Type == itemCheck.License.Type) {
                                countHelper += itemCheck.License.getGenericCount();
                            }
                        }
                        when 'Count & Type Match' {
                            if (sku.License.Type == itemCheck.License.Type) {
                                countHelper += itemCheck.License.getGenericCount();
                            }
                        }
                        when 'Type Match' {
                            if (sku.License.Type != itemCheck.License.Type) {
                                throw new KeyGeneratorException('This Key Group configuration violates the Licensing Model\r\n Feature: ' + sku.keyFeature + ' requires ' + workException + ' of the following Features: ' + featureRequirements + '\r\n');
                            }
                        }
                        when 'At Least One' {
                            minimumConfig = true;
                        }
                        when
                            else {
                            countHelper += itemCheck.License.getGenericCount();
                        }
                    }
                }

                switch on workException {
                    when 'Named or CPU Count Match' {
                        if (sku.License.isNamedUser() && !hasCPUinConfig && !(sku.License.getGenericCount() <= countHelper)) {
                            //Each Named User requires samed Number of Named Users of the required modules
                            throw new KeyGeneratorException('This Key Group configuration violates the Licensing Model\r\n Feature: ' + sku.keyFeature + ' requires ' + workException + ' of the following Features: ' + featureRequirements + '\r\n');
                        }
                        if (sku.License.isCPU() && sku.License.getGenericCount() != countHelper) {
                            //if CPU License, CPU counts must match
                            throw new KeyGeneratorException('This Key Group configuration violates the Licensing Model\r\n Feature: ' + sku.keyFeature + ' requires ' + workException + ' of the following Features: ' + featureRequirements + '\r\n');
                        }
                    }
                    when 'Type Match' {
                        //Do nothing
                    }
                    when 'At Least One' {
                        if (!minimumConfig) {
                            //if there is no match on RequestedKeyFeature
                            throw new KeyGeneratorException('This Key Group configuration violates the Licensing Model\r\n Feature: ' + sku.keyFeature + ' requires ' + workException + ' of the following Features: ' + featureRequirements + '\r\n');
                        }
                    }
                    when
                        else {
                        if (sku.License.getGenericCount() != countHelper) {
                            throw new KeyGeneratorException('This Key Group configuration violates the Licensing Model\r\n Feature: ' + sku.keyFeature + ' requires ' + workException + ' of the following Features: ' + featureRequirements + '\r\n');
                        }
                    }
                }

            }
        }


    }

    public static void ValidateBusinessRules(Cart keyCart, List < KeyGeneratorCartItem > cartItems) {
        // confirm version, types, quantities are valid.
        // in modular
        if (keyCart.isModular) {
            // check that universal bundle options have base products

            /*
for (KeyGeneratorCartItem cartItem: cartItems) {

if (cartItem.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_INTELLIGENCESERVERUNIVERSAL) {
if (!KeyGeneratorController.containsBase(cartItems, KeyGeneratorConstants.BASEPRODUCTTYPE_INTELLIGENCESERVER)) {
throw new KeyGeneratorException(cartItem.type + ' is missing its base product');
}
} else if (cartItem.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_OLAPSERVICESUNIVERSAL) {
if (!KeyGeneratorController.containsBase(cartItems, KeyGeneratorConstants.BASEPRODUCTTYPE_OLAPSERVICES)) {
throw new KeyGeneratorException(cartItem.type + ' is missing its base product');
}
} else if (cartItem.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_REPORTSERVICESUNIVERSAL) {
if (!KeyGeneratorController.containsBase(cartItems, KeyGeneratorConstants.BASEPRODUCTTYPE_REPORTSERVICES)) {
throw new KeyGeneratorException(cartItem.type + ' is missing its base product');
}
} else if (cartItem.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_WEBANALYSTUNIVERSAL) {
if (!KeyGeneratorController.containsBase(cartItems, KeyGeneratorConstants.BASEPRODUCTTYPE_WEBANALYST)) {
throw new KeyGeneratorException(cartItem.type + ' is missing its base product');
}
} else if (cartItem.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_WEBMMTUNIVERSAL) {
if (!KeyGeneratorController.containsBase(cartItems, KeyGeneratorConstants.BASEPRODUCTTYPE_WEBMMT)) {
throw new KeyGeneratorException(cartItem.type + ' is missing its base product');
}
} else if (cartItem.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_WEBPROFESSIONALUNIVERSAL) {
if (!KeyGeneratorController.containsBase(cartItems, KeyGeneratorConstants.BASEPRODUCTTYPE_WEBPROFESSIONAL)) {
throw new KeyGeneratorException(cartItem.type + ' is missing its base product');
}
} else if (cartItem.baseProductType == KeyGeneratorConstants.BASEPRODUCTTYPE_WEBREPORTERUNIVERSAL) {
if (!KeyGeneratorController.containsBase(cartItems, KeyGeneratorConstants.BASEPRODUCTTYPE_WEBREPORTER)) {
throw new KeyGeneratorException(cartItem.type + ' is missing its base product');
}
}
switch not supported in APEX
switch (cartItem.BaseProductType)
{
case BaseProductType.IntelligenceServerUniversal:
if (!this.ContainsBase(BaseProductType.IntelligenceServer)) result = cartItem.Type.ToString() + message;
break;
case BaseProductType.OLAPServicesUniversal:
if (!this.ContainsBase(BaseProductType.OLAPServices)) result = cartItem.Type.ToString() + message;
break;
case BaseProductType.ReportServicesUniversal:
if (!this.ContainsBase(BaseProductType.ReportServices)) result = cartItem.Type.ToString() + message;
break;
case BaseProductType.WebAnalystUniversal:
if (!this.ContainsBase(BaseProductType.WebAnalyst)) result = cartItem.Type.ToString() + message;
break;
case BaseProductType.WebMMTUniversal:
if (!this.ContainsBase(BaseProductType.WebMMT)) result = cartItem.Type.ToString() + message;
break;
case BaseProductType.WebProfessionalUniversal:
if (!this.ContainsBase(BaseProductType.WebProfessional)) result = cartItem.Type.ToString() + message;
break;
case BaseProductType.WebReporterUniversal:
if (!this.ContainsBase(BaseProductType.WebReporter)) result = cartItem.Type.ToString() + message;
break;
// TODO: if bundles are universal, make sure each one has universal option in the list
}
}*/

        }
    }

    public static void validateVersion(Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart, Product_Key_Request__c request) {

        for (List < KeyGeneratorCartItem > shopCarts : dsiShoppingCart.values()) {

            Integer cartItemVersion = !shopCarts.isEmpty() ? getCartItemMinVersion(shopCarts) : -1;

            System.debug(' minVersion ' + cartItemVersion);
            System.debug(' request.Version_ID__c ' + request.Version_ID__c);
            //method only applies to autogenerated keys, non employee keys with assets
            if (request.Manual_Key__c || request.Employee_Key__c || cartItemVersion == -1) return;

            switch on(Integer) request.Version_ID__c {
                when 96, 98, 102, 103 { // Version 9.4.1, 9.5.1, 10.2 and 10.3 requested
                    if (cartItemVersion > request.Version_ID__c) { // is DSI config
                        throw new KeyGeneratorException('DSI Version Configuration: ' + cartItemVersion + ' is not compatible with the Requested version ' + request.Version_List__c + ' (Exception DSI cannot be downgraded)');
                    }
                }
                when 104, 105, 106, 107, 108 { // Version 10.4,10.4.8 and 2019 Requested
                    if (cartItemVersion < 104) {
                        throw new KeyGeneratorException('DSI Version Configuration: ' + cartItemVersion + ' is not compatible with the Requested version ' + request.Version_List__c + ' (Exception DSI lower than v10 cannot request v10 or higher)');
                    }
                }
                when
                    else { // Catch alL
                    if (cartItemVersion < 104) {
                        throw new KeyGeneratorException('DSI Version Configuration: ' + cartItemVersion + ' is not compatible with the Requested version ' + request.Version_List__c + ' (Exception DSI lower than v10)');
                    }
                }
            }

        }
    }


    public static void validateKeyGroups(Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart, Product_Key_Request__c request) {
        if (!request.Manual_Key__c) {
            List < Key_Generator_Exception__mdt > exceptionList = [
                SELECT Label
                FROM Key_Generator_Exception__mdt
                WHERE Type__c = 'Key Group'
                AND Inactive__c = FALSE
            ];
            String blockKeyGroup = '';
            for (Key_Generator_Exception__mdt exc : exceptionList) {
                blockKeyGroup = blockKeyGroup + ';' + exc.Label;
            }

            for (String kgrp : dsiShoppingCart.keySet()) {
                String currExc = kgrp.left(kgrp.indexOf('-'));
                System.debug(currExc);
                if (blockKeyGroup.contains(currExc)) {
                    //create Case
                    //throw exception
                    throw new KeyGeneratorException('This organization is not allowed to generate KeyGeneratorLicense keys automatically for this key group: (Exception A-004)');

                }

            }
        }
    }

    public static Datetime getLatestModDate(Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart) {
        Datetime maxDate = Datetime.now().addYears(-1000);
        for (List < KeyGeneratorCartItem > shopCart : dsiShoppingCart.values()) {
            for (KeyGeneratorCartItem item : shopCart) {
                maxDate = (item.lastModDate != null && item.lastModDate > maxDate) ? item.lastModDate : maxDate;
            }
        }

        return maxDate;
    }

    public static void validateExceptions(Map < String, List < KeyGeneratorCartItem >> dsiShoppingCart, Product_Key_Request__c request) {

        List < Key_Generator_Exception__mdt > exceptionList = [
            SELECT Label, Exception_Type__c
            FROM Key_Generator_Exception__mdt
            WHERE Type__c = 'Exception'
            AND Inactive__c = FALSE
        ];

        String blockExceptions = '';
        String blockNoMX = '';
        String noBlockMX = '';

        for (Key_Generator_Exception__mdt exc : exceptionList) {
            switch on exc.Exception_Type__c {
                when 'Block' {
                    blockExceptions = blockExceptions + ';' + exc.Label;
                }
                when 'Block if no MX' {
                    blockNoMX = blockNoMX + ';' + exc.Label;
                }
                when 'No Block on MX' {
                    noBlockMX = noBlockMX + ';' + exc.Label;
                }
            }
        }

        for (List < KeyGeneratorCartItem > shopCart : dsiShoppingCart.values()) {
            if (shopCart.size() > 0 && !request.Manual_Key__c) {


                for (KeyGeneratorCartItem asst : shopCart) {
                    List < String > currExc = String.isNotBlank(asst.exceptions) ? asst.exceptions.split(';') : new List < String > ();
                    for (String st : currExc) {

                        if (blockNoMX.contains(st) && asst.ExpirationDate == null) {
                            //throw exception
                            throw new KeyGeneratorException('This organization is not allowed to generate KeyGeneratorLicense keys automatically because of an Exception: (Exception A-010)' + st);

                        }
                        if (blockExceptions.contains(st)) {
                            //throw exception
                            throw new KeyGeneratorException('This organization is not allowed to generate KeyGeneratorLicense keys automatically because of an Exception: (Exception A-010)' + st);

                        }
                        //validate expired MX
                        if (asst.ExpirationDate != null && asst.ExpirationDate < Date.today() && !noBlockMX.contains(st)) {
                            throw new KeyGeneratorException('This organization is not allowed to generate KeyGeneratorLicense keys automatically because of an Expired MX: (Exception A-012)');

                        }
                    }

                    if (currExc.isEmpty()) {
                        //validate expired MX
                        if (asst.ExpirationDate != null && asst.ExpirationDate < Date.today()) {
                            throw new KeyGeneratorException('This organization is not allowed to generate KeyGeneratorLicense keys automatically because of an Expired MX: (Exception A-012)');

                        }
                    }

                }
            }
        }

        //create Case


    }

    public static Integer getCartItemMinVersion(List < KeyGeneratorCartItem > shoppingCart) {
        Decimal minVersion = **********; //MAX integer
        for (KeyGeneratorCartItem asst : shoppingCart) {
            minVersion = (asst.VersionID != null && asst.VersionID > 20 && asst.VersionID < minVersion) ? asst.VersionID : minVersion;
        }
        return (Integer) minVersion;
    }


    public static List < KeyGeneratorCartItem > groupSKUs(Boolean isDowngrade, Cart keyCart, Boolean emailView) {

        keyCart.listUnbundled = checkReportingSuiteUpgrade(keyCart.listUnbundled);
        keyCart.listUnbundled = CheckMobileSuiteUpgrade(keyCart.listUnbundled);

        if (keyCart.isModular) {
            //keyCart.calculateDepth(keyCart.listUnbundled);
        }
        keyCart.listUnbundled.sort();
        System.debug(keyCart.listUnbundled);
        List < KeyGeneratorCartItem > newList = new List < KeyGeneratorCartItem > ();

        for (KeyGeneratorCartItem sku : keyCart.listUnbundled) {
            System.debug(sku.SKUID + ' ' + sku.License + ' ' + sku.ProductID);
            if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER && sku.type == KeyGeneratorConstants.PRODUCTTYPE_MOBILEAPP && !keyCart.isDowngrade) {
                sku.namedUsers = 0;
            }

            if (newList.contains(sku)) {
                // combine identical products

                // get reference to existing sku, and increment quantity
                Integer currIndex = newList.indexOf(sku);
                KeyGeneratorCartItem alreadyInList = newList[currIndex];

                // check if both are mhz rated
                if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED && alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED && !keyCart.isDowngrade) {
                    // set to highest of the two mhz
                    // unrestricted exception case
                    if (sku.License.ClockSpeed == 0 && alreadyInList.License.ClockSpeed > 0)
                        alreadyInList.License.ClockSpeed = sku.License.ClockSpeed; else if (sku.License.ClockSpeed > 0 && alreadyInList.License.ClockSpeed == 0)
                        alreadyInList.License.ClockSpeed = alreadyInList.License.ClockSpeed; // do nothing
                    else {
                        if (alreadyInList.License.ClockSpeed < sku.License.ClockSpeed)
                            alreadyInList.License.ClockSpeed = sku.License.ClockSpeed;
                    }
                }

                if (alreadyInList.type == KeyGeneratorConstants.PRODUCTTYPE_WEB || alreadyInList.type == KeyGeneratorConstants.PRODUCTTYPE_WEBUNIVERSAL) {
                    if (sku.MMTNamedUsers != null && alreadyInList.MMTNamedUsers == null)
                        alreadyInList.MMTNamedUsers = 0;
                    if (sku.MMTCount != null && alreadyInList.MMTCount == null)
                        alreadyInList.MMTCount = 0;
                    //changed from switch to IFs
                    if (alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
                        if (sku.MMTCount != null)
                            alreadyInList.MMTCount += sku.MMTCount;
                    } else if (alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER) {
                        if (sku.MMTNamedUsers != null)
                            alreadyInList.MMTNamedUsers += sku.MMTNamedUsers;
                    } else if (alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED) {
                        if (sku.MMTNamedUsers != null)
                            alreadyInList.MMTNamedUsers += sku.MMTNamedUsers;
                        if (sku.MMTCount != null)
                            alreadyInList.MMTCount += sku.MMTCount;
                    }


                    if (alreadyInList.type == KeyGeneratorConstants.PRODUCTTYPE_WEBUNIVERSAL && sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEB)
                        alreadyInList.ProductID = sku.ProductID;
                }

                //#region resolve variation type differences
                // standard & enterprise combine to form standard
                if (alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_ENTERPRISE && sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_STANDARD)
                    alreadyInList.VariationType = KeyGeneratorConstants.VARIATIONTYPE_STANDARD;

                // standard & universal combine to form standard
                if (alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_UNIVERSAL && sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_STANDARD)
                    alreadyInList.VariationType = KeyGeneratorConstants.VARIATIONTYPE_STANDARD;

                // enterprise & universal combine to form enterprise
                if (alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_UNIVERSAL && sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_ENTERPRISE)
                    alreadyInList.VariationType = KeyGeneratorConstants.VARIATIONTYPE_ENTERPRISE;
                //#endregion

                // make we don't loose track of bundle parent (important so we know when to add analytics module)
                if (alreadyInList.BundleParent == null && sku.BundleParent != null)
                    alreadyInList.BundleParent = sku.BundleParent;

                // mhz + nu case = mhz
                if ((sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED && alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER) ||
                    (alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED && sku.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER)) {
                    if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) { // otherwise ignore named user license{
                        alreadyInList.License = new KeyGeneratorLicense(KeyGeneratorConstants.LICENSETYPE_MHZRATED, null, sku.quantity, sku.License.ClockSpeed);
                        //added by Szepeda 8/3 to propagate to quantity
                        alreadyInList.quantity = alreadyInList.License.CpuCount;
                        //alreadyInList.NamedUsers = alreadyInList.License.NamedUsers;
                        alreadyInList.SKUID = emailView ? sku.SKUID : alreadyInList.SKUID;

                    }

                } // nu + un = un
                else if ((sku.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER && alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED) ||
                    (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED && alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER)) {
                    if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER)
                        alreadyInList.namedUsers += sku.namedUsers; // aggregate named users but keep cpu count
                    else {
                        //refactored
                        alreadyInList.License = new KeyGeneratorLicense(KeyGeneratorConstants.LICENSETYPE_UNRATED, alreadyInList.namedUsers + sku.namedUsers, sku.License.CpuCount, null);
                        alreadyInList.quantity = alreadyInList.License.CpuCount;
                        alreadyInList.namedUsers = alreadyInList.License.NamedUsers;
                        alreadyInList.SKUID = emailView ? sku.SKUID : alreadyInList.SKUID;
                    }
                } // mhz + un = error
                else if ((sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED && alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED) ||
                    (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED && alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED)) {
                    if (sku.CPURestriction > 0 || alreadyInList.CPURestriction > 0) // treat NU with CPU restriction as NU
                    {
                        if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
                            alreadyInList.License = new KeyGeneratorLicense(KeyGeneratorConstants.LICENSETYPE_MHZRATED, null, sku.quantity, sku.License.ClockSpeed);


                        } // otherwise ignore named user license

                    } else
                        throw new KeyGeneratorException('You cannot combine MHz-Rated and Unrated products');
                } // non modular: mhz web + mhz web
                else if ((alreadyInList.type == KeyGeneratorConstants.PRODUCTTYPE_WEB || alreadyInList.type == KeyGeneratorConstants.PRODUCTTYPE_WEBUNIVERSAL) &&
                    alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED &&
                    alreadyInList.VariationID != sku.VariationID) {
                    // increment cpu count
                    alreadyInList.quantity += sku.quantity;
                    if ((alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_REPORTER || alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_REPORTERMMT) // reporter + analyst = analyst
                        &&
                        (sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_ANALYST || sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_ANALYSTMMT))
                        alreadyInList.VariationType = sku.VariationType; else if ((alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_REPORTER || alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_REPORTERMMT) // reporter + profesional = prof
                        &&
                        (sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_PROFESSIONAL || sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_PROFESSIONALMMT))
                        alreadyInList.VariationType = sku.VariationType; else if ((alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_ANALYST || alreadyInList.VariationType == KeyGeneratorConstants.VARIATIONTYPE_ANALYSTMMT) // analyst + prof =  prof
                        &&
                        (sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_PROFESSIONAL || sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_PROFESSIONALMMT))
                        alreadyInList.VariationType = sku.VariationType;
                } else {
                    // resolve clustering option case
                    if (sku.clustering) {
                        alreadyInList.clustering = true;
                    } else {

                        if (alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER) {
                            //system.debug(sku);
                            //system.debug(alreadyInList.BundleParent != null && alreadyInList.BundleParent.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED);
                            //system.debug(sku.BundleParent != null && sku.BundleParent.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED);
                            if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_MOBILEAPP || alreadyInList.ProductID == KeyGeneratorConstants.PRODUCTTYPE_MOBILEAPP) {
                                alreadyInList.namedUsers = 0;
                            }
                            //if the parent product is mhz rated then we  should take the named user quantity from mhz rated product
                            else if (alreadyInList.BundleParent != null && alreadyInList.BundleParent.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
                                // in this case don't do anything
                            } else if (sku.BundleParent != null && sku.BundleParent.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) //condition added for making the named users to be unlimited when cpu and named user bundle is included
                            {
                                //system.debug(sku);
                                alreadyInList.namedUsers = sku.namedUsers;
                                alreadyInList.BundleParent = sku.BundleParent;
                            } else {
                                alreadyInList.namedUsers += sku.namedUsers;
                                //system.debug(alreadyInList);
                            }
                        } else if (alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED) {
                            alreadyInList.namedUsers += sku.namedUsers;
                            // handle the additional 500 case:
                            if (sku.unratedType == KeyGeneratorConstants.UNRATEDTYPE_UNRATED500 && alreadyInList.unratedType == KeyGeneratorConstants.UNRATEDTYPE_ADDITIONAL500)
                                alreadyInList.quantity = sku.quantity; else if (!(alreadyInList.unratedType == KeyGeneratorConstants.UNRATEDTYPE_UNRATED500 && sku.unratedType == KeyGeneratorConstants.UNRATEDTYPE_ADDITIONAL500)) {
                                if (alreadyInList.CPURestriction == 0) // cpu restictions do not aggregate, other cases do
                                    alreadyInList.quantity += sku.quantity;
                            }
                        } else if (alreadyInList.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
                            System.debug(alreadyInList.SKUID + ' ' + alreadyInList.License + ' ' + alreadyInList.ProductID + ' ' + alreadyInList.quantity);
                            System.debug(sku.SKUID + ' ' + sku.License + ' ' + sku.ProductID + ' ' + sku.quantity);


                            alreadyInList.quantity += sku.quantity;
                        }
                        // final step, aggregate
                        /*switch not supported.
switch (alreadyInList.licenseType)
{
case KeyGeneratorConstants.LICENSETYPE_NAMEDUSER:
if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_MOBILEAPP || alreadyInList.ProductID == (int)KeyGeneratorConstants.PRODUCTTYPE_MOBILEAPP)
{
alreadyInList.namedUsers = 0;
}
//if the parent product is mhz rated then we  should take the named user quantity from mhz rated product
else if (alreadyInList.BundleParent != null && alreadyInList.BundleParent.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED)
{
// in this case don't do anything
}
else if (sku.BundleParent!=null && sku.BundleParent.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) //condition added for making the named users to be unlimited when cpu and named user bundle is included
{
alreadyInList.NamedUsers = sku.NamedUsers;
alreadyInList.BundleParent = sku.BundleParent;
}
else
{
alreadyInList.NamedUsers += sku.NamedUsers;
}
break;
case KeyGeneratorConstants.LICENSETYPE_UNRATED:
alreadyInList.NamedUsers += sku.NamedUsers;
// handle the additional 500 case:
if (sku.UnratedType == KeyGeneratorConstants.UNRATEDTYPE_UNRATED500 && alreadyInList.UnratedType == KeyGeneratorConstants.UNRATEDTYPE_ADDITIONAL500)
alreadyInList.Quantity = sku.Quantity;
else if (!(alreadyInList.UnratedType == KeyGeneratorConstants.UNRATEDTYPE_UNRATED500 && sku.UnratedType == KeyGeneratorConstants.UNRATEDTYPE_ADDITIONAL500))
{
if (alreadyInList.CPURestriction == 0) // cpu restictions do not aggregate, other cases do
alreadyInList.Quantity += sku.Quantity;
}
break;
case KeyGeneratorConstants.LICENSETYPE_MHZRATED:
alreadyInList.Quantity += sku.Quantity;
break;
}*/
                    }
                }
                //system.debug(alreadyInList.SKUID+' '+alreadyInList.License+' '+alreadyInList.ProductID);
            } else {
                //system.debug(sku);
                newList.add(sku);
            }

        }

        return newList;

    }

    public static List < KeyGeneratorCartItem > groupSKUs11(Boolean isDowngrade, Cart keyCart, Boolean fromEmail) {
        keyCart.listUnbundled.sort();
        System.debug(keyCart.listUnbundled);

        List <KeyGeneratorCartItem> newList = new List <KeyGeneratorCartItem> ();

        for (KeyGeneratorCartItem sku : keyCart.listUnbundled) {
            //convert to fromEmail
            sku.fromEmail = fromEmail;

            if (newList.contains(sku)) {
                // combine identical products
                // get reference to existing sku, and increment quantity
                Integer currIndex = newList.indexOf(sku);
                KeyGeneratorCartItem alreadyInList = newList[currIndex];

                //Make license changes
                switch on alreadyInList.License.Type {
                    when 1 { // Named User
                        //IF in list is NU, propagate to incoming
                        if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER) {
                            alreadyInList.namedUsers += sku.namedUsers;
                        } else if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED) {
                            alreadyInList.namedUsers += sku.namedUsers;
                            alreadyInList.quantity = sku.quantity;
                        } else if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
                            //alreadyInList.NamedUsers += sku.NamedUsers;
                            alreadyInList.quantity = sku.quantity;
                            if (sku.keyFeature == KeyGeneratorConstants.KEYFEATURE_CLDESKTOP) {
                                alreadyInList.namedUsers = 0;
                            }
                        }
                        alreadyInList.License.Type = sku.License.Type;
                    }
                    when 2 {
                        // CPU MHZ Rated
                        if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER) {
                            alreadyInList.namedUsers += sku.namedUsers;
                        } else if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED) {
                            throw new KeyGeneratorException('Cannot mix CPU MHZ Rated and Unrated Products');
                        } else if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
                            //alreadyInList.NamedUsers += sku.NamedUsers;
                            alreadyInList.quantity += sku.quantity;
                        }
                    }
                    when 3 { // CPU Unrated
                        if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_NAMEDUSER) {
                            alreadyInList.namedUsers += sku.namedUsers;
                        } else if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_UNRATED) {
                            alreadyInList.quantity += sku.quantity;
                        } else if (sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
                            throw new KeyGeneratorException('Cannot mix CPU MHZ Rated and Unrated Products');
                        }
                    }
                    when
                        else { // default block, optional
                        // code block 4
                    }
                }
                if (sku.LicenseInheritanceType != null) {
                    System.debug('sku.LicenseInheritanceType' + sku.LicenseInheritanceType);
                    //Propagate inheritance type
                    alreadyInList.LicenseInheritanceType = sku.LicenseInheritanceType;
                }

            } else {

                //2019 HyperIntelligence CPU should default to 0 NU
                if (keyCart.VersionID == 106 && sku.keyFeature == KeyGeneratorConstants.KEYFEATURE_HYPERWEB && sku.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
                    sku.namedUsers = 0;
                    sku.License.Type = KeyGeneratorConstants.LICENSETYPE_NAMEDUSER;
                }

                //system.debug(alreadyInList.SKUID+' '+alreadyInList.License+' '+alreadyInList.ProductID+' '+alreadyInList.quantity);
//              System.debug(sku.SKUID + ' ' + sku.License + ' ' + sku.ProductID + ' ' + sku.quantity);
                newList.add(sku);
            }

        }

        return newList;

    }

    /*  public static CDKeyMod CheckWebMHz(CDKeyMod keyGen) {

if (keyGen.WebReporter != null) {
if (keyGen.WebReporter.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED) {
// find the highest clock speed
Integer highestMHz = keyGen.WebReporter.License.ClockSpeed;

if (keyGen.WebReporter.WebAnalystOption != null)
if (keyGen.WebReporter.WebAnalystOption.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED)
if ((keyGen.WebReporter.WebAnalystOption.License.ClockSpeed > highestMHz && highestMHz != 0) || keyGen.WebReporter.WebAnalystOption.License.ClockSpeed == 0)
highestMHz = keyGen.WebReporter.WebAnalystOption.License.ClockSpeed;

if (keyGen.WebReporter.WebProfessionalOption != null)
if (keyGen.WebReporter.WebProfessionalOption.License.Type == KeyGeneratorConstants.LICENSETYPE_MHZRATED)
if ((keyGen.WebReporter.WebProfessionalOption.License.ClockSpeed > highestMHz && highestMHz != 0) || keyGen.WebReporter.WebProfessionalOption.License.ClockSpeed == 0)
highestMHz = keyGen.WebReporter.WebProfessionalOption.License.ClockSpeed;

// set all 3 to the highest MHz
keyGen.WebReporter.License.ClockSpeed = highestMHz;
if (keyGen.WebReporter.WebAnalystOption != null)
keyGen.WebReporter.WebAnalystOption.License.ClockSpeed = highestMHz;
if (keyGen.WebReporter.WebProfessionalOption != null)
keyGen.WebReporter.WebProfessionalOption.License.ClockSpeed = highestMHz;
}
}
return keyGen;
}
*/
    public static CDKeyMod BuildModular(Cart keyCart, CDKeyMod keyGen) {

        for (KeyGeneratorCartItem sku : keyCart.ListConsolidated) {

            keyGen = CheckAnalyticsModule(sku, keyGen);
            keyGen = CheckReportingSuite(sku, keyGen);
            keyGen = CheckWebBundle(sku, keyGen);
            System.debug(sku);
            if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_INTELLIGENCESERVERSMBMODULE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_INTELLIGENCESERVERMODULE) {

                keyGen.IServer = new IServerModule(null, sku.License, keyCart.IsAuditingDisabled, keyCart.IsDailyReportEnabled, sku.clustering, 0);
            }

        }

        for (KeyGeneratorCartItem sku : keyCart.ListConsolidated) {

            keyGen = CheckAnalyticsModule(sku, keyGen);
            keyGen = CheckReportingSuite(sku, keyGen);
            keyGen = CheckWebBundle(sku, keyGen);

            if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_ADMINISTRATORSMB ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_ADMINISTRATOR) {
                keyGen.Administrator = new AdministratorMod(false, null, sku.namedUsers, sku.ManagedUsers);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_COMMANDMANAGER ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_COMMANDMANAGERSMB) {
                if (sku.VariationType == KeyGeneratorConstants.VARIATIONTYPE_OEMRUNTIME)
                    keyGen.CommandManagerLight = true; else
                    keyGen.CommandManager = new CommandManager(null, keyGen.IServer != null && keyGen.IServer.License.isCPU() ? 0 : sku.namedUsers, sku.ManagedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_ENTERPRISEMANAGER ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_ENTERPRISEMANAGERSMB) {
                keyGen.EnterpriseManager = new EnterpriseManager(null, keyGen.IServer != null && keyGen.IServer.License.isCPU() ? 0 : sku.namedUsers, sku.ManagedUsers);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_OBJECTMANAGER ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_OBJECTMANAGERSMB) {
                keyGen.ObjectManager = new ObjectManager(null, sku.namedUsers, sku.ManagedUsers);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_OFFICESMB ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_OFFICE) {
                KeyGeneratorLicense tempLicense = sku.License;

                sku.License.NamedUsers = keyGen.IServer != null && keyGen.IServer.License.isCPU() ? 0 : sku.License.NamedUsers;
                keyGen.Office = new Office(tempLicense);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_DESKTOP) {
                keyGen.Desktop = new DesktopMod(null);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBREPORTERSMBMODULE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBREPORTERMODULE) {
                keyGen.WebReporter = new WebReporterModule(sku.License, null);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_SDKSMB ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_SDK) {
                keyGen.SDK = true;
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_INTEGRITYMANAGER ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_INTEGRITYMANAGERSMB) {
                keyGen.IntegrityManager = new IntegrityManager(null, sku.namedUsers, sku.ManagedUsers, 0);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_MOBILE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_MOBILESMB ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_MOBILEAPP) {
                keyGen.Mobile = new Mobile(null, sku.License);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_SYSTEMMANAGER ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_SYSTEMMANAGERSMB) {
                keyGen.SystemManager = new SystemManager(null, keyGen.IServer != null && keyGen.IServer.License.isCPU() ? 0 : sku.namedUsers, sku.ManagedUsers);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_USHERSECURITY) {
                keyGen.UsherSecurity = new UsherSecurity(null, sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_USHERSECURITYFORANALYTICS) {
                keyGen.UsherSecurityForAnalytics = new UsherSecurityForAnalytics(null, sku.namedUsers);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_ONETIERDESKTOP) {
                keyGen.OneTierDeskTop = new OneTierDeskTop(sku.namedUsers);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_USHERSECURITYSERVER) {
                keyGen.UsherSecurityServer = new UsherSecurityServer(null, sku.namedUsers);

            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_USHERSECURITYSERVERTERM) {
                keyGen.UsherSecurityServer = new UsherSecurityServer(null, sku.namedUsers);

            }

        }

        for (KeyGeneratorCartItem sku : keyCart.ListConsolidated) {


            if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_INTELLIGENCESERVERSMBUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_INTELLIGENCESERVERUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_REPORTSERVICESUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_REPORTSERVICESSMBUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_OLAPSERVICESUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_OLAPSERVICESSMBUNIVERSALOPTION
                ) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add ' + sku.type);
                keyGen.IServer.IsUniversal = true;
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_CLUSTERINGOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_CLUSTERINGSMBOPTION) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add  ' + sku.type);
                keyGen.IServer.Clustering = true;
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_MULTISOURCEOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_MULTISOURCEOPTIONSMB) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add  ' + sku.type);

                keyGen.IServer.MultiSource = new MultisourceOption(keyGen.IServer.License.isCPU() ? 0 : sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_CLUSTERINGUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_CLUSTERINGSMBUNIVERSALOPTION) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add  ' + sku.type);
                keyGen.IServer.Clustering = true;
                keyGen.IServer.IsUniversal = true;
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_TRANSACTIONSERVICESOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_TRANSACTIONSERVICESOPTIONSMB) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add  ' + sku.type);
                keyGen.IServer.TransactionServicesOption = new IServerOption(sku.License);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_NARROWCASTSERVERSMBMODULE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_NARROWCASTSERVERMODULE) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add  ' + sku.type);
                keyGen.IServer.NarrowcastOption = new IServerOption(sku.License);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_REPORTSERVICESSMBOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_REPORTSERVICESOPTION) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add  ' + sku.type);
                keyGen.IServer.ReportServicesOption = new IServerOption(sku.License);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_OLAPSERVICESSMBOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_OLAPSERVICESOPTION) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add  ' + sku.type);
                keyGen.IServer.OlapServicesOption = new IServerOption(sku.License);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_DISTRIBUTIONSERVICESOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_DISTRIBUTIONSERVICESOPTIONSMB) {
                if (keyGen.IServer == null) throw new KeyGeneratorException('IServer is missing, cannot add  ' + sku.type);
                keyGen.IServer.DistributionServicesOption = new IServerOption(sku.License);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_DESKTOPANALYSTSMBMODULE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_DESKTOPANALYSTMODULE) {
                if (keyGen.Desktop == null) keyGen.Desktop = new DesktopMod(null);
                keyGen.Desktop.DesktopAnalyst = new DesktopProduct(sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_DESKTOPDESIGNERSMBOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_DESKTOPDESIGNEROPTION) {
                if (keyGen.Desktop == null) keyGen.Desktop = new DesktopMod(null);
                keyGen.Desktop.DesignerOption = new DesktopProduct(sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_DESKTOPDESIGNER ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_DESKTOPDESIGNER94 ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_DEVELOPER ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_DEVELOPERSMB ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_DESKTOPDESIGNERSMB) {
                if (keyGen.Desktop == null) keyGen.Desktop = new DesktopMod(null);
                keyGen.Desktop.DesktopAnalyst = new DesktopProduct(sku.namedUsers);
                keyGen.Desktop.DesignerOption = new DesktopProduct(sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_ARCHITECTSMB ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_ARCHITECT) {
                if (keyGen.Desktop == null) keyGen.Desktop = new DesktopMod(null);
                keyGen.Desktop.DesktopArchitect = new DesktopProduct(sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBREPORTERUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBREPORTERSMBUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBPROFESSIONALUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBPROFESSIONALSMBUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBANALYSTUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBANALYSTSMBUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBMMTUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBMMTSMBUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBUNIVERSALOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBUNIVERSALOPTIONSMB) {
                if (keyGen.WebReporter == null) throw new KeyGeneratorException('Web Reporter is missing, cannot add ' + sku.type);
                keyGen.WebReporter.UniversalOptions++;
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBANALYSTSMBOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBANALYSTOPTION) {
                if (keyGen.WebReporter == null) throw new KeyGeneratorException('Web Reporter is missing, cannot add ' + sku.type);
                keyGen.WebReporter.WebAnalystOption = new WebProduct(sku.License);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBPROFESSIONALSMBOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBPROFESSIONALOPTION) {
                if (keyGen.WebReporter == null) throw new KeyGeneratorException('Web Reporter is missing, cannot add ' + sku.type);
                keyGen.WebReporter.WebProfessionalOption = new WebProduct(sku.License);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBMMTSMBOPTION ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBMMTOPTION) {
                if (keyGen.WebReporter == null) throw new KeyGeneratorException('Web Reporter is missing, cannot add ' + sku.type);
                keyGen.WebReporter.MMTOption = new eTrainer(sku.License);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_EMAILCLIENTREPORTBUNDLE) {
                if (keyGen.ReportBundles == null) keyGen.ReportBundles = new ReportBundles(keyCart.IsDailyReportEnabled, keyCart.IsAuditingDisabled);

                keyGen.ReportBundles.AddBundle(KeyGeneratorConstants.REPORTERBUNDLETYPE_EMAILCLIENT, sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_EMAILREPORTCLIENTREPORTBUNDLE) {
                if (keyGen.ReportBundles == null) keyGen.ReportBundles = new ReportBundles(keyCart.IsDailyReportEnabled, keyCart.IsAuditingDisabled);
                keyGen.ReportBundles.AddBundle(KeyGeneratorConstants.REPORTERBUNDLETYPE_EMAILREPORTCLIENT, sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBCLIENTREPORTBUNDLE) {
                if (keyGen.ReportBundles == null) keyGen.ReportBundles = new ReportBundles(keyCart.IsDailyReportEnabled, keyCart.IsAuditingDisabled);
                keyGen.ReportBundles.AddBundle(KeyGeneratorConstants.REPORTERBUNDLETYPE_WEBCLIENT, sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBEMAILCLIENTREPORTBUNDLE) {
                if (keyGen.ReportBundles == null) keyGen.ReportBundles = new ReportBundles(keyCart.IsDailyReportEnabled, keyCart.IsAuditingDisabled);
                keyGen.ReportBundles.AddBundle(KeyGeneratorConstants.REPORTERBUNDLETYPE_WEBEMAILCLIENT, sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBEMAILREPORTCLIENTREPORTBUNDLE) {
                if (keyGen.ReportBundles == null) keyGen.ReportBundles = new ReportBundles(keyCart.IsDailyReportEnabled, keyCart.IsAuditingDisabled);
                keyGen.ReportBundles.AddBundle(KeyGeneratorConstants.REPORTERBUNDLETYPE_WEBEMAILREPORTCLIENT, sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBREPORTCLIENTREPORTBUNDLE) {
                if (keyGen.ReportBundles == null) keyGen.ReportBundles = new ReportBundles(keyCart.IsDailyReportEnabled, keyCart.IsAuditingDisabled);
                keyGen.ReportBundles.AddBundle(KeyGeneratorConstants.REPORTERBUNDLETYPE_WEBREPORTCLIENT, sku.namedUsers);
            } else if (sku.type == KeyGeneratorConstants.PRODUCTTYPE_EMAILCLIENTUNIVERSALOPTIONREPORTBUNDLE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_EMAILREPORTCLIENTUNIVERSALOPTIONREPORTBUNDLE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBCLIENTUNIVERSALOPTIONREPORTBUNDLE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBEMAILCLIENTUNIVERSALOPTIONREPORTBUNDLE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBEMAILREPORTCLIENTUNIVERSALOPTIONREPORTBUNDLE ||
                sku.type == KeyGeneratorConstants.PRODUCTTYPE_WEBREPORTCLIENTUNIVERSALOPTIONREPORTBUNDLE) {
                if (keyGen.ReportBundles == null) keyGen.ReportBundles = new ReportBundles(keyCart.IsDailyReportEnabled, keyCart.IsAuditingDisabled);
                keyGen.ReportBundles.IsUniversal = true;
            }

        }

        return keyGen;

    }

    public static CDKeyMod11 BuildModular11_1(Cart keyCart, CDKeyMod11 keyGen) {

        List < Key_Generator_Exception__mdt > exceptionList = [
            SELECT Label, Exception_Entity__c
            FROM Key_Generator_Exception__mdt
            WHERE Type__c = 'Module Requirement'
            AND Inactive__c = FALSE
        ];
        Map < String, String > exceptionMap = new Map < String, String > ();

        for (Key_Generator_Exception__mdt kg : exceptionList) {
            exceptionMap.put(kg.Label, kg.Exception_Entity__c);
        }

        //generate base modules First

        for (KeyGeneratorCartItem sku : keyCart.ListConsolidated) {
            if (KeyGeneratorConstants.baseModuleList.contains(sku.keyFeature)) {
                BaseModule tempInstance = new BaseModule(null, sku.License);
                keyGen.putBase(sku.keyFeature, tempInstance);
            }
        }

        for (KeyGeneratorCartItem sku : keyCart.ListConsolidated) {
            if (exceptionMap.containsKey(sku.keyFeature)) {
                //check dependencies
                if (!keyGen.bm.containsKey(exceptionMap.get(sku.keyFeature))) {
                    throw new KeyGeneratorException(exceptionMap.get(sku.keyFeature) + ' is missing, cannot add ' + sku.type);
                }
                keyModule tempInstance = new keyModule(null, sku.License);
                keyGen.bm.get(exceptionMap.get(sku.keyFeature)).put(sku.keyFeature, tempInstance);
            } else {
                keyModule tempInstance = new keyModule(null, sku.License);
                keyGen.put(sku.keyFeature, tempInstance);
            }
        }
        return keyGen;
    }


    public static CDKeyMod CheckWebBundle(KeyGeneratorCartItem item, CDKeyMod keyGen) {
        if (item.BundleParent != null) {
            if (item.BundleParent.ProductID == KeyGeneratorConstants.PRODUCTTYPE_MICROSTRATEGYWEBBUNDLE) {
                keyGen.IsWebUniversal = true;
            }
        }

        return keyGen;

    }
    public static CDKeyMod CheckReportingSuite(KeyGeneratorCartItem item, CDKeyMod keyGen) {
        if (item.BundleParent != null) {
            if (item.BundleParent.ProductID == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITE) {
                keyGen.ReportingSuite = true;
            }
        }

        return keyGen;
    }
    public static CDKeyMod CheckAnalyticsModule(KeyGeneratorCartItem item, CDKeyMod keyGen) {

        if (item.BundleParent != null) {
            if (item.BundleParentType == KeyGeneratorConstants.PRODUCTTYPE_BIARCHITECTMOD ||
                item.BundleParentType == KeyGeneratorConstants.PRODUCTTYPE_BIARCHITECTNONMOD ||
                item.BundleParentType == KeyGeneratorConstants.PRODUCTTYPE_BIDEVELOPERKIT ||
                item.BundleParentType == KeyGeneratorConstants.PRODUCTTYPE_BIDEVELOPERKITMODULE ||
                item.BundleParentType == KeyGeneratorConstants.PRODUCTTYPE_BIDEVELOPERKITSMBMODULE ||
                item.BundleParentType == KeyGeneratorConstants.PRODUCTTYPE_STARTERKITDEV) {
                if (keyGen.AnalyticsModule == null)
                    keyGen.AnalyticsModule = new AnalyticsModule(false);
            }
            /*
switch (item.BundleParent.Type)
{
case ProductType.BIArchitectMod:
case ProductType.BIArchitectNonMod:
case ProductType.BIDeveloperKit:
case ProductType.BIDeveloperKitModule:
case ProductType.BIDeveloperKitSMBModule:
case ProductType.StarterKitDev:
if (keyGen.AnalyticsModule == null)
keyGen.AnalyticsModule = new AnalyticsModule(false);
break;
}*/
        }

        // include analytics with architect!
        if (item.type == KeyGeneratorConstants.PRODUCTTYPE_ARCHITECT || item.type == KeyGeneratorConstants.PRODUCTTYPE_ARCHITECTSMB)
            if (keyGen.AnalyticsModule == null)
                keyGen.AnalyticsModule = new AnalyticsModule(false);

        return keyGen;
    }

    public static List < KeyGeneratorCartItem > checkReportingSuiteUpgrade(List < KeyGeneratorCartItem > cartItems) {
        Boolean containsUpgradeUnlimited = false;
        Boolean containsUpgradeSMB = false;

        for (KeyGeneratorCartItem item : cartItems) {
            if (item.type == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITEUPGRADEAUNOCPULIMIT) {
                containsUpgradeUnlimited = true;
            }
            if (item.type == KeyGeneratorConstants.PRODUCTTYPE_REPORTINGSUITEUPGRADESMB) {
                containsUpgradeSMB = true;
            }
        }

        for (KeyGeneratorCartItem item : cartItems) {
            if (containsUpgradeSMB) {
                if (item.CPURestriction > 0)
                    item.quantity = 4;
            }
            if (containsUpgradeUnlimited) {
                if (item.CPURestriction > 0)
                    item.quantity = 0;
            }

        }

        return cartItems;
    }

    public static List < KeyGeneratorCartItem > CheckMobileSuiteUpgrade(List < KeyGeneratorCartItem > cartItems) {
        Boolean containsUpgradeUnlimited = false;
        Boolean containsUpgradeSMB = false;

        for (KeyGeneratorCartItem item : cartItems) {
            if (item.type == KeyGeneratorConstants.PRODUCTTYPE_MOBILESUITEUPGRADENOCPULIMIT) {
                containsUpgradeUnlimited = true;
            }
            if (item.type == KeyGeneratorConstants.PRODUCTTYPE_MOBILESUITEUPGRADESMB) {
                containsUpgradeSMB = true;
            }
        }

        for (KeyGeneratorCartItem item : cartItems) {
            if (containsUpgradeSMB) {
                if (item.CPURestriction > 0)
                    item.quantity = 4;
            }
            if (containsUpgradeUnlimited) {
                if (item.CPURestriction > 0)
                    item.quantity = 0;
            }

        }
        return cartItems;
    }

    private static void setCatsId(Product_Key_Request__c request) {
        String accountId = request.Account__c;
        try {
            //if(accountId != null && request.Cats_Id__c != 1)
            if (accountId != null) {
                List<Opportunity> oppList = [SELECT Id, SBCF_QuoteNumber__c FROM Opportunity WHERE AccountId = :accountId AND StageName = 'S6 - Closed Won' ORDER BY CloseDate DESC LIMIT 1];
                if (oppList.size() > 0 && oppList[0].SBCF_QuoteNumber__c != null) {
                    System.debug('oppList[0].SBCF_QuoteNumber__c ' + oppList[0].SBCF_QuoteNumber__c);
                    request.Cats_Id__c = Decimal.valueOf(oppList[0].SBCF_QuoteNumber__c);
                } else {
                    //no s6 opp so set catsid to 1
                    request.Cats_Id__c = 1;
                }
            } else {
                //unable to query for opp so set as 1
                request.Cats_Id__c = 1;
            }
        } catch (Exception e) {
            //something went wrong set CatsId as 1
            request.Cats_Id__c = 1;
        }
    }
    public class Cart {
        public Boolean isModular = false;
        public Boolean isUniversalKey = false;
        public Boolean isDowngrade = false;
        public Boolean IsAuditingDisabled = false;
        public Boolean IsDailyReportEnabled = true;
        public Integer UsherMobileNamedUsers = 0;
        public Integer UsherAnalyticsNamedusers = 0;
        public Boolean hasUsherMobile = false;
        public Boolean hasUsherAnalytics = false;
        public Integer UsherProfessionalNamedUsers = 0;
        public Boolean hasUsherProfessional = false;
        public Integer CurrentVersionID {
            get;
            set;
        }
        public Integer VersionID {
            get;
            set;
        }
        public Integer DSI {
            get;
            set;
        }
        @AuraEnabled
        public Integer KeyGroupID {
            get;
            set;
        }
        @AuraEnabled
        public String keyGroupDesc {
            get;
            set;
        }

        @AuraEnabled
        public Integer LKG {
            get;
            set;
        }
        public Boolean isAWS = false;
        public Boolean DisableRegistration = false;
        public Boolean CloudBI = false;
        public Integer CATSId {
            get;
            set;
        }
        public Boolean BypassCPUChecking = false;

        public Boolean IsActivationDisabled {
            get;
            set;
        }
        public Boolean IsBypassCPUEnabled {
            get;
            set;
        }
        public Datetime ExpirationDate = Date.newInstance(1700, 1, 1);
        public Boolean IsCloudBIKey {
            get;
            set;
        }

        public Boolean IsVCPUs {
            get;
            set;
        }
        public Integer Platform {
            get;
            set;
        }
        public List < KeyGeneratorCartItem > dsiAssetlist {
            get;
            set;
        }
        public List < KeyGeneratorCartItem > listToShip {
            get;
            set;
        }

        public List < KeyGeneratorCartItem > listUnbundled {
            get;
            set;
        }
        @AuraEnabled
        public List < KeyGeneratorCartItem > ListConsolidated {
            get;
            set;
        }
        //Start - P Translate
        public Cart() {

        }
        //End - P Translate
        public Cart(Product_Key_Request__c request, Integer LKG) {
            this.LKG = LKG;
            this.isAWS = request.isAWS__c;
            this.ExpirationDate = request.Expiration_Date__c;
            if (request.DSI__c != null) {
                this.DSI = request.DSI__r.DSI_ID__c != null ? Integer.valueOf(request.DSI__r.DSI_ID__c) : 1; //Integer.ValueOf(request.DSI__r.DSIID__c) ;
            } else {
                this.DSI = 1;
            }
            //this.DSI = request.DSI__r.DSI_ID__c!=null ? Integer.ValueOf(request.DSI__r.DSI_ID__c): Integer.ValueOf(request.DSI__r.DSIID__c) ;
            this.DisableRegistration = request.Disable_Registration__c;
            this.CloudBI = request.CloudBI__c;
            this.CATSId = (Integer) request.Cats_Id__c;
            this.BypassCPUChecking = request.Bypass_CPU_Checking__c;
            this.isUniversalKey = request.isUniversal__c;
            listUnbundled = new List < KeyGeneratorCartItem > ();
            ListConsolidated = new List < KeyGeneratorCartItem > ();
            dsiAssetlist = new List < KeyGeneratorCartItem > ();
            listToShip = new List < KeyGeneratorCartItem > ();
            this.IsVCPUs = request.DSI__r.Elastic_Cloud_Console__c;
            this.VersionID = (Integer) request.Version_ID__c;

            this.Platform = KeyGeneratorConstants.keyOSMap.containsKey(request.DSI__r.Operating_System__c) ? KeyGeneratorConstants.keyOSMap.get(request.DSI__r.Operating_System__c) : 0;

            //this.employee=request.Employee_Key__c;

        }
    }

    public class CDKeyRequest {

        public List < CDKeyMod > LicenseClassInfo {
            get;
            set;
        }

        public List < CDKeyMod11 > LicenseClassInfo11 {
            get;
            set;
        }

        public Integer ReleaseVersion {
            get;
            set;
        }
        public Boolean employee {
            get;
            set;
        }
        public Integer employeeID {
            get;
            set;
        }
        public Boolean IsUniversal {
            get;
            set;
        }


    }

    public class KeyPOC {
        public Datetime ExpirationDate {
            get;
            set;
        }

    }

    public class GeospatialRequest {
        public Integer orgId {
            get;
            set;
        }
        public Integer dsiId {
            get;
            set;
        }
        public String orgName {
            get;
            set;
        }
        public Integer userId {
            get;
            set;
        }

        public String EncryptedKey {
            get;
            set;
        }
        public Boolean isEmployee {
            get;
            set;
        }

    }

    public class CDKeyMod {

        public Datetime ExpirationDate {
            get;
            set;
        }

        public KeyPOC EnablePOC2 {
            get;
            set;
        }

        public Integer employeeID {
            get;
            set;
        }
        public Boolean IsUniversal {
            get;
            set;
        }
        public Boolean EnablePOC {
            get;
            set;
        }
        public Date KDate {
            get;
            set;
        }
        public String KstDate {
            get;
            set;
        }

        public String kGrpKey {
            get;
            set;
        }

        public Integer CATSId {
            get;
            set;
        }
        public Integer DSI {
            get;
            set;
        }
        public Integer LKG {
            get;
            set;
        }
        public Boolean PreRelease {
            get;
            set;
        }
        public Boolean DisableRegistration {
            get;
            set;
        }
        public Boolean BypassCPUChecking {
            get;
            set;
        }
        public Boolean CloudBI {
            get;
            set;
        }
        public Boolean IsAWS {
            get;
            set;
        }
        public Boolean SDK {
            get;
            set;
        }
        public Boolean ReportingSuite = false;
        public Boolean IsInternal = false;
        public Boolean CloudExpress = false;
        public Boolean IsMSTROne = false;
        public String encryptKey {
            get;
            set;
        }
        public Boolean IsWebUniversal = true; //fix web boundle
        public Boolean CommandManagerLight = false;
        public CommandManager CommandManager {
            get;
            set;
        }
        public EnterpriseManager EnterpriseManager {
            get;
            set;
        }
        public ObjectManager ObjectManager {
            get;
            set;
        }
        public Office Office {
            get;
            set;
        }
        public DesktopMod Desktop {
            get;
            set;
        }
        public Mobile Mobile {
            get;
            set;
        }
        public IServerModule IServer {
            get;
            set;
        }
        public WebReporterModule WebReporter {
            get;
            set;
        }
        public AdministratorMod Administrator {
            get;
            set;
        }
        public IntegrityManager IntegrityManager {
            get;
            set;
        }
        public ReportBundles ReportBundles {
            get;
            set;
        }
        public SystemManager SystemManager {
            get;
            set;
        }
        public UsherSecurity UsherSecurity {
            get;
            set;
        }
        public UsherSecurityForAnalytics UsherSecurityForAnalytics {
            get;
            set;
        }
        public OneTierDeskTop OneTierDeskTop {
            get;
            set;
        }
        public UsherSecurityServer UsherSecurityServer {
            get;
            set;
        }

        public Integer UsherAnalyticsNamedUsers = 0;
        public Integer UsherMobileNamedUsers = 0;
        public Integer UsherProfessionalNamedUsers = 0;
        public Boolean HasUsherMobile = false;
        public Boolean HasUsherAnalytics = false;
        public Boolean HasUsherProfessional = false;

        public AnalyticsModule AnalyticsModule {
            get;
            set;
        }

    }

    public class CDKeyMod11 {

        public transient Map < String, keyModule > m = new Map < String, keyModule > ();

        public transient Map < String, BaseModule > bm = new Map < String, BaseModule > ();

        public Datetime ExpirationDate {
            get;
            set;
        }
        public Integer employeeID {
            get;
            set;
        }
        public Boolean IsUniversal {
            get;
            set;
        }
        public KeyPOC EnablePOC {
            get;
            set;
        }
        public Date KDate {
            get;
            set;
        }
        public String KstDate {
            get;
            set;
        }

        public String kGrpKey {
            get;
            set;
        }

        public Integer CATSId {
            get;
            set;
        }
        public Integer DSI {
            get;
            set;
        }
        public Integer LKG {
            get;
            set;
        }
        public Boolean PreRelease {
            get;
            set;
        }
        public Boolean DisableRegistration {
            get;
            set;
        }

        public Boolean ByPassAudit {
            get;
            set;
        }
        public Boolean IsVCPUs {
            get;
            set;
        }
        public Integer Platform {
            get;
            set;
        }

        public Boolean CloudBI {
            get;
            set;
        }
        public Boolean BypassCPUChecking {
            get;
            set;
        }
        public Boolean EnableDailyReport {
            get;
            set;
        }
        public Boolean KeylevelEval {
            get;
            set;
        }

        public Boolean IsAWS {
            get;
            set;
        }
        public Boolean SDK {
            get;
            set;
        }
        public Boolean IsInternal = false;

        public String encryptKey {
            get;
            set;
        }

        public void put(String key, keyModule value) {
            m.put(key, value);
        }

        public void putBase(String key, BaseModule value) {
            bm.put(key, value);
        }

        public BaseModule IServer {
            get {
                return bm.get('IServer');
            }
            set {
                putBase('IServer', value);
            }
        }
        public BaseModule IdBaseServer {
            get {
                return bm.get('IdBaseServer');
            }
            set {
                putBase('IdBaseServer', value);
            }
        }
        public keyModule ReportServer {
            get {
                return m.get('ReportServer');
            }
            set {
                put('ReportServer', value);
            }
        }
        public keyModule Product02 {
            get {
                return m.get('Product02');
            }
            set {
                put('Product02', value);
            }
        }
        public keyModule Product03 {
            get {
                return m.get('Product03');
            }
            set {
                put('Product03', value);
            }
        }
        public keyModule Product04 {
            get {
                return m.get('Product04');
            }
            set {
                put('Product04', value);
            }
        }
        public keyModule ServerDistribution {
            get {
                return m.get('ServerDistribution');
            }
            set {
                put('ServerDistribution', value);
            }
        }
        public keyModule ServerCollaboration {
            get {
                return m.get('ServerCollaboration');
            }
            set {
                put('ServerCollaboration', value);
            }
        }
        public keyModule ServerTransaction {
            get {
                return m.get('ServerTransaction');
            }
            set {
                put('ServerTransaction', value);
            }
        }
        public keyModule ServerGeospatial {
            get {
                return m.get('ServerGeospatial');
            }
            set {
                put('ServerGeospatial', value);
            }
        }
        public keyModule ServerHyperIntelligence {
            get {
                return m.get('ServerHyperIntelligence');
            }
            set {
                put('ServerHyperIntelligence', value);
            }
        }
        public keyModule ClReporterWeb {
            get {
                return m.get('ClReporterWeb');
            }
            set {
                put('ClReporterWeb', value);
            }
        }
        public keyModule ClApi {
            get {
                return m.get('ClApi');
            }
            set {
                put('ClApi', value);
            }
        }
        public keyModule ClMobile {
            get {
                return m.get('ClMobile');
            }
            set {
                put('ClMobile', value);
            }
        }
        public keyModule ClDesktop {
            get {
                return m.get('ClDesktop');
            }
            set {
                put('ClDesktop', value);
            }
        }
        public keyModule HyperWeb {
            get {
                return m.get('HyperWeb');
            }
            set {
                put('HyperWeb', value);
            }
        }
        public keyModule HyperMobile {
            get {
                return m.get('HyperMobile');
            }
            set {
                put('HyperMobile', value);
            }
        }
        public keyModule HyperOffice {
            get {
                return m.get('HyperOffice');
            }
            set {
                put('HyperOffice', value);
            }
        }
        public keyModule HyperAPI {
            get {
                return m.get('HyperAPI');
            }
            set {
                put('HyperAPI', value);
            }
        }
        public keyModule HyperVoice {
            get {
                return m.get('HyperVoice');
            }
            set {
                put('HyperVoice', value);
            }
        }
        public keyModule ApplicationTableau {
            get {
                return m.get('ApplicationTableau');
            }
            set {
                put('ApplicationTableau', value);
            }
        }
        public keyModule ApplicationJupyter {
            get {
                return m.get('ApplicationJupyter');
            }
            set {
                put('ApplicationJupyter', value);
            }
        }
        public keyModule ApplicationRStudio {
            get {
                return m.get('ApplicationRStudio');
            }
            set {
                put('ApplicationRStudio', value);
            }
        }
        public keyModule ApplicationPowerBI {
            get {
                return m.get('ApplicationPowerBI');
            }
            set {
                put('ApplicationPowerBI', value);
            }
        }
        public keyModule ApplicationQlik {
            get {
                return m.get('ApplicationQlik');
            }
            set {
                put('ApplicationQlik', value);
            }
        }
        public keyModule ApplicationDataScience {
            get {
                return m.get('ApplicationDataScience');
            }
            set {
                put('ApplicationDataScience', value);
            }
        }
        public keyModule ApplicationMicrosoftOffice {
            get {
                return m.get('ApplicationMicrosoftOffice');
            }
            set {
                put('ApplicationMicrosoftOffice', value);
            }
        }
        public keyModule Airwatch {
            get {
                return m.get('Airwatch');
            }
            set {
                put('Airwatch', value);
            }
        }
        public keyModule MobileIron {
            get {
                return m.get('MobileIron');
            }
            set {
                put('MobileIron', value);
            }
        }
        public keyModule Blackberry {
            get {
                return m.get('Blackberry');
            }
            set {
                put('Blackberry', value);
            }
        }
        public keyModule AppConfig {
            get {
                return m.get('AppConfig');
            }
            set {
                put('AppConfig', value);
            }
        }
        public keyModule BigData {
            get {
                return m.get('BigData');
            }
            set {
                put('BigData', value);
            }
        }
        public keyModule Olap {
            get {
                return m.get('Olap');
            }
            set {
                put('Olap', value);
            }
        }


    }

    public class AnalyticsModule {
        public Boolean IsEvaluation {
            get;
            set;
        }

        public AnalyticsModule(Boolean IsEvaluation) {
            this.IsEvaluation = IsEvaluation;
        }
    }

    public class DesktopMod {
        public Evaluation Evaluation {
            get;
            set;
        }
        public DesktopProduct DesktopAnalyst {
            get;
            set;
        }
        public DesktopProduct DesignerOption {
            get;
            set;
        }
        public DesktopProduct DesktopArchitect {
            get;
            set;
        }

        public DesktopMod(Evaluation evaluation) {
            this.Evaluation = evaluation;
        }

    }

    public class DesktopProduct {
        public Integer NamedUsers {
            get;
            set;
        }

        public DesktopProduct(Integer namedUsers) {
            this.NamedUsers = namedUsers;
        }
    }

    public class OneTierDeskTop {
        public Integer NamedUsers {
            get;
            set;
        }

        public OneTierDeskTop(Integer namedUsers) {
            this.NamedUsers = namedUsers;
        }
    }

    public class IServerModule {
        public Boolean IsUniversal {
            get;
            set;
        }
        public Boolean Evaluation {
            get;
            set;
        }
        public Boolean ResellerFlag {
            get;
            set;
        }
        public Boolean Reseller {
            get;
            set;
        }
        public Boolean dailyReport {
            get;
            set;
        }
        public Boolean EnableDailyReport {
            get;
            set;
        }
        public Boolean Clustering {
            get;
            set;
        }
        public Integer ConcurrentUsers {
            get;
            set;
        }
        public KeyGeneratorLicense License {
            get;
            set;
        }
        public MultisourceOption MultiSource {
            get;
            set;
        }
        public IServerOption TransactionServicesOption {
            get;
            set;
        }
        public IServerOption NarrowcastOption {
            get;
            set;
        }
        public IServerOption ReportServicesOption {
            get;
            set;
        }
        public IServerOption OlapServicesOption {
            get;
            set;
        }
        public IServerOption DistributionServicesOption {
            get;
            set;
        }


        public IServerModule(Boolean evaluation, KeyGeneratorLicense license, Boolean reseller, Boolean dailyReport, Boolean clustering, Integer concurrentUsers) {
            this.License = license;
            this.ResellerFlag = reseller;
            this.Reseller = reseller;
            this.dailyReport = dailyReport;
            this.EnableDailyReport = dailyReport;
            this.Clustering = clustering;
            this.ConcurrentUsers = concurrentUsers;
        }


    }

    public class AdministratorMod {
        public Boolean IsEntryLevel {
            get;
            set;
        }
        public Boolean Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }
        public Integer ManagedNamedUsers {
            get;
            set;
        }
        public Integer managedUsers {
            get;
            set;
        }

        public AdministratorMod(Boolean isEntryLevel, Boolean evaluation, Integer namedUsers, Integer managedUsers) {
            this.IsEntryLevel = isEntryLevel;
            //.this.evaluation=evaluation;
            this.NamedUsers = namedUsers;
            this.managedUsers = managedUsers;
            this.ManagedNamedUsers = managedUsers;
        }
    }

    public class CommandManager {
        public Evaluation Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }
        public Integer ManagedNamedUsers {
            get;
            set;
        }
        public Integer managedUsers {
            get;
            set;
        }

        public CommandManager(Evaluation evaluation, Integer namedUsers, Integer managedUsers) {
            this.Evaluation = evaluation;
            this.NamedUsers = namedUsers;
            this.managedUsers = managedUsers;
            this.ManagedNamedUsers = managedUsers;
        }

    }
    public class SystemManager {
        public Evaluation Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }
        public Integer ManagedNamedUsers {
            get;
            set;
        }
        public Integer managedUsers {
            get;
            set;
        }


        public SystemManager(Evaluation evaluation, Integer namedUsers, Integer managedUsers) {
            this.Evaluation = evaluation;
            this.NamedUsers = namedUsers;
            this.managedUsers = managedUsers;
            this.ManagedNamedUsers = managedUsers;

        }

    }

    public class UsherSecurity {
        public Evaluation Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }

        public UsherSecurity(Evaluation evaluation, Integer namedUsers) {
            this.Evaluation = evaluation;
            this.NamedUsers = namedUsers;
        }

    }

    public class UsherSecurityServer {
        public Evaluation Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }

        public UsherSecurityServer(Evaluation evaluation, Integer namedUsers) {
            this.Evaluation = evaluation;
            this.NamedUsers = namedUsers;
        }

    }

    public class UsherSecurityForAnalytics {
        public Evaluation Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }

        public UsherSecurityForAnalytics(Evaluation evaluation, Integer namedUsers) {
            this.Evaluation = evaluation;
            this.NamedUsers = namedUsers;
        }

    }

    public class Office {
        public KeyGeneratorLicense License {
            get;
            set;
        }

        public Office(KeyGeneratorLicense license) {
            this.License = license;
        }
    }

    public class ObjectManager {
        public Evaluation Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }
        public Integer ManagedNamedUsers {
            get;
            set;
        }

        public Integer managedUsers {
            get;
            set;
        }

        public ObjectManager(Evaluation evaluation, Integer namedUsers, Integer managedUsers) {
            this.Evaluation = evaluation;
            this.NamedUsers = namedUsers;
            this.managedUsers = managedUsers;
            this.ManagedNamedUsers = managedUsers;

        }

    }

    public class EnterpriseManager {
        public Evaluation Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }
        public Integer ManagedNamedUsers {
            get;
            set;
        }
        public Integer managedUsers {
            get;
            set;
        }


        public EnterpriseManager(Evaluation evaluation, Integer namedUsers, Integer managedUsers) {
            this.Evaluation = evaluation;
            this.NamedUsers = namedUsers;
            this.managedUsers = managedUsers;
            this.ManagedNamedUsers = managedUsers;

        }

    }

    public class IntegrityManager {
        public Evaluation Evaluation {
            get;
            set;
        }
        public Integer NamedUsers {
            get;
            set;
        }
        public Integer ManagedNamedUsers {
            get;
            set;
        }

        public Integer managedUsers {
            get;
            set;
        }

        public Integer ConcurrentJobs {
            get;
            set;
        }

        public IntegrityManager(Evaluation evaluation, Integer namedUsers, Integer managedUsers, Integer concurrentJobs) {
            this.Evaluation = evaluation;
            this.NamedUsers = namedUsers;
            this.ManagedNamedUsers = managedUsers;
            this.ConcurrentJobs = concurrentJobs;

            this.managedUsers = managedUsers;
        }

    }

    public class WebReporterModule {
        public KeyGeneratorLicense License {
            get;
            set;
        }
        public Evaluation Evaluation {
            get;
            set;
        }
        public WebProduct WebAnalystOption {
            get;
            set;
        }
        public WebProduct WebProfessionalOption {
            get;
            set;
        }
        public eTrainer MMTOption = null;
        public Integer UniversalOptions {
            get;
            set;
        }
        public Boolean IsUniversal {
            get {
                return (this.TotalWebProducts <= this.UniversalOptions);
            }
        }

        public Integer TotalWebProducts {
            get {
                Integer i = 1; // count this instance
                if (this.WebAnalystOption != null)
                    i++;
                if (this.WebProfessionalOption != null)
                    i++;
                if (this.MMTOption != null)
                    i++;
                return i;
            }
        }

        public WebReporterModule(KeyGeneratorLicense license, Evaluation evaluation) {
            this.License = license;
            this.Evaluation = evaluation;
            this.UniversalOptions = 0;

        }
    }

    public class IServerOption {
        public KeyGeneratorLicense License {
            get;
            set;
        }
        public IServerOption(KeyGeneratorLicense license) {
            this.License = license;
        }
    }

    public class Mobile {
        public KeyGeneratorLicense License {
            get;
            set;
        }
        public Evaluation Evaluation {
            get;
            set;
        }
        public Mobile(Evaluation Evaluation, KeyGeneratorLicense license) {
            this.License = license;
            this.Evaluation = Evaluation;
        }
    }

    public class MultisourceOption {
        public Integer NamedUsers {
            get;
            set;
        }

        public MultisourceOption(Integer namedUsers) {
            this.NamedUsers = namedUsers;
        }
    }

    public class WebProduct {
        public KeyGeneratorLicense License {
            get;
            set;
        }
        public WebProduct(KeyGeneratorLicense License) {
            this.License = License;
        }
    }

    public class eTrainer {
        public KeyGeneratorLicense License {
            get;
            set;
        }
        public eTrainer(KeyGeneratorLicense License) {
            this.License = License;
        }
    }

    public class ReportBundles {
        public Boolean IsUniversalAux {
            get;
            set;
        }
        public Boolean EnableDailyReport {
            get;
            set;
        }
        public Boolean Reseller {
            get;
            set;
        }
        public List < ReportBundle > listRB {
            get;
            set;
        }

        public Boolean IsUniversal {
            get {
                return IsUniversalAux;
            }
            set {
                IsUniversalAux = value;
                // all bundles must be universal or not universal
                for (ReportBundle bundle : listRB) {
                    if (IsUniversalAux && bundle.Type < 16)
                        bundle.Type = bundle.Type + KeyGeneratorConstants.REPORTERBUNDLETYPE_UNIVERSALCONST; else if (!IsUniversalAux && bundle.Type > 16)
                        bundle.Type = bundle.Type - KeyGeneratorConstants.REPORTERBUNDLETYPE_UNIVERSALCONST;
                }
            }
        }

        public ReportBundles(Boolean enableDailyReport, Boolean reseller) {
            this.EnableDailyReport = enableDailyReport;
            this.Reseller = reseller;
            this.listRB = new List < ReportBundle > ();
        }

        public void AddBundle(Integer type, Integer count) {
            if (this.IsUniversal)
                this.listRB.add(new ReportBundle(type + (KeyGeneratorConstants.REPORTERBUNDLETYPE_UNIVERSALCONST), count)); else
                this.listRB.add(new ReportBundle(type, count));
        }

    }

    public class ReportBundleType {

    }

    public class ReportBundle {

        public Integer Count {
            get;
            set;
        }
        public Integer Type {
            get;
            set;
        }
        public Boolean IsUniversal {
            get {
                return (this.Type - KeyGeneratorConstants.REPORTERBUNDLETYPE_UNIVERSALCONST) > 0;
            }
        }

        public ReportBundle(Integer type, Integer count) {
            this.Type = type;
            this.Count = count;
        }
    }

    public class Evaluation {
        public Integer Period {
            get;
            set;
        }
        public Integer Duration {
            get;
            set;
        }

        public Evaluation(Integer period, Integer duration) {
            this.Period = period;
            this.Duration = duration;
        }
    }


    //Classes to Support V11.1


    public class keyModule {
        public KeyGeneratorLicense License {
            get;
            set;
        }
        public Evaluation Evaluation {
            get;
            set;
        }
        public keyModule(Evaluation Evaluation, KeyGeneratorLicense license) {
            this.License = license;
            this.Evaluation = Evaluation;
        }
    }

    public class IServer {

        public transient Map < String, keyModule > m = new Map < String, keyModule > ();

        public void put(String key, keyModule value) {
            m.put(key, value);
        }

        public KeyGeneratorLicense License {
            get;
            set;
        }
        public Evaluation Evaluation {
            get;
            set;
        }
        public keyModule ServerAnalytics {
            get {
                return m.get('ServerAnalytics');
            }
            set {
                put('ServerAnalytics', value);
            }
        }
        public keyModule ClWeb {
            get {
                return m.get('ClWeb');
            }
            set {
                put('ClWeb', value);
            }
        }

        public keyModule ClArch {
            get {
                return m.get('ClArch');
            }
            set {
                put('ClArch', value);
            }
        }

        public keyModule ClDesktop {
            get {
                return m.get('ClDesktop');
            }
            set {
                put('ClDesktop', value);
            }
        }

        public keyModule ClWorkstation {
            get {
                return m.get('ClWorkstation');
            }
            set {
                put('ClWorkstation', value);
            }
        }

        public keyModule ClWorkflow {
            get {
                return m.get('ClWorkflow');
            }
            set {
                put('ClWorkflow', value);
            }
        }
        public IServer(Evaluation Evaluation, KeyGeneratorLicense license) {
            this.License = license;
            this.Evaluation = Evaluation;
        }
    }

    public class BaseModule {

        public transient Map < String, keyModule > m = new Map < String, keyModule > ();

        public void put(String key, keyModule value) {
            m.put(key, value);
        }

        public KeyGeneratorLicense License {
            get;
            set;
        }
        public Evaluation Evaluation {
            get;
            set;
        }
        public keyModule ServerAnalytics {
            get {
                return m.get('ServerAnalytics');
            }
            set {
                put('ServerAnalytics', value);
            }
        }
        public keyModule ClWeb {
            get {
                return m.get('ClWeb');
            }
            set {
                put('ClWeb', value);
            }
        }

        public keyModule ClArch {
            get {
                return m.get('ClArch');
            }
            set {
                put('ClArch', value);
            }
        }

        public keyModule ClDesktop {
            get {
                return m.get('ClDesktop');
            }
            set {
                put('ClDesktop', value);
            }
        }

        public keyModule ClWorkstation {
            get {
                return m.get('ClWorkstation');
            }
            set {
                put('ClWorkstation', value);
            }
        }

        public keyModule ClWorkflow {
            get {
                return m.get('ClWorkflow');
            }
            set {
                put('ClWorkflow', value);
            }
        }
        public keyModule Telemetry {
            get {
                return m.get('Telemetry');
            }
            set {
                put('Telemetry', value);
            }
        }
        public keyModule Collaboration {
            get {
                return m.get('Collaboration');
            }
            set {
                put('Collaboration', value);
            }
        }

        public keyModule ClBadge {
            get {
                return m.get('ClBadge');
            }
            set {
                put('ClBadge', value);
            }
        }

        public keyModule ClUsher {
            get {
                return m.get('ClUsher');
            }
            set {
                put('ClUsher', value);
            }
        }

        public keyModule Pacs {
            get {
                return m.get('Pacs');
            }
            set {
                put('Pacs', value);
            }
        }

        public keyModule LogicalAccess {
            get {
                return m.get('LogicalAccess');
            }
            set {
                put('LogicalAccess', value);
            }
        }

        public keyModule UsherAnalytics {
            get {
                return m.get('UsherAnalytics');
            }
            set {
                put('UsherAnalytics', value);
            }
        }
        public BaseModule(Evaluation Evaluation, KeyGeneratorLicense license) {
            this.License = license;
            this.Evaluation = Evaluation;
        }
    }


    public class IdBaseServer {

        public transient Map < String, keyModule > m = new Map < String, keyModule > ();

        public void put(String key, keyModule value) {
            m.put(key, value);
        }


        public KeyGeneratorLicense License {
            get;
            set;
        }
        public Evaluation Evaluation {
            get;
            set;
        }
        public keyModule Telemetry {
            get {
                return m.get('Telemetry');
            }
            set {
                put('Telemetry', value);
            }
        }
        public keyModule Collaboration {
            get {
                return m.get('Collaboration');
            }
            set {
                put('Collaboration', value);
            }
        }

        public keyModule ClBadge {
            get {
                return m.get('ClBadge');
            }
            set {
                put('ClBadge', value);
            }
        }

        public keyModule ClUsher {
            get {
                return m.get('ClUsher');
            }
            set {
                put('ClUsher', value);
            }
        }

        public keyModule Pacs {
            get {
                return m.get('Pacs');
            }
            set {
                put('Pacs', value);
            }
        }

        public keyModule LogicalAccess {
            get {
                return m.get('LogicalAccess');
            }
            set {
                put('LogicalAccess', value);
            }
        }

        public keyModule UsherAnalytics {
            get {
                return m.get('UsherAnalytics');
            }
            set {
                put('UsherAnalytics', value);
            }
        }
        public IdBaseServer(Evaluation Evaluation, KeyGeneratorLicense license) {
            this.License = license;
            this.Evaluation = Evaluation;
        }
    }
    public class DSIPayloadItem {
        public Integer sku {
            get;
            set;
        }
        public String productName {
            get;
            set;
        }
    }

}