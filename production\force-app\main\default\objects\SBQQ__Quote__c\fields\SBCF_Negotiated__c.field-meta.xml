<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>SBCF_Negotiated__c</fullName>
    <formula>OR(
    OR(
        TEXT(SBQQ__Type__c) == &quot;Amendment&quot;,
        TEXT(SBQQ__Type__c) == &quot;Rip And Replace&quot;,
        TEXT(Billing_Frequency__c) == &quot;Custom&quot;
    ),
    SBCF_License_Conversion__c,
    SBCF_License_Restrictions__c,
    SBCF_Multi_Year_Support_Commitments__c,
    SBCF_FPGs_Attached__c,
    Right_to_Terminate_and_Refund__c,
    SBCF_Hot_failover_disaster_recover_use__c,
    SBCF_Failure_to_Deliver_Penalties__c,
    SBCF_Free_development_rights__c,
    SBCF_No_charge_software_installation__c,
    SBCF_Right_to_deploy_license__c ,   
    SBCF_Right_to_Store_License__c,
    SBCF_Text_Enterprise_Terms_Entered__c,
    SBCF_Sum_of_Tech_Support_Extension__c&gt; 0,
    IF(
        CreatedDate &gt;= DATETIMEVALUE(&quot;2020-08-27 00:00:00&quot;),
        FALSE,
        SOW_Uploaded__c
    ),
    AND(
        Sum_of_Term__c &gt; 0,
        OR(
            ISPICKVAL( SBQQ__Opportunity2__r.Type , &quot;Maintenance Renewal&quot;) ,
            ISPICKVAL(SBQQ__Opportunity2__r.Type, &quot;Term Migration&quot;)
        )
    ),
    Enterprise_Terms_SKU__c &gt; 0,
    IF(
        CreatedDate &gt; DATETIMEVALUE(&quot;2025-02-06 00:00:00&quot;),
        TEXT(Billing_Frequency__c) == &quot;Quarterly&quot;,
        OR(
            SBCF_Sum_Of_Cloud__c &gt; 0,
            AND(
                DATEVALUE(CreatedDate) &lt; DATE(2024,03,14),
                MicroStrategy_AI_Starter_Pack_SKU__c &gt; 0
            ),
            Platform__c == &apos;MCS&apos;,
            Platform__c == &apos;CMC&apos;,
            Platform__c == &apos;MCG&apos;,
            Platform__c == &apos;MCE&apos;,
            Simplified_Cloud_SKU__c &gt; 0,
            (
                Sum_of_Term__c &gt; 0 &amp;&amp; 
                IF(
                    CreatedDate&gt;DATETIMEVALUE(&quot;2020-03-03 00:00:00&quot;),
                    GME__c &gt; 0,
                    TRUE
                )
    )
        )
    )
)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>Negotiated?</label>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Checkbox</type>
</CustomField>
