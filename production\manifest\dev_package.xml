<?xml version="1.0" encoding="UTF-8"?>
<Package xmlns="http://soap.sforce.com/2006/04/metadata">
    <types>
        <members>Integration_Error_Log__c</members>
        <members>MCS_Config__mdt</members>
        <members>MCS_Event__e</members>
        <members>Transaction_Log__c</members>
        <name>CustomObject</name>
    </types>
    <types>
        <members>AccountOwnerHistoryHelper</members>
        <members>CampaignMemberTriggerExecutionController</members>
        <members>CampaignMemberTriggerHelper</members>
        <members>CreateMCSDSI</members>
        <members>CreateMCSDSITest</members>
        <members>DSITriggerHelper</members>
        <members>EntitlementContactExecutionController</members>
        <members>EntitlementContactTriggerHelper</members>
        <members>GetMCSInfo</members>
        <members>IntegrationErrorLogger</members>
        <members>IntegrationErrorLoggerTest</members>
        <members>KeyGeneratorAPICallout</members>
        <members>LogPersisterQueueable</members>
        <members>MCSDependencyBatch</members>
        <members>MCSDependencyBatchTest</members>
        <members>MCSDependencyEmailService</members>
        <members>MCSDependencyQueueable</members>
        <members>MCSEnvironmentController</members>
        <members>MCSEnvironmentControllerTest</members>
        <members>MCSEnvironmentUpdatePayload</members>
        <members>MCSEventTriggerHelper</members>
        <members>MCSEventTriggerHelperTest</members>
        <members>MCSRestRequest</members>
        <members>MCSTrialRequestPayload</members>
        <members>SubscriptionTriggerExecutionController</members>
        <name>ApexClass</name>
    </types>
    <types>
        <members>DSITrigger</members>
        <members>MCSEventTrigger</members>
        <name>ApexTrigger</name>
    </types>
    <types>
        <members>DSI__c.MCSActivationDateTime__c</members>
        <members>DSI__c.MCSBatchStatus__c</members>
        <members>DSI__c.MCSEnvironmentName__c</members>
        <members>DSI__c.MCSEnvironmentOwner__c</members>
        <members>DSI__c.MCSEnvironmentUrl__c</members>
        <members>DSI__c.MCSExpirationDatetime__c</members>
        <members>Integration_Error_Log__c.Apex_Class__c</members>
        <members>Integration_Error_Log__c.Apex_Method__c</members>
        <members>Integration_Error_Log__c.Error_Code__c</members>
        <members>Integration_Error_Log__c.Error_Message__c</members>
        <members>Integration_Error_Log__c.Integration_Name__c</members>
        <members>Integration_Error_Log__c.Integration_Point__c</members>
        <members>Integration_Error_Log__c.Log_Message__c</members>
        <members>Integration_Error_Log__c.Log_Type__c</members>
        <members>Integration_Error_Log__c.Related_Record_ID__c</members>
        <members>Integration_Error_Log__c.Related_Record_Object__c</members>
        <members>Integration_Error_Log__c.Request_Body__c</members>
        <members>Integration_Error_Log__c.Request_Endpoint__c</members>
        <members>Integration_Error_Log__c.Request_Headers__c</members>
        <members>Integration_Error_Log__c.Request_Method__c</members>
        <members>Integration_Error_Log__c.Response_Body__c</members>
        <members>Integration_Error_Log__c.Response_Headers__c</members>
        <members>Integration_Error_Log__c.Response_Status_Code__c</members>
        <members>Integration_Error_Log__c.Severity__c</members>
        <members>Integration_Error_Log__c.Stack_Trace__c</members>
        <members>Integration_Error_Log__c.Status__c</members>
        <members>Integration_Error_Log__c.Timestamp__c</members>
        <members>Integration_Error_Log__c.Transaction_ID__c</members>
        <members>Integration_Error_Log__c.Transaction_Log__c</members>
        <members>Integration_Error_Log__c.User__c</members>
        <members>MCS_Config__mdt.URL__c</members>
        <members>MCS_Config__mdt.X_MSTR_Key__c</members>
        <members>MCS_Event__e.Account__c</members>
        <members>MCS_Event__e.Contact__c</members>
        <members>MCS_Event__e.DSI__c</members>
        <members>MCS_Event__e.EndDate__c</members>
        <members>MCS_Event__e.Environment_Owner__c</members>
        <members>MCS_Event__e.Opportunity__c</members>
        <members>Transaction_Log__c.Apex_Class_Name__c</members>
        <members>Transaction_Log__c.Apex_Method_Name__c</members>
        <members>Transaction_Log__c.Debug_Log_Count__c</members>
        <members>Transaction_Log__c.End_Time__c</members>
        <members>Transaction_Log__c.Error_Count__c</members>
        <members>Transaction_Log__c.Info_Log_Count__c</members>
        <members>Transaction_Log__c.Initial_Context__c</members>
        <members>Transaction_Log__c.Start_Time__c</members>
        <members>Transaction_Log__c.Status__c</members>
        <members>Transaction_Log__c.User__c</members>
        <name>CustomField</name>
    </types>
    <types>
        <members>MCS_Config.Config</members>
        <members>MSTR_Global_Configuation.MCS</members>
        <members>MSTR_Global_Configuation.MCS_API_Active</members>
        <members>MSTR_Global_Configuation.MCS_Default_Owner</members>
        <members>MSTR_Global_Configuation.MCS_Products</members>
        <members>MSTR_Global_Configuation.MCS_Trial_Campaign_Id</members>
        <name>CustomMetadata</name>
    </types>
    <types>
        <members>MCS Dependency Email Service</members>
        <name>EmailServicesFunction</name>
    </types>
    <types>
        <members>cloud mcs_v1</members>
        <name>EntitlementProcess</name>
    </types>
    <types>
        <members>Integration_Error_Log__c-Integration Error Log Layout</members>
        <members>Transaction_Log__c-Transaction Log Layout</members>
        <name>Layout</name>
    </types>
    <types>
        <members>Product_Key_Request__c</members>
        <name>Workflow</name>
    </types>
    <types>
        <members>Product_Key_Request__c.Send Key Email after Key is generated</members>
        <name>WorkflowRule</name>
    </types>
    <version>61.0</version>
</Package>