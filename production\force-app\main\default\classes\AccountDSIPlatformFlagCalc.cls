/************************************* MODIFICATION LOG ********************************************************************************************
* AccountDSIPlatformFlagCalc -- BatchAccountESBudgetHours
*
* DESCRIPTION : Apex class to Populate Platform flag for DSI and Account called from BatchAccountESBudgetHours batch class
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Lam Lawrence                01/20/2020                       -- Original Version
* Amritha Buddharaju          03/19/2020                       -- Updated Platform to null on DSI and Account,When all the subscriptions have expired on the DSI(Case 387289)
*/
public class AccountDSIPlatformFlagCalc {
    
    public static void PlatformFlagCalculation(List<Account> scope){
        
        List<String> mceSKUs = new List<String>();
        List<String> mcpSKUs = new List<String>();
        List<String> mepSKUs = new List<String>();
        List<String> mciSKUs = new List<String>();
        List<String> mcgSKUs = new List<String>();
        List<String> cmcSKUs = new List<String>();
        List<String> mcsSKUs = new List<String>();
        
        List<Product_Type__mdt> productTypes = Product_Type__mdt.getAll().values();
        for(Product_Type__mdt productType : productTypes) {
            if(productType.Is_Active__c) {
                if(productType.Platform__c == 'MCE') {
                    mceSKUs.add(productType.Product_Code__c);
                }
                else if(productType.Platform__c == 'MCP') {
                    mcpSKUs.add(productType.Product_Code__c);
                }
                else if(productType.Platform__c == 'MEP') {
                    mepSKUs.add(productType.Product_Code__c);
                }
                else if(productType.Platform__c == 'MCI') {
                    mciSKUs.add(productType.Product_Code__c);
                }
                else if(productType.Platform__c == 'MCG') {
                    mcgSKUs.add(productType.Product_Code__c);
                }
                else if(productType.Platform__c == 'CMC') {
                    cmcSKUs.add(productType.Product_Code__c);
                }
                else if(productType.Platform__c == 'MCS') {
                    mcsSKUs.add(productType.Product_Code__c);
                }
            }
        }

        
        //used only to make a more efficient search query
        List<String> PlatformSKUs = new List<String>();
        PlatformSKUs.addall(mceSKUs);
        PlatformSKUs.addall(mcpSKUs);
        PlatformSKUs.addall(mepSKUs);
        PlatformSKUs.addall(mciSKUs);
        PlatformSKUs.addall(mcgSKUs);
        PlatformSKUs.addall(cmcSKUs);
        PlatformSKUs.addall(mcsSKUs);
        
        //Map<Id,PlatformWrapper> DSIMap = new Map<Id,PlatformWrapper>();
        //Map<Id,Account> accountMap = new Map <Id,Account>();
        
        List<DSI__c> updateDSIList = new List<DSI__c>();
        List<Account> updateAccountList = new List<Account>();
        
        Map<Id,PlatformWrapper> platformDSIMap = new Map <Id,PlatformWrapper>();
        Map<Id,PlatformWrapper> platformAccMap = new Map <Id,PlatformWrapper>();
        
        //grab assets and subscriptions
        List<Asset> assetList = [Select Id, Product2Id,Product2.ProductCode, Product2.Product_Category__c, SBCF_DSI__c, SBCF_DSI__r.Account__c, SBCF_DSI__r.Platform_Locked__c , SBCF_DSI__r.Account__r.Platform__c, SBCF_DSI__r.Platform__c
                                 from Asset
                                 where SBCF_DSI__r.Account__c in: scope 
                                 and (Product2.ProductCode in: PlatformSKUs or Product2.Product_Category__c = 'Software')];
        
        List<SBQQ__Subscription__c> subscriptionList = [Select Id, SBQQ__EndDate__c, SBQQ__Product__c,SBQQ__Product__r.ProductCode,SBCF_DSI__c,SBCF_DSI__r.Account__c, SBCF_DSI__r.Platform__C, SBCF_DSI__r.Account__r.Platform__c, SBCF_DSI__r.Platform_Locked__c, SBQQ__Product__r.Product_Category__c 
                                                        from SBQQ__Subscription__c
                                                        where SBCF_DSI__r.Account__c in: scope 
                                                        and (SBQQ__Product__r.ProductCode in: PlatformSKUs or SBQQ__Product__r.Product_Category__c = 'Software')];
        
        //loop through assets
        system.debug('subscriptionList ===@@ '+subscriptionList);
        
        for(Asset dsiAsset : assetList){
            
            PlatformWrapper wrapperObj;
            PlatformWrapper accWrapperObj;
            
            //check map for dsi
            if(!platformDSIMap.containsKey(dsiAsset.SBCF_DSI__c)){
                //not in map so create wrapper and insert
                wrapperObj = new PlatformWrapper();
                wrapperObj.dsi = new DSI__c (id = dsiAsset.SBCF_DSI__c, Platform__c = dsiAsset.SBCF_DSI__r.Platform__c, Platform_Locked__c = dsiAsset.SBCF_DSI__r.Platform_Locked__c);
                wrapperObj.accId = dsiAsset.SBCF_DSI__r.Account__c;
                platformDSIMap.put(dsiAsset.SBCF_DSI__c,wrapperObj);
            }else{
                wrapperObj = platformDSIMap.get(dsiAsset.SBCF_DSI__c);
            }
            
            //check map for account
            if(!platformAccMap.containsKey(dsiAsset.SBCF_DSI__r.Account__c)){
                //not in map so create wrapper and insert
                accWrapperObj = new PlatformWrapper();
                accWrapperObj.acc = new Account (id = dsiAsset.SBCF_DSI__r.Account__c, Platform__c = dsiAsset.SBCF_DSI__r.Account__r.Platform__c);
                platformAccMap.put(dsiAsset.SBCF_DSI__r.Account__c,accWrapperObj);
            }
            
            //run 3 checks on DSI
            if (mceSKUs.contains(dsiAsset.Product2.ProductCode)){
                wrapperObj.mce = true;
            }
            if(mcpSKUs.contains(dsiAsset.Product2.ProductCode)){
                wrapperObj.mcp = true;
            }
            if (mepSKUs.contains(dsiAsset.Product2.ProductCode)){
                wrapperObj.mep = true;
            }
            if (mciSKUs.contains(dsiAsset.Product2.ProductCode)){
                wrapperObj.mci = true;
            }   
            if (mcgSKUs.contains(dsiAsset.Product2.ProductCode)){
                wrapperObj.mcg = true;
            } 
            if (cmcSKUs.contains(dsiAsset.Product2.ProductCode)){
                wrapperObj.cmc = true;
            } 
            if (mcsSKUs.contains(dsiAsset.Product2.ProductCode)){
                wrapperObj.mcs = true;
            }
            if (dsiAsset.Product2.Product_Category__c == 'Software'){
                wrapperObj.softwareProd = true;
            }        
        } // END ASSET FOR
        
        
        for(SBQQ__Subscription__c sub : subscriptionList){
            PlatformWrapper wrapperObj;
            PlatformWrapper accWrapperObj;
            
            //check map for dsi
            System.debug('----'+sub.SBCF_DSI__r.Platform__c);
            if(!platformDSIMap.containsKey(sub.SBCF_DSI__c)){
                //not in map so create wrapper and insert
                wrapperObj = new PlatformWrapper();
                wrapperObj.dsi = new DSI__c (id = sub.SBCF_DSI__c, Platform__c = sub.SBCF_DSI__r.Platform__c, Platform_Locked__c = sub.SBCF_DSI__r.Platform_Locked__c);
                wrapperObj.accId = sub.SBCF_DSI__r.Account__c;
                platformDSIMap.put(sub.SBCF_DSI__c,wrapperObj);
                
            }else{
                wrapperObj = platformDSIMap.get(sub.SBCF_DSI__c);
            }
            
            //check map for account
            if(!platformAccMap.containsKey(sub.SBCF_DSI__r.Account__c)){
                //not in map so create wrapper and insert
                accWrapperObj = new PlatformWrapper();
                accWrapperObj.acc = new Account (id = sub.SBCF_DSI__r.Account__c, Platform__c = sub.SBCF_DSI__r.Platform__c);
                platformAccMap.put(sub.SBCF_DSI__r.Account__c,accWrapperObj);
            }else{
                accWrapperObj = platformAccMap.get(sub.SBCF_DSI__r.Account__c);
            }
            
            
            if(sub.SBQQ__EndDate__c >= system.today().addDays(-180)){
                if (mceSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                    wrapperObj.mce = true;
                }
                if (mcpSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                    wrapperObj.mcp = true;
                }
                if (mepSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                    wrapperObj.mep = true;
                }
                if (mciSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                    wrapperObj.mci = true;
                }
                if (mcgSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                    wrapperObj.mcg = true;
                }
                if (cmcSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                    wrapperObj.cmc = true;
                }
                if (mcsSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                    wrapperObj.mcs = true;
                }
                if (sub.SBQQ__Product__r.Product_Category__c == 'Software'){
                    wrapperObj.softwareProd = true;
                }            
            } 
            
            if (mceSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                wrapperObj.mceAll = true;
            }
            if (mcpSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                wrapperObj.mcpAll = true;
            } 
            if (mepSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                wrapperObj.mepAll = true;
            }
            system.debug('sub.SBQQ__Product__r.ProductCode ===@@ '+sub.SBQQ__Product__r.ProductCode);
            system.debug('mciSKUs ===@@ '+mciSKUs);
            system.debug('mciSKUs contains ProductCode ===@@ '+mciSKUs.contains(sub.SBQQ__Product__r.ProductCode));
            if (mciSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                wrapperObj.mciAll = true;
            }  
            if (mcgSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                wrapperObj.mcgAll = true;
            }  
            if (cmcSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                wrapperObj.cmcAll = true;
            }
            if (mcsSKUs.contains(sub.SBQQ__Product__r.ProductCode)){
                wrapperObj.mcsAll = true;
            }
            if (sub.SBQQ__Product__r.Product_Category__c == 'Software'){
                wrapperObj.softwareProdAll = true;
            }        
            system.debug('wrapperObj ===@@ '+wrapperObj); 
        } // END SUBSCRIPTION FOR
        
        system.debug('platformDSIMap ===@@ '+platformDSIMap); 
        system.debug('platformAccMap ===@@ '+platformAccMap); 
        
        //loop through DSI determining platform
        
        for (Id key : platformDSIMap.keySet() ){
            system.debug('key ===@@ '+key); 
            
            PlatformWrapper dsiObjWrapper = platformDSIMap.get(key);
            PlatformWrapper accWrapper = platformAccMap.get(dsiObjWrapper.accId);
            system.debug('DSIwarapper for the key  ===@@ '+platformDSIMap.get(key)); 
            
            if(dsiObjWrapper.mce){
                //?? dsiObjWrapper.tempPlatform = 'MCE;';
                accWrapper.mce = true;
            }
            if (dsiObjWrapper.mcp && !dsiObjWrapper.mce){
                //?? dsiObjWrapper.tempPlatform = 'MCP;';
                accWrapper.mcp = true;
            }
            if(dsiObjWrapper.mep){
                //?? dsiObjWrapper.tempPlatform = dsiObjWrapper.tempPlatform + 'MEP;';
                accWrapper.mep = true;
            }
            if(dsiObjWrapper.mci){
                //?? dsiObjWrapper.tempPlatform = dsiObjWrapper.tempPlatform + 'MCI;';
                accWrapper.mci = true;
            }  
            if(dsiObjWrapper.mcg){
                accWrapper.mcg = true;
            }
            if(dsiObjWrapper.cmc){
                accWrapper.cmc = true;
            }
            if(dsiObjWrapper.mcs){
                accWrapper.mcs = true;
            }
            
            if (!dsiObjWrapper.mce && !dsiObjWrapper.mcp && !dsiObjWrapper.mep && dsiObjWrapper.softwareProd && !dsiObjWrapper.mci && !dsiObjWrapper.mcg && !dsiObjWrapper.cmc && !dsiObjWrapper.mcs){
                //??  dsiObjWrapper.tempPlatform = 'MEP;';
                accWrapper.mep = true;
            }
            
            if(dsiObjWrapper.mceAll || dsiObjWrapper.mce){
                dsiObjWrapper.tempPlatform = 'MCE;';
            }
            if ((dsiObjWrapper.mcpAll || dsiObjWrapper.mcp) && !dsiObjWrapper.mceAll){
                dsiObjWrapper.tempPlatform = 'MCP;';
            }
            if(dsiObjWrapper.mepAll || dsiObjWrapper.mep){
                dsiObjWrapper.tempPlatform = dsiObjWrapper.tempPlatform + 'MEP;';
            }
            if(dsiObjWrapper.mciAll || dsiObjWrapper.mci){
                system.debug('inside if mciAll is true ===@@ ');
                dsiObjWrapper.tempPlatform = dsiObjWrapper.tempPlatform + 'MCI;';
            }   
            if(dsiObjWrapper.mcgAll || dsiObjWrapper.mcg){
               dsiObjWrapper.tempPlatform = dsiObjWrapper.tempPlatform + 'MCG;';
            }
            if(dsiObjWrapper.cmcAll || dsiObjWrapper.cmc){
               dsiObjWrapper.tempPlatform = dsiObjWrapper.tempPlatform + 'CMC;';
            }
            if(dsiObjWrapper.mcsAll || dsiObjWrapper.mcs){
               dsiObjWrapper.tempPlatform = dsiObjWrapper.tempPlatform + 'MCS;';
            }
            
            if (!dsiObjWrapper.mceAll && !dsiObjWrapper.mcpAll && !dsiObjWrapper.mepAll && !dsiObjWrapper.mciAll && !dsiObjWrapper.mcgAll && !dsiObjWrapper.cmcAll && !dsiObjWrapper.mcsAll && !dsiObjWrapper.mce && !dsiObjWrapper.mcp && !dsiObjWrapper.mep && !dsiObjWrapper.mci && !dsiObjWrapper.mcg && !dsiObjWrapper.cmc && !dsiObjWrapper.mcs && (dsiObjWrapper.softwareProdAll || dsiObjWrapper.softwareProd)){
                system.debug('inside MEP - Priyank ===@@ ');
                dsiObjWrapper.tempPlatform = 'MEP;';
            }
            
            system.debug('dsiObjWrapper.tempPlatform ===@@ '+dsiObjWrapper.tempPlatform);
            system.debug('dsiObjWrapper.dsi.Platform__c ===@@ '+dsiObjWrapper.dsi.Platform__c);
            system.debug('dsiObjWrapper.dsi.Platform_Locked__c ===@@ '+dsiObjWrapper.dsi.Platform_Locked__c);
            //Platform value changed, add to update list
            if( !dsiObjWrapper.mceAll && !dsiObjWrapper.mcpAll && !dsiObjWrapper.mepAll && !dsiObjWrapper.softwareProdAll && !dsiObjWrapper.mciAll && !dsiObjWrapper.mcgAll && !dsiObjWrapper.cmcAll && !dsiObjWrapper.mcsAll && !dsiObjWrapper.mce && !dsiObjWrapper.mcp && !dsiObjWrapper.mep && !dsiObjWrapper.softwareProd && !dsiObjWrapper.mci && !dsiObjWrapper.mcg && !dsiObjWrapper.cmc && !dsiObjWrapper.mcs){
                system.debug('dsiObjWrapper ' + dsiObjWrapper);
                dsiObjWrapper.dsi.Platform__c = null;
                updateDSIList.add(dsiObjWrapper.dsi);  
            } 
            
            else if(dsiObjWrapper.dsi != null && ((dsiObjWrapper.dsi.Platform__c != null &&  dsiObjWrapper.tempPlatform != dsiObjWrapper.dsi.Platform__c) || dsiObjWrapper.dsi.Platform__c == null ) && !dsiObjWrapper.dsi.Platform_Locked__c){
                system.debug('inside final if b4 update ===@@ ');
                dsiObjWrapper.dsi.Platform__c = dsiObjWrapper.tempPlatform;
                updateDSIList.add(dsiObjWrapper.dsi);            
            } 
        }
        
        system.debug('updateDSIList === '+updateDSIList);
        //looping through account to know what to save
        for (Id key : platformAccMap.keySet() ){
        	    
            PlatformWrapper accObj = platformAccMap.get(key);
            
            if(accObj.mce){
                accObj.tempPlatform = accObj.tempPlatform +'MCE;';
            }
            if (accObj.mcp){
                accObj.tempPlatform = accObj.tempPlatform +'MCP;';
            }
            if(accObj.mep){
                accObj.tempPlatform = accObj.tempPlatform + 'MEP;';
            }
            if(accObj.mci){
                accObj.tempPlatform = accObj.tempPlatform + 'MCI;';
            }  
            if(accObj.mcg){
                accObj.tempPlatform = accObj.tempPlatform + 'MCG;';
            }
            if(accObj.cmc){
                accObj.tempPlatform = accObj.tempPlatform + 'CMC;';
            }
            if(accObj.mcs){
                accObj.tempPlatform = accObj.tempPlatform + 'MCS;';
            }
            
            //add to update list
            if(!accObj.mcp && !accObj.mce && !accObj.mep && !accObj.mci && !accObj.mcg && !accObj.cmc && !accObj.mcs){
                accObj.acc.Platform__c = null;
                updateAccountList.add(accObj.acc);
            } else if (accObj.acc != null && accObj.tempPlatform != accObj.acc.Platform__c){
                accObj.acc.Platform__c = accObj.tempPlatform;
                updateAccountList.add(accObj.acc);
            }
            
        }

        if(!updateDSIList.isEmpty()){
            update updateDSIList;
        }if (!updateAccountList.isEmpty()){
            update updateAccountList;
        }
    }
    
    public class PlatformWrapper{
        public DSI__c dsi  {get;set;}
        public Account acc {get;set;}
        public String tempPlatform {get;set;}
        
        /*Assets*/
        public boolean mce {get;set;}
        public boolean mcp {get;set;}
        public boolean mep {get;set;}
        public boolean mci {get;set;}
        public boolean mcg {get;set;}
        public boolean cmc {get;set;}
        public boolean mcs {get;set;}
        public boolean softwareProd {get;set;}
        
        /*Subscriptions*/
        public boolean mceAll {get;set;}
        public boolean mcpAll {get;set;}
        public boolean mepAll {get;set;}
        public boolean mciAll {get;set;}
        public boolean mcgAll {get;set;}
        public boolean cmcAll {get;set;}
        public boolean mcsAll {get;set;}
        public boolean softwareProdAll {get;set;}
        
        public Id accId {get;set;}
        
        public PlatformWrapper(){
            tempPlatform = '';
            mce = false;
            mcp = false;
            mep = false;
            mci = false;
            mcg = false;
            cmc = false;
            mcs = false;
            softwareProd = false;
            
            mceAll = false;
            mcpAll = false;
            mepAll = false;
            mciAll = false;
            mcgAll = false;
            cmcAll = false;
            mcsAll = false;
            softwareProdAll = false;
        }
        
    }
}