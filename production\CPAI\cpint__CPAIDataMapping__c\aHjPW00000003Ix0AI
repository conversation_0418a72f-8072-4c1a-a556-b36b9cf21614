[{"configId": "RpzM2UnAKfj033JK8x", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "id", "sf_fieldLabel": "Record ID", "sf_childRelationshipName": "", "cp_fieldName": "integrationId", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "irgGGoaLBSX3hTV242", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "SBQQ__Opportunity2__r.id", "sf_fieldLabel": "SBQQ__Opportunity2__r.Opportunity ID", "sf_childRelationshipName": "", "cp_fieldName": "integrationId", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "7dOB7qbiwizA901E0J", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "SBQQ__Account__r.id", "sf_fieldLabel": "SBQQ__Account__r.Account ID", "sf_childRelationshipName": "", "cp_fieldName": "integrationId", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "VudzYo6APXne0if3pX", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "clientDetails", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "WUmQajncWznrwgycCr", "isConstant": false, "cp_fieldName": "clientTypeId", "cp_fieldLabel": "Standard : Partner Type", "sf_fieldName": "SBQQ__Account__r.type", "sf_fieldLabel": "SBQQ__Account__r.Account Type"}, {"configId": "IZuSacUbVwudXkLdwZ", "isConstant": false, "cp_fieldName": "customerName", "cp_fieldLabel": "Standard : Partner Name", "sf_fieldName": "SBQQ__Account__r.name", "sf_fieldLabel": "SBQQ__Account__r.Account Name"}, {"configId": "nxQbihgaZ4nMV3MlYY", "isConstant": false, "cp_fieldName": "addressLine1", "cp_fieldLabel": "Standard : Address", "sf_fieldName": "billing_street__c", "sf_fieldLabel": "Billing Street"}, {"configId": "ihb2vwZYz1aYX7KWSb", "isConstant": false, "cp_fieldName": "countryId", "cp_fieldLabel": "Standard : Country", "sf_fieldName": "sbqq__billingcountry__c", "sf_fieldLabel": "<PERSON>"}, {"configId": "5XvyOaXULTYZ2jSlR1", "isConstant": false, "cp_fieldName": "city", "cp_fieldLabel": "Standard : City", "sf_fieldName": "sbqq__billingcity__c", "sf_fieldLabel": "<PERSON>"}, {"configId": "Q8ITORtTzcQD1Ni6nT", "isConstant": false, "cp_fieldName": "state", "cp_fieldLabel": "Standard : State/Province", "sf_fieldName": "sbqq__billingstate__c", "sf_fieldLabel": "Bill To State"}, {"configId": "uL1w7FFrxN0LTjWODt", "isConstant": false, "cp_fieldName": "pincode", "cp_fieldLabel": "Standard : Pincode/Postal Code", "sf_fieldName": "sbqq__billingpostalcode__c", "sf_fieldLabel": "Bill To Postal Code"}], "configId": "fjp48PE0mprjVBtecQ", "sf_sortorder_field": "", "sf_sortorder_direction": ""}], "showFilter": false}, {"configId": "y1S7bkecLWAMJdpcEo", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "assignees", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "b2D0CqCgYazgwY5kpF", "isConstant": true, "cp_fieldName": "assigntoUsername", "cp_fieldLabel": "Standard : Assignee", "sf_fieldName": "ContractPod", "sf_fieldLabel": "ContractPod"}, {"configId": "iWG4OTAKSA1AyRMv8V", "isConstant": true, "cp_fieldName": "departmentId", "cp_fieldLabel": "Standard : Department", "sf_fieldName": "CPAi Support", "sf_fieldLabel": "CPAi Support"}], "configId": "srDU2axnlDffWSOi7Y"}], "showFilter": false}, {"configId": "yeIUuLE1if01nlwaKB", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "SBQQ__Opportunity2__r.SBCF_Legal_Entity__r.name", "sf_fieldLabel": "SBQQ__Opportunity2__r.SBCF_Legal_Entity__r.Account Name", "sf_childRelationshipName": "", "cp_fieldName": "contractingPartyId", "cp_fieldType": "Standard", "isCompositeMap": false, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "b2D0CqCgYazgwY5kpF", "isConstant": true, "cp_fieldName": "assigntoUsername", "cp_fieldLabel": "Standard : Assignee", "sf_fieldName": "ContractPod", "sf_fieldLabel": "ContractPod"}, {"configId": "iWG4OTAKSA1AyRMv8V", "isConstant": true, "cp_fieldName": "departmentId", "cp_fieldLabel": "Standard : Department", "sf_fieldName": "CPAi Support", "sf_fieldLabel": "CPAi Support"}], "configId": "srDU2axnlDffWSOi7Y"}], "showFilter": false}, {"configId": "mteLUfUrrR5eYJJrHQ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "footer_so__c", "sf_fieldLabel": "Footer SO", "sf_childRelationshipName": "", "cp_fieldName": "OrderForm", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "uytyoFhlXV5bCLEGyQ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_company__c", "sf_fieldLabel": "Billing Company", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Customer", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "HLCVRihTyD5AuLloLW", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_name__c", "sf_fieldLabel": "<PERSON> Contact Name", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Contact", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "lXBDtQtWeX8k8sMWtW", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_email__c", "sf_fieldLabel": "Billing Email", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Email", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "TDmIcn14Ebp8KgOhEk", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_address__c", "sf_fieldLabel": "Billing Address", "sf_childRelationshipName": "", "cp_fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "eWiNWVMyNSjk1NHJPz", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__billingcountry__c", "sf_fieldLabel": "<PERSON>", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Country", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "em2EE4rpPIgJMiz6Tp", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_name__c", "sf_fieldLabel": "Ship To Contact Name", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Contact", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "IkbiYjXynzdUcpUaHr", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_email__c", "sf_fieldLabel": "Shipping Email", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Email", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "W5EVPzeNSbAmAfPmPa", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_address__c", "sf_fieldLabel": "Shipping Address", "sf_childRelationshipName": "", "cp_fieldName": "ShiptoAddress", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "m2ZskM1DmrGBX5nOCs", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__shippingcountry__c", "sf_fieldLabel": "Ship To Country", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Country", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "JcddaJAxxhiSgGhtuX", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_company__c", "sf_fieldLabel": "Shipping Company", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Customer", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "dXKnzzUrdvS1iuzhAw", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "output_expiration_date_full_month__c", "sf_fieldLabel": "Output Expiration Date - Full Month", "sf_childRelationshipName": "", "cp_fieldName": "QuoteExpDateNew", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "fqkTrXu9KwsVgdN2JU", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "dsiname__c", "sf_fieldLabel": "DSI Name", "sf_childRelationshipName": "", "cp_fieldName": "DSICustom", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "dWjcLj5m4KvobQhPKz", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "billing_frequency__c", "sf_fieldLabel": "Billing Frequency", "sf_childRelationshipName": "", "cp_fieldName": "InvoicingSchedule", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "RVhTl0bjNDdDc90Cvx", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "currencyisocode", "sf_fieldLabel": "Currency ISO Code", "sf_childRelationshipName": "", "cp_fieldName": "CurrencyISOCode", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "gnikHcNyMAn04sqri8", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "software_term_total__c", "sf_fieldLabel": "Software Term Total", "sf_childRelationshipName": "", "cp_fieldName": "SoftwareTotalCustomStandAlone", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "jUSJXhXbSa7xZp1R9j", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_partner_account_name__c", "sf_fieldLabel": "Partner Account Name", "sf_childRelationshipName": "", "cp_fieldName": "PartnerAccountName", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "qd6xDr6h3l2ZrP5r2F", "mappingType": "relatedDataMapping", "isConstant": false, "isRelatedMap": true, "isReferenceMapping": false, "sf_fieldName": "SBQQ__QuoteLine__c", "sf_fieldLabel": "", "sf_childRelationshipName": "SBQQ__LineItems__r", "cp_fieldName": "QuoteLineAll7", "cp_fieldType": "table", "isCompositeMap": false, "sf_sortorder_field": "", "sf_sortorder_direction": "", "relatedDataMapping": [{"configId": "eeLQZ8Tft8wtLMe4ER", "isConstant": false, "cp_fieldName": "ProductCategoryAll7", "cp_fieldLabel": "Product Category", "sf_fieldName": "product_category__c", "sf_fieldLabel": "Product Category"}, {"configId": "e6Jc6wChERhT6rXUVD", "isConstant": false, "cp_fieldName": "QuoteGroupingAll7", "cp_fieldLabel": "Quote Grouping", "sf_fieldName": "group_name__c", "sf_fieldLabel": "Group Name"}, {"configId": "ls8zHix6LvBm7aLQTz", "isConstant": false, "cp_fieldName": "QuoteLineSKUAll7", "cp_fieldLabel": "Quote Line SKU", "sf_fieldName": "sbqq__productcode__c", "sf_fieldLabel": "Product Code"}, {"configId": "KmXAr7r18nzGIXDoAm", "isConstant": false, "cp_fieldName": "QuoteLineSKUDescriptionAll7", "cp_fieldLabel": "Quote Line SKU Description", "sf_fieldName": "sbqq__productname__c", "sf_fieldLabel": "Product Name"}, {"configId": "Ac9pyTIdKLlJYVvCbb", "isConstant": false, "cp_fieldName": "QuoteLineSKUTypeAll7", "cp_fieldLabel": "Quote Line SKU Type", "sf_fieldName": "sbcf_text_environment__c", "sf_fieldLabel": "Environment"}, {"configId": "yCuUY6BCmnjzRX0vAX", "isConstant": false, "cp_fieldName": "QuoteItemTermAll7", "cp_fieldLabel": "Quote <PERSON><PERSON>", "sf_fieldName": "ppw_initial_term_text__c", "sf_fieldLabel": "PPW Initial Term Text"}, {"configId": "SByh9hJatf5Nm1KcFW", "isConstant": false, "cp_fieldName": "QuoteLineAnnualListPriceAll7", "cp_fieldLabel": "Quote Line Annual List Price", "sf_fieldName": "plp__c", "sf_fieldLabel": "Paperwork List Price"}, {"configId": "tp1Shqzy55nHekZUma", "isConstant": false, "cp_fieldName": "QuoteLineQuantityAll7", "cp_fieldLabel": "Quote Line Quantity", "sf_fieldName": "sbqq__effectivequantity__c", "sf_fieldLabel": "Effective Quantity"}, {"configId": "Z4VIkE7XLmQSlANBq9", "isConstant": false, "cp_fieldName": "QuoteLineGrossTotalAll7", "cp_fieldLabel": "Quote Line Gross Total", "sf_fieldName": "plt__c", "sf_fieldLabel": "Paperwork List Total"}, {"configId": "WG28HuKEpkqo21uLcW", "isConstant": false, "cp_fieldName": "QuoteLineDiscountPercentAll7", "cp_fieldLabel": "Quote Line Discount Percent", "sf_fieldName": "sbqq__totaldiscountrate__c", "sf_fieldLabel": "Total Discount (%)"}, {"configId": "lqj9iqn8MEk0WPlHrk", "isConstant": false, "cp_fieldName": "QuoteLineDiscountAmountAll7", "cp_fieldLabel": "Quote Line Discount Amount", "sf_fieldName": "pwda__c", "sf_fieldLabel": "Paperwork Discount Amount"}, {"configId": "OYAzOP3cUioONxtjwL", "isConstant": false, "cp_fieldName": "QuoteLineTotalAll7", "cp_fieldLabel": "Quote Line Total", "sf_fieldName": "pgt__c", "sf_fieldLabel": "Paperwork Gross Total"}, {"configId": "BxPjbgIVfnQOZTDOFp", "isConstant": false, "cp_fieldName": "ISOLineCurrency7", "cp_fieldLabel": "ISO Line Currency", "sf_fieldName": "currencyisocode", "sf_fieldLabel": "Currency ISO Code"}, {"configId": "MCYA1bp1gNwsF9MAN3", "isConstant": false, "cp_fieldName": "QuoteLineInterval7", "cp_fieldLabel": "Quote Line Interval", "sf_fieldName": "sbcf_text_interval__c", "sf_fieldLabel": "Interval"}], "criteria": {"conditions": [{"configid": "m1to6nw9kwe5os90tjl", "id": 1, "key": "paperwork_section__c", "opr": "equals", "value": "Software", "isValRequired": true, "valueLabel": "Software", "keyLabel": "Paperwork Section", "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}, {"configid": "macqzbby7h4kavulsnj", "id": 2, "key": "paperwork_section__c", "opr": "equals", "value": "TNI", "isValRequired": true, "valueLabel": "TNI", "keyLabel": "Paperwork Section", "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}], "action": "OR", "expression": "1 OR 2"}, "showFilter": false}, {"configId": "ZZHNnAp2kvVEiVxFed", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__status__c", "sf_fieldLabel": "Status", "sf_childRelationshipName": "", "cp_fieldName": "Quote<PERSON><PERSON><PERSON>", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "ecAAvaIqAcDmim7ERO", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__lineitemsgrouped__c", "sf_fieldLabel": "Group Line Items", "sf_childRelationshipName": "", "cp_fieldName": "GroupLineItems", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "pxW6PSUWSFHNjWdmc2", "mappingType": "relatedDataMapping", "isConstant": false, "isRelatedMap": true, "isReferenceMapping": false, "sf_fieldName": "SBQQ__QuoteLine__c", "sf_fieldLabel": "", "sf_childRelationshipName": "SBQQ__LineItems__r", "cp_fieldName": "QuoteLineAllService", "cp_fieldType": "table", "isCompositeMap": false, "sf_sortorder_field": "", "sf_sortorder_direction": "", "relatedDataMapping": [{"configId": "bNc0WxgwPwunhU4LmJ", "isConstant": false, "cp_fieldName": "ProductCategoryAllService8", "cp_fieldLabel": "Product Category", "sf_fieldName": "product_category__c", "sf_fieldLabel": "Product Category"}, {"configId": "3Ur8YW0IL1Ud8WJmG2", "isConstant": false, "cp_fieldName": "QuoteGroupingAllService8", "cp_fieldLabel": "Quote Grouping", "sf_fieldName": "group_name__c", "sf_fieldLabel": "Group Name"}, {"configId": "lhdqWdppT5mc803F3A", "isConstant": false, "cp_fieldName": "QuoteLineSKUAllService8", "cp_fieldLabel": "Quote Line SKU", "sf_fieldName": "sbqq__productcode__c", "sf_fieldLabel": "Product Code"}, {"configId": "e8KnXi4bJogzTW7tP2", "isConstant": false, "cp_fieldName": "QuoteLineSKUDescriptionAllService8", "cp_fieldLabel": "Quote Line SKU Description", "sf_fieldName": "sbqq__productname__c", "sf_fieldLabel": "Product Name"}, {"configId": "5JSu6mv6yCSIJFwsE6", "isConstant": false, "cp_fieldName": "QuoteLineSKUTypeAllService8", "cp_fieldLabel": "Quote Line SKU Type", "sf_fieldName": "sbcf_text_environment__c", "sf_fieldLabel": "Environment"}, {"configId": "KqMC1yRrUwm0QkYoi8", "isConstant": false, "cp_fieldName": "QuoteItemTermAllService8", "cp_fieldLabel": "Quote <PERSON><PERSON>", "sf_fieldName": "ppw_initial_term_text__c", "sf_fieldLabel": "PPW Initial Term Text"}, {"configId": "IfX83gNJ7oRsYoryCP", "isConstant": false, "cp_fieldName": "QuoteLineAnnualListPriceAllService8", "cp_fieldLabel": "Quote Line Annual List Price", "sf_fieldName": "plp__c", "sf_fieldLabel": "Paperwork List Price"}, {"configId": "bYJo12EYwzYYMqn0VV", "isConstant": false, "cp_fieldName": "QuoteLineQuantityAllService8", "cp_fieldLabel": "Quote Line Quantity", "sf_fieldName": "sbqq__effectivequantity__c", "sf_fieldLabel": "Effective Quantity"}, {"configId": "zwmEBOthtsTNE1vTFA", "isConstant": false, "cp_fieldName": "QuoteLineGrossTotalAllService8", "cp_fieldLabel": "Quote Line Gross Total", "sf_fieldName": "plt__c", "sf_fieldLabel": "Paperwork List Total"}, {"configId": "nco1wtMZq1jITnH0bD", "isConstant": false, "cp_fieldName": "QuoteLineDiscountPercentAllService8", "cp_fieldLabel": "Quote Line Discount Percent", "sf_fieldName": "sbqq__totaldiscountrate__c", "sf_fieldLabel": "Total Discount (%)"}, {"configId": "9896Wj5HBr0Q7ofLA3", "isConstant": false, "cp_fieldName": "QuoteLineDiscountAmountAllService8", "cp_fieldLabel": "Quote Line Discount Amount", "sf_fieldName": "pwda__c", "sf_fieldLabel": "Paperwork Discount Amount"}, {"configId": "O80SZI765SxEpcYrhO", "isConstant": false, "cp_fieldName": "QuoteLineTotalAllService8", "cp_fieldLabel": "Quote Line Total", "sf_fieldName": "pgt__c", "sf_fieldLabel": "Paperwork Gross Total"}, {"configId": "UR4dGPdqAxWurv19sm", "isConstant": false, "cp_fieldName": "ISOLineCurrency8", "cp_fieldLabel": "ISO Line Currency", "sf_fieldName": "currencyisocode", "sf_fieldLabel": "Currency ISO Code"}, {"configId": "WZ1GNlWD3hpI4DEAu7", "isConstant": false, "cp_fieldName": "QuoteEffectiveQuantityServices8", "cp_fieldLabel": "Quote Effective Quantity", "sf_fieldName": "sbqq__effectivequantity__c", "sf_fieldLabel": "Effective Quantity"}], "criteria": {"conditions": [{"configid": "m1pvvn579mnxhme41ya", "id": 1, "key": "paperwork_section__c", "opr": "equals", "value": "Services", "isValRequired": true, "valueLabel": "Services", "keyLabel": "Paperwork Section", "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}], "action": "AND", "expression": 1}, "showFilter": false}, {"configId": "r9FVWxKmIcqiczUXsT", "mappingType": "relatedDataMapping", "isConstant": false, "isRelatedMap": true, "isReferenceMapping": false, "sf_fieldName": "SBQQ__QuoteLine__c", "sf_fieldLabel": "", "sf_childRelationshipName": "SBQQ__LineItems__r", "cp_fieldName": "QuoteLineOthersT", "cp_fieldType": "table", "isCompositeMap": false, "sf_sortorder_field": "", "sf_sortorder_direction": "", "relatedDataMapping": [{"configId": "bNc0WxgwPwunhU4LmJ", "isConstant": "false", "cp_fieldName": "ProductCategoryOtherT", "cp_fieldLabel": "Product Category", "sf_fieldName": "product_category__c", "sf_fieldLabel": "Product Category"}, {"configId": "3Ur8YW0IL1Ud8WJmG2", "isConstant": "false", "cp_fieldName": "QuoteGroupingOtherT", "cp_fieldLabel": "Quote Grouping", "sf_fieldName": "group_name__c", "sf_fieldLabel": "Group Name"}, {"configId": "lhdqWdppT5mc803F3A", "isConstant": "false", "cp_fieldName": "QuoteLineSKUOtherT", "cp_fieldLabel": "Quote Line SKU", "sf_fieldName": "sbqq__productcode__c", "sf_fieldLabel": "Product Code"}, {"configId": "e8KnXi4bJogzTW7tP2", "isConstant": "false", "cp_fieldName": "QuoteLineSKUDescriptionOtherT", "cp_fieldLabel": "Quote Line SKU Description", "sf_fieldName": "sbqq__productname__c", "sf_fieldLabel": "Product Name"}, {"configId": "5JSu6mv6yCSIJFwsE6", "isConstant": "false", "cp_fieldName": "QuoteLineSKUTypeOtherT", "cp_fieldLabel": "Quote Line SKU Type", "sf_fieldName": "sbcf_text_environment__c", "sf_fieldLabel": "Environment"}, {"configId": "KqMC1yRrUwm0QkYoi8", "isConstant": "false", "cp_fieldName": "QuoteItemTermOtherT", "cp_fieldLabel": "Quote <PERSON><PERSON>", "sf_fieldName": "ppw_initial_term_text__c", "sf_fieldLabel": "PPW Initial Term Text"}, {"configId": "IfX83gNJ7oRsYoryCP", "isConstant": false, "cp_fieldName": "QuoteLineAnnualListPriceOtherT", "cp_fieldLabel": "Quote Line Annual List Price", "sf_fieldName": "plp__c", "sf_fieldLabel": "Paperwork List Price"}, {"configId": "bYJo12EYwzYYMqn0VV", "isConstant": false, "cp_fieldName": "QuoteLineQuantityOtherT", "cp_fieldLabel": "Quote Line Quantity", "sf_fieldName": "sbqq__effectivequantity__c", "sf_fieldLabel": "Effective Quantity"}, {"configId": "zwmEBOthtsTNE1vTFA", "isConstant": false, "cp_fieldName": "QuoteLineGrossTotalOtherT", "cp_fieldLabel": "Quote Line Gross Total", "sf_fieldName": "plt__c", "sf_fieldLabel": "Paperwork List Total"}, {"configId": "nco1wtMZq1jITnH0bD", "isConstant": "false", "cp_fieldName": "QuoteLineDiscountPercentOtherT", "cp_fieldLabel": "Quote Line Discount %", "sf_fieldName": "sbqq__totaldiscountrate__c", "sf_fieldLabel": "Total Discount (%)"}, {"configId": "9896Wj5HBr0Q7ofLA3", "isConstant": false, "cp_fieldName": "QuoteLineDiscountAmountOtherT", "cp_fieldLabel": "Quote Line Discount Amount", "sf_fieldName": "pwda__c", "sf_fieldLabel": "Paperwork Discount Amount"}, {"configId": "O80SZI765SxEpcYrhO", "isConstant": "false", "cp_fieldName": "QuoteLineTotalOtherT", "cp_fieldLabel": "Quote Line Total", "sf_fieldName": "pgt__c", "sf_fieldLabel": "Paperwork Gross Total"}, {"configId": "UR4dGPdqAxWurv19sm", "isConstant": "false", "cp_fieldName": "ISOLineCurrencyOtherT", "cp_fieldLabel": "ISO Line Currency", "sf_fieldName": "currencyisocode", "sf_fieldLabel": "Currency ISO Code"}], "criteria": {"conditions": [{"configid": "m1pw133cpaudnjy1tfn", "id": 1, "key": "paperwork_section__c", "keyLabel": "Paperwork Section", "opr": "equals", "value": "Additional PaaS Components", "valueLabel": "Additional PaaS Components", "isValRequired": true, "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}], "action": "AND", "expression": 1}, "showFilter": false}, {"configId": "tCYObwmgWawa3Xcir6", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "currencyisocode", "sf_fieldLabel": "Currency ISO Code", "sf_childRelationshipName": "", "cp_fieldName": "CurrencyISOCode", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "FqgnDDpImJcU5PRqra", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "paperwork_sections__c", "sf_fieldLabel": "Paperwork Sections", "sf_childRelationshipName": "", "cp_fieldName": "PaperworkSection", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "EDggB0OqWi0AmVibTc", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_document_language__c", "sf_fieldLabel": "Territory of Sale", "sf_childRelationshipName": "", "cp_fieldName": "TerritoryOfSale", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "DXab7tJ1PVZZK9KWb2", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__type__c", "sf_fieldLabel": "Type", "sf_childRelationshipName": "", "cp_fieldName": "TypeNew", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "ZvmK5pdxb9pKvqy1uj", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "ma_ref__c", "sf_fieldLabel": "Reference to Negotiated Master Agreement", "sf_childRelationshipName": "", "cp_fieldName": "Ref2NegotiatedMANewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "m9EtsIWl9b3bC7bA25", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "opportunity_record_type__c", "sf_fieldLabel": "Opportunity Record Type", "sf_childRelationshipName": "", "cp_fieldName": "OpportunityRecordType", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "FDnOQxFQUvYHlHpgMR", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "platform__c", "sf_fieldLabel": "Platform", "sf_childRelationshipName": "", "cp_fieldName": "Platform", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "0Y6QB7eHRZDRBVi652", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "quote_line_groups__c", "sf_fieldLabel": "# Quote Line Groups", "sf_childRelationshipName": "", "cp_fieldName": "QuoteLineGroups", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "t1scluksYEsDHd8yIc", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "services_total__c", "sf_fieldLabel": "Services Total", "sf_childRelationshipName": "", "cp_fieldName": "ServicesTotal", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "Xp9V8EDqFZta2eQsJN", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "paas_components_total__c", "sf_fieldLabel": "PaaS Components Total", "sf_childRelationshipName": "", "cp_fieldName": "NetPaaS", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "dipDWMY5UuVkh7IUYH", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_order_grand_total__c", "sf_fieldLabel": "Order Grand Total", "sf_childRelationshipName": "", "cp_fieldName": "OrderFormGrandTotal", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "Kh5XGP2sJ6MjxQLI1d", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "paperwork_license_term__c", "sf_fieldLabel": "Paperwork License Term", "sf_childRelationshipName": "", "cp_fieldName": "LicenseTerm", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "qjUEUSauu5HXl74tqW", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_partner_account_name__c", "sf_fieldLabel": "Partner Account Name", "sf_childRelationshipName": "", "cp_fieldName": "PartnerAccountName", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "b8Vk25SK0pCljSMNU7", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_document_language__c", "sf_fieldLabel": "Territory of Sale", "sf_childRelationshipName": "", "cp_fieldName": "TerritoryOfSale", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "baPHiwWFpYZGJTErgz", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "show_additional_business_terms__c", "sf_fieldLabel": "Show Additional Business Terms", "sf_childRelationshipName": "", "cp_fieldName": "ShowABT", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "Jedj1D8OndtM9y1fg4", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "billing_frequency__c", "sf_fieldLabel": "Billing Frequency", "sf_childRelationshipName": "", "cp_fieldName": "InvoicingSchedule", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "6EBF0jJlvZyaAOCIq3", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "show_dynamic_contract_language__c", "sf_fieldLabel": "Show Dynamic Contract Language", "sf_childRelationshipName": "", "cp_fieldName": "ShowDynamicContractLanguage", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "qc8dwYZ5jklodP9WGB", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_license_terms__c", "sf_fieldLabel": "License Terms", "sf_childRelationshipName": "", "cp_fieldName": "LicenseTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "sS02HJOY7bFaOYDXNU", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_other_enterprise_terms__c", "sf_fieldLabel": "Other Enterprise Terms", "sf_childRelationshipName": "", "cp_fieldName": "OtherEnterpriseTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "U8EE2NKR9hjnZLwbo2", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_pricing_payment_and_invoice_terms__c", "sf_fieldLabel": "Pricing, Payment and Invoicing Terms", "sf_childRelationshipName": "", "cp_fieldName": "PricingAndInvoicingTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "5be32fiscet77lgFdL", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_technical_support_terms__c", "sf_fieldLabel": "Technical Support Terms", "sf_childRelationshipName": "", "cp_fieldName": "TechnicalSupportTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "f8YAvdn1u2LgIxXAcw", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "services_terms__c", "sf_fieldLabel": "Services Terms", "sf_childRelationshipName": "", "cp_fieldName": "ServicesTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "x2Jy5u6Y31hMMjZSBE", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "is_perpetual__c", "sf_fieldLabel": "is Perpetual", "sf_childRelationshipName": "", "cp_fieldName": "IsPerpetualNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "fl30OsmweqxWc781dM", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "send_to_rev_rec__c", "sf_fieldLabel": "Send to Rev Rec", "sf_childRelationshipName": "", "cp_fieldName": "RevRec", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "dgnuG4M5qZcNLXnRCD", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_pre_approval_of_travel_expenses__c", "sf_fieldLabel": "Pre-Approval of Travel Expenses", "sf_childRelationshipName": "", "cp_fieldName": "PreApprovalTravelExpensesNew", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "ixZGkqM4nRDfZI7liN", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_reimbursement_not_required__c", "sf_fieldLabel": "No Reimbursement of Travel Expenses", "sf_childRelationshipName": "", "cp_fieldName": "NoReimbursementTravelExpensesNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "qpgsYrS3VECeFEZ6zq", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "account_name__c", "sf_fieldLabel": "Account Name", "sf_childRelationshipName": "", "cp_fieldName": "Account<PERSON><PERSON>", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "cay4MQe51fHpTF6Jgb", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "number_of_days__c", "sf_fieldLabel": "Number of Days", "sf_childRelationshipName": "", "cp_fieldName": "Noofdays", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "MtuHinyxcK931wkxnL", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "CPAI_Template_Field__r.paperwork_section_headers__c", "sf_fieldLabel": "CPAI_Template_Field__r.Paperwork Section Headers", "sf_childRelationshipName": "", "cp_fieldName": "PaperworkSectionHeaderNew", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "5zwUyHkesfRkSXulke", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__startdate__c", "sf_fieldLabel": "Start Date", "sf_childRelationshipName": "", "cp_fieldName": "StartDate", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "bKDI8Ig2H4NpQSwbDa", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__enddate__c", "sf_fieldLabel": "End Date", "sf_childRelationshipName": "", "cp_fieldName": "EndDate", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "vXkMOluDQhiXfFKhzY", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "Amended_Quote__r.sbcf_document_quote_name__c", "sf_fieldLabel": "Amended_Quote__r.Document Quote Name", "sf_childRelationshipName": "", "cp_fieldName": "AmendedOrderForm", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "YB2Pvd7butPsxN5LuR", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_negotiated__c", "sf_fieldLabel": "Negotiated?", "sf_childRelationshipName": "", "cp_fieldName": "Negotiated", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "VShrW5Jaq81LWbebz7", "cp_fieldName": "ConsultingOnly", "cp_fieldType": "Custom", "isCompositeMap": false, "isConstant": false, "isReferenceMapping": true, "isRelatedMap": false, "mappingType": "fieldMapping", "sf_childRelationshipName": "", "sf_fieldLabel": "PaperWorkConsultingOnly", "sf_fieldName": "paperwork_consulting_only__c", "showFilter": false}, {"configId": "LphoZ321ylpH1ocNMV", "cp_fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cp_fieldType": "Custom", "isCompositeMap": false, "isConstant": false, "isReferenceMapping": true, "isRelatedMap": false, "mappingType": "fieldMapping", "sf_childRelationshipName": "", "sf_fieldLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sf_fieldName": "eduonly__c", "showFilter": false}, {"configId": "jcYbYvdq0ja7c0aMRe", "cp_fieldName": "ConsultingAndEducationOnly", "cp_fieldType": "Custom", "isCompositeMap": false, "isConstant": false, "isReferenceMapping": true, "isRelatedMap": false, "mappingType": "fieldMapping", "sf_childRelationshipName": "", "sf_fieldLabel": "ConsultingandEducationOnly", "sf_fieldName": "consulting_and_education_only__c", "showFilter": false}, {"configId": "6TkcenNLp4dQhUdRjc", "cp_fieldName": "QuoteLineConsultingAll", "cp_fieldType": "table", "criteria": {"conditions": [{"isConstant": true, "keyLabel": "PaperworkSection", "valueLabel": "Consulting", "configId": "qeScpyM5QsHHIIeb7B", "cssCls": "filter", "filterType": "STRING", "id": 1, "key": "paperwork_section__c", "keySrc": "sfdc", "opr": "equals", "value": "Consulting", "valueSrc": "constant"}], "action": "AND", "expression": "1"}, "isCompositeMap": false, "isConstant": false, "isReferenceMapping": false, "isRelatedMap": true, "mappingType": "relatedDataMapping", "relatedDataMapping": [{"configId": "0pj9YOaI5wSpGS6f2f", "cp_fieldLabel": "QuoteGrouping", "cp_fieldName": "QuoteGroupingConAll", "isConstant": false, "sf_fieldLabel": "GroupName", "sf_fieldName": "group_name__c"}, {"configId": "kjDGNCyDV0TabAyhgD", "cp_fieldLabel": "QuoteLineSKU", "cp_fieldName": "QuoteLineSKUConAll", "isConstant": false, "sf_fieldLabel": "ProductCode", "sf_fieldName": "sbqq__productcode__c"}, {"configId": "8xWcFVqVWevj5qAqaS", "cp_fieldLabel": "QuoteLineSKUDescription", "cp_fieldName": "QuoteLineSKUDescriptionConAll", "isConstant": false, "sf_fieldLabel": "ProductName", "sf_fieldName": "sbqq__productname__c"}, {"configId": "NzMPv0ePTbOtYO0kxj", "cp_fieldLabel": "QuoteLineInterval", "cp_fieldName": "QuoteLineIntervalConAll", "isConstant": false, "sf_fieldLabel": "Interval", "sf_fieldName": "sbcf_text_interval__c"}, {"configId": "mRcK4To9FYe2SoGZRl", "cp_fieldLabel": "QuoteLineListPrice", "cp_fieldName": "QuoteLineListPriceConAll", "isConstant": false, "sf_fieldLabel": "PaperworkListPrice", "sf_fieldName": "plp__c"}, {"configId": "FEkyNEWNbQGKC9peRo", "cp_fieldLabel": "QuoteLineQuantity", "cp_fieldName": "QuoteLineQuantityConAll", "isConstant": false, "sf_fieldLabel": "Effective Quantity", "sf_fieldName": "sbqq__effectivequantity__c"}, {"configId": "qHKq378aFv79XcrGJl", "cp_fieldLabel": "QuoteLineGrossTotal", "cp_fieldName": "QuoteLineGrossTotalConAll", "isConstant": false, "sf_fieldLabel": "Paperwork Net List Price", "sf_fieldName": "pnp__c"}, {"configId": "E9kybVBb5QrijBO2vy", "cp_fieldLabel": "QuoteLineDiscount%", "cp_fieldName": "QuoteLineDiscountPercentConAll", "isConstant": false, "sf_fieldLabel": "TotalDiscount(%)", "sf_fieldName": "sbqq__totaldiscountrate__c"}, {"configId": "WLo74kjBslH7KIRJdn", "cp_fieldLabel": "QuoteLineTotal", "cp_fieldName": "QuoteLineTotalConAll", "isConstant": false, "sf_fieldLabel": "Paperwork Gross Total", "sf_fieldName": "pgt__c"}, {"configId": "S3tEjNA2kbpomXfGNZ", "cp_fieldLabel": "ISOLineCurrency", "cp_fieldName": "ISOLineCurrencyConAll", "isConstant": false, "sf_fieldLabel": "CurrencyISOCode", "sf_fieldName": "currencyisocode"}], "sf_childRelationshipName": "SBQQ__LineItems__r", "sf_fieldLabel": "", "sf_fieldName": "SBQQ__QuoteLine__c", "sf_sortorder_direction": "", "sf_sortorder_field": "", "showFilter": false}, {"configId": "d1IIVQ6MP6i62AHBqp", "cp_fieldName": "QuoteLineEducationAll", "cp_fieldType": "table", "criteria": {"conditions": [{"isConstant": true, "keyLabel": "PaperworkSection", "valueLabel": "Education", "configId": "Xd4nIkBnz3LiRifA0F", "cssCls": "filter", "filterType": "STRING", "id": 1, "key": "paperwork_section__c", "keySrc": "sfdc", "opr": "equals", "value": "Education", "valueSrc": "constant"}], "action": "AND", "expression": "1"}, "isCompositeMap": false, "isConstant": false, "isReferenceMapping": false, "isRelatedMap": true, "mappingType": "relatedDataMapping", "relatedDataMapping": [{"configId": "6LkUXgTOMhLPkz9mmR", "cp_fieldLabel": "QuoteGrouping", "cp_fieldName": "QuoteGroupingEduAll", "isConstant": false, "sf_fieldLabel": "GroupName", "sf_fieldName": "group_name__c"}, {"configId": "D2oOoIHhppKJA2qg4p", "cp_fieldLabel": "QuoteLineSKU", "cp_fieldName": "QuoteLineSKUEduAll", "isConstant": false, "sf_fieldLabel": "ProductCode", "sf_fieldName": "sbqq__productcode__c"}, {"configId": "LVTQ5NR5gWfpbG4b74", "cp_fieldLabel": "QuoteLineSKUDescription", "cp_fieldName": "QuoteLineSKUDescriptionEduAll", "isConstant": false, "sf_fieldLabel": "ProductName", "sf_fieldName": "sbqq__productname__c"}, {"configId": "lIyqQ7gcYnooAcwOGH", "cp_fieldLabel": "QuoteLineInterval", "cp_fieldName": "QuoteLineIntervalEduAll", "isConstant": false, "sf_fieldLabel": "Interval", "sf_fieldName": "sbcf_text_interval__c"}, {"configId": "D4OkMvPGOL6AvnrpdT", "cp_fieldLabel": "QuoteLineListPrice", "cp_fieldName": "QuoteLineListPriceEduAll", "isConstant": false, "sf_fieldLabel": "PaperworkListPrice", "sf_fieldName": "plp__c"}, {"configId": "9Xkf2PxbihwnoWgaIW", "cp_fieldLabel": "QuoteLineQuantity", "cp_fieldName": "QuoteLineQuantityEduAll", "isConstant": false, "sf_fieldLabel": "Effective Quantity", "sf_fieldName": "sbqq__effectivequantity__c"}, {"configId": "wgEVu8gsIX6rIQDnuY", "cp_fieldLabel": "QuoteLineGrossTotal", "cp_fieldName": "QuoteLineGrossTotalEduAll", "isConstant": false, "sf_fieldLabel": "PaperworkNetListPrice", "sf_fieldName": "pnp__c"}, {"configId": "ivforBLjcEpEXW7KUk", "cp_fieldLabel": "QuoteLineDiscount%", "cp_fieldName": "QuoteLineDiscountPercentEduAll", "isConstant": false, "sf_fieldLabel": "TotalDiscount(%)", "sf_fieldName": "sbqq__totaldiscountrate__c"}, {"configId": "uU9f7bJt3vnjWf3Wk9", "cp_fieldLabel": "QuoteLineDiscountAmount", "cp_fieldName": "QuoteLineDiscountAmtEduAll", "isConstant": false, "sf_fieldLabel": "PaperworkDiscountAmount", "sf_fieldName": "pwda__c"}, {"configId": "17weRjUjX1xyJ8L02K", "cp_fieldLabel": "QuoteLineTotal", "cp_fieldName": "QuoteLineTotalEduAll", "isConstant": false, "sf_fieldLabel": "PaperworkGrossTotal", "sf_fieldName": "pgt__c"}, {"configId": "EfZjV35AvbGTEBqMW2", "cp_fieldLabel": "ISOLineCurrency", "cp_fieldName": "ISOLineCurrencyEduAll", "isConstant": false, "sf_fieldLabel": "CurrencyISOCode", "sf_fieldName": "currencyisocode"}, {"configId": "nrtAR47qixZMaVP3H1", "isConstant": false, "cp_fieldName": "StartDateEduAll", "cp_fieldLabel": "Start Date", "sf_fieldName": "sbcf_output_start_date__c", "sf_fieldLabel": "Output Start Date"}, {"configId": "c715jtXKPwWuxMm1hq", "isConstant": false, "cp_fieldName": "EndDateEduAll", "cp_fieldLabel": "End Date", "sf_fieldName": "sbcf_output_end_date__c", "sf_fieldLabel": "Output End Date"}], "sf_childRelationshipName": "SBQQ__LineItems__r", "sf_fieldLabel": "", "sf_fieldName": "SBQQ__QuoteLine__c", "sf_sortorder_direction": "", "sf_sortorder_field": "", "showFilter": false}, {"configId": "iUyorVGpB7X5qw6kTQ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "is_mce__c", "sf_fieldLabel": "is MCE", "sf_childRelationshipName": "", "cp_fieldName": "IsMCENewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "29wORXe1V3RLsk5yye", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_no_name_logo_rights__c", "sf_fieldLabel": "No Name/Logo Rights", "sf_childRelationshipName": "", "cp_fieldName": "NoNameLogoRightsNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "eZISDDg1RCqMRjsoxZ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "software_total_no_ti__c", "sf_fieldLabel": "Software Total No TI", "sf_childRelationshipName": "", "cp_fieldName": "SoftwareTotalNoTI", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "Iz1HlDyib5YQIt7BZ2", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "document_execution_requirement__c", "sf_fieldLabel": "Document Execution Requirement", "sf_childRelationshipName": "", "cp_fieldName": "DocumentExecutionRequirement", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "qZjXGguBCzPLuiFr6g", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "mstr_signs_first__c", "sf_fieldLabel": "MSTR Signs First", "sf_childRelationshipName": "", "cp_fieldName": "MSTRSignsFirst", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "5u18gwTd2zJdXwJirW", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "internal_comments__c", "sf_fieldLabel": "Internal Comments", "sf_childRelationshipName": "", "cp_fieldName": "InternalComments", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "FSM1WwMMyPZuhljldK", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "customer_signer_email__c", "sf_fieldLabel": "Customer Signer <PERSON><PERSON>", "sf_childRelationshipName": "", "cp_fieldName": "CustomerSignerEmail", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "NTSdwlwupODpvqV7Ss", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "legal_clarification_notes__c", "sf_fieldLabel": "Legal Clarification Notes", "sf_childRelationshipName": "", "cp_fieldName": "LegalClarificationNotes", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "lyKDMUKz3evxtnzbDd", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "show_additional_business_terms__c", "sf_fieldLabel": "Show Additional Business Terms", "sf_childRelationshipName": "", "cp_fieldName": "ShowABT", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "xULcfAADdkPBSD12jx", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "show_dynamic_contract_language__c", "sf_fieldLabel": "Show Dynamic Contract Language", "sf_childRelationshipName": "", "cp_fieldName": "ShowDynamicContractLanguage", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "9kkGo0V2B2GRDVNVra", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_cold_failover_disaster_recovery__c", "sf_fieldLabel": "Cold Failover Disaster Recovery Use", "sf_childRelationshipName": "", "cp_fieldName": "ColdFailoverDisasterRecoveryUseNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "cc0f6G7muWSDe83Oly", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_broaden_license_grant__c", "sf_fieldLabel": "License for Internal & External Bus Ops", "sf_childRelationshipName": "", "cp_fieldName": "LicenseIntExtBusOpsNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "TUTz3CzE6chiATNqD1", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_payment_from_receipt_of_invoice__c", "sf_fieldLabel": "Payment from Receipt of Invoice", "sf_childRelationshipName": "", "cp_fieldName": "PaymentFromRcptInvoiceNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "3ssWy6QXG43jIPiPjA", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_non_standard_payment_terms__c", "sf_fieldLabel": "Non-Standard Payment Terms < 120 Days", "sf_childRelationshipName": "", "cp_fieldName": "NonStdPayTermsLT120DaysNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "o47LniiNaRVDn6m8Zx", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "no_dispute_timeline__c", "sf_fieldLabel": "No Invoice Dispute Time Limitation", "sf_childRelationshipName": "", "cp_fieldName": "NoInvoiceDisputeTimeLimitNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "bvhwERJzEJk3D1AP5E", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_no_press_rights__c", "sf_fieldLabel": "No Press Release", "sf_childRelationshipName": "", "cp_fieldName": "NoPressReleaseNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "QwE3tvJR5VV8lXs3de", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "prior_order_incorporated_terms__c", "sf_fieldLabel": "Prior Order Incorporated Terms", "sf_childRelationshipName": "", "cp_fieldName": "PriorOrderIncorporatedTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "mYYB0JiiH4IVM3M96q", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "prepaid_consulting_terms__c", "sf_fieldLabel": "Prepaid Consulting Terms", "sf_childRelationshipName": "", "cp_fieldName": "PrepaidConsultingTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "dN95sdrSII4TeHe4Xy", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_legal_notes__c", "sf_fieldLabel": "Legal Notes", "sf_childRelationshipName": "", "cp_fieldName": "LegalNotes", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "NKf4ys0px28bJqqIk0", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "Reseller__r.name", "sf_fieldLabel": "Reseller__r.Account Name", "sf_childRelationshipName": "", "cp_fieldName": "Reseller", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "FKLRM4FUa65eolZJMf", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "SBQQ__Distributor__r.name", "sf_fieldLabel": "SBQQ__Distributor__r.Account Name", "sf_childRelationshipName": "", "cp_fieldName": "Distributor", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "QDQpg9XumtlVmIMOSb", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_type_of_sale__c", "sf_fieldLabel": "Type of Sale", "sf_childRelationshipName": "", "cp_fieldName": "TypeOfSale", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "3toRwbklNR4S1SOy0i", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_po_required__c", "sf_fieldLabel": "PO Required?", "sf_childRelationshipName": "", "cp_fieldName": "PORequired", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}]