/**
 * Created by ya<PERSON><PERSON><PERSON>chuiev on 26/12/2021.
 */

public with sharing class EntitlementContactTriggerHelper implements Queueable  {
    private static List<String> ACTIONS = new List<String>{
        'INSERT', 'DELETE'
    };
    private static List<String> FIELDS_FOR_DSI = new List<String>{
        'sys_id', 'u_contacts', 'sys_created_on'
    };
    public string action2EntContactsVar;
    public string queueableMethod;

    private static Map<Id, Contact> relatedContacts {
        get {
            if (relatedContacts == null) {
                Map<Id, Contact> conId2SNId = new Map<Id, Contact >();
                for (Contact c : [
                    SELECT Id,ServiceNow_Id__c,pse__Salesforce_User__c,Worker_Type__c
                    FROM Contact
                    WHERE Id IN :relatedContactIds
                ]) {
                    conId2SNId.put(c.Id, c);
                }
                relatedContacts = conId2SNId;
            }
            relatedContactIds = new Set<Id>();
            return relatedContacts;
        }
        set;
    }

    private static Map<Id, Entitlement> relatedEntitlements {
        get {
            if (relatedEntitlements == null) {
                return new Map<Id, Entitlement>([
                    SELECT Id, ServiceNow_Id__c, DSI__c,DSI__r.ServiceNow_Id__c
                    FROM Entitlement
                    WHERE Id IN :relatedEntitlementIds
                ]);
            }
            relatedEntitlementIds = new Set<Id>();
            return relatedEntitlements;
        }
        set;
    }

    private static Set<Id> relatedContactIds = new Set<Id>();
    private static Set<Id> relatedEntitlementIds = new Set<Id>();

    private static Set<FF_Integration_Log__c> failureLogList = new Set<FF_Integration_Log__c>();

    public static void updateUsersProfiles (Map<String, List<EntitlementContact>> action2EntContacts) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.updateUsersProfiles');
        // updateUsersProfilesFuture(JSON.serialize(action2EntContacts));
        System.enqueueJob(new EntitlementContactTriggerHelper(JSON.serialize(action2EntContacts), 'updateUsersProfilesFuture'));
    }

    //@Future
    public static void updateUsersProfilesFuture (String action2EntContactsJSON) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.updateUsersProfilesFuture');
        Map<String, List<EntitlementContact>> action2EntContacts = (Map<String, List<EntitlementContact>>) JSON.deserialize(action2EntContactsJSON, Map<String, List<EntitlementContact>>.class);

        System.debug('YC Debug-action2EntContacts-' + JSON.serialize(action2EntContacts));
        List<EntitlementContact> entConsToProcess = getEntConToProcess(action2EntContacts);

        String profileJSON = MSTR_Global_Configuation__mdt.getInstance('EntCon_AssignedProfiles').Value__c;
        ProfilesWrapper profilesWrapper = (ProfilesWrapper) JSON.deserialize(profileJSON, ProfilesWrapper.class);
        profilesWrapper.setIds();

        List<EntitlementContact> relatedEntConsForCons = [
            SELECT Id, ContactId
            FROM EntitlementContact
            WHERE ContactId IN :relatedContacts.keySet()
            AND Id NOT IN :entConsToProcess
        ];
        System.debug('YC Debug-relatedEntConsForCons-' + JSON.serialize(relatedEntConsForCons));

        Map<Id, List<EntitlementContact>> conId2AllEntCons = new Map<Id, List<EntitlementContact>>();

        Map<Id, User> contactId2RelatedUser = new Map<Id, User>();
        for (User u : [SELECT Id, ContactId,Contact.Worker_Type__c FROM User WHERE ContactId IN :relatedContacts.keySet()]) {
            contactId2RelatedUser.put(u.ContactId, u);
        }

        for (EntitlementContact entCon : relatedEntConsForCons) {
            if (conId2AllEntCons.containsKey(entCon.Id)) {
                conId2AllEntCons.get(entCon.ContactId).add(entCon);
            } else {
                conId2AllEntCons.put(entCon.ContactId, new List<EntitlementContact>{
                    entCon
                });
            }
        }

        System.debug('YC Debug-conId2AllEntCons-' + JSON.serialize(conId2AllEntCons));

        List<User> usersToUpdateProfile = new List<User>();

        for (String action : action2EntContacts.keySet()) {
            if (action == 'DELETE') {
                for (EntitlementContact entCon : action2EntContacts.get(action)) {
                    if (!conId2AllEntCons.containsKey(entCon.ContactId)) {//Iterated Contact is not used on any entitlement
                        User relatedUser = contactId2RelatedUser.get(entCon.ContactId);

                        if (relatedUser != null
                            && (String.isBlank(relatedUser.Contact.Worker_Type__c)
                            ||
                            (String.isNotBlank(relatedUser.Contact.Worker_Type__c)
                                && relatedUser.Contact.Worker_Type__c != 'Employee'))
                            ) {
                            usersToUpdateProfile.add(new User(Id = relatedUser.Id, ProfileId = profilesWrapper.deactivatedProfileId));
                        }
                    }
                }
            } else if (action == 'INSERT') {
                for (EntitlementContact entCon : action2EntContacts.get(action)) {
                    User relatedUser = contactId2RelatedUser.get(entCon.ContactId);

                    if (relatedUser != null
                        && (String.isBlank(relatedUser.Contact.Worker_Type__c)
                        ||
                        (String.isNotBlank(relatedUser.Contact.Worker_Type__c)
                            && relatedUser.Contact.Worker_Type__c != 'Employee'))
                        ) {
                        usersToUpdateProfile.add(new User(Id = relatedUser.Id, ProfileId = profilesWrapper.activeProfileId));
                    }
                }
            }
        }

        System.debug('YC Debug-usersToUpdateProfile-' + usersToUpdateProfile);
        if (!usersToUpdateProfile.isEmpty()) {
            update usersToUpdateProfile;
        }
    }

    public static void postToSN (Map<String, List<EntitlementContact>> action2EntContacts) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.postToSN');
        // executeCallouts(JSON.serialize(action2EntContacts));
        system.enqueueJob(new EntitlementContactTriggerHelper(JSON.serialize(action2EntContacts), 'executeCallouts'));
        
    }

    public EntitlementContactTriggerHelper(String action2EntContacts, String methodName) {
        this.action2EntContactsVar = action2EntContacts;
        this.queueableMethod = methodName;
    }

    public void execute(QueueableContext ctx) {
        if(this.queueableMethod == 'executeCallouts'){
            executeCallouts(JSON.serialize(this.action2EntContactsVar));
        } else if(this.queueableMethod == 'updateUsersProfilesFuture'){
            updateUsersProfilesFuture(JSON.serialize(this.action2EntContactsVar));
        }
    }

    // @Future(Callout=true)
    private static void executeCallouts (String action2EntContactsJSON) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.postToSN');

        Map<String, List<EntitlementContact>> action2EntContacts = (Map<String, List<EntitlementContact>>) JSON.deserialize(action2EntContactsJSON, Map<String, List<EntitlementContact>>.class);

        System.debug('YC Debug-action2EntContacts-' + JSON.serialize(action2EntContacts));

        updateDSIInServiceNow(action2EntContacts);
        updateEntitlementContactsInSN(action2EntContacts);
        if (!failureLogList.isEmpty() && !Test.isRunningTest()) {
            insert new List<FF_Integration_Log__c>(failureLogList);
        }

    }

    private static void updateEntitlementContactsInSN (Map<String, List<EntitlementContact>> action2EntContacts) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.updateEntitlementContactsInSN');

        List<EntitlementContact> entConsToProcess = getEntConToProcess(action2EntContacts);

        String query = generateFiltersForEntCon(entConsToProcess);
        if (String.isBlank(query)) {
            return;
        }

        String url = SN_IntegrationHelper.generateQuery(SN_IntegrationHelper.SN_ENT_CON_TABLE_NAME, query);
        String returnBody = SN_TableAPIController.executeTableAPIQuery(url);
        System.debug('YC Debug-Fetched Entitlement Contacts-' +
            SN_Wrappers.mapSNObjectFieldsToWrapper(returnBody, SN_Wrappers.SN_ENT_CON_FIELD_MAPPING)
        );

        SN_Wrappers.FetchResultEntConWrapper fetchResult = (
            (SN_Wrappers.FetchResultEntConWrapper) JSON.deserialize(
                SN_Wrappers.mapSNObjectFieldsToWrapper(returnBody, SN_Wrappers.SN_ENT_CON_FIELD_MAPPING),
                SN_Wrappers.FetchResultEntConWrapper.class
            )
        );

        if (fetchResult.status != 'Failure') {
            System.debug('YC Debug--' + JSON.serialize(fetchResult.result));
            updateSNSupports(fetchResult.result, action2EntContacts);
        }
    }

    private static void updateDSIInServiceNow (Map<String, List<EntitlementContact>> action2EntContacts) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.updateDSIInServiceNow');
        List<EntitlementContact> entConsToProcess = getEntConToProcess(action2EntContacts);

        SN_Wrappers.FetchResultDSIWrapper dsiFetchResult = fetchDSIFromServiceNow(entConsToProcess);
        System.debug('YC Debug-dsiFetchResult-' + JSON.serialize(dsiFetchResult));
        if (dsiFetchResult == null) {
            return ;
        }
        if (dsiFetchResult.result != null) {
            updateDSIsInServiceNow(action2EntContacts, dsiFetchResult);
        } else {
            failureLogList.add(SN_IntegrationHelper.logError(dsiFetchResult.error.message, dsiFetchResult.error.message.left(40)));
        }
    }

    private static String generateQueryForDSI (List<EntitlementContact> entConsToProcess) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.generateQueryForDSI');

        Set<String> relatedDsiSNowIds = new Set<String>();
        for (EntitlementContact entCon : entConsToProcess) {
            Entitlement ent = relatedEntitlements.get(entCon.EntitlementId);
            if (String.isNotBlank(ent.DSI__r.ServiceNow_Id__c)) {
                relatedDsiSNowIds.add(ent.DSI__r.ServiceNow_Id__c);
            } else {
                failureLogList.add(SN_IntegrationHelper.logError('Please check DSI ServiceNow Id', ent.DSI__c));
            }
        }

        String query = '';
        for (String dsiSNId : relatedDsiSNowIds) {
            query += 'sys_id=' + dsiSNId + SN_IntegrationHelper.COND_OR ;
        }
        query = query.removeEnd(SN_IntegrationHelper.COND_OR);
        return query;
    }

    private static SN_Wrappers.FetchResultDSIWrapper fetchDSIFromServiceNow (List<EntitlementContact> entConsToProcess) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.fetchDSIFromServiceNow');
        String dsiQuery = generateQueryForDSI(entConsToProcess);

        if (String.isBlank(dsiQuery)) {
            return null;
        }

        String url = SN_IntegrationHelper.generateQuery(SN_IntegrationHelper.SN_DSI_TABLE_NAME, dsiQuery, FIELDS_FOR_DSI, 'sys_created_on');
        String returnBody = SN_TableAPIController.executeTableAPIQuery(url);
        System.debug('YC Debug-returnBody-' + returnBody);
        return ((SN_Wrappers.FetchResultDSIWrapper) JSON.deserialize(returnBody, SN_Wrappers.FetchResultDSIWrapper.class));
    }

    private static void updateDSIsInServiceNow (Map<String, List<EntitlementContact>> action2EntContacts, SN_Wrappers.FetchResultDSIWrapper dsiFetchResult) {
        for (String action : action2EntContacts.keySet()) {
            if (action == 'DELETE') {
                for (SN_Wrappers.DSIWrapper dsiWrapper : dsiFetchResult.result) {
                    for (EntitlementContact entCon : action2EntContacts.get(action)) {
                        Entitlement relatedEnt = relatedEntitlements.get(entCon.EntitlementId);
                        Contact sfContact = relatedContacts.get(entCon.ContactId);

                        if (relatedEnt.DSI__r.ServiceNow_Id__c == dsiWrapper.sys_id) {
                            System.debug('YC Debug--' + dsiWrapper);
                            if (String.isNotBlank(sfContact.ServiceNow_Id__c)) {
                                if (dsiWrapper.u_contacts.contains(sfContact.ServiceNow_Id__c)) {
                                    dsiWrapper.u_contacts = dsiWrapper.u_contacts.remove(sfContact.ServiceNow_Id__c).replace(',,', ',').removeEnd(',');
                                }
                            } else {
                                failureLogList.add(SN_IntegrationHelper.logError('Please check Contact ServiceNow Id', sfContact.Id));
                                continue;
                            }
                        }

                    }
                }
            } else if (action == 'INSERT') {
                for (SN_Wrappers.DSIWrapper dsiWrapper : dsiFetchResult.result) {
                    for (EntitlementContact entCon : action2EntContacts.get(action)) {
                        Entitlement relatedEnt = relatedEntitlements.get(entCon.EntitlementId);
                        Contact sfContact = relatedContacts.get(entCon.ContactId);

                        if (relatedEnt.DSI__r.ServiceNow_Id__c == dsiWrapper.sys_id) {
                            System.debug('YC Debug--' + dsiWrapper);
                            if (String.isNotBlank(sfContact.ServiceNow_Id__c)) {
                                if (sfContact.ServiceNow_Id__c != null && !dsiWrapper.u_contacts.contains(sfContact.ServiceNow_Id__c)) {
                                    dsiWrapper.u_contacts += ',' + sfContact.ServiceNow_Id__c;
                                }
                            } else {
                                failureLogList.add(SN_IntegrationHelper.logError('Please check Contact ServiceNow Id', sfContact.Id));
                                continue;
                            }
                        }

                    }
                }
            }
        }

        for (SN_Wrappers.DSIWrapper dsiWrapper : dsiFetchResult.result) {
            String resp = SN_TableAPIController.executeTableAPIPut(
                SN_IntegrationHelper.SN_DSI_TABLE_NAME + '/' + dsiWrapper.sys_id,
                JSON.serialize(dsiWrapper));
            System.debug('YC Debug--' + resp);
        }

    }

    private static List<EntitlementContact> getEntConToProcess (Map<String, List<EntitlementContact>> action2EntContacts) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.getEntConToProcess');

        List<EntitlementContact> entitlementContacts = new List<EntitlementContact>();

        for (List<EntitlementContact> entContactList : action2EntContacts.values()) {
            for (EntitlementContact entCon : entContactList) {
                entitlementContacts.add(entCon);
            }
        }

        for (EntitlementContact entitlementContact : entitlementContacts) {
            relatedContactIds.add(entitlementContact.ContactId);
            relatedEntitlementIds.add(entitlementContact.EntitlementId);
        }
        return entitlementContacts;
    }


    private static String generateFiltersForEntCon (List<EntitlementContact> entConsToProcess) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.generateFiltersForEntCon');

        String query = '';
        for (EntitlementContact entCon : entConsToProcess) {
            Entitlement ent = relatedEntitlements.get(entCon.EntitlementId);

            if (String.isBlank(ent.ServiceNow_Id__c)) {
                failureLogList.add(SN_IntegrationHelper.logError('Please check Entitlement ServiceNow Id', ent.Id));
                continue;
            }

            if (String.isBlank(ent.DSI__r.ServiceNow_Id__c)) {
                failureLogList.add(SN_IntegrationHelper.logError('Please check DSI ServiceNow Id', ent.DSI__c));
                continue;
            }

            query += 'u_entitlemnt=' + ent.ServiceNow_Id__c +
                SN_IntegrationHelper.COND_OR + 'u_dsi=' + ent.DSI__r.ServiceNow_Id__c + SN_IntegrationHelper.COND_OR;
        }
        query = query.removeEnd(SN_IntegrationHelper.COND_OR);
        System.debug('YC Debug--' + query);
        return query;
    }

    private static void updateSNSupports (List<SN_Wrappers.EntConWrapper> fetchedEntCons, Map<String, List<EntitlementContact>> action2EntContacts) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.updateSNSupports');

        for (String action : ACTIONS) {
            if (action == 'DELETE') {
                //IF DSI u_contacts already contains that contact -> remove
                for (EntitlementContact entCon : action2EntContacts.get(action)) {
                    String entConServiceNowId = getEntConServiceNowId(entCon, fetchedEntCons);
                    if (String.isNotBlank(entConServiceNowId)) {
                        deleteEntConInServiceNow(entConServiceNowId);
                    }
                }

            } else if (action == 'INSERT') {
                //IF DSI u_contacts doesn't contain that contact -> add
                for (EntitlementContact entCon : action2EntContacts.get(action)) {
                    if (String.isBlank(getEntConServiceNowId(entCon, fetchedEntCons))) {
                        createEntConInServiceNow(entCon);
                    }
                }
            }
        }
    }

    private static String getEntConServiceNowId (EntitlementContact entCon, List<SN_Wrappers.EntConWrapper> fetchedEntCons) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.getEntConServiceNowId');
        String snId = null;

        for (SN_Wrappers.EntConWrapper entConWrapper : fetchedEntCons) {
            System.debug('YC Debug-entConWrapper-' + entConWrapper);
            Contact sfContact = relatedContacts.get(entCon.ContactId);
            Entitlement sfEntitlement = relatedEntitlements.get(entCon.EntitlementId);

            if (sfContact.ServiceNow_Id__c == entConWrapper.u_contact.value
                && sfEntitlement.ServiceNow_Id__c == entConWrapper.u_entitlemnt.value
                && sfEntitlement.DSI__r.ServiceNow_Id__c == entConWrapper.u_dsi.value
                ) {
                System.debug('YC Debug--' + 'found match');
                snId = entConWrapper.sys_id;
                break;
            }
        }
        return snId;
    }

    private static void createEntConInServiceNow (EntitlementContact entitlementContact) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.createEntConInServiceNow');

        try {
            String url = SN_IntegrationHelper.SN_ENT_CON_TABLE_NAME;
            Contact sfContact = relatedContacts.get(entitlementContact.ContactId);
            Entitlement sfEntitlement = relatedEntitlements.get(entitlementContact.EntitlementId);

            SN_Wrappers.EntConInsertWrapper entConWrapper = new SN_Wrappers.EntConInsertWrapper();

            if (String.isBlank(sfEntitlement.DSI__r.ServiceNow_Id__c)) {
                failureLogList.add(SN_IntegrationHelper.logError('Please check DSI ServiceNow Id', sfEntitlement.DSI__c));
                return;
            } else if (String.isBlank(sfEntitlement.ServiceNow_Id__c)) {
                failureLogList.add(SN_IntegrationHelper.logError('Please check Entitlement ServiceNow Id', sfEntitlement.Id));
                return;
            } else if (String.isBlank(sfContact.ServiceNow_Id__c)) {
                failureLogList.add(SN_IntegrationHelper.logError('Please check Contact ServiceNow Id', sfContact.Id));
                return;
            }

            System.debug('YC Debug-entitlementContact.Id-' + entitlementContact.Id);
            entConWrapper.u_sf_record_id = entitlementContact.Id;
            entConWrapper.u_dsi = sfEntitlement.DSI__r.ServiceNow_Id__c;
            entConWrapper.u_entitlemnt = sfEntitlement.ServiceNow_Id__c;
            entConWrapper.u_contact = sfContact.ServiceNow_Id__c;

            System.debug('YC Debug-entConWrapper-' + JSON.serialize(entConWrapper));
            String response = SN_TableAPIController.executeTableAPIPost(url, JSON.serialize(entConWrapper));
            System.debug('YC Debug-response-' + response);

        } catch (CalloutException e) {
            failureLogList.add(SN_IntegrationHelper.logError(e.getMessage(), e.getMessage().left(40)));
        }

    }

    private static void deleteEntConInServiceNow (String entConServiceNowId) {
        System.debug(LoggingLevel.INFO, 'EntitlementContactTriggerHelper.deleteEntConInServiceNow');

        try {
            String url = SN_IntegrationHelper.SN_ENT_CON_TABLE_NAME + '/' + entConServiceNowId;
            String response = SN_TableAPIController.executeTableAPIDelete(url);
        } catch (CalloutException e) {
            failureLogList.add(SN_IntegrationHelper.logError(e.getMessage(), e.getMessage().left(40)));
        }

    }

    public class ProfilesWrapper {
        public String activeProfileName;
        public String deactivatedProfileName;
        public String activeProfileId;
        public String deactivatedProfileId;

        void setIds () {
            for (Profile p : [SELECT Id,Name FROM Profile WHERE Name = :activeProfileName or Name = :deactivatedProfileName]) {
                if (p.Name == activeProfileName) {
                    activeProfileId = p.Id;
                } else if (p.Name == deactivatedProfileName) {
                    deactivatedProfileId = p.Id;
                }
            }
        }
    }
}