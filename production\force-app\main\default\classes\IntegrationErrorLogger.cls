/**
 * @description Utility class to prepare integration errors and debug information
 * for asynchronous logging. All DML operations are deferred to LogPersisterQueueable.
 * Logs are grouped by a parent Transaction_Log__c.
 * Declared as 'without sharing' for context, though DML is handled by Queueable.
 */
public without sharing class IntegrationErrorLogger {

    // Static list to hold child logs in memory
    private static List<Integration_Error_Log__c> logsToProcessQueue = new List<Integration_Error_Log__c>();
    // Static variable to hold the current parent Transaction_Log__c SObject (in memory)
    private static Transaction_Log__c currentTransactionLogSObject;

    // Default values
    public static final String DEFAULT_ERROR_SEVERITY = 'Error';
    public static final String DEFAULT_DEBUG_SEVERITY = 'Debug';
    public static final String DEFAULT_INFO_SEVERITY  = 'Info';
    public static final String DEFAULT_STATUS         = 'New'; // For child logs

    // Log Types
    public static final String LOG_TYPE_ERROR = 'Error';
    public static final String LOG_TYPE_DEBUG = 'Debug';
    public static final String LOG_TYPE_INFO  = 'Info';

    // Transaction Log Statuses
    public static final String TX_STATUS_IN_PROGRESS = 'In Progress';
    public static final String TX_STATUS_COMPLETED = 'Completed';
    public static final String TX_STATUS_ERROR = 'Error';
    public static final String TX_STATUS_COMPLETED_WITH_ERRORS = 'Completed with Errors';

    /**
     * @description Initializes a new transaction log SObject in memory.
     * Does NOT insert it. This should be called at the beginning of a logical transaction.
     * @param apexClassName The name of the primary Apex class initiating the transaction.
     * @param apexMethodName The name of the primary Apex method initiating the transaction.
     * @param initialContext Optional: A string providing initial context for the transaction.
     */
    public static void initializeTransactionLog(String apexClassName, String apexMethodName, String initialContext) {
        // If a transaction is already initialized (e.g. from a previous unfinalized call in same static context),
        // log a warning and overwrite. Ideally, finalizeTransactionLog should always be called.
        if (currentTransactionLogSObject != null) {
            System.debug(LoggingLevel.WARN, 
                'IntegrationErrorLogger.initializeTransactionLog: Overwriting an existing unfinalized transaction log for class ' + 
                currentTransactionLogSObject.Apex_Class_Name__c + '. Ensure finalizeTransactionLog is always called.');
        }

        currentTransactionLogSObject = new Transaction_Log__c(
            Apex_Class_Name__c = apexClassName,
            Apex_Method_Name__c = apexMethodName,
            User__c = UserInfo.getUserId(),
            Start_Time__c = System.now(),
            Status__c = TX_STATUS_IN_PROGRESS, // Initial status
            Initial_Context__c = initialContext,
            Error_Count__c = 0,
            Debug_Log_Count__c = 0,
            Info_Log_Count__c = 0
        );
        // Clear any previous child logs from an unfinalized transaction
        if(!logsToProcessQueue.isEmpty()){
            System.debug(LoggingLevel.WARN, 
                'IntegrationErrorLogger.initializeTransactionLog: Clearing ' + logsToProcessQueue.size() + 
                ' uncommitted child logs from a previous context.');
            logsToProcessQueue.clear();
        }
    }

    /**
     * @description Adds a detailed log entry to an in-memory queue.
     * The child log will NOT have its parent Transaction_Log__c ID set at this point.
     * (Parameters are similar to the previous queueLog)
     */
    public static void queueLog(
        String logType, String severity, String integrationName, String integrationPoint,
        String apexClassName, String apexMethodName, String message,
        String errorCode, String stackTrace, String requestEndpoint, String requestMethod,
        String requestHeaders, String requestBody, Integer responseStatusCode,
        String responseHeaders, String responseBody, Id relatedRecordId, String relatedRecordObject
    ) {
        if (currentTransactionLogSObject == null) {
            System.debug(LoggingLevel.WARN,
                'IntegrationErrorLogger.queueLog: Attempted to queue a log before initializeTransactionLog was called for ' +
                apexClassName + '.' + apexMethodName + '. This log will not be associated with a parent transaction.');
        }

        try {
            Integration_Error_Log__c logEntry = new Integration_Error_Log__c();
            // Transaction_Log__c will be set by the Queueable job after parent insertion.
            logEntry.Timestamp__c = System.now();
            logEntry.Log_Type__c = String.isBlank(logType) ? LOG_TYPE_INFO : logType;
            logEntry.Severity__c = severity;
            logEntry.Integration_Name__c = integrationName;
            logEntry.Integration_Point__c = integrationPoint;
            logEntry.Apex_Class__c = apexClassName;
            logEntry.Apex_Method__c = apexMethodName;
            logEntry.Log_Message__c = message;
            logEntry.Error_Code__c = errorCode;
            logEntry.Error_Message__c = (logType == LOG_TYPE_ERROR && String.isNotBlank(errorCode)) ? message : null;
            logEntry.Stack_Trace__c = stackTrace;
            logEntry.Request_Endpoint__c = requestEndpoint;
            logEntry.Request_Method__c = requestMethod;
            logEntry.Request_Headers__c = requestHeaders;
            logEntry.Request_Body__c = requestBody;
            logEntry.Response_Status_Code__c = responseStatusCode;
            logEntry.Response_Headers__c = responseHeaders;
            logEntry.Response_Body__c = responseBody;
            logEntry.Related_Record_ID__c = relatedRecordId != null ? String.valueOf(relatedRecordId) : null;
            logEntry.Related_Record_Object__c = relatedRecordObject;
            logEntry.User__c = UserInfo.getUserId();
            logEntry.Status__c = DEFAULT_STATUS;

            logsToProcessQueue.add(logEntry);

            // Update counts on the in-memory parent log SObject
            if (currentTransactionLogSObject != null) {
                if (logType == LOG_TYPE_ERROR) {
                    currentTransactionLogSObject.Error_Count__c = (currentTransactionLogSObject.Error_Count__c == null ? 0 : currentTransactionLogSObject.Error_Count__c) + 1;
                } else if (logType == LOG_TYPE_DEBUG) {
                    currentTransactionLogSObject.Debug_Log_Count__c = (currentTransactionLogSObject.Debug_Log_Count__c == null ? 0 : currentTransactionLogSObject.Debug_Log_Count__c) + 1;
                } else if (logType == LOG_TYPE_INFO) {
                    currentTransactionLogSObject.Info_Log_Count__c = (currentTransactionLogSObject.Info_Log_Count__c == null ? 0 : currentTransactionLogSObject.Info_Log_Count__c) + 1;
                }
            }
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR,
                'IntegrationErrorLogger.queueLog: Exception during log SObject preparation. ' +
                'Message: ' + e.getMessage() + '. Stacktrace: ' + e.getStackTraceString());
        }
    }

    /**
     * @description Finalizes the current transaction logging process.
     * This method enqueues a LogPersisterQueueable job with the prepared parent transaction log
     * (updated with end time, final status, and counts) and all queued child logs.
     * Clears the current transaction context.
     * This should be called at the very end of a logical transaction, ideally in a finally block.
     * @param finalStatus The final status for the Transaction_Log__c (e.g., IntegrationErrorLogger.TX_STATUS_COMPLETED).
     */
    public static void finalizeTransactionLog(String finalStatus) {
        if (currentTransactionLogSObject == null) {
            System.debug(LoggingLevel.WARN, 'IntegrationErrorLogger.finalizeTransactionLog: Called without an initialized transaction log. No action taken for parent log.');
            // If there are orphaned child logs, we might still want to commit them, or clear them.
            if (!logsToProcessQueue.isEmpty()) {
                 System.debug(LoggingLevel.WARN, 'IntegrationErrorLogger.finalizeTransactionLog: Found ' + logsToProcessQueue.size() + ' child logs without an initialized parent. These will NOT be committed.');
                 logsToProcessQueue.clear(); // Clear orphaned logs
            }
            return;
        }

        try {
            // Update the in-memory parent log with final details
            currentTransactionLogSObject.End_Time__c = System.now();
            currentTransactionLogSObject.Status__c = finalStatus;

            // Prepare data for the queueable job
            // Create a new list for child logs to avoid issues with static list modification if queueable is delayed.
            List<Integration_Error_Log__c> logsForJob = new List<Integration_Error_Log__c>(logsToProcessQueue);
            Transaction_Log__c parentLogForJob = currentTransactionLogSObject.clone(true, true, true, true); // Deep clone

            // Enqueue the job
            System.enqueueJob(new LogPersisterQueueable(parentLogForJob, logsForJob));

        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR,
                'IntegrationErrorLogger.finalizeTransactionLog: Failed to enqueue LogPersisterQueueable job. ' +
                'Error: ' + e.getMessage() + '. Logs for transaction starting with class ' +
                (currentTransactionLogSObject != null ? currentTransactionLogSObject.Apex_Class_Name__c : 'N/A') +
                ' might not be persisted.'
            );
        } finally {
            // Always clear the static context for the next transaction, regardless of enqueue success.
            currentTransactionLogSObject = null;
            logsToProcessQueue.clear();
        }
    }

    // --- Logging Helper Methods (Debug, Info, Error) ---
    // These all call queueLog.

    public static void logDebug(String apexClassName, String apexMethodName, String message) {
        queueLog(LOG_TYPE_DEBUG, DEFAULT_DEBUG_SEVERITY, null, null, apexClassName, apexMethodName, message, null, null, null, null, null, null, null, null, null, null, null);
    }
    public static void logDebug(String integrationName, String integrationPoint, String apexClassName, String apexMethodName, String message) {
        queueLog(LOG_TYPE_DEBUG, DEFAULT_DEBUG_SEVERITY, integrationName, integrationPoint, apexClassName, apexMethodName, message, null, null, null, null, null, null, null, null, null, null, null);
    }
    public static void logDebug(String apexClassName, String apexMethodName, String message, String requestBody, String responseBody, Id relatedRecordId) {
        String relatedObjType = relatedRecordId != null ? String.valueOf(relatedRecordId.getSObjectType()) : null;
        queueLog(LOG_TYPE_DEBUG, DEFAULT_DEBUG_SEVERITY, null, null, apexClassName, apexMethodName, message, null, null, null, null, null, requestBody, null, null, responseBody, relatedRecordId, relatedObjType);
    }
    public static void logInfo(String apexClassName, String apexMethodName, String message) {
        queueLog(LOG_TYPE_INFO, DEFAULT_INFO_SEVERITY, null, null, apexClassName, apexMethodName, message, null, null, null, null, null, null, null, null, null, null, null);
    }
    public static void logInfo(String integrationName, String integrationPoint, String apexClassName, String apexMethodName, String message) {
        queueLog(LOG_TYPE_INFO, DEFAULT_INFO_SEVERITY, integrationName, integrationPoint, apexClassName, apexMethodName, message, null, null, null, null, null, null, null, null, null, null, null);
    }
    public static void logError(String integrationName, String integrationPoint, String apexClassName, String apexMethodName, String severity, String errorCode, String errorMessage, String stackTrace, String requestEndpoint, String requestMethod, String requestHeaders, String requestBody, Integer responseStatusCode, String responseHeaders, String responseBody, Id relatedRecordId, String relatedRecordObject) {
        queueLog(LOG_TYPE_ERROR, (String.isBlank(severity) ? DEFAULT_ERROR_SEVERITY : severity), integrationName, integrationPoint, apexClassName, apexMethodName, errorMessage, errorCode, stackTrace, requestEndpoint, requestMethod, requestHeaders, requestBody, responseStatusCode, responseHeaders, responseBody, relatedRecordId, relatedRecordObject);
    }
    public static void logError(Exception ex, String integrationName, String integrationPoint, String apexClassName, String apexMethodName, HttpRequest req, HttpResponse res, Id relatedRecordId) {
        String errorMessageVal = ex != null ? ex.getMessage() : 'No exception message.';
        String stackTraceVal = ex != null ? ex.getStackTraceString() : null;
        String reqEndpoint = req != null ? req.getEndpoint() : null;
        String reqMethod = req != null ? req.getMethod() : null;
        String reqHeadersVal = null; String reqBodyVal = req != null ? req.getBody() : null;
        Integer resStatusCodeVal = res != null ? res.getStatusCode() : null;
        String resHeadersVal = null; String resBodyVal = res != null ? res.getBody() : null;
        String errorCodeVal = res != null ? String.valueOf(res.getStatusCode()) : (ex != null ? ex.getTypeName() : null);
        String relatedObjType = relatedRecordId != null ? String.valueOf(relatedRecordId.getSObjectType()) : null;
        queueLog(LOG_TYPE_ERROR, DEFAULT_ERROR_SEVERITY, integrationName, integrationPoint, apexClassName, apexMethodName, errorMessageVal, errorCodeVal, stackTraceVal, reqEndpoint, reqMethod, reqHeadersVal, reqBodyVal, resStatusCodeVal, resHeadersVal, resBodyVal, relatedRecordId, relatedObjType);
    }
    public static void logError(Exception ex, String integrationName, String integrationPoint, String apexClassName, String apexMethodName, Id relatedRecordId) {
        logError(ex, integrationName, integrationPoint, apexClassName, apexMethodName, null, null, relatedRecordId);
    }
    public static void logError(String integrationName, String errorMessage, String severity, String apexClassName, String apexMethodName) {
        queueLog(LOG_TYPE_ERROR, (String.isBlank(severity) ? DEFAULT_ERROR_SEVERITY : severity), integrationName, null, apexClassName, apexMethodName, errorMessage, null, null, null, null, null, null, null, null, null, null, null);
    }
    public static void logError(Exception ex, String integrationName, String apexClassName, String apexMethodName) {
        String errorMessageVal = ex != null ? ex.getMessage() : 'No exception message.';
        String stackTraceVal = ex != null ? ex.getStackTraceString() : null;
        String errorCodeVal = ex != null ? ex.getTypeName() : null;
        queueLog(LOG_TYPE_ERROR, DEFAULT_ERROR_SEVERITY, integrationName, null, apexClassName, apexMethodName, errorMessageVal, errorCodeVal, stackTraceVal, null, null, null, null, null, null, null, null, null);
    }
    public static void logError(String integrationName, String integrationPoint, String apexClassName, String apexMethodName, HttpRequest req, HttpResponse res, String errorMessage, Id relatedRecordId) {
        String reqEndpoint = req != null ? req.getEndpoint() : null;
        String reqMethod = req != null ? req.getMethod() : null;
        String reqHeadersVal = null; String reqBodyVal = req != null ? req.getBody() : null;
        Integer resStatusCodeVal = res != null ? res.getStatusCode() : null;
        String resHeadersVal = null; String resBodyVal = res != null ? res.getBody() : null;
        String finalErrorMessage = errorMessage;
        if (String.isBlank(finalErrorMessage) && res != null && res.getStatusCode() >= 400) {
            finalErrorMessage = 'HTTP Error: ' + res.getStatusCode() + ' - ' + res.getStatus();
            if (String.isNotBlank(res.getBody())) { finalErrorMessage += '. Response: ' + (res.getBody().length() > 500 ? res.getBody().substring(0, 500) + '...' : res.getBody()); }
        } else if (String.isBlank(finalErrorMessage)) { finalErrorMessage = 'Callout completed, but an unspecified error was logged.'; }
        String errorCodeVal = res != null ? String.valueOf(res.getStatusCode()) : null;
        String relatedObjType = relatedRecordId != null ? String.valueOf(relatedRecordId.getSObjectType()) : null;
        queueLog(LOG_TYPE_ERROR, DEFAULT_ERROR_SEVERITY, integrationName, integrationPoint, apexClassName, apexMethodName, finalErrorMessage, errorCodeVal, null, reqEndpoint, reqMethod, reqHeadersVal, reqBodyVal, resStatusCodeVal, resHeadersVal, resBodyVal, relatedRecordId, relatedObjType);
    }
}