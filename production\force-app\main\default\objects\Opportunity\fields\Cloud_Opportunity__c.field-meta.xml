<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Cloud_Opportunity__c</fullName>
    <formula>IF(
CASE( SBQQ__PrimaryQuote__r.Platform__c , 
&apos;MCE&apos;, &apos;true&apos;, &apos;CMC&apos;, &apos;true&apos;, &apos;MCS&apos;, &apos;true&apos;,
&apos;false&apos;) 
= &apos;true&apos;, true, false)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>Cloud Opportunity?</label>
    <trackHistory>false</trackHistory>
    <type>Checkbox</type>
</CustomField>
