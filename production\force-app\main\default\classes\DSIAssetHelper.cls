/************************************* MODIFICATION LOG ********************************************************************************************
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                 MODIFICATION DATE               REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Priyank                   06/17/2015                      Case 381435 - CPQ - Renewals - Enable the translation of DSIs by the SOP team
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Test Class - DSIAssetHelperTest
*/

public class DSIAssetHelper {
    
    public static List < Asset > getDSIAssets(string dsiID, Boolean standardToEnh, Boolean isTranslation) {
        String qry = 'SELECT'+
            '                   ID,'+
            '                   Name,'+
            '                   Quantity,'+
            '                   Key_Group__c,'+
            '                   Version__c,'+
            '                   Product2.ProductCode,'+
            '                   Product2.Previous_SKU__r.ProductCode,'+
            '                   SBQQ__SubscriptionEndDate__c,'+
            '                   End_Date__c,'+
            '                   SBQQ__SubscriptionStartDate__c,'+
            '                   Previous_MHz__c,'+
            '                   Product2.Name,'+
            '                   Product2.Id,'+
            '                   Product2.cpuRestriction__c,'+
            '                   Product2.RMS_Product_ID__c,'+
            '                   Product2.SKU_Category_ID__c,'+
            '                   Product2.Version_ID__c,'+
            '                   Product2.Variation_Type__c,'+
            '                   Product2.Mhz__c,'+
            '                   Product2.NU_Quantity__c,'+
            '                   Product2.SKU_Type_ID__c,'+
            '                   Product2.isBundle__c,'+
            '                   Product2.Software_Type_ID__c,'+
            '                   Product2.Clustering__c,'+
            '                   Product2.BaseProductID__c,'+
            '                   Product2.Variation_ID__c,'+
            '                   Product2.Previous_SKU__c,'+
            '                   Product2.Interval__c,'+
            '                   Product2.Environment__c,'+
            '                   Product2.Version__c,'+
            '                   Product2.Key_Feature__c,'+
            '                   Product2.LegacyCatalog__c,'+
            '                   Key_Group_ID__c,'+
            '                   Quote_ID__c,'+
            '                   CreatedDate,'+
            '                   LastModifiedDate,'+
            '                   Key_Group_multi__c,'+
            '                   Exception_multi__c,'+
            '                   Aggregation_Comments__c,'+
            '                   SBQQ__CurrentSubscription__c,'+
            '                   SBQQ__Currentsubscription__r.Name,'+ 
            '                   SBQQ__Currentsubscription__r.SBQQ__Contract__r.Contractnumber,'+ 
            '                   ParentId '+
            '                   FROM Asset where SBCF_DSI__c =: dsiID'+
            '                   AND Product2.ProductCode NOT IN (\'TXINC\',\'ENVINC\')'+
            '                   AND Product2.Family NOT IN (\'Consulting\',\'Other Consulting\',\'Consulting Expense\',\'Education\')'+
            '                   AND Product2.Product_Category__c != \'Service\'' +
            '                   AND Product2.Interval__c NOT IN (\'Negotiated\')'+
            '                   AND Key_Group_multi__c!=\'\''+
            '                   AND SBCF_DSI__c!=NULL ' +
            '                   AND Exception_multi__c excludes (\'Retired\') ';		
        
        if(standardToEnh){
            qry += ' AND Translated__c = true';
        }
        
        if(isTranslation){
            qry += ' AND Exception_multi__c excludes (\'Terminated\',\'Converted\',\'Non Renewed\',\'Retired\') '; //Case 381435
            //qry += ' AND (Exception_multi__c includes (\'Multi DSI Use\') AND (SBQQ__SubscriptionStartDate__c != NULL or SBQQ__SubscriptionEndDate__c != NULL or SBQQ__CurrentSubscription__c != NULL )) ' ;//Case 381435
        }
        
        /*
* remove because we have to use it as a part of key checks
* else{
qry += ' AND Key_Group_multi__c excludes (\'N/A\')';
}*/
        if(!standardToEnh && isTranslation){
            qry += ' AND Product2.ExcludeFromTranslation__c = false';
        }
        qry+=' ORDER BY CreatedDate DESC';
        
        return DataBase.Query(qry);
        /*
return [SELECT
ID,
Name,
Quantity,
Key_Group__c,
Version__c,
Product2.ProductCode,
Product2.Previous_SKU__r.ProductCode,
SBQQ__SubscriptionEndDate__c,
End_Date__c,
SBQQ__SubscriptionStartDate__c,
Previous_MHz__c,
Product2.Name,
Product2.Id,
Product2.cpuRestriction__c,
Product2.RMS_Product_ID__c,
Product2.SKU_Category_ID__c,
Product2.Version_ID__c,
Product2.Variation_Type__c,
Product2.Mhz__c,
Product2.NU_Quantity__c,
Product2.SKU_Type_ID__c,
Product2.isBundle__c,
Product2.Software_Type_ID__c,
Product2.Clustering__c,
Product2.BaseProductID__c,
Product2.Variation_ID__c,
Product2.Previous_SKU__c,
Product2.Interval__c,
Product2.Environment__c,
Product2.Version__c,
Product2.Key_Feature__c,
Key_Group_ID__c,
Quote_ID__c,
CreatedDate,
LastModifiedDate,
Key_Group_multi__c,
Exception_multi__c,
Aggregation_Comments__c,
SBQQ__CurrentSubscription__c,
ParentId

FROM Asset where SBCF_DSI__c =: dsiID
AND Product2.ProductCode NOT IN ('TXINC','ENVINC')
AND Product2.Family NOT IN ('Consulting','Other Consulting')
AND Product2.Interval__c NOT IN ('Negotiated')
AND Key_Group_multi__c!=''
AND Key_Group_multi__c excludes ('N/A')
AND SBCF_DSI__c!=NULL
ORDER BY CreatedDate DESC
];
*/
    }
    /*
public static Integer getMaxVersion(List < Asset > assets) {
decimal maxVersion = 0;
for(Asset  asst: assets){
maxVersion = (asst.Version__c != null && asst.Version__c > maxVersion ) ?  asst.Version__c : maxVersion;
}
return (Integer)maxVersion;
}


public static integer getMaxVersionSubs(List < SBQQ__Subscription__c > assets) {
decimal maxVersion = 0;
for(SBQQ__Subscription__c asst: assets){
maxVersion = (asst.Version__c != null && asst.Version__c > maxVersion ) ?  asst.Version__c : maxVersion;
}
return (Integer)maxVersion;
}
*/
    
    public static List < SBQQ__Subscription__c > getSubscriptions(string dsiID) {
        return [SELECT
                ID,
                Name,
                Key_Group_multi__c,
                Version__c,
                SBQQ__Quantity__c,
                SBQQ__Product__r.ProductCode,
                SBQQ__SubscriptionEndDate__c,
                SBQQ__SubscriptionStartDate__c,
                SBQQ__EndDate__c,
                Previous_MHz__c,
                SBQQ__Product__r.Name,
                SBQQ__Product__r.Id,
                SBQQ__Product__r.cpuRestriction__c,
                SBQQ__Product__r.RMS_Product_ID__c,
                SBQQ__Product__r.SKU_Category_ID__c,
                SBQQ__Product__r.Version_ID__c,
                SBQQ__Product__r.Variation_Type__c,
                SBQQ__Product__r.Mhz__c,
                SBQQ__Product__r.NU_Quantity__c,
                SBQQ__Product__r.SKU_Type_ID__c,
                SBQQ__Product__r.isBundle__c,
                SBQQ__Product__r.Software_Type_ID__c,
                SBQQ__Product__r.Clustering__c,
                SBQQ__Product__r.BaseProductID__c,
                SBQQ__Product__r.Variation_ID__c,
                SBQQ__Product__r.Previous_SKU__r.ProductCode,
                SBQQ__Product__r.Interval__c,
                SBQQ__Product__r.Environment__c,
                SBQQ__Product__r.Version__c,
                SBQQ__Product__r.Key_Feature__c,
                SBQQ__Product__r.LegacyCatalog__c, 
                Key_Group_ID__c,
                SBCF_Quote_ID__c,
                CreatedDate,
                LastModifiedDate,
                Exception_multi__c,
                Aggregation_Comments__c,
                SBQQ__Contract__r.Contractnumber 
                FROM SBQQ__Subscription__c
                WHERE SBCF_DSI__c =: dsiID
                AND SBQQ__Product__r.RecordTypeID='012E0000000DDFfIAO'
                AND SBQQ__Product__r.Interval__c IN ('Term','Subscription')
                AND Key_Group_multi__c!=''
                AND Key_Group_multi__c excludes ('N/A', 'MYC On Hold', 'Flat MYC')
                AND SBCF_DSI__c!=NULL
				AND Exception_multi__c excludes ('Retired')
                ORDER BY CreatedDate DESC
               ];
    }
    
    
    public static List < Product2 > getAllProductsByVersion(Integer version) {
        return [SELECT
                ID,
                Name,
                ProductCode,
                SKU_Type_ID__c,
                SKU_Category_ID__c,
                Software_Type_ID__c,
                Version_ID__c,
                cpuRestriction__c,
                RMS_Product_ID__c,
                Variation_Type__c,
                Mhz__c,
                NU_Quantity__c,
                isBundle__c,
                Clustering__c,
                BaseProductID__c,
                Variation_ID__c,
                Key_Feature__c,
                Environment__c,
                Interval__c,
                Version__c,
                LegacyCatalog__c
                
                FROM Product2 WHERE Version_ID__c =: version
                AND isActive = TRUE
                AND Software_Type_ID__c IN(3, 4, 5, 6, 8, 11, 12)
                AND RMS_Product_ID__c NOT IN(544, 403, 949, 963, 1023, 1022) //-- 3,407 don't include web/web universal
                AND(RMS_Product_ID__c < 609 OR RMS_Product_ID__c > 612)
                AND SKU_Type_ID__c NOT IN(26, 32, 73, 74, 75) //-- filter out dept server
                //AND RMS_Product_ID__c =1183
                //AND ProductCode IN ('82200','82197')
               ];
    }
    
    public static List < Product2 > getAllProductsByPrevious(List<String> previous) {
        return [SELECT
                ID,
                Name,
                ProductCode,
                SKU_Type_ID__c,
                SKU_Category_ID__c,
                Software_Type_ID__c,
                Version_ID__c,
                cpuRestriction__c,
                RMS_Product_ID__c,
                Variation_Type__c,
                Mhz__c,
                NU_Quantity__c,
                isBundle__c,
                Clustering__c,
                BaseProductID__c,
                Variation_ID__c,
                Previous_SKU__c,
                Version__c,
                Key_Feature__c,
                Environment__c,
                Interval__c,
                LegacyCatalog__c,
                Previous_SKU__r.ProductCode
                FROM Product2 WHERE Previous_SKU__r.ProductCode in: previous
                
               ];
    }
    
    public static List<String> getProductsFromAsset(List<Asset> assets){
        return new List<String>();
    }
    
    public static List<String> getProductsFromSObject(List<sObject> assets){
        List<String> products = new List<String>();
        for(sObject asst: assets){
            String prodPrefixId = asst.getSObjectType().getDescribe().getName()=='Asset'?'Product2':'SBQQ__Product__r';
            products.add((String)asst.getSObject(prodPrefixId).get('ProductCode'));
        }
        return products;
    }
    
    public static List<String> getProductsFromSubscription(List<SBQQ__Subscription__c> assets){
        return new List<String>();
    }
    
    public static List < Product_Hierarchy__c > getProductBundles(List < String > products, String unbundleType, Integer level, Boolean isEnhancedTranslation, Boolean isStandardToEnh, String version) {
        String qry =
            'SELECT'+
            '            ID,'+
            '            Keep_Parent__c,'+
            '            LicenseInheritanceType__c,'+
            '            Parent_Product__c,'+
            '            Parent_Product__r.Name,'+
            '            Parent_Product__r.ProductCode,'+
            '            Parent_Product__r.SKU_Type_ID__c,'+
            '            Parent_Product__r.SKU_Category_ID__c,'+
            '            Parent_Product__r.Software_Type_ID__c,'+
            '            Parent_Product__r.Version_ID__c,'+
            '            Parent_Product__r.cpuRestriction__c,'+
            '            Parent_Product__r.RMS_Product_ID__c,'+
            '            Parent_Product__r.Variation_Type__c,'+
            '            Parent_Product__r.Mhz__c,'+
            '            Parent_Product__r.NU_Quantity__c,'+
            '            Parent_Product__r.isBundle__c,'+
            '            Parent_Product__r.Clustering__c,'+
            '            Parent_Product__r.BaseProductID__c,'+
            '            Parent_Product__r.Environment__c,'+
            '            Parent_Product__r.Interval__c,'+
            '            Parent_Product__r.Version__c,'+
            '            Parent_Product__r.Variation_ID__c,'+
            '            Parent_Product__r.Key_Feature__c,'+
            '            Child_Product__c,'+
            '            Child_Product__r.Name,'+
            '            Child_Product__r.ProductCode,'+
            '            Child_Product__r.SKU_Type_ID__c,'+
            '            Child_Product__r.SKU_Category_ID__c,'+
            '            Child_Product__r.Software_Type_ID__c,'+
            '            Child_Product__r.Version_ID__c,'+
            '            Child_Product__r.cpuRestriction__c,'+
            '            Child_Product__r.RMS_Product_ID__c,'+
            '            Child_Product__r.Variation_Type__c,'+
            '            Child_Product__r.Mhz__c,'+
            '            Child_Product__r.NU_Quantity__c,'+
            '            Child_Product__r.isBundle__c,'+
            '            Child_Product__r.Clustering__c,'+
            '            Child_Product__r.BaseProductID__c,'+
            '            Child_Product__r.Environment__c,'+
            '            Child_Product__r.Interval__c,'+
            '            Child_Product__r.Version__c,'+
            '            Child_Product__r.Variation_ID__c,'+
            '            Child_Product__r.Key_Feature__c,' +
            '			 Child_Product__r.LegacyCatalog__c,' + 
            '            insertOnTranslation__c ';
        qry+= unbundleType=='Downgrade'?
            ' FROM Product_Hierarchy__c WHERE Parent_Product__r.ProductCode IN: products':
        ' FROM Product_Hierarchy__c WHERE Parent_Product__c IN: products';
        
        qry+='            AND RecordType.Name=:unbundleType';
        
        //exclude enhanced translation bundles
        if(isStandardToEnh){
            qry+= ' AND (LicenseInheritanceType__c = \'Enhanced Translation Only\' and LicenseInheritanceType__c != \'Translation\') ';
        }else{
            qry+= isEnhancedTranslation ?
                '':
            ' AND ( LicenseInheritanceType__c != \'Enhanced Translation Only\' and LicenseInheritanceType__c != \'Translation\') ';
        }
        if(version!=''){
            qry+= ' AND ExcludedVersions__c excludes (:version)';
        }
        
        //query due to recursion
        qry= level>0?qry+' AND LicenseInheritanceType__c!=\'First Level Only\'':qry;
        //Need to narrow down this query
        //
        system.debug('qry ' + qry);
        return DataBase.Query(qry);
    }

    public static List<Product2> getProductsByCodes(Set<String> productCodes) {
        if (productCodes == null || productCodes.isEmpty()) {
            return new List<Product2>();
        }
        return [SELECT
                ID,
                Name,
                ProductCode,
                SKU_Type_ID__c,
                SKU_Category_ID__c,
                Software_Type_ID__c,
                Version_ID__c,
                cpuRestriction__c,
                RMS_Product_ID__c,
                Variation_Type__c,
                Mhz__c,
                NU_Quantity__c,
                isBundle__c,
                Clustering__c,
                BaseProductID__c,
                Variation_ID__c,
                Key_Feature__c,
                Environment__c,
                Interval__c,
                Version__c,
                LegacyCatalog__c
                FROM Product2 WHERE ProductCode IN :productCodes
               ];
    }
    /*
public static DateTime getLatestModDate(List < Asset > dsiAssets) {

//initialize to min datetime
DateTime maxDate = DateTime.newInstance(0);

for (Asset asst: dsiAssets) {
if (asst.LastModifiedDate > maxDate) {
maxDate = asst.LastModifiedDate;
}
}

return maxDate;

}

public static DateTime getLatestModDateSubs(List < SBQQ__Subscription__c > dsiAssets) {

//initialize to min datetime
DateTime maxDate = DateTime.newInstance(0);

for (SBQQ__Subscription__c asst: dsiAssets) {
if (asst.LastModifiedDate > maxDate) {
maxDate = asst.LastModifiedDate;
}
}

return maxDate;

}



public static Map < String, List < Asset >> groupAssetsbyKeyGroup(List < Asset > allAssets) {
Integer currKeyGroup = -99; //helper
Boolean start = true;
List < Asset > currAssets = new List < Asset > (); //contains asset during iteration
Map < String, List < Asset >> resultAssets = new Map < String, List < Asset >> ();
System.debug(allAssets);
for (Asset asst: allAssets) {
if (start) {
currKeyGroup = (Integer) asst.Key_Group_ID__c;
start = false;
}
if (!start && (Integer) asst.Key_Group_ID__c != currKeyGroup) {
//create New Asset Group
resultAssets.put(currKeyGroup.format(), currAssets);
currKeyGroup = (Integer) asst.Key_Group_ID__c;
currAssets = new List < Asset > ();
}
currAssets.add(asst);
}
//add last keyGroup
if (allAssets.size() > 0) {
resultAssets.put(currKeyGroup.format(), currAssets);
}
return resultAssets;
}

public static Map < String, List < SBQQ__Subscription__c >> groupSubsbyKeyGroup(List < SBQQ__Subscription__c > allAssets) {
Integer currKeyGroup = -99; //helper
Boolean start = true;
List < SBQQ__Subscription__c > currAssets = new List < SBQQ__Subscription__c > (); //contains asset during iteration
Map < String, List < SBQQ__Subscription__c >> resultAssets = new Map < String, List < SBQQ__Subscription__c >> ();
System.debug(allAssets);
for (SBQQ__Subscription__c asst: allAssets) {
if (start) {
currKeyGroup = (Integer) asst.Key_Group_ID__c;
start = false;
}
if (!start && (Integer) asst.Key_Group_ID__c != currKeyGroup) {
//create New Asset Group
resultAssets.put(currKeyGroup.format(), currAssets);
currKeyGroup = (Integer) asst.Key_Group_ID__c;
currAssets = new List < SBQQ__Subscription__c > ();
}
currAssets.add(asst);
}
//add last keyGroup
if (allAssets.size() > 0) {
resultAssets.put(currKeyGroup.format(), currAssets);
}
return resultAssets;
}

public static Integer getAssetVersion(List < Asset > assets) {
return (Integer) assets[0].Version__c;
}

public static Integer getSubsVersion(List < SBQQ__Subscription__c > assets) {
return (Integer) assets[0].Version__c;
}

public static Integer getAssetQuoteID(List < Asset > assets) {

//return assets.size()>0 && assets[0].Version__c != null ? (Integer) assets[0].Version__c : 123456;
return assets[0].Version__c != null ? (Integer) assets[0].Version__c : 123456;

}*/
    
}