//SBQQ__CustomScript__c/aBQ44000000gVO5GAM

export function onInit(quoteLineModels) {
    if (!quoteLineModels) {
        return Promise.resolve();
    }
    for (var i = 0; i < quoteLineModels.length; i++) {
        if (quoteLineModels[i].record["SBQQ__Product__r"]["DisableProration__c"]) {
            quoteLineModels[i].calculateFullTermPrice = true;
        }
    }
    return Promise.resolve();
};

export function onBeforeCalculate(quoteModel, quoteLineModels, conn) {

    var newStartDate = null;
    var daysBetween = null;
    const cpiByProduct = new Map();
    var hasCPI = false;
    const startDateByGroup = new Map();
    const endDateByGroup = new Map();
    quoteModel.record["Groups_Multiple_Dates__c"] = false;
    const endDateGroup = new Map();

    if (quoteLineModels != null) {

        quoteLineModels.forEach(function (line) {

            if(quoteModel.record["SBQQ__Type__c"] == "Renewal" && line.record["SBCF_CPI_Uplift__c"] != null && line.record["SBQQ__RenewedSubscription__c"] != null){
                /* HDR 744899 - Nogueira
                cpiByProduct.set(line.record["SBQQ__Product__c"], line.record["SBCF_CPI_Uplift__c"]);
                hasCPI = true;
                */
            }

            var groupClone = quoteModel.record["Has_Groups__c"] == true && line.record["SBQQ__Group__c"] != null;
            groupClone |= quoteModel.record["Has_Groups__c"] == false;
            console.log('check ' + groupClone);

            if(groupClone == true){

                if(quoteModel.record["SBQQ__LineItemsGrouped__c"] == true && line.record["SBQQ__Group__r"]["Name"] != null){

                    if(line.record["SBQQ__StartDate__c"] != null){
                        var lStartDate = new Date(line.record["SBQQ__StartDate__c"]);
                    }

                    if(startDateByGroup.has(line.record["SBQQ__Group__r"]["Name"]) == true){
                        var gStartDate = new Date(startDateByGroup.get(line.record["SBQQ__Group__r"]["Name"]));
                    }

                    var startDateCheck = line.record["SBQQ__StartDate__c"] != null && startDateByGroup.has(line.record["SBQQ__Group__r"]["Name"]) == false;
                    startDateCheck |= gStartDate != null && lStartDate != null && lStartDate < gStartDate;

                    if(startDateCheck == true){
                        startDateByGroup.set(line.record["SBQQ__Group__r"]["Name"], line.record["SBQQ__StartDate__c"]);
                    }

                    if(line.record["SBQQ__StartDate__c"] != null && line.record["SBQQ__EndDate__c"] != null){
                        var lEndDate = new Date(line.record["SBQQ__EndDate__c"]);
                    }

                    if(endDateByGroup.has(line.record["SBQQ__Group__r"]["Name"]) == true){
                        var gEndDate = new Date(endDateByGroup.get(line.record["SBQQ__Group__r"]["Name"]));
                    }

                    var endDateCheck = line.record["SBQQ__EndDate__c"] != null && endDateByGroup.has(line.record["SBQQ__Group__r"]["Name"]) == false;
                    endDateCheck |= gEndDate != null && lEndDate != null && lEndDate > gEndDate;

                    if(endDateCheck == true){
                        endDateByGroup.set(line.record["SBQQ__Group__r"]["Name"], line.record["SBQQ__EndDate__c"]);
                    }
                }
            }

            var lineEndDate = null;

            if(line.record["SBQQ__EndDate__c"] != null && !line.record["ProductFamilyLabel__c"].toLocaleLowerCase().includes("education")){
                lineEndDate = new Date(line.record["SBQQ__EndDate__c"]);
            }

            var multipleEndDateCheck = quoteModel.record["SBQQ__LineItemsGrouped__c"] == true;
            multipleEndDateCheck &= quoteModel.record["Groups_Multiple_Dates__c"] == false;
            multipleEndDateCheck &= lineEndDate != null;

            if(multipleEndDateCheck == true && groupClone == true){

                if(line.record["SBQQ__Group__r"]["Name"] != null) {

                    if(endDateGroup.has(line.record["SBQQ__Group__r"]["Name"]) == true){

                        var groupEndDate = new Date(endDateGroup.get(line.record["SBQQ__Group__r"]["Name"]));
                        if(groupEndDate < lineEndDate){
                            quoteModel.record["Groups_Multiple_Dates__c"] = true;
                        }else if(groupEndDate > lineEndDate){
                            quoteModel.record["Groups_Multiple_Dates__c"] = true;
                        }
                    }
                    endDateGroup.set(line.record["SBQQ__Group__r"]["Name"], lineEndDate);
                }
            }

        });

        /* HDR 744899 - Nogueira
        if(hasCPI == true){
            quoteLineModels.forEach(function (line) {

                var condition = cpiByProduct.has(line.record["SBQQ__Product__c"]) == true;
                condition &= line.record["Old_CPI__c"] == null;

                if(condition){
                    line.record["SBCF_CPI_Uplift__c"] = cpiByProduct.get(line.record["SBQQ__Product__c"]);
                    line.record["Old_CPI__c"] = cpiByProduct.get(line.record["SBQQ__Product__c"]);
                }

            });
        }
        */
    }


    quoteModel.groups.forEach(function(group){

        if (group.record != null){

            if(group.record["Default_End_Date__c"] != null){
                var defaultEndDate = new Date(group.record["Default_End_Date__c"]);
                group.record["SBQQ__EndDate__c"] = toApexDate(defaultEndDate);
            }

            if(newStartDate != null && group.record["SBQQ__StartDate__c"] != null){

                var oldStartDate = new Date(group.record["SBQQ__StartDate__c"]);
                group.record["SBQQ__StartDate__c"] = toApexDate(newStartDate);

                if(group.record["SBQQ__EndDate__c"] != null){

                    console.log('check1');
                    var timeBetween = newStartDate.getTime() - oldStartDate.getTime();
                    daysBetween = timeBetween / (1000 * 3600 * 24);
                    console.log('daysBetween:' + daysBetween);

                    var endDate = new Date(group.record["SBQQ__EndDate__c"]);
                    endDate.setUTCDate(endDate.getUTCDate() + daysBetween);
                    group.record["SBQQ__EndDate__c"] = toApexDate(endDate);

                    var timeBetweenNew = newStartDate.getTime() - endDate.getTime();
                    var daysBetweenNew = timeBetweenNew / (1000 * 3600 * 24);
                    console.log('daysBetweenNew:' + daysBetweenNew);
                    console.log('checkbox: ' + group.record["Manage_Leap_Year__c"]);

                    if(daysBetweenNew == -364 && group.record["Manage_Leap_Year__c"] == true){
                        endDate.setUTCDate(endDate.getUTCDate() + 1);
                        group.record["SBQQ__EndDate__c"] = toApexDate(endDate);
                    }

                    if(daysBetweenNew == -365 && group.record["Manage_Leap_Year__c"] == false){
                        endDate.setUTCDate(endDate.getUTCDate() - 1);
                        group.record["SBQQ__EndDate__c"] = toApexDate(endDate);
                    }
                }
            }

            if(group.record["SBQQ__EndDate__c"] != null){
                newStartDate = new Date(group.record["SBQQ__EndDate__c"]);
                newStartDate.setUTCDate(newStartDate.getUTCDate() + 1);
            }

            if(group.record["SBQQ__StartDate__c"] == null && group.record["SBQQ__EndDate__c"] == null){

                if(startDateByGroup.has(group.record["Name"]) == true){
                    var lineStart = new Date(startDateByGroup.get(group.record["Name"]));
                    group.record["SBQQ__StartDate__c"] = toApexDate(lineStart);
                }

                if(endDateByGroup.has(group.record["Name"]) == true){
                    var lineEnd = new Date(endDateByGroup.get(group.record["Name"]));
                    group.record["SBQQ__EndDate__c"] = toApexDate(lineEnd);
                }
            }

        }
    });

    setCloudTier(quoteModel,quoteLineModels);
    checkUncheckCloudArchitectureOrInfrastructure(quoteModel,quoteLineModels);
    checkUncheckCustomSkus(quoteModel,quoteLineModels);
    populatePaperworkSections(quoteModel, quoteLineModels);

    return conn.query("SELECT SBQQ__Opportunity2__r.RecordType.Name FROM SBQQ__Quote__c WHERE Id = '" + quoteModel.record['Id'] + "'")
        .then(function (results) {
            if (results.totalSize) {
                if ((results.records[0]['SBQQ__Opportunity2__r']['RecordType']['Name'] === 'Renewal Opportunity (New)' || results.records[0]['SBQQ__Opportunity2__r']['RecordType']['Name'] === 'Amendment Opportunity') && !quoteModel.record['PriceLock__c'] && quoteModel != null) {
                    checkUncheckPartialMonth(quoteModel, quoteLineModels, results);
                    checkUncheckCoTerm(quoteModel, quoteLineModels, results);
                } else {
                    console.log('Conditions not met');
                }
            } else {
                console.log('No records returned')
            }
        });
    return Promise.resolve();
};

function roundLeapYears(startDate, endDate){
    let minDate = new Date(startDate);
    let maxDate = new Date(endDate);
    let diffDays;
    let leapYears = 0;
    if (minDate != null && maxDate != null) {
        diffDays = Math.ceil(Math.abs(minDate - maxDate) / (1000 * 60 * 60 * 24)) + 1; // Days Between
        //Substract leap year days

        if ((new Date(minDate.getFullYear(), 1, 29).getMonth() === 1) && minDate <= new Date(minDate.getFullYear(), 1, 29)){
            leapYears += 1;
        }
        if ((new Date(maxDate.getFullYear(), 1, 29).getMonth() === 1) && maxDate >= new Date(maxDate.getFullYear(), 1, 29)){
            leapYears += 1;
        }
        //If it's MultiYear, evaluate all the years in the range
        if(minDate.getFullYear() + 1 != maxDate.getYear()){
            for (var year = minDate.getFullYear()+1; year < maxDate.getFullYear(); year++) {
                if (new Date(year, 1, 29).getMonth() === 1){
                    leapYears += 1;
                }
            }
        }
        diffDays -= leapYears;
    }
    return diffDays;
}

function calculateNumberOfDays(quoteModel, quoteLineModels) {
    let minDate,maxDate;
    quoteLineModels.forEach(ql => {
        console.log('ql',ql)
        let currentQLStartDate = Date.parse(ql.record['SBQQ__StartDate__c'])
        let currentQLEndDate = Date.parse(ql.record['SBQQ__EndDate__c'])
        if (currentQLStartDate &&
            ql.record['SBQQ__NetTotal__c'] && ql.record['SBQQ__NetTotal__c'] > 0 &&
            (!minDate || (currentQLStartDate < minDate))){
            minDate = new Date(currentQLStartDate);
        }
        if (currentQLEndDate && (!maxDate || (currentQLEndDate > maxDate))) {
            maxDate = new Date(currentQLEndDate);
        }
    })

    return roundLeapYears(minDate, maxDate);
}

export function onAfterPriceRules(quoteModel, quoteLineModels) {

    return Promise.resolve();
};
export function onAfterCalculate(quoteModel, quoteLineModels, conn) {

    populateFxSoftware(quoteModel, quoteLineModels);

    let queryString = "SELECT Id, " +
        "SBQQ__Opportunity2__r.RecordType.Name, " +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.Software_Term_Total_No_Tax__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.TX_Incentive_Positive__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.CTR__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.ARRF__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.Num_Days__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.Min_Start_Date__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.Max_End_Date__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.AASV__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.MXIT__c," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.Net_Cloud__c," +
        "SBQQ__Opportunity2__r.Cloud_Migration_Forecast_Lock__c, " +
        "SBQQ__Opportunity2__r.Cloud_Migration_Forecast__c," +
        "SBQQ__Opportunity2__r.License_Forecast__c, " +
        "SBQQ__Opportunity2__r.MYC_Opportunity_Cutoff__c, " +
        "SBQQ__Opportunity2__r.Type," +
        "SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Opportunity__r.Platform__c," +
        "(SELECT Id,SBQQ__RenewedSubscription__r.SBQQ__EndDate__c," +
        "SBQQ__EffectiveEndDate__c " +
        "FROM SBQQ__LineItems__r" +
        ") " +
        "FROM SBQQ__Quote__c " +
        "WHERE Id = '" + quoteModel.record.Id + "'";
    return conn.query(queryString).then(results => {
        if (results.done && results.totalSize === 1) {
            let reQueriedQuote = results.records[0];
            console.log('reQueriedQuote', reQueriedQuote);
            quoteModel.numberOfDays = calculateNumberOfDays(quoteModel,quoteLineModels)
            quoteModel.record['AASV__c'] = setAASV(quoteModel);
            setValuesFromPrevQuote(quoteModel, reQueriedQuote);
            calculateForecast(quoteModel, reQueriedQuote);
        }
    });
}

export function calculateForecast(quoteModel, reQueriedQuote) {
    let softwareTermTotalNoTax = getSoftwareTermTotalNoTax(quoteModel);
    let txIncentive = getTXIncentive(quoteModel);
    console.log('Calculating forecast')
    console.log('quoteModel', quoteModel)
    console.log('quoteModel Software_Term_Total_No_Tax__c', quoteModel.record['Software_Term_Total_No_Tax__c'])
    console.log('quoteModel TX_Incentive_Positive__c', quoteModel.record.TX_Incentive_Positive__c)
    console.log('Quote_Offer_Type__c', quoteModel.record.Quote_Offer_Type__c)
    console.log('PLA__c', quoteModel.record['PLA__c']);
    console.log('softwareTermTotalNoTax', softwareTermTotalNoTax);
    console.log('txIncentive', txIncentive);
    console.log('platform', quoteModel.record['Platform__c']);
    console.log('Max_End_Date__c - ', quoteModel.record['Max_End_Date__c']);
    console.log('Min_Start_Date__c - ', quoteModel.record['Min_Start_Date__c']);

    console.log('Num_Days__c', quoteModel.record['Num_Days__c']);
    console.log('numberOfDays', quoteModel.numberOfDays);
    console.log('Prev_Num_Days__c', quoteModel.record['Prev_Num_Days__c']);
    console.log('AASV__c', quoteModel.record['AASV__c']);
    console.log('Prev_AASV__c', quoteModel.record['Prev_AASV__c']);
    console.log('MYC_Opportunity_Cutoff__c', reQueriedQuote.SBQQ__Opportunity2__r['MYC_Opportunity_Cutoff__c']);


    if (quoteModel.record['PriceLock__c']) {
        console.log("Record is locked");
        return;
    }
    let forecastedAmount = 0;


    if (quoteModel.record['Platform__c'] && (quoteModel.record['Platform__c'].includes('MCE') || quoteModel.record['Platform__c'].includes('MCG') || quoteModel.record['Platform__c'].includes('CMC') || quoteModel.record['Platform__c'].includes('MCS'))) {
        let cloudMigForecast = 0;

        if (quoteModel.record.Quote_Offer_Type__c === 'Term Migration') {
            if (!reQueriedQuote.SBQQ__Opportunity2__r['Cloud_Migration_Forecast_Lock__c']) {
                cloudMigForecast = reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']['ARRF__c'];
            } else {
                cloudMigForecast = reQueriedQuote.SBQQ__Opportunity2__r['Cloud_Migration_Forecast__c'];
            }
        } else {
            cloudMigForecast = 0;
        }
        quoteModel.record['Cloud_Migration_Forecast__c'] = cloudMigForecast;
    } else {
        quoteModel.record['Cloud_Migration_Forecast__c'] = 0;
    }
    console.log(quoteModel.record)
    console.log('Cloud_Migration_Forecast__c', quoteModel.record['Cloud_Migration_Forecast__c'])

    if (reQueriedQuote.SBQQ__Opportunity2__r.RecordType.Name === 'Sales Opportunity (New)') {
        forecastedAmount = setForecastForSalesOpp(reQueriedQuote, quoteModel);
    } else if (reQueriedQuote.SBQQ__Opportunity2__r.RecordType.Name === 'Renewal Opportunity (New)') {
        console.log('Renewal opp')

        if (quoteModel.record.Quote_Offer_Type__c === 'Term Migration') {
            forecastedAmount = setForecastForTermMigrationOpp(reQueriedQuote, quoteModel);
        } else if (
            quoteModel.record.Quote_Offer_Type__c === 'Maintenance Renewal' ||
            quoteModel.record.Quote_Offer_Type__c === 'Education Renewal' ||
            quoteModel.record.Quote_Offer_Type__c === 'Support Renewal' ||
            quoteModel.record.Quote_Offer_Type__c === 'Royalty Renewal'
        ) {
            forecastedAmount = setForecastForEduMainSupRenewOpp(quoteModel);
        } else {
            forecastedAmount = setForecastForOtherRenewalOpp(reQueriedQuote, quoteModel);
        }
    }  else if (reQueriedQuote.SBQQ__Opportunity2__r.RecordType.Name === 'Amendment Opportunity') {
        forecastedAmount = setForecastForAmmendmentOpp(reQueriedQuote, quoteModel);
    }

    console.log('forecastedAmount before', forecastedAmount);
    forecastedAmount = forecastedAmount > 0 ? Math.round((forecastedAmount + Number.EPSILON) * 100) / 100 : 0;
    console.log('forecastedAmount after', forecastedAmount);




    quoteModel.record['LF2__c'] = forecastedAmount;
}

export function setForecastForSalesOpp(reQueriedQuote, quoteModel) {
    console.log('setForecastForSalesOpp')
    let softwareTermTotalNoTax = getSoftwareTermTotalNoTax(quoteModel);
    let txIncentive = getTXIncentive(quoteModel);

    if (reQueriedQuote.SBQQ__Opportunity2__r['MYC_Opportunity_Cutoff__c']) {
        return setForecastForSalesOpp_HISTORICAL(quoteModel, txIncentive, softwareTermTotalNoTax);
    }

    if (quoteModel.numberOfDays < 365) {
        return (quoteModel.record['PLA__c'] > 0 ?
                quoteModel.record['PLA__c'] - txIncentive :
                softwareTermTotalNoTax - txIncentive + quoteModel.record['Net_Cloud__c']
        )
    } else {
        return (quoteModel.record['PLA__c'] > 0 ?
                quoteModel.record['PLA__c'] - txIncentive :
                quoteModel.record['AASV__c']
        )
    }
};
function setForecastForAmmendmentOpp(reQueriedQuote, quoteModel) {
    console.log('setForecastForAmmendmentOpp')
    let softwareTermTotalNoTax = getSoftwareTermTotalNoTax(quoteModel);
    let txIncentive = getTXIncentive(quoteModel);

    if (quoteModel.numberOfDays < 365) {
        return (quoteModel.record['PLA__c'] > 0 ?
                quoteModel.record['PLA__c'] - txIncentive :
                softwareTermTotalNoTax - txIncentive + quoteModel.record['Net_Cloud__c']
        )
    } else {
        return (quoteModel.record['PLA__c'] > 0 ?
                quoteModel.record['PLA__c'] - txIncentive :
                quoteModel.record['AASV__c']
        )
    }
}


export function setForecastForEduMainSupRenewOpp(quoteModel) {
    console.log('Set Forecast For Edu Main Sup Renew Opp')
    return 0;
};

export function setForecastForOtherRenewalOpp(reQueriedQuote, quoteModel) {
    console.log('Set Forecast For Other RenewalOpp');

    let currSTTNTMinusCurrTI = getSoftwareTermTotalNoTax(quoteModel) - getTXIncentive(quoteModel);
    let currCTR = quoteModel.record.CTR__c;
    let prevCTR = 0;
    let prevSTTNTMinusPrevTI = 0;
    let forecastAmount = 0;

    if (reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r'] && reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']) {
        prevCTR = reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']['CTR__c'];
        prevSTTNTMinusPrevTI =
            reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']['Software_Term_Total_No_Tax__c'] -
            reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']['TX_Incentive_Positive__c'];
        if(!quoteModel.record["Submitted_For_Approval__c"] || compareDates(quoteModel.record["Submitted_For_Approval__c"], toApexDate(new Date(2024,1,16))) !== -1)
            quoteModel.record['Prev_Net_Cloud__c'] = reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']['Net_Cloud__c'];
    }

    console.log('currSTTNTMinusCurrTI-', currSTTNTMinusCurrTI);
    console.log('prevSTTNTMinusPrevTI-', prevSTTNTMinusPrevTI);

    if (reQueriedQuote.SBQQ__Opportunity2__r['MYC_Opportunity_Cutoff__c']){
        return setForecastForOtherRenewalOpp_HISTORICAL(currCTR, currSTTNTMinusCurrTI, prevSTTNTMinusPrevTI, prevCTR);
    }


    if ( quoteModel.numberOfDays < 365) {
        forecastAmount = currSTTNTMinusCurrTI + quoteModel.record['Net_Cloud__c'] - ((prevSTTNTMinusPrevTI + quoteModel.record['Prev_Net_Cloud__c'])/ quoteModel.record['Prev_Num_Days__c'] * quoteModel.numberOfDays)
    } else {
        if (quoteModel.record['Prev_Num_Days__c'] < 365 &&
            quoteModel.record['Prev_Num_Days__c'] != quoteModel.numberOfDays) {
            forecastAmount = currSTTNTMinusCurrTI + quoteModel.record['Net_Cloud__c'] - ((prevSTTNTMinusPrevTI + quoteModel.record['Prev_Net_Cloud__c']) / quoteModel.record['Prev_Num_Days__c'] * quoteModel.numberOfDays);
        } else {
            forecastAmount = quoteModel.record['AASV__c'] - quoteModel.record['Prev_AASV__c'];
        }
    }

    return forecastAmount;
};


//Renewal - Term Migration
// When Opportunity.Type = Term Migration and Platform = MCE, the Incremental Forecast formula should be updated as follows:

// Term <= 12, formula "(Software Term Total No Tax)-(((Cloud_Migration_Forecast__c )/12)*Current CTR)
// Term > 12, formula "(((Software Term Total No Tax)/CTR)*12)-Cloud_Migration_Forecast__c

// Otherwise, for any other term migration where Platform != MCE, leave the same formulas for Incremental forecast:

// Term <= 12, formula "(Software Term Total No Tax)-(((Previous ARR)/12)*Current CTR)
// Term > 12, formula "(((Software Term Total No Tax)/CTR)*12)-Previous ARR
export function setForecastForTermMigrationOpp(reQueriedQuote, quoteModel) {
    console.log('Set Forecast For Term Migration Opp')
    let softwareTermTotalNoTax = getSoftwareTermTotalNoTax(quoteModel);
    let prevCTR = 0;
    let prevArr = 0;
    let cloudMigForecast = 0;

    prevCTR = reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']['CTR__c'];
    //SBQQ__RenewedContract__r.SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.ARRF__c
    prevArr = reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']['ARRF__c'];

    //SBQQ__RenewedContract__r.SBQQ__Quote__r.ARRF__c
    if (reQueriedQuote.SBQQ__Opportunity2__r['Cloud_Migration_Forecast_Lock__c']) {
        cloudMigForecast = reQueriedQuote.SBQQ__Opportunity2__r['Cloud_Migration_Forecast__c'];
    } else {
        cloudMigForecast = reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']['ARRF__c'];
    }

    console.log('cloudMigForecast', cloudMigForecast)
    console.log(quoteModel.record['Platform__c']);
    if (reQueriedQuote.SBQQ__Opportunity2__r['MYC_Opportunity_Cutoff__c']){
        return setForecastForTermMigrationOpp_HISTORICAL(quoteModel, softwareTermTotalNoTax, cloudMigForecast, prevArr);
    }

    if (quoteModel.record['Platform__c'] === 'MCE' || quoteModel.record['Platform__c'] === 'MCG' || quoteModel.record['Platform__c'] === 'CMC' ||
    quoteModel.record['Platform__c'] === 'MCS') {
        if (quoteModel.numberOfDays <= 365) {
            return softwareTermTotalNoTax + quoteModel.record['Net_Cloud__c'] - (cloudMigForecast / 365 * quoteModel.numberOfDays)
        } else {
            return (softwareTermTotalNoTax + quoteModel.record['Net_Cloud__c']) / quoteModel.numberOfDays * 365 - cloudMigForecast
        }
    } else{
        if (quoteModel.numberOfDays <= 365) {
            return softwareTermTotalNoTax - (prevArr / 365 * quoteModel.numberOfDays)
        } else {
            return softwareTermTotalNoTax / quoteModel.numberOfDays * 365 - prevArr
        }
    }
}


export function getSoftwareTermTotalNoTax(quoteModel) {
    let result = quoteModel.record['Net_Analytics__c'] +
        quoteModel.record['Net_Security__c'] +
        quoteModel.record['Net_Mobility__c'];
    result = result > 0 ? result : 0;
    return result;
}

export function getTXIncentive(quoteModel) {
    return Math.abs(quoteModel.record['Tx_Incentive__c']);
}

export function checkUncheckCoTerm(quoteModel, quoteLineModels, results) {

    console.log('rt name ' + results.records[0]['SBQQ__Opportunity2__r']['RecordType']['Name']);
    console.log('inside results');
    var qlDate;
    var qlg2startDate = new Map();
    var allDatesAreSame = true;
    if(!quoteModel.record['SBQQ__LineItemsGrouped__c']) {
        for (let i = 0; i < quoteLineModels.length && !qlDate; ++i) {
            if (quoteLineModels[i].record['SBQQ__StartDate__c']) {
                qlDate = quoteLineModels[i].record['SBQQ__StartDate__c'];
            }
        }

        if (qlDate) {
            for (let i = 0; i < quoteLineModels.length && allDatesAreSame; ++i) {
                console.log('quoteLine ' + i + ' ' + quoteLineModels[i].record['SBQQ__StartDate__c']);
                if (quoteLineModels[i].record['SBQQ__StartDate__c'] && quoteLineModels[i].record['SBQQ__StartDate__c'] != qlDate) {
                    allDatesAreSame = false;
                }
            }
        }

    } else{
        for (let i = 0; i < quoteLineModels.length; ++i) {

            if (quoteLineModels[i].record['SBQQ__StartDate__c']) {
                if (!qlg2startDate.has(quoteLineModels[i].record['SBQQ__Group__r']['SBQQ__Number__c'])) {
                    qlg2startDate.set(quoteLineModels[i].record['SBQQ__Group__r']['SBQQ__Number__c'], quoteLineModels[i].record['SBQQ__StartDate__c']);
                }
            }
        }

        if (qlg2startDate.size > 0) {
            for (let i = 0; i < quoteLineModels.length && allDatesAreSame; ++i) {
                console.log('quoteLine ' + i + ' ' + quoteLineModels[i].record['SBQQ__StartDate__c']);
                if (quoteLineModels[i].record['SBQQ__StartDate__c'] &&
                    qlg2startDate.get(quoteLineModels[i].record['SBQQ__Group__r']['SBQQ__Number__c']) != quoteLineModels[i].record['SBQQ__StartDate__c']) {
                    allDatesAreSame = false;
                }
            }
        }



    }

    quoteModel.record['SBCF_Co_termed__c'] = !allDatesAreSame;

};

export function checkUncheckPartialMonth(quoteModel, quoteLineModels, results) {
    console.log('INSIDE PARTIAL MONTH')
    var partialMonth = false;
    var isEndOfMonth = false;
    for (const ql of quoteLineModels) {
        console.log('INSIDE LOOP PM');
        if (ql.record['SBQQ__EndDate__c']) {
            let endDate = new Date(ql.record['SBQQ__EndDate__c']);
            let startDate = new Date(ql.record['SBQQ__StartDate__c']);
            var firstDayOfMonthStart = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
            var lastDayOfMonthEnd = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0);

            if (startDate.getDate() === firstDayOfMonthStart.getDate() && startDate.getMonth() === firstDayOfMonthStart.getMonth() && startDate.getFullYear() === firstDayOfMonthStart.getFullYear()) {
                if (!(endDate.getDate() === lastDayOfMonthEnd.getDate() && endDate.getMonth() === lastDayOfMonthEnd.getMonth() && endDate.getFullYear() === lastDayOfMonthEnd.getFullYear())) {
                    partialMonth = true;
                }
            } else {
                if (endDate.getDate() != startDate.getDate() - 1)
                    partialMonth = true;
            }
        }
    }
    quoteModel.record['Partial_Month__c'] = partialMonth;
};

export function setCloudTier(quoteModel, quoteLineModels) {

    let tierOne = false;
    let tierTwo = false;
    let tierThree = false;
    let tierFour = false;
    let tierCMC = false;
    let tierOneMCG = false;
    let tierTwoMCG = false;
    let tierThreeMCG = false;
    let tierFourMCG = false;

    for (const ql of quoteLineModels) {
        if (ql.record['SBQQ__Product__r']['Name'].includes('Tier 1')) {
            tierOne = true;
        }
        if (ql.record['SBQQ__Product__r']['Name'].includes('Tier 2')) {
            tierTwo = true;
        }
        if (ql.record['SBQQ__Product__r']['Name'].includes('Tier 3')) {
            tierThree = true;
        }
        if (ql.record['SBQQ__Product__r']['Name'].includes('Tier 4')) {
            tierFour = true;
        }
        if (ql.record['SBQQ__Product__r']['Name'].includes('CMC')) {
            tierCMC = true;
        }
        if (ql.record['SBQQ__Product__r']['Name'].includes('Tier 1 - MCG')) {
            tierOne = false;
            tierOneMCG = true;
        }
        if (ql.record['SBQQ__Product__r']['Name'].includes('Tier 2 - MCG')) {
            tierTwo = false;
            tierTwoMCG = true;
        }
        if (ql.record['SBQQ__Product__r']['Name'].includes('Tier 3 - MCG')) {
            tierThree = false;
            tierThreeMCG = true;
        }
        if (ql.record['SBQQ__Product__r']['Name'].includes('Tier 4 - MCG')) {
            tierFour = false;
            tierFourMCG = true;
        }
    }
    if (tierFour) {
        quoteModel.record['Cloud_Tier__c'] = 'Tier 4';
    } else if (tierThree) {
        quoteModel.record['Cloud_Tier__c'] = 'Tier 3';
    } else if (tierTwo) {
        quoteModel.record['Cloud_Tier__c'] = 'Tier 2';
    } else if (tierOne) {
        quoteModel.record['Cloud_Tier__c'] = 'Tier 1';
    } else if (tierCMC) {
        quoteModel.record['Cloud_Tier__c'] = 'CMC';
    }else if (tierOneMCG) {
        quoteModel.record['Cloud_Tier__c'] = 'Tier 1 - MCG';
    }else if (tierTwoMCG) {
        quoteModel.record['Cloud_Tier__c'] = 'Tier 2 - MCG';
    }else if (tierThreeMCG) {
        quoteModel.record['Cloud_Tier__c'] = 'Tier 3 - MCG';
    }else if (tierFourMCG) {
        quoteModel.record['Cloud_Tier__c'] = 'Tier 4 - MCG';
    }else {
        quoteModel.record['Cloud_Tier__c'] = 'N/A';
    }
}

export function populateFxSoftware(quoteModel, quoteLineModels){
    let fx = 0;
    let gfx = 0;
    for(const ql of quoteLineModels){
        if(ql.record['PC__c'] === 'Software'){
            fx += ql.record['ARRTI__c'];
            if(ql.record['SBQQ__AdditionalDiscount__c'] !== null){
                gfx += ql.record['SBQQ__RegularTotal__c'];
            } else {
                gfx += ql.record['ARR__c'];
            }
        }
    }
    quoteModel.record['FX_Software__c'] = fx;
    quoteModel.record['Gross_FX_Software__c'] = gfx;

}

export function checkUncheckCloudArchitectureOrInfrastructure(quoteModel, quoteLineModels){
    let cb = false;
    for (const ql of quoteLineModels) {
        if((ql.record['SBQQ__Product__r']['Name'].includes('Cloud Architecture') && ql.record['SBQQ__Product__r']['ProductCode'] !== '89970' )||ql.record['SBQQ__Product__r']['Name'].includes('Cloud Infrastructure') ){
            cb = true;
        }
    }
    quoteModel.record['Has_Cloud_Architecture_or_Infrastructure__c'] = cb;
}


//PAGE SECURITY PLUG-IN START
export function isFieldVisible(fieldName, line) {

    if (fieldName == 'SBQQ__StartDate__c' || fieldName == 'SBQQ__EndDate__c') {
        if ((line.SBQQ__Quote__r.SBQQ__Type__c != 'Renewal' && (line.SBQQ__ProductCode__c === '82574' || line.SBQQ__Product__c === '01t4400000AoPNcAAN')) || line.SBQQ__Quote__r.SBQQ__Type__c == 'Renewal') {
            return true;
        }
    }

    return null;

};

export function isFieldEditable(fieldName, line) {

    if(fieldName == 'SBQQ__Quantity__c' && line.Product_Selection_Group__c == 'Platforms') {
        return true;
    } else if (line.SBQQ__Quote__r.SBQQ__Type__c == 'Renewal' && fieldName == 'SBCF_CPI_Uplift__c') {
        if ((line.SBQQ__ProductCode__c === '30078' || line.SBCF_Text_Interval__c === 'Term') &&
            line.SBQQ__RenewedSubscription__c != null &&
            line.SBQQ__ProductCode__c !== '89290' && line.SBQQ__ProductCode__c !== '89295' && line.SBQQ__ProductCode__c !== '30072' && line.SBQQ__ProductCode__c !== '30047' && line.SBQQ__ProductCode__c !== '89970'
        ) {
            return true;
        } else {
            return false;
        }
    } else if (line.SBQQ__ProductFamily__c == 'T&I' && (fieldName == 'SBQQ__StartDate__c' || fieldName == 'SBQQ__EndDate__c')) {
        return true;
    } else if ((line.SBQQ__ProductFamily__c == 'Consulting' ||  line.SBQQ__ProductFamily__c == 'Other Consulting'
            || (line.SBQQ__SubscriptionPricing__c == null
                && (line.SBQQ__ProductFamily__c == 'Expense' || line.SBQQ__ProductFamily__c == 'Education')))
        && (fieldName == 'SBQQ__StartDate__c' || fieldName == 'SBQQ__EndDate__c')) {
        return false;
    } else if (line.SBQQ__Quote__r.SBQQ__Type__c == 'Amendment' && line.SBQQ__UpgradedSubscription__c != null && (fieldName == 'SBQQ__StartDate__c' || fieldName == 'SBQQ__EndDate__c')) {
        return false;
    } else if (line.SBQQ__Quote__r.SBQQ__Type__c == 'Renewal' && line.SBQQ__Quote__r.SBQQ__CustomerAmount__c >= 0 && (fieldName == 'SBQQ__StartDate__c' || fieldName == 'SBQQ__EndDate__c')) {
        return true;
    } else if (line.SBQQ__Quote__r.SBQQ__Type__c == 'Amendment' && line.SBQQ__UpgradedSubscription__c != null && fieldName == 'SBQQ__ListPrice__c') {
        return false;
	} else if ((line.SBQQ__ProductCode__c == '89412' || line.SBQQ__ProductCode__c == '89413') && fieldName == 'SBQQ__AdditionalDiscount__c') {
        return false;
    }
};

function toApexDate(/*Date*/ date) {
    if (date == null) {
        return null;
    }
    // Get the ISO formatted date string.
    // This will be formatted: YYYY-MM-DDTHH:mm:ss.sssZ
    var dateIso = date.toISOString();

    // Replace everything after the T with an empty string
    return dateIso.replace(new RegExp('[Tt].*'), "");
}

function setForecastForSalesOpp_HISTORICAL(quoteModel, txIncentive, softwareTermTotalNoTax) {
    console.log('setForecastForSalesOpp_HISTORICAL')
    if (quoteModel.record['PLA__c'] > 0) {
        console.log('quote.PLA__c > 0');
        return quoteModel.record['PLA__c'] - txIncentive;
    } else {
        console.log('quote.PLA__c < 0');
        if (quoteModel.record['CTR__c'] > 12) {
            console.log('quote.CTR__c > 12');
            return (softwareTermTotalNoTax - txIncentive) / quoteModel.record['CTR__c'] * 12;
        } else {
            console.log('quote.CTR__c < 12');
            return softwareTermTotalNoTax - txIncentive;
        }
    }
}
function setForecastForTermMigrationOpp_HISTORICAL(quoteModel, softwareTermTotalNoTax, cloudMigForecast, prevArr) {
// Term <= 12, formula "(Software Term Total No Tax)-(((Cloud_Migration_Forecast__c )/12)*Current CTR)
// Term > 12, formula "(((Software Term Total No Tax)/CTR)*12)-Cloud_Migration_Forecast__c
    console.log('setForecastForTermMigrationOpp_HISTORICAL')
    let currCTR = quoteModel.record['CTR__c'];

    if (quoteModel.record['Platform__c'] === 'MCE' || quoteModel.record['Platform__c'] === 'MCG' || quoteModel.record['Platform__c'] === 'CMC' || 
	quoteModel.record['Platform__c'] === 'MCS') {
        console.log('MCE opp');
        if (currCTR > 12) {
            console.log('quote.CTR__c > 12');
            return (softwareTermTotalNoTax / currCTR * 12) - cloudMigForecast
        } else {
            console.log('quote.CTR__c < 12');
            return softwareTermTotalNoTax - (cloudMigForecast / 12 * currCTR)
        }

    } else {
        console.log('Non MCE opp');
        if (currCTR > 12) {
            console.log('quote.CTR__c > 12');
            return (softwareTermTotalNoTax / currCTR * 12) - prevArr
        } else {
            console.log('quote.CTR__c < 12');
            return softwareTermTotalNoTax - (prevArr / 12 * currCTR)
        }
    }
}
function setForecastForOtherRenewalOpp_HISTORICAL(currCTR, currSTTNTMinusCurrTI, prevSTTNTMinusPrevTI, prevCTR) {
    console.log('setForecastForOtherRenewalOpp_HISTORICAL')
    //Renewal - Term, Cloud, Royalty
    if (currCTR > 12) {
        console.log('quote.CTR__c > 12');
        return (currSTTNTMinusCurrTI / currCTR * 12) - (prevSTTNTMinusPrevTI / prevCTR * 12);
    } else {
        console.log('quote.CTR__c < 12');
        return currSTTNTMinusCurrTI - (prevSTTNTMinusPrevTI / prevCTR * currCTR);
    }
}
function setAASV(quoteModel) {
    console.log((quoteModel.numberOfDays));
    if(quoteModel.record["Opportunity_Close_Date__c"] && compareDates(quoteModel.record["Opportunity_Close_Date__c"], toApexDate(new Date(2024,0,1))) === 1) {
        return (getSoftwareTermTotalNoTax(quoteModel) + quoteModel.record["Net_Cloud__c"] - getTXIncentive(quoteModel)) * (365 / quoteModel.numberOfDays);
    } else{
        return (getSoftwareTermTotalNoTax(quoteModel) - getTXIncentive(quoteModel)) * (365 / quoteModel.numberOfDays);
    }
}

function setValuesFromPrevQuote(quoteModel, reQueriedQuote) {
    if (reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r'] && reQueriedQuote.SBQQ__Opportunity2__r['SBQQ__RenewedContract__r']['SBQQ__Quote__r']) {
        quoteModel.record['Prev_Num_Days__c'] = roundLeapYears(reQueriedQuote.SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.Min_Start_Date__c, reQueriedQuote.SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r.Max_End_Date__c);
        quoteModel.record['Prev_AASV__c'] = calculatePrevAASV(quoteModel, reQueriedQuote);
    }
}

function calculatePrevAASV(quoteModel, reQueriedQuote) {
    let prevQuote = reQueriedQuote.SBQQ__Opportunity2__r.SBQQ__RenewedContract__r.SBQQ__Quote__r;
    let prevAASV;
    if (compareDates(quoteModel.record["Opportunity_Close_Date__c"], toApexDate(new Date(2024, 0, 1))) === -1 || (compareDates(quoteModel.record["Submitted_For_Approval__c"], toApexDate(new Date(2024, 1, 16))) === -1 && quoteModel.record["Submitted_For_Approval__c"])) {
        if (prevQuote.AASV__c) {
            console.log('Prev AASV__c exists')
            prevAASV = prevQuote.AASV__c;
        } else {
            console.log('Prev AASV__c not exists')
            prevAASV = (prevQuote.Software_Term_Total_No_Tax__c - prevQuote.TX_Incentive_Positive__c) / (365 * (prevQuote.CTR__c / prevQuote.MXIT__c)) * 365
        }
    } else {
        if (prevQuote.AASV__c) {
            prevAASV = prevQuote.AASV__c;
        } else {
            prevAASV = (prevQuote.Software_Term_Total_No_Tax__c + prevQuote.Net_Cloud__c - prevQuote.TX_Incentive_Positive__c) / (365 * (prevQuote.CTR__c / prevQuote.MXIT__c)) * 365
        }
    }
    prevAASV = prevAASV > 0 ? Math.round((prevAASV + Number.EPSILON) * 100) / 100 : 0;
    console.log('Prev AASV', prevAASV);
    return prevAASV;

}

function compareDates(dateString1, dateString2) {
    if(dateString1 == null && dateString2 !== null){
        return -1;
    } else if( dateString2 == null && dateString1 !== null){
        return 1;
    } else if (dateString1 == null && dateString2 == null){
        return 0;
    }
    const date1Components = dateString1.split(/[-T:.Z]/);
    const date2Components = dateString2.split(/[-T:.Z]/);

    // Convert components to numbers
    const date1Values = date1Components.map(Number);
    const date2Values = date2Components.map(Number);

    // Compare each component
    for (let i = 0; i < date1Values.length; i++) {
        if (date1Values[i] < date2Values[i]) {
            return -1;
        } else if (date1Values[i] > date2Values[i]) {
            return 1;
        }
    }

    return 0; // Dates are equal
}



function checkUncheckCustomSkus(quoteModel, quoteLineModels){
    let containsCustomSKUs = false;
    const customSKUsNames = ['Enterprise License', 'Product Royalty', 'Store User', 'Cloud Architecture - Other'];

    for (const ql of quoteLineModels) {
        if(customSKUsNames.includes(ql.record['SBQQ__ProductName__c'])){
            containsCustomSKUs = true;
        }
    }

    quoteModel.record['Contains_Custom_SKUs__c'] = containsCustomSKUs;
}

function populatePaperworkSections(quoteModel, quoteLineModels){
    var ppwSectionValues = [];
    let softwareCounter = 0;
    let serviceCounter = 0;
    let paasCounter = 0;
    let consultingCounter = 0;
    let educationCounter = 0;
    for (const ql of quoteLineModels) {
        if(ql.record['PC__c'] === 'Software'){
                softwareCounter++;
            }
        if(ql.record['PC__c'] === 'Service') {
            if (ql.record['ProductFamilyLabel__c'] === 'MCE' || ql.record['ProductFamilyLabel__c'] === 'Cloud' || ql.record['ProductFamilyLabel__c'] === 'MCE Sup') {
                    paasCounter++;
            } else if(ql.record['ProductFamilyLabel__c'].toLocaleLowerCase().includes("education") || ql.record['ProductFamilyLabel__c'] === 'Event' ){
                    educationCounter++;
            } else if (ql.record['ProductFamilyLabel__c'].toLocaleLowerCase().includes("consulting") || ql.record['ProductFamilyLabel__c'] === 'Expense'){
                    consultingCounter++;
                } else {
                    serviceCounter++;
                }
            }
        }

    if (softwareCounter > 0) {
        ppwSectionValues.push("Software");
    }
    if (paasCounter > 0) {
        ppwSectionValues.push("PaaS Components");
    }
    if (serviceCounter > 0) {
        ppwSectionValues.push("Services");
    }
    if (consultingCounter > 0) {
        ppwSectionValues.push("Consulting");
    }
    if (educationCounter > 0) {
        ppwSectionValues.push("Education");
    }

    quoteModel.record['Paperwork_Sections__c'] = ppwSectionValues.join(";");
}