[{"configId": "RpzM2UnAKfj033JK8x", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "id", "sf_fieldLabel": "Record ID", "sf_childRelationshipName": "", "cp_fieldName": "integrationId", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "VudzYo6APXne0if3pX", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "clientDetails", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "WUmQajncWznrwgycCr", "isConstant": false, "cp_fieldName": "clientTypeId", "cp_fieldLabel": "Standard : Partner Type", "sf_fieldName": "SBQQ__Account__r.type", "sf_fieldLabel": "SBQQ__Account__r.Account Type"}, {"configId": "IZuSacUbVwudXkLdwZ", "isConstant": false, "cp_fieldName": "customerName", "cp_fieldLabel": "Standard : Partner Name", "sf_fieldName": "SBQQ__Account__r.name", "sf_fieldLabel": "SBQQ__Account__r.Account Name"}, {"configId": "nxQbihgaZ4nMV3MlYY", "isConstant": false, "cp_fieldName": "addressLine1", "cp_fieldLabel": "Standard : Address", "sf_fieldName": "billing_street__c", "sf_fieldLabel": "Billing Street"}, {"configId": "ihb2vwZYz1aYX7KWSb", "isConstant": false, "cp_fieldName": "countryId", "cp_fieldLabel": "Standard : Country", "sf_fieldName": "sbqq__billingcountry__c", "sf_fieldLabel": "<PERSON>"}, {"configId": "5XvyOaXULTYZ2jSlR1", "isConstant": false, "cp_fieldName": "city", "cp_fieldLabel": "Standard : City", "sf_fieldName": "sbqq__billingcity__c", "sf_fieldLabel": "<PERSON>"}, {"configId": "Q8ITORtTzcQD1Ni6nT", "isConstant": false, "cp_fieldName": "state", "cp_fieldLabel": "Standard : State/Province", "sf_fieldName": "sbqq__billingstate__c", "sf_fieldLabel": "Bill To State"}, {"configId": "uL1w7FFrxN0LTjWODt", "isConstant": false, "cp_fieldName": "pincode", "cp_fieldLabel": "Standard : Pincode/Postal Code", "sf_fieldName": "sbqq__billingpostalcode__c", "sf_fieldLabel": "Bill To Postal Code"}], "configId": "fjp48PE0mprjVBtecQ", "sf_sortorder_field": "", "sf_sortorder_direction": ""}], "showFilter": false}, {"configId": "y1S7bkecLWAMJdpcEo", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "assignees", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "b2D0CqCgYazgwY5kpF", "isConstant": true, "cp_fieldName": "assigntoUsername", "cp_fieldLabel": "Standard : Assignee", "sf_fieldName": "ContractPod", "sf_fieldLabel": "ContractPod"}, {"configId": "iWG4OTAKSA1AyRMv8V", "isConstant": true, "cp_fieldName": "departmentId", "cp_fieldLabel": "Standard : Department", "sf_fieldName": "CPAi Support", "sf_fieldLabel": "CPAi Support"}], "configId": "srDU2axnlDffWSOi7Y"}], "showFilter": false}, {"configId": "mteLUfUrrR5eYJJrHQ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_document_quote_name__c", "sf_fieldLabel": "Document Quote Name", "sf_childRelationshipName": "", "cp_fieldName": "OrderForm", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "uytyoFhlXV5bCLEGyQ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_company__c", "sf_fieldLabel": "Billing Company", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Customer", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "HLCVRihTyD5AuLloLW", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_name__c", "sf_fieldLabel": "<PERSON> Contact Name", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Contact", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "lXBDtQtWeX8k8sMWtW", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_email__c", "sf_fieldLabel": "Billing Email", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Email", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "TDmIcn14Ebp8KgOhEk", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_address__c", "sf_fieldLabel": "Billing Address", "sf_childRelationshipName": "", "cp_fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "eWiNWVMyNSjk1NHJPz", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__billingcountry__c", "sf_fieldLabel": "<PERSON>", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Country", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "em2EE4rpPIgJMiz6Tp", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_name__c", "sf_fieldLabel": "Ship To Contact Name", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Contact", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "IkbiYjXynzdUcpUaHr", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_email__c", "sf_fieldLabel": "Shipping Email", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Email", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "W5EVPzeNSbAmAfPmPa", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_address__c", "sf_fieldLabel": "Shipping Address", "sf_childRelationshipName": "", "cp_fieldName": "ShiptoAddress", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "m2ZskM1DmrGBX5nOCs", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__shippingcountry__c", "sf_fieldLabel": "Ship To Country", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Country", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "JcddaJAxxhiSgGhtuX", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_company__c", "sf_fieldLabel": "Shipping Company", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Customer", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "dXKnzzUrdvS1iuzhAw", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "output_expiration_date_full_month__c", "sf_fieldLabel": "Output Expiration Date - Full Month", "sf_childRelationshipName": "", "cp_fieldName": "QuoteExpDateNew", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "fqkTrXu9KwsVgdN2JU", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "dsiname__c", "sf_fieldLabel": "DSI Name", "sf_childRelationshipName": "", "cp_fieldName": "DSICustom", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "dWjcLj5m4KvobQhPKz", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "billing_frequency__c", "sf_fieldLabel": "Billing Frequency", "sf_childRelationshipName": "", "cp_fieldName": "InvoicingSchedule", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "9mAmN1LK6GnM3kwVhX", "mappingType": "relatedDataMapping", "isConstant": false, "isRelatedMap": true, "isReferenceMapping": false, "sf_fieldName": "SBQQ__QuoteLine__c", "sf_fieldLabel": "", "sf_childRelationshipName": "SBQQ__LineItems__r", "cp_fieldName": "ServiceQuoteTable", "cp_fieldType": "table", "isCompositeMap": false, "sf_sortorder_field": "", "sf_sortorder_direction": "", "relatedDataMapping": [{"configId": "xivUXMPBPzttCg2nAM", "isConstant": false, "cp_fieldName": "ProductCategory", "cp_fieldLabel": "Product Category", "sf_fieldName": "pc__c", "sf_fieldLabel": "Product Category"}, {"configId": "ngyMndHFu2NkCPdX9V", "isConstant": false, "cp_fieldName": "ServiceGrouping", "cp_fieldLabel": "Quote Grouping", "sf_fieldName": "SBQQ__Group__r.name", "sf_fieldLabel": "SBQQ__Group__r.Group Name"}, {"configId": "hO6Lb0UfsjAObIUCeP", "isConstant": false, "cp_fieldName": "ServiceSKU", "cp_fieldLabel": "Quote Line SKU", "sf_fieldName": "sbqq__productcode__c", "sf_fieldLabel": "Product Code"}, {"configId": "f6CAj5jZgqnLgdy9os", "isConstant": false, "cp_fieldName": "ServiceSKUDescription", "cp_fieldLabel": "Quote Line SKU Description", "sf_fieldName": "sbqq__productname__c", "sf_fieldLabel": "Product Name"}, {"configId": "KkCKyN0WQ6v94FCXGx", "isConstant": false, "cp_fieldName": "ServiceSKUType", "cp_fieldLabel": "Quote Line SKU Type", "sf_fieldName": "sbcf_text_environment__c", "sf_fieldLabel": "Environment"}, {"configId": "kq5Vs0IaOCp21QDekz", "isConstant": false, "cp_fieldName": "ServiceItemTerm", "cp_fieldLabel": "Quote <PERSON><PERSON>", "sf_fieldName": "ppw_initial_term_text__c", "sf_fieldLabel": "PPW Initial Term Text"}, {"configId": "t0iVrayhh2TA8eJmqV", "isConstant": false, "cp_fieldName": "ServiceAnnualListPrice", "cp_fieldLabel": "Quote Line Annual List Price", "sf_fieldName": "sbcf_annual_value__c", "sf_fieldLabel": "Annual Value"}, {"configId": "PfW1JSHSi1bKaCkYWh", "isConstant": false, "cp_fieldName": "ServiceQuantity", "cp_fieldLabel": "Quote Line Quantity", "sf_fieldName": "sbqq__quantity__c", "sf_fieldLabel": "Quantity"}, {"configId": "dfGaC8r2MEqSs0rGPQ", "isConstant": false, "cp_fieldName": "ServiceGrossTotal", "cp_fieldLabel": "Quote Line Gross Total", "sf_fieldName": "sbcf_annual_value__c", "sf_fieldLabel": "Annual Value"}, {"configId": "tO7g9z056xR0pfcH6w", "isConstant": false, "cp_fieldName": "ServiceDiscount", "cp_fieldLabel": "Quote Line Discount  %", "sf_fieldName": "sbqq__totaldiscountrate__c", "sf_fieldLabel": "Total Discount (%)"}, {"configId": "dOfVn3PxCGjUNSQjrT", "isConstant": false, "cp_fieldName": "ServiceDiscountAmount", "cp_fieldLabel": "Quote Line Discount Amount", "sf_fieldName": "sbqq__totaldiscountamount__c", "sf_fieldLabel": "Total Discount (Amt)"}, {"configId": "51ddlDvAasLQzKCDGY", "isConstant": false, "cp_fieldName": "ServiceTotal", "cp_fieldLabel": "Quote Line Total", "sf_fieldName": "pgt__c", "sf_fieldLabel": "Paperwork Gross Total"}, {"configId": "mxfw6ua7mqQ6JkQ2rO", "isConstant": false, "cp_fieldName": "ServiceSKUInterval", "cp_fieldLabel": "Interval", "sf_fieldName": "sbcf_text_interval__c", "sf_fieldLabel": "Interval"}], "criteria": {"conditions": [{"configid": "m1wki8eqsbech4gg2na", "id": 1, "key": "paperwork_section__c", "keyLabel": "Paperwork Section", "opr": "equals", "value": "Services", "valueLabel": "Services", "isValRequired": true, "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}], "action": "AND", "expression": 1}, "showFilter": false}, {"configId": "bncLgnHwPpK8QCONAB", "mappingType": "relatedDataMapping", "isConstant": false, "isRelatedMap": true, "isReferenceMapping": false, "sf_fieldName": "SBQQ__QuoteLine__c", "sf_fieldLabel": "", "sf_childRelationshipName": "SBQQ__LineItems__r", "cp_fieldName": "SoftwareQuoteTable", "cp_fieldType": "table", "isCompositeMap": false, "sf_sortorder_field": "", "sf_sortorder_direction": "", "relatedDataMapping": [{"configId": "4Idi54QI6M7iVQzgSs", "isConstant": false, "cp_fieldName": "SoftwareProductCategory", "cp_fieldLabel": "Product Category", "sf_fieldName": "product_category__c", "sf_fieldLabel": "Product Category"}, {"configId": "GPU8fyZdb5nZKrcmDT", "isConstant": false, "cp_fieldName": "SoftwareGrouping", "cp_fieldLabel": "Quote Grouping", "sf_fieldName": "SBQQ__Group__r.name", "sf_fieldLabel": "SBQQ__Group__r.Group Name"}, {"configId": "uF0GlnhBhXn40GkOqj", "isConstant": false, "cp_fieldName": "SoftwareSKU", "cp_fieldLabel": "Quote Line SKU", "sf_fieldName": "sbqq__productcode__c", "sf_fieldLabel": "Product Code"}, {"configId": "eUBXwjkjnoviRiNn2k", "isConstant": false, "cp_fieldName": "SoftwareSKUDescription", "cp_fieldLabel": "Quote Line SKU Description", "sf_fieldName": "sbqq__productname__c", "sf_fieldLabel": "Product Name"}, {"configId": "2t61814TGRRSKFOuRF", "isConstant": false, "cp_fieldName": "SoftwareSKUType", "cp_fieldLabel": "Quote Line SKU Type", "sf_fieldName": "sbcf_text_environment__c", "sf_fieldLabel": "Environment"}, {"configId": "BtTvgcpotHQGkFoI86", "isConstant": false, "cp_fieldName": "SoftwareItemTerm", "cp_fieldLabel": "Quote <PERSON><PERSON>", "sf_fieldName": "ppw_initial_term_text__c", "sf_fieldLabel": "PPW Initial Term Text"}, {"configId": "13XHe4N9sW5tnCKKDT", "isConstant": false, "cp_fieldName": "SoftwareAnnualListPrice", "cp_fieldLabel": "Quote Line Annual List Price", "sf_fieldName": "sbcf_annual_value__c", "sf_fieldLabel": "Annual Value"}, {"configId": "urpDsrFtb9ZAjQpKNL", "isConstant": false, "cp_fieldName": "SoftwareQuantity", "cp_fieldLabel": "Quote Line Quantity", "sf_fieldName": "sbqq__quantity__c", "sf_fieldLabel": "Quantity"}, {"configId": "ftIpm4whd4R2WCSbHs", "isConstant": false, "cp_fieldName": "SoftwareGrossTotal", "cp_fieldLabel": "Quote Line Gross Total", "sf_fieldName": "sbcf_annual_value__c", "sf_fieldLabel": "Annual Value"}, {"configId": "sRKOAKF9FEkKeDsvbc", "isConstant": false, "cp_fieldName": "SoftwareDiscount", "cp_fieldLabel": "Quote Line Discount  %", "sf_fieldName": "sbqq__totaldiscountrate__c", "sf_fieldLabel": "Total Discount (%)"}, {"configId": "ZfQ4sQUkFcDtpzmrYo", "isConstant": false, "cp_fieldName": "SoftwareDiscountAmount", "cp_fieldLabel": "Quote Line Discount Amount", "sf_fieldName": "sbqq__totaldiscountamount__c", "sf_fieldLabel": "Total Discount (Amt)"}, {"configId": "VYXHBb2BTVC8Lg9pMU", "isConstant": false, "cp_fieldName": "SoftwareTotal", "cp_fieldLabel": "Quote Line Total", "sf_fieldName": "pgt__c", "sf_fieldLabel": "Paperwork Gross Total"}, {"configId": "tTZQb8DkcKLnTDd1o7", "isConstant": false, "cp_fieldName": "SoftwareStartDate", "cp_fieldLabel": "Start Date", "sf_fieldName": "sbcf_output_start_date__c", "sf_fieldLabel": "Output Start Date"}, {"configId": "u2Dxj48Ww8jWYaVZFj", "isConstant": false, "cp_fieldName": "SoftwareEndDate", "cp_fieldLabel": "End Date", "sf_fieldName": "sbcf_output_end_date__c", "sf_fieldLabel": "Output End Date"}, {"configId": "gH6Coeyo9udxNZCVDD", "isConstant": false, "cp_fieldName": "SoftwareInterval", "cp_fieldLabel": "Interval", "sf_fieldName": "sbcf_text_interval__c", "sf_fieldLabel": "Interval"}], "criteria": {"conditions": [{"configid": "m1whwygztb2nsy3av8i", "id": 1, "key": "paperwork_section__c", "keyLabel": "Paperwork Section", "opr": "equals", "value": "Software", "valueLabel": "Software", "isValRequired": true, "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}], "action": "AND", "expression": 1}, "showFilter": false}, {"configId": "OO5Bf7x9g89s0TmyGR", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "paperwork_sections__c", "sf_fieldLabel": "Paperwork Sections", "sf_childRelationshipName": "", "cp_fieldName": "PaperworkSection", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "Zpt7Ye5IgXQP10YQQo", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_document_language__c", "sf_fieldLabel": "Territory of Sale", "sf_childRelationshipName": "", "cp_fieldName": "TerritoryOfSale", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "WnT5MCGwMXXw4pePUD", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "ma_ref__c", "sf_fieldLabel": "Reference to Negotiated Master Agreement", "sf_childRelationshipName": "", "cp_fieldName": "Ref2NegotiatedMANewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "vE6TihctleThsv9jmC", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_partner_account_name__c", "sf_fieldLabel": "Partner Account Name", "sf_childRelationshipName": "", "cp_fieldName": "PartnerAccountName", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "E9L1IPb9kts0rQds01", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_reseller_quote__c", "sf_fieldLabel": "Reseller Quote?", "sf_childRelationshipName": "", "cp_fieldName": "ResellerQuoteNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "sofIyFLvf1xfMoMJ6j", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__status__c", "sf_fieldLabel": "Status", "sf_childRelationshipName": "", "cp_fieldName": "Quote<PERSON><PERSON><PERSON>", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "QT2mUbGWTRQ7cwViPf", "mappingType": "relatedDataMapping", "isConstant": false, "isRelatedMap": true, "isReferenceMapping": false, "sf_fieldName": "SBQQ__QuoteLine__c", "sf_fieldLabel": "", "sf_childRelationshipName": "SBQQ__LineItems__r", "cp_fieldName": "AdditionalPaaSQuoteTable", "cp_fieldType": "table", "isCompositeMap": false, "sf_sortorder_field": "", "sf_sortorder_direction": "", "relatedDataMapping": [{"configId": "Cnu4spmNAQM6PM8NVF", "isConstant": false, "cp_fieldName": "PaaSProductCategory", "cp_fieldLabel": "Product Category", "sf_fieldName": "product_category__c", "sf_fieldLabel": "Product Category"}, {"configId": "7gyraMinSDpeqnfitW", "isConstant": false, "cp_fieldName": "PaaSGrouping", "cp_fieldLabel": "Quote Grouping", "sf_fieldName": "group_name__c", "sf_fieldLabel": "Group Name"}, {"configId": "jaMkrVmyR9F6vtaz5K", "isConstant": false, "cp_fieldName": "PaaSSKUDescription", "cp_fieldLabel": "Quote Line SKU Description", "sf_fieldName": "sbqq__productname__c", "sf_fieldLabel": "Product Name"}, {"configId": "KsfvUUvQfe6h823E1N", "isConstant": false, "cp_fieldName": "PaaSSKUType", "cp_fieldLabel": "Quote Line SKU Type", "sf_fieldName": "sbcf_text_environment__c", "sf_fieldLabel": "Environment"}, {"configId": "GeYweCxuNftGuth9Ia", "isConstant": false, "cp_fieldName": "PaaSItemTerm", "cp_fieldLabel": "Quote <PERSON><PERSON>", "sf_fieldName": "ppw_initial_term_text__c", "sf_fieldLabel": "PPW Initial Term Text"}, {"configId": "LBASYMpCUYNf78bqyL", "isConstant": false, "cp_fieldName": "PaaSAnnualListPrice", "cp_fieldLabel": "Quote Line Annual List Price", "sf_fieldName": "sbcf_annual_value__c", "sf_fieldLabel": "Annual Value"}, {"configId": "69uD3gaflPsTWdwtRK", "isConstant": false, "cp_fieldName": "PaaSLineQuantity", "cp_fieldLabel": "Quote Line Quantity", "sf_fieldName": "sbqq__quantity__c", "sf_fieldLabel": "Quantity"}, {"configId": "WCZ5pkfUX72XEadrSw", "isConstant": false, "cp_fieldName": "PaaSGrossTotal", "cp_fieldLabel": "Quote Line Gross Total", "sf_fieldName": "sbcf_annual_value__c", "sf_fieldLabel": "Annual Value"}, {"configId": "dDDSu2laBYBOYUnr8E", "isConstant": false, "cp_fieldName": "PaaSSKU", "cp_fieldLabel": "Quote Line SKU", "sf_fieldName": "sbqq__productcode__c", "sf_fieldLabel": "Product Code"}, {"configId": "xOufVvcWkZJuklKO4v", "isConstant": false, "cp_fieldName": "<PERSON><PERSON><PERSON>unt", "cp_fieldLabel": "Quote Line Discount  %", "sf_fieldName": "sbqq__totaldiscountrate__c", "sf_fieldLabel": "Total Discount (%)"}, {"configId": "MyRpleFAfd9Vq0Ppkd", "isConstant": false, "cp_fieldName": "PaaSDiscountAmount", "cp_fieldLabel": "Quote Line Discount Amount", "sf_fieldName": "sbqq__totaldiscountamount__c", "sf_fieldLabel": "Total Discount (Amt)"}, {"configId": "E0qneHIlYR3dSOkVPh", "isConstant": false, "cp_fieldName": "PaaSTotal", "cp_fieldLabel": "Quote Line Total", "sf_fieldName": "pgt__c", "sf_fieldLabel": "Paperwork Gross Total"}], "criteria": {"conditions": [{"configid": "m1v79ns6vz0y18vvu7", "id": 1, "key": "paperwork_section__c", "keyLabel": "Paperwork Section", "opr": "equals", "value": "Additional PaaS Components", "valueLabel": "Additional PaaS Components", "isValRequired": true, "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}], "action": "AND", "expression": 1}, "showFilter": false}, {"configId": "RJrGKQYgu2siXtEXD7", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "CPAI_Template_Field__r.paperwork_section_headers__c", "sf_fieldLabel": "CPAI_Template_Field__r.Paperwork Section Headers", "sf_childRelationshipName": "", "cp_fieldName": "PaperworkSectionHeaderNew", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "fdlWThIfMScJ5zY6km", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_negotiated__c", "sf_fieldLabel": "Negotiated?", "sf_childRelationshipName": "", "cp_fieldName": "Negotiated", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "YCiLCrnmHb1rILB710", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "document_execution_requirement__c", "sf_fieldLabel": "Document Execution Requirement", "sf_childRelationshipName": "", "cp_fieldName": "DocumentExecutionRequirement", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "LJ3QbRY1JLvkaTBvy4", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "mstr_signs_first__c", "sf_fieldLabel": "MSTR Signs First", "sf_childRelationshipName": "", "cp_fieldName": "MSTRSignsFirst", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "HTABM9fFMs6rVpLFg1", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "internal_comments__c", "sf_fieldLabel": "Internal Comments", "sf_childRelationshipName": "", "cp_fieldName": "InternalComments", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "KuxiywxyIqm8KgsFVV", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "customer_signer_email__c", "sf_fieldLabel": "Customer Signer <PERSON><PERSON>", "sf_childRelationshipName": "", "cp_fieldName": "CustomerSignerEmail", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "y7OKu7zIps4Iz4LPoM", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "legal_clarification_notes__c", "sf_fieldLabel": "Legal Clarification Notes", "sf_childRelationshipName": "", "cp_fieldName": "LegalClarificationNotes", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "1spseYhaBNP7Gawzx8", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "account_name__c", "sf_fieldLabel": "Account Name", "sf_childRelationshipName": "", "cp_fieldName": "Account<PERSON><PERSON>", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "YNmXXHCgcaAP5Q4waK", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "quote_offer_type__c", "sf_fieldLabel": "Quote Offer Type", "sf_childRelationshipName": "", "cp_fieldName": "QuoteOfferType", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "KqHMPlJ1sHb389pWkb", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "is_perpetual__c", "sf_fieldLabel": "is Perpetual", "sf_childRelationshipName": "", "cp_fieldName": "IsPerpetualNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "GMk6EOOYwUtg6I3ZnJ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__type__c", "sf_fieldLabel": "Type", "sf_childRelationshipName": "", "cp_fieldName": "TypeNew", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "iOGX4zjnundZD6UeBh", "mappingType": "relatedDataMapping", "isConstant": false, "isRelatedMap": true, "isReferenceMapping": false, "sf_fieldName": "SBQQ__QuoteLine__c", "sf_fieldLabel": "", "sf_childRelationshipName": "SBQQ__LineItems__r", "cp_fieldName": "EduQuoteTable", "cp_fieldType": "table", "isCompositeMap": false, "isFileUpload": false, "sf_sortorder_field": "", "sf_sortorder_direction": "", "relatedDataMapping": [{"configId": "V5mI34RBwipXYzCCum", "isConstant": false, "cp_fieldName": "EduSKU", "cp_fieldLabel": "SKU", "sf_fieldName": "sbqq__productcode__c", "sf_fieldLabel": "Product Code"}, {"configId": "8KBoPa3h6bI2qV1OLU", "isConstant": false, "cp_fieldName": "EduSKUDescription", "cp_fieldLabel": "Description", "sf_fieldName": "sbqq__productname__c", "sf_fieldLabel": "Product Name"}, {"configId": "D6NvfVNss2pYt1us7Z", "isConstant": false, "cp_fieldName": "EduSKUTerm", "cp_fieldLabel": "License Term", "sf_fieldName": "ppw_initial_term_text__c", "sf_fieldLabel": "PPW Initial Term Text"}, {"configId": "MahCndUADbMuHX7IVo", "isConstant": false, "cp_fieldName": "EduSKUQty", "cp_fieldLabel": "Qty", "sf_fieldName": "sbqq__quantity__c", "sf_fieldLabel": "Quantity"}, {"configId": "PlOOpa8BhIris5SZql", "isConstant": false, "cp_fieldName": "EduSKUGrouping", "cp_fieldLabel": "Group Name", "sf_fieldName": "group_name__c", "sf_fieldLabel": "Group Name"}], "criteria": {"conditions": [{"configid": "m1wjygmr5slxdbqzhcv", "id": 1, "key": "paperwork_section__c", "keyLabel": "Paperwork Section", "opr": "equals", "value": "Education", "valueLabel": "Education", "isValRequired": true, "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}], "action": "AND", "expression": 1}, "showFilter": false}, {"configId": "MeHDVv1kOvx8vW1on7", "mappingType": "relatedDataMapping", "isConstant": false, "isRelatedMap": true, "isReferenceMapping": false, "sf_fieldName": "SBQQ__QuoteLine__c", "sf_fieldLabel": "", "sf_childRelationshipName": "SBQQ__LineItems__r", "cp_fieldName": "ConQuoteTable", "cp_fieldType": "table", "isCompositeMap": false, "isFileUpload": false, "sf_sortorder_field": "", "sf_sortorder_direction": "", "relatedDataMapping": [{"configId": "90kbfJo2NIwjcDTwns", "isConstant": false, "cp_fieldName": "ConSku", "cp_fieldLabel": "SKU", "sf_fieldName": "sbqq__productcode__c", "sf_fieldLabel": "Product Code"}, {"configId": "c6xWDo2YZwXxczZVYK", "isConstant": false, "cp_fieldName": "ConDescription", "cp_fieldLabel": "Description", "sf_fieldName": "sbqq__productname__c", "sf_fieldLabel": "Product Name"}, {"configId": "YpJoXGrPN032Z0Mfqy", "isConstant": false, "cp_fieldName": "ConInterval", "cp_fieldLabel": "Interval", "sf_fieldName": "sbcf_text_interval__c", "sf_fieldLabel": "Interval"}, {"configId": "eM3y37PVbaO2ETqDTI", "isConstant": false, "cp_fieldName": "<PERSON><PERSON><PERSON>", "cp_fieldLabel": "Qty", "sf_fieldName": "sbqq__quantity__c", "sf_fieldLabel": "Quantity"}, {"configId": "9mbxuTwKCVWkV0QtOa", "isConstant": false, "cp_fieldName": "ConGrouping", "cp_fieldLabel": "Group Name", "sf_fieldName": "group_name__c", "sf_fieldLabel": "Group Name"}], "criteria": {"conditions": [{"configid": "m23bjzpz1off367xqee", "id": 1, "key": "paperwork_section__c", "keyLabel": "Paperwork Section", "opr": "equals", "value": "Consulting", "valueLabel": "Consulting", "isValRequired": true, "keySrc": "sfdc", "filterType": "STRING", "disableValue": false, "isConstant": true, "valueSrc": "constant"}], "action": "AND", "expression": 1}, "showFilter": false}, {"configId": "9SZK0N7OK956qyt0Lb", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__lineitemsgrouped__c", "sf_fieldLabel": "Group Line Items", "sf_childRelationshipName": "", "cp_fieldName": "GroupLineItems", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "4Y6kaXNxTPgUm6my1F", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "Reseller__r.name", "sf_fieldLabel": "Reseller__r.Account Name", "sf_childRelationshipName": "", "cp_fieldName": "Reseller", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "iB4NmH6Ex6jctKAIlV", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "SBQQ__Distributor__r.name", "sf_fieldLabel": "SBQQ__Distributor__r.Account Name", "sf_childRelationshipName": "", "cp_fieldName": "Distributor", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}]