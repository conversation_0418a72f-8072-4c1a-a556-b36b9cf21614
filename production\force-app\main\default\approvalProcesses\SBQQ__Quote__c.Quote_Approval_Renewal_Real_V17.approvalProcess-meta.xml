<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <submitter>All_Internal_Users1</submitter>
        <type>group</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>Account_Customer_Status__c</field>
        <field>SBCF_CPI__c</field>
        <field>Renewal_Discount__c</field>
        <field>years_commitment__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Quote_Status_pending_SEVP_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Status_to_SVP_Q03</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1a</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>EMEA, ( &lt;250k AND &gt;=3% ,  OR  , &gt;=250k AND &gt;0)</description>
        <entryCriteria>
            <booleanFilter>(1 OR 2)  AND ((3 AND 4) OR (5 AND 6))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Austria,Belgium,Denmark,EMEA Distribution,France,Germany,Israel,Italy</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Middle East,Netherlands,Poland,Portugal,South Africa,Spain,Sweden,Switzerland - English,Switzerland - German,Turkey,United Kingdom</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_for_higher_than_250K</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Mx_Business_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Quote_Status_pending_legal_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Record_Type_Pending_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Status_NEW_Q5_LEG</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1_Legal</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>EMEA, &lt;250K AND &lt;3%, OR , &gt;=250K AND =0% Negotiated</description>
        <entryCriteria>
            <booleanFilter>(1 OR 2) AND 5 AND ((3 AND 4) OR (6 AND 7))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Austria,Belgium,Denmark,EMEA Distribution,France,Germany,Israel,Italy</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Middle East,Netherlands,Poland,Portugal,South Africa,Spain,Sweden,Switzerland - English,Switzerland - German,Turkey,United Kingdom</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>equals</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_for_less_250K</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>EMEA, &lt;250K AND &lt;3%, OR , &gt;=250K AND =0% NON negotiated</description>
        <entryCriteria>
            <booleanFilter>(1 OR 2) AND 5 AND ((3 AND 4) OR (6 AND 7))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Austria,Belgium,Denmark,EMEA Distribution,France,Germany,Israel,Italy</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Middle East,Netherlands,Poland,Portugal,South Africa,Spain,Sweden,Switzerland - English,Switzerland - German,Turkey,United Kingdom</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>False</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>equals</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_NON_negotiated</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Quote_Status_pending_SEVP_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Status_to_SVP_Q03</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1a</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>APAC, ( &lt;250k AND &gt;=3% , OR , &gt;=250k AND &gt;0)</description>
        <entryCriteria>
            <booleanFilter>1 AND ((2 AND 3) OR (4 AND 5))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Australia,China,Japan,Korea,New Zealand,Singapore,Taiwan</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_for_higher_than_250K_APAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Mx_Business_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Quote_Status_pending_legal_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Record_Type_Pending_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Status_NEW_Q5_LEG</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1_Legal</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>APAC, &lt;250K AND &lt;3%, OR , &gt;=250K AND =0% Negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 4 AND ((2 AND 3) OR (5 AND 6))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Australia,China,Japan,Korea,New Zealand,Singapore,Taiwan</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>equals</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_for_less_250K_APAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>APAC, &lt;250K AND &lt;3%, OR , &gt;=250K AND =0% NON negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 4 AND ((2 AND 3) OR (5 AND 6))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Australia,China,Japan,Korea,New Zealand,Singapore,Taiwan</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>False</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>equals</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_NON_negotiated_APAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Quote_Status_pending_SEVP_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Status_to_SVP_Q03</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1a</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>LATAM,
, ( &lt;250k AND &gt;=3% , OR , &gt;=250k AND &gt;0)</description>
        <entryCriteria>
            <booleanFilter>1 AND ((2 AND 3) OR (4 AND 5))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Argentina,Bolivia,Brazil,Central America,Chile,Colombia,Ecuador,Mexico,Paraguay,Peru</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>LatAm_theatre_CSM_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Mx_Business_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Quote_Status_pending_legal_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Record_Type_Pending_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Status_NEW_Q5_LEG</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1_Legal</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>LATAM, &lt;250K AND &lt;3%, OR , &gt;=250K AND =0% Negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 4 AND ((2 AND 3) OR (5 AND 6))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Argentina,Bolivia,Brazil,Central America,Chile,Colombia,Ecuador,Mexico,Paraguay,Peru</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>equals</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_for_LATAM_less_250K</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>LATAM, &lt;250K AND &lt;3%, OR , &gt;=250K AND =0% NON negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 4 AND ((2 AND 3) OR (5 AND 6))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Argentina,Bolivia,Brazil,Central America,Chile,Colombia,Ecuador,Mexico,Paraguay,Peru</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>False</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>equals</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_NON_Negotiated_LATAM</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Quote_Status_pending_SEVP_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Status_to_SVP_Q03</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1a</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Others ( &lt;250k AND &gt;=3% , OR , &gt;=250k AND &gt;0)</description>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND ((3 AND 4) OR (5 AND 6))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Argentina,Australia,Austria,Belgium,Bolivia,Brazil,Central America,Chile,China,Colombia,Denmark,Ecuador,EMEA Distribution,France,Germany,Israel,Italy,Japan,Korea</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Mexico,Middle East,Netherlands,New Zealand,Paraguay,Peru,Poland,Portugal,Singapore,South Africa,Spain,Sweden,Switzerland - English,Switzerland - German,Taiwan,Turkey,United Kingdom</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>Other_theatre_CSM_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Mx_Business_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Quote_Status_pending_legal_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Record_Type_Pending_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Status_NEW_Q5_LEG</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1_Legal</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Others, &lt;250K AND &lt;3%, OR , &gt;=250K AND =0% Negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 5 AND ((3 AND 4) OR (6 AND 7))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Argentina,Australia,Austria,Belgium,Bolivia,Brazil,Central America,Chile,China,Colombia,Denmark,Ecuador,EMEA Distribution,France,Germany,Israel,Italy,Japan,Korea</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Mexico,Middle East,Netherlands,New Zealand,Paraguay,Peru,Poland,Portugal,Singapore,South Africa,Spain,Sweden,Switzerland - English,Switzerland - German,Taiwan,Turkey,United Kingdom</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>equals</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_Others_less_250K</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Others, &lt;250K AND &lt;3%, OR , &gt;=250K AND =0% NON negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 5 AND ((3 AND 4) OR (6 AND 7))</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Argentina,Australia,Austria,Belgium,Bolivia,Brazil,Central America,Chile,China,Colombia,Denmark,Ecuador,EMEA Distribution,France,Germany,Israel,Italy,Japan,Korea</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Mexico,Middle East,Netherlands,New Zealand,Paraguay,Peru,Poland,Portugal,Singapore,South Africa,Spain,Sweden,Switzerland - English,Switzerland - German,Taiwan,Turkey,United Kingdom</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>False</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>equals</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CSM Approval</label>
        <name>CSM_Approval_NON_NegotiatedOthers</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_by_CS_VP_Pending_EVP_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>set_Status_to_Q03_EVP</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1a</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>( &lt;250k AND &gt;=3% , OR , &gt;=250k AND &gt;0)</description>
        <entryCriteria>
            <booleanFilter>(1 AND 2 ) OR (3 AND 4)</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>lessThan</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CS VP Approval</label>
        <name>CS_VP_Approval_To_SVP</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_by_EVP_Pending_CXO_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Status_C4_CFO</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1c</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>AM and  &gt;=5%  )</description>
        <entryCriteria>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Canada,United States</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>5</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>EVP Approval</label>
        <name>EVP_Approval_AM</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Mx_Business_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Quote_Status_pending_legal_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Record_Type_Pending_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Status_NEW_Q5_LEG</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1_Legal</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>AM, &gt;=3 AND &lt;5 , OR , &gt;=250K AND &lt;5% AND &gt;0% Negotiated</description>
        <entryCriteria>
            <booleanFilter>(1 AND 4) AND ( (2 AND 3 ) OR  (2 AND 5 AND 6) )</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Canada,United States</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>5</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>EVP Approval</label>
        <name>EVP_Approval_AM_bw_3_and_5</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>AM,  &gt;=3 AND &lt;5 , OR , &gt;=250K AND &lt;5% AND &gt;0%  NON Negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 4 AND  ( (2 AND 3 ) OR (2 AND 5 AND 6) )</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>equals</operation>
                <value>Canada,United States</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>5</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>False</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>EVP Approval</label>
        <name>EVP_Approval_AM_NON_Negotiated</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_by_EVP_Pending_CXO_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Status_C4_CFO</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1d</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>International and   &gt;=5%  )</description>
        <entryCriteria>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Canada,United States</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>5</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>EVP Approval</label>
        <name>EVP_Approval_International</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Mx_Business_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Quote_Status_pending_legal_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Record_Type_Pending_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Status_NEW_Q5_LEG</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1_Legal</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>International, &gt;=3 AND &lt;5 , OR , &gt;=250K AND &lt;5% AND &gt;0% Negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 4 AND ( (2 AND  3 ) OR (2 AND 5 AND 6) )</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Canada,United States</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>5</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>EVP Approval</label>
        <name>EVP_Approval_International_Negotiated</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>International,  &gt;=3 AND &lt;5 , OR , &gt;=250K AND &lt;5% AND &gt;0% NON Negotiated</description>
        <entryCriteria>
            <booleanFilter>1 AND 4 AND ( (2 AND  3 ) OR (2 AND 5 AND 6) )</booleanFilter>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Document_Language__c</field>
                <operation>notEqual</operation>
                <value>Canada,United States</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>lessThan</operation>
                <value>5</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>3</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Negotiated__c</field>
                <operation>equals</operation>
                <value>False</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Order_Grand_Total__c</field>
                <operation>greaterOrEqual</operation>
                <value>&quot;USD 250,000&quot;</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterThan</operation>
                <value>0</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>EVP Approval</label>
        <name>EVP_Approval_International_NON_negotiated</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Mx_Business_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Discount &gt;= 5%, Standard</description>
        <entryCriteria>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>5</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Agreement_Classification__c</field>
                <operation>equals</operation>
                <value>Standard</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>CXO Approval - Standard</label>
        <name>CXO_Approval_Standard</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Mx_Business_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Quote_Status_pending_legal_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Set_Record_Type_Pending_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Status_NEW_Q5_LEG</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>X3_Approved_1_Legal</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Discount &gt;= 5%</description>
        <entryCriteria>
            <criteriaItems>
                <field>SBQQ__Quote__c.AverageRenewalDiscount__c</field>
                <operation>greaterOrEqual</operation>
                <value>5</value>
            </criteriaItems>
            <criteriaItems>
                <field>SBQQ__Quote__c.SBCF_Agreement_Classification__c</field>
                <operation>equals</operation>
                <value>Negotiated</value>
            </criteriaItems>
        </entryCriteria>
        <label>CXO Approval</label>
        <name>CXO_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>QuoteApprovalApproved</name>
                <type>Alert</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <description>SF Approval Process revamp for CPAI</description>
    <emailTemplate>X2_System_Admin_Templates/Quote_Template_Submitted_Apr_Rej</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <booleanFilter>1 AND 2 AND 3 AND 4 AND 5</booleanFilter>
        <criteriaItems>
            <field>SBQQ__Quote__c.SBQQ__Type__c</field>
            <operation>equals</operation>
            <value>Renewal</value>
        </criteriaItems>
        <criteriaItems>
            <field>SBQQ__Quote__c.SBCF_Approval_Status__c</field>
            <operation>equals</operation>
            <value>Proposal generation with Account Team</value>
        </criteriaItems>
        <criteriaItems>
            <field>SBQQ__Quote__c.Sum_of_Term__c</field>
            <operation>equals</operation>
            <value>0</value>
        </criteriaItems>
        <criteriaItems>
            <field>SBQQ__Quote__c.Q2C_Transition__c</field>
            <operation>notEqual</operation>
            <value>True</value>
        </criteriaItems>
        <criteriaItems>
            <field>SBQQ__Quote__c.Is_SF_Approval_Completed__c</field>
            <operation>equals</operation>
            <value>False</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Quote_Approved_By_Renewal_AP</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>SF_Approval_Completed</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Deactivate_Price_Lock</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Quote_Recalled</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Reject_Approval_Type</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Set_CPQ_Approval_Status_to_Draft</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Set_Quote_to_Draft_Record_Type</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>SF_Approval_Pending</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Uncheck_Mx_Business_Approved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Uncheck_Pending_Approval_Checkbox</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>X3_Reject</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Approved_Approval_Type</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Check_Pending_Approval</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Deactivate_Price_Lock</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>QuoteApprovalApproved</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Set_Q2_CSM</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Set_Quote_to_Submitted_Record_Type</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Submitted_Pending_Operations_Approval</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>X3_Submitted</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Quote Approval - Renewal Real V17</label>
    <processOrder>5</processOrder>
    <recallActions>
        <action>
            <name>Deactivate_Price_Lock</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Quote_Recalled</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>QuoteApprovalApproved</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Set_CPQ_Approval_Status_to_Draft</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Set_Quote_to_Draft_Record_Type</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Set_Status_to_Draft</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>SF_Approval_Pending</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Uncheck_Mx_Business_Approved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Uncheck_Pending_Approval_Checkbox</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>X3_Recalled</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
