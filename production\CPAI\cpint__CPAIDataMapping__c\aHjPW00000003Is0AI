[{"configId": "RpzM2UnAKfj033JK8x", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "id", "sf_fieldLabel": "Record ID", "sf_childRelationshipName": "", "cp_fieldName": "integrationId", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "VudzYo6APXne0if3pX", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "clientDetails", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "WUmQajncWznrwgycCr", "isConstant": true, "cp_fieldName": "clientTypeId", "cp_fieldLabel": "Standard : Partner Type", "sf_fieldName": "Customer", "sf_fieldLabel": "Customer"}, {"configId": "IZuSacUbVwudXkLdwZ", "isConstant": false, "cp_fieldName": "customerName", "cp_fieldLabel": "Standard : Partner Name", "sf_fieldName": "SBQQ__Account__r.name", "sf_fieldLabel": "SBQQ__Account__r.Account Name"}, {"configId": "nxQbihgaZ4nMV3MlYY", "isConstant": false, "cp_fieldName": "addressLine1", "cp_fieldLabel": "Standard : Address", "sf_fieldName": "SBQQ__Account__r.billingstreet", "sf_fieldLabel": "SBQQ__Account__r.<PERSON>"}, {"configId": "ihb2vwZYz1aYX7KWSb", "isConstant": false, "cp_fieldName": "countryId", "cp_fieldLabel": "Standard : Country", "sf_fieldName": "SBQQ__Account__r.billingcountry", "sf_fieldLabel": "SBQQ__Account__r.<PERSON>"}, {"configId": "5XvyOaXULTYZ2jSlR1", "isConstant": false, "cp_fieldName": "city", "cp_fieldLabel": "Standard : City", "sf_fieldName": "SBQQ__Account__r.billingcity", "sf_fieldLabel": "SBQQ__Account__r.Billing City"}, {"configId": "Q8ITORtTzcQD1Ni6nT", "isConstant": false, "cp_fieldName": "state", "cp_fieldLabel": "Standard : State/Province", "sf_fieldName": "SBQQ__Account__r.billingstate", "sf_fieldLabel": "SBQQ__Account__r.Billing State/Province"}, {"configId": "uL1w7FFrxN0LTjWODt", "isConstant": false, "cp_fieldName": "pincode", "cp_fieldLabel": "Standard : Pincode/Postal Code", "sf_fieldName": "SBQQ__Account__r.billingpostalcode", "sf_fieldLabel": "SBQQ__Account__r.Billing Zip/Postal Code"}, {"configId": "vqMFQylYB6YA18tSFL", "isConstant": false, "cp_fieldName": "contactDetailsId", "cp_fieldLabel": "Standard : Contact", "sf_fieldName": "SBQQ__Account__r.phone", "sf_fieldLabel": "SBQQ__Account__r.Account Phone"}], "configId": "fjp48PE0mprjVBtecQ"}], "showFilter": false}, {"configId": "y1S7bkecLWAMJdpcEo", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "assignees", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "b2D0CqCgYazgwY5kpF", "isConstant": true, "cp_fieldName": "assigntoUsername", "cp_fieldLabel": "Standard : Assignee", "sf_fieldName": "ContractPod", "sf_fieldLabel": "ContractPod"}, {"configId": "iWG4OTAKSA1AyRMv8V", "isConstant": true, "cp_fieldName": "departmentId", "cp_fieldLabel": "Standard : Department", "sf_fieldName": "CPAi Support", "sf_fieldLabel": "CPAi Support"}], "configId": "srDU2axnlDffWSOi7Y"}], "showFilter": false}, {"configId": "mteLUfUrrR5eYJJrHQ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "footer_so__c", "sf_fieldLabel": "Footer SO", "sf_childRelationshipName": "", "cp_fieldName": "OrderForm", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "smFfiXfFWwAAWv69ds", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "customer_signer_email__c", "sf_fieldLabel": "Customer Signer <PERSON><PERSON>", "sf_childRelationshipName": "", "cp_fieldName": "CustomerSignerEmail", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "4gBpFtW6bi5VctcycA", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "internal_comments__c", "sf_fieldLabel": "Internal Comments", "sf_childRelationshipName": "", "cp_fieldName": "InternalComments", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "gjLpxyTi7ei7madg5s", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "mstr_signs_first__c", "sf_fieldLabel": "MSTR Signs First", "sf_childRelationshipName": "", "cp_fieldName": "MSTRSignsFirst", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "9pI49PWG9eRAmSgGGP", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "legal_clarification_notes__c", "sf_fieldLabel": "Legal Clarification Notes", "sf_childRelationshipName": "", "cp_fieldName": "LegalClarificationNotes", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}]