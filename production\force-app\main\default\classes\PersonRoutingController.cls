/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 17/06/2021.
 */

public without sharing class PersonRoutingController {

    private List<SObject> personRecords;
    private Set<Id> MQLPersonRecords = new Set<Id>();
    private Set<Id> newOwners = new Set<Id>();

    public PersonRoutingController (List<SObject> personRecords, Set<Id> MQLPersonRecords) {
        this.personRecords = personRecords;
        this.MQLPersonRecords = MQLPersonRecords;
    }

    public void run () {
        if (personRecords != null && !personRecords.isEmpty()) {
            for (SObject person : personRecords) {
                assignDefaultOwner(person);
                switch on routeDecision(person) {
                    when 'AccountOwner' {
                        assignOwnerByAccountOwner(person);
                    }
                    when 'Territory' {
                        assignOwnerByPersonTerritory(person);
                        assignOwnerByAccountTerritory(person);
                    }
                }
                assignCadence(person);
            }
        }
    }

    public void commitChanges () {
        if(personRecords != null && !personRecords.isEmpty()){
            for (sObject person : personRecords) {
                if(MQLPersonRecords.contains((Id)person.get('Id'))){
                    newOwners.add((Id)person.get('OwnerId'));
                }
            }
            if(!newOwners.isEmpty()){
                CampaignMemberTriggerHelper.processMQLChatterNotifications(personRecords, newOwners);
            }
            update personRecords;
        }
    }

    public void assignDefaultOwner (SObject person) {
        person.put('OwnerId', defaultOwnerId);
    }
    
    public String routeDecision(SObject person) {
        String accountId = '';
        String accountCategory = '';
        String accountStrategy = '';
        switch on person.getSObjectType().getDescribe().getName() {
            when 'Contact' {
                Contact personContact = (Contact) person;
                accountId = personContact.AccountId;
                accountCategory = personContact.Account.AccountCategory__c;
                accountStrategy = personContact.Account.AccountStrategy__c;
            }
            when 'Lead' {
                Lead personLead = (Lead) person;
                accountId = personLead.Account_2__c;
                accountCategory = personLead.Account_2__r.AccountCategory__c;
                accountStrategy = personLead.Account_2__r.AccountStrategy__c;
            }
        }
        if (String.isNotBlank(accountId) 
            && accountId != TriggersHelper.unmappedAccountId 
            && accountId != TriggersHelper.resourceCentreAccountId
           	&& !String.isBlank(accountCategory)) {
                if(accountCategory.contains('C2 - Major Customer')
                   || accountCategory.contains('C3 - Strategic Customer')
                   || accountCategory.contains('I1 - Integrator')
                   || accountCategory.contains('I2 - Major Integrator')
                   || accountCategory.contains('I3 - Strategic Integrator')
                   || accountCategory.contains('T1 - Technology')
                   || accountCategory.contains('T2 - Major Technology')
                   || accountCategory.contains('T3 - Strategic Technology')
                   || accountCategory.contains('R1 - Reseller') 
                   || accountCategory.contains('R2 - Major Reseller')
                   || accountCategory.contains('R3 - Strategic Reseller')
                   || ((accountCategory.contains('P1 - Prospect')
                       || accountCategory.contains('P2 - Major Prospect')
                       || accountCategory.contains('P3 - Strategic Prospect'))
                  			&& (!String.isBlank(accountStrategy) && (accountStrategy == 'MKT-Target' || accountStrategy == 'ABM')))) {
                       			return 'AccountOwner';
				}
              	else if (accountCategory.contains('C1 - Customer') 
                         || ((accountCategory.contains('P1 - Prospect')
                              || accountCategory.contains('P2 - Major Prospect')
                              || accountCategory.contains('P3 - Strategic Prospect'))
                             		&& (String.isBlank(accountStrategy) || accountStrategy == 'BDR-Focus'))) {
                             			return 'Territory';
                }
    	}
        return '';
    }

    public void assignOwnerByPersonTerritory (SObject person) {
        String key = '';
        String personCountry = '';
        String personState = '';
        switch on person.getSObjectType().getDescribe().getName() {
            when 'Contact' {
                Contact personContact = (Contact) person;
				personCountry = personContact.MailingCountry;
                personState = personContact.MailingState;
            }
            when 'Lead' {
                Lead personLead = (Lead) person;
				personCountry = personLead.Country;
                personState = personLead.State;
            }
        }
        
        if (String.isNotBlank(personCountry)) {
        	key = territoryKey(personCountry, personState);
        }
        if (String.isNotBlank(key) && personRoutingMap.containsKey(key)) {
            person.put('OwnerId', personRoutingMap.get(key).Person_Owner__c);
        }
    }

    public void assignOwnerByAccountTerritory (SObject person) {
        String key = '';
        Account relatedAccount = findAccount(person);
        key = territoryKey(relatedAccount.BillingCountry, relatedAccount.BillingState);
        if (String.isNotBlank(key) && personRoutingMap.containsKey(key)) {
            person.put('OwnerId', personRoutingMap.get(key).Person_Owner__c);
        }
    }
    
    public String territoryKey(String country, String state){
        String key = '';
        key = country + ';';
        key += countryStateRequired.contains(country)? state: null;
        key = key.toLowerCase();
        //Second attempt to match for Canada: treat not found as blank
        if(countryStateTreatNotFoundAsBlank.contains(country) && !personRoutingMap.containsKey(key)){
            key = country + ';' + null;
        }
        key = key.toLowerCase();
        return key;
    }

    public void assignOwnerByAccountOwner (SObject person) {
        Account relatedAccount = findAccount(person);
        if (relatedAccount != null && String.isNotBlank(relatedAccount.OwnerId)) {
            person.put('OwnerId', relatedAccount.OwnerId);
        }
    }

    public void assignCadence (SObject person) {
        if (String.isNotBlank((String) person.get('Most_Recent_Offer__c'))) {
            String key = (String) person.get('Most_Recent_Offer__c');
            if (personRoutingMap.containsKey(key)) {
                String language = 'English';
                if (languageByOwner.containsKey((Id) person.get('OwnerId'))) {
                    language = languageByOwner.get((Id) person.get('OwnerId'));
                }

                String oldCadenceName = getOldCadenceName((String) person.get('Cadence_Name__c'));
                String newCadenceName = personRoutingMap.get(key).Cadence_Name__c;

                if (String.isBlank(oldCadenceName) || !priorityByCadence.containsKey(oldCadenceName) ||
                    (priorityByCadence.containsKey(oldCadenceName) && priorityByCadence.get(oldCadenceName) > priorityByCadence.get(newCadenceName))
                ) {
                    person.put('Cadence_Name__c', newCadenceName+ '_' + language);
                }
            }
        }
    }

    public String getOldCadenceName (String oldCadenceName) {
        String cadenceName = '';
        if (String.isNotBlank(oldCadenceName)) {
            List<String> cadenceParts = oldCadenceName.split('_');
            if (cadenceParts.size() > 1) {
                cadenceParts.remove(cadenceParts.size() - 1);
            }
            cadenceName = String.join(cadenceParts, '_');
        }
        return cadenceName;
    }

    public Account findAccount (SObject person) {
        Account relatedAccount = null;
        switch on person.getSObjectType().getDescribe().getName() {
            when 'Contact' {
                Contact personContact = (Contact) person;
                if (String.isNotBlank(personContact.AccountId) && personContact.AccountId !=
                    TriggersHelper.unmappedAccountId && personContact.AccountId != TriggersHelper.resourceCentreAccountId) {
                        relatedAccount = personContact.Account;
                    }
            }
            when 'Lead' {
                Lead personLead = (Lead) person;
                if (String.isNotBlank(personLead.Account_2__c) && personLead.Account_2__c !=
                    TriggersHelper.unmappedAccountId && personLead.Account_2__c != TriggersHelper.resourceCentreAccountId) {
                        relatedAccount = personLead.Account_2__r;
                    }
            }
        }
        return relatedAccount;
    }
    
    private static Map<Id, String> languageByOwner;
    private static Map<String, Decimal> priorityByCadence;
    private static Set<String> countryStateRequired = new Set<String>{'United States','Canada'};
    private static Set<String> countryStateTreatNotFoundAsBlank = new Set<String>{'Canada'};    
    public static Map<String, Person_Routing__c> personRoutingMap {
        get {
            if (personRoutingMap == null) {
                personRoutingMap = new Map<String, Person_Routing__c>();
                priorityByCadence = new Map<String, Decimal>();
                languageByOwner = new Map<Id, String>();
                for (Person_Routing__c personRouting : [
                    SELECT Id, Account_Owner__c, Country__c, State__c, Person_Owner__c, RecordType.Name,
                        Campaign__c, Priority__c, Cadence_Name__c, Language__c
                    FROM Person_Routing__c
                    WHERE (Person_Owner__c != NULL AND Person_Owner__r.IsActive = TRUE AND (Account_Owner__c != NULL OR Country__c != '')) OR
                        (Campaign__c != NULL AND Cadence_Name__c != NULL AND Priority__c != NULL)
                ]) {
                    String key;
                    if (personRouting.RecordType.Name == 'Owner Based') {
                        key = personRouting.Account_Owner__c;
                    } else if (personRouting.RecordType.Name == 'Territory Based') {
                        key = personRouting.Country__c + ';' + personRouting.State__c;
                        key = key.toLowerCase();
                    } else if (personRouting.RecordType.Name == 'Campaign Routing') {
                        key = personRouting.Campaign__c;

                        if (!priorityByCadence.containsKey(personRouting.Cadence_Name__c) ||
                            (priorityByCadence.get(personRouting.Cadence_Name__c) < personRouting.Priority__c)
                        ) {
                            priorityByCadence.put(personRouting.Cadence_Name__c, personRouting.Priority__c);
                        }
                    }

                    if (personRouting.Person_Owner__c != null && String.isNotBlank(personRouting.Language__c) && !languageByOwner.containsKey(personRouting.Person_Owner__c)) {
                        languageByOwner.put(personRouting.Person_Owner__c, personRouting.Language__c);
                    }

                    personRoutingMap.put(key, personRouting);
                }
            }
            return personRoutingMap;
        }
        set {
            throw new PersonRoutingException('Collection is read only.');
        }
    }

    private static Id defaultOwnerId {
        get {
            if (defaultOwnerId == null) {
                defaultOwnerId = [SELECT Id, Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'Default_Person_Owner' LIMIT 1].Value__c;
            }
            return defaultOwnerId;
        }
        set;
    }

    private class PersonRoutingException extends Exception {}
}