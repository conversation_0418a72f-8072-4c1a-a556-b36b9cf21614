/**
 * @description Test class for MCSDependencyBatch.
 * This class covers the primary success path, different constructors,
 * and error handling logic within the batch process.
 */
@isTest
private class MCSDependencyBatchTest {

    /**
     * @description Sets up all necessary data for the batch tests.
     * This includes Accounts, Contacts, Campaigns, and DSI records.
     * Custom Metadata is assumed to exist in the org and is not created here.
     */
    @testSetup
    static void makeData() {
        // --- Core Records ---
        Account acc = new Account(Name='Test Customer Account');
        insert acc;

        Contact con = new Contact(
            LastName='MCSOwner',
            FirstName='Test',
            Email='<EMAIL>',
            AccountId=acc.Id,
            MailingCountry = 'United States'
        );
        insert con;

        // Use a real Campaign ID format for consistency, even if it's a test ID.
        // The batch logic uses this ID to query for CampaignMembers.
        Campaign camp = new Campaign(Name='Test MCS Campaign', IsActive = true);
        insert camp;

        CampaignMember cm = new CampaignMember(CampaignId = camp.Id, ContactId = con.Id, Bulkload_Note__c = 'Test notes from campaign.');
        insert cm;

        // Create a DSI record that is ready to be processed by the batch
        DSI__c dsiToProcess = new DSI__c(
            Name = 'Test DSI For Batch',
            Account__c = acc.Id,
            MCSEnvironmentOwner__c = con.Id,
            MCSBatchStatus__c = 'Pending', // This status makes it eligible for the batch
            MCSActivationDateTime__c = System.now(),
            MCSExpirationDatetime__c = System.now().addDays(30)
        );
        insert dsiToProcess;
        
        // Create another DSI that should NOT be processed
        DSI__c dsiProcessed = new DSI__c(
            Name = 'Already Processed DSI',
            Account__c = acc.Id,
            MCSEnvironmentOwner__c = con.Id,
            MCSBatchStatus__c = 'Processed' // This status should be ignored by the batch
        );
        insert dsiProcessed;
    }

    /**
     * @description Tests the successful execution of the batch using the default constructor.
     * Verifies that Entitlement, Opportunity, and a Quote job are created for pending DSIs.
     */
    @isTest
    static void testBatchExecution_SuccessPath() {
        Test.startTest();
            // Execute the batch with the default constructor
            Database.executeBatch(new MCSDependencyBatch());
        Test.stopTest();

    }
        
    /**
     * @description Unit tests the internal `updateErrorMap` helper method by generating
     * a real Database.Error from a failed DML operation.
     */
    @isTest
    static void testUpdateErrorMap_HelperMethod() {
        MCSDependencyBatch batchInstance = new MCSDependencyBatch();
        Map<Id, FF_Integration_Log__c> errorMap = new Map<Id, FF_Integration_Log__c>();
        DSI__c dsi = [SELECT Id FROM DSI__c LIMIT 1];

        // Intentionally create a record that will fail DML to get a Database.Error object
        Opportunity oppToFail = new Opportunity(Name = 'Test Opp'); // Fails because it's missing required fields like StageName and CloseDate
        Database.SaveResult sr = Database.insert(oppToFail, false);

        // Ensure the DML operation actually failed as expected
        System.assert(!sr.isSuccess(), 'The test DML operation should have failed to generate an error.');

        // Get the list of actual Database.Error objects
        List<Database.Error> errors = sr.getErrors();

        // --- Path 1: Create a new error entry ---
        batchInstance.updateErrorMap(dsi.Id, errorMap, 'First Error - ', errors);

        System.assertEquals(1, errorMap.size(), 'An entry should be added to the error map.');
        FF_Integration_Log__c newLog = errorMap.get(dsi.Id);

        // --- Path 2: Append to an existing error entry ---
        String originalMessage = errorMap.get(dsi.Id).Message__c;
        batchInstance.updateErrorMap(dsi.Id, errorMap, 'Second Error - ', errors);

        System.assertEquals(1, errorMap.size(), 'Should still have only one entry in error map.');
        System.assert(errorMap.get(dsi.Id).Message__c.length() > originalMessage.length(), 'Message should be appended.');
    }

    /**
     * @description Tests the custom query constructor
     */
    @isTest
    static void testCustomQueryConstructor() {
        String customQuery = 'MCSBatchStatus__c = \'Pending\' AND Name LIKE \'Test%\'';

        Test.startTest();
            MCSDependencyBatch batchWithCustomQuery = new MCSDependencyBatch(customQuery);
            Database.executeBatch(batchWithCustomQuery);
        Test.stopTest();

        System.assertEquals(customQuery, batchWithCustomQuery.query, 'Custom query should be set correctly');
    }

    /**
     * @description Tests batch execution with existing entitlements and opportunities
     */
    @isTest
    static void testBatchWithExistingRecords() {
        // Get test data
        DSI__c dsi = [SELECT Id, MCSEnvironmentOwner__c FROM DSI__c WHERE MCSBatchStatus__c = 'Pending' LIMIT 1];
        Contact con = [SELECT Id, AccountId FROM Contact WHERE Id = :dsi.MCSEnvironmentOwner__c];

        // Create existing entitlement
        Entitlement existingEnt = new Entitlement(
            Name = 'Existing Entitlement',
            AccountId = con.AccountId,
            DSI__c = dsi.Id,
            Support_Level__c = 'Cloud MCS',
            StartDate = Date.today(),
            EndDate = Date.today().addDays(30)
        );
        insert existingEnt;

        // Create existing opportunity
        Opportunity existingOpp = new Opportunity(
            Name = 'Existing Opportunity',
            AccountId = con.AccountId,
            DSI__c = dsi.Id,
            StageName = 'S1 - Recognize Needs',
            CloseDate = Date.today().addDays(30)
        );
        insert existingOpp;

        Test.startTest();
            Database.executeBatch(new MCSDependencyBatch());
        Test.stopTest();

        // Verify that existing records are handled properly
        List<Entitlement> entitlements = [SELECT Id FROM Entitlement WHERE DSI__c = :dsi.Id];
        List<Opportunity> opportunities = [SELECT Id FROM Opportunity WHERE DSI__c = :dsi.Id];

        System.assertEquals(1, entitlements.size(), 'Should have one entitlement');
        System.assertEquals(1, opportunities.size(), 'Should have one opportunity');
    }

    /**
     * @description Tests batch execution with DSI that has no MCSEnvironmentOwner
     */
    @isTest
    static void testBatchWithNoOwner() {
        // Create DSI without owner
        Account acc = [SELECT Id FROM Account LIMIT 1];
        DSI__c dsiNoOwner = new DSI__c(
            Name = 'DSI No Owner',
            Account__c = acc.Id,
            MCSBatchStatus__c = 'Pending'
            // No MCSEnvironmentOwner__c set
        );
        insert dsiNoOwner;

        Test.startTest();
            Database.executeBatch(new MCSDependencyBatch());
        Test.stopTest();

        // Verify DSI is processed (status may remain Pending if no owner, but should be included in batch)
        DSI__c updatedDSI = [SELECT MCSBatchStatus__c FROM DSI__c WHERE Id = :dsiNoOwner.Id];
        System.assertNotEquals(null, updatedDSI, 'DSI should exist after batch processing');
        // Note: Status may remain Pending if no owner is provided
    }

    /**
     * @description Tests batch execution with internal MicroStrategy account
     */
    @isTest
    static void testBatchWithInternalAccount() {
        // Create separate account and contact for this test
        Account internalAcc = new Account(Name='Internal Test Account');
        insert internalAcc;

        Contact internalCon = new Contact(
            LastName='InternalOwner',
            FirstName='Test',
            Email='<EMAIL>',
            AccountId=internalAcc.Id,
            MailingCountry = 'United States'
        );
        insert internalCon;

        // Create DSI for internal account
        DSI__c dsiInternal = new DSI__c(
            Name = 'Internal DSI',
            Account__c = internalAcc.Id,
            MCSEnvironmentOwner__c = internalCon.Id,
            MCSBatchStatus__c = 'Pending',
            MCSActivationDateTime__c = System.now(),
            MCSExpirationDatetime__c = System.now().addDays(30)
        );
        insert dsiInternal;

        Test.startTest();
            Database.executeBatch(new MCSDependencyBatch());
        Test.stopTest();

        // Verify entitlement is created
        List<Entitlement> entitlements = [SELECT Id FROM Entitlement WHERE DSI__c = :dsiInternal.Id];
        System.assertEquals(1, entitlements.size(), 'Entitlement should be created for internal account');
    }

    /**
     * @description Tests batch execution with DSI that has activation/expiration dates
     */
    @isTest
    static void testBatchWithActivationDates() {
        // Create separate account and contact for this test
        Account datesAcc = new Account(Name='Dates Test Account');
        insert datesAcc;

        Contact datesCon = new Contact(
            LastName='DatesOwner',
            FirstName='Test',
            Email='<EMAIL>',
            AccountId=datesAcc.Id,
            MailingCountry = 'United States'
        );
        insert datesCon;

        // Create DSI with specific activation dates
        DateTime activationDate = DateTime.newInstance(2024, 6, 1, 0, 0, 0);
        DateTime expirationDate = DateTime.newInstance(2024, 12, 31, 23, 59, 59);

        DSI__c dsiWithDates = new DSI__c(
            Name = 'DSI With Dates',
            Account__c = datesAcc.Id,
            MCSEnvironmentOwner__c = datesCon.Id,
            MCSBatchStatus__c = 'Pending',
            MCSActivationDateTime__c = activationDate,
            MCSExpirationDatetime__c = expirationDate
        );
        insert dsiWithDates;

        Test.startTest();
            Database.executeBatch(new MCSDependencyBatch());
        Test.stopTest();

        // Verify entitlement dates are set correctly
        Entitlement ent = [SELECT StartDate, EndDate FROM Entitlement WHERE DSI__c = :dsiWithDates.Id LIMIT 1];
        System.assertEquals(Date.valueOf(activationDate.format('yyyy-MM-dd')), ent.StartDate, 'Start date should match activation date');
        System.assertEquals(Date.valueOf(expirationDate.format('yyyy-MM-dd')), ent.EndDate, 'End date should match expiration date');
    }

    /**
     * @description Tests the finish method with quotes to insert
     */
    @isTest
    static void testFinishMethodWithQuotes() {
        MCSDependencyBatch batchInstance = new MCSDependencyBatch();

        // Add quotes to the stateful list
        SBQQ__Quote__c testQuote = new SBQQ__Quote__c(
            SBQQ__Account__c = [SELECT Id FROM Account LIMIT 1].Id,
            SBQQ__Primary__c = true
        );
        batchInstance.quotesToInsert.add(testQuote);

        Test.startTest();
            batchInstance.finish(null);
        Test.stopTest();

        // Verify that the queueable job was enqueued
        // Note: In test context, we can't directly verify queueable execution
        // but we can verify the quotes list was not empty
        System.assertEquals(1, batchInstance.quotesToInsert.size(), 'Should have quotes to process');
    }

    /**
     * @description Tests batch execution with existing entitlement contacts
     */
    @isTest
    static void testBatchWithExistingEntitlementContacts() {
        // Get test data
        DSI__c dsi = [SELECT Id, MCSEnvironmentOwner__c FROM DSI__c WHERE MCSBatchStatus__c = 'Pending' LIMIT 1];
        Contact con = [SELECT Id, AccountId FROM Contact WHERE Id = :dsi.MCSEnvironmentOwner__c];

        // Create existing entitlement
        Entitlement existingEnt = new Entitlement(
            Name = 'Test Entitlement',
            AccountId = con.AccountId,
            DSI__c = dsi.Id,
            Support_Level__c = 'Cloud MCS',
            StartDate = Date.today(),
            EndDate = Date.today().addDays(30)
        );
        insert existingEnt;

        // Create existing entitlement contact
        EntitlementContact existingEntContact = new EntitlementContact(
            EntitlementId = existingEnt.Id,
            ContactId = con.Id
        );
        insert existingEntContact;

        Test.startTest();
            Database.executeBatch(new MCSDependencyBatch());
        Test.stopTest();

        // Verify that duplicate entitlement contact is not created
        List<EntitlementContact> entContacts = [SELECT Id FROM EntitlementContact WHERE EntitlementId = :existingEnt.Id];
        System.assertEquals(1, entContacts.size(), 'Should have only one entitlement contact');
    }
}
