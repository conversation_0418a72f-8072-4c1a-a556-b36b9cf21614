<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <submitter>Contract_Cancellation</submitter>
        <type>group</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>ContractNumber</field>
        <field>Owner</field>
        <field>Cancellation_Reason__c</field>
        <field>SBQQ__Opportunity__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Cancellation_Rev_Ops</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Request_Cancellation</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>Rev Ops Approvals</label>
        <name>Rev_Ops_Approvals</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Cancellation_Transactions</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Request_Cancellation</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>Net_License__c = 0 &amp;&amp;  
OR(
ISPICKVAL(SBQQ__Opportunity__r.Type, &quot;Maintenance Renewal&quot;),
ISPICKVAL(SBQQ__Opportunity__r.Type, &quot;Support Renewal&quot;)
)</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Maintenance Renewal Transactions</label>
        <name>Maintenance_Renewal_Transactions</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Cancellation_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Request_Cancellation</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>SBQQ__Opportunity__r.Gross_License__c &gt; 0 &amp;&amp;
NOT(ISBLANK( Approval_Region__c)) &amp;&amp;
Approval_Region__c = &quot;APAC&quot;</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Legal Approval APAC</label>
        <name>Legal_Approval_APAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Cancellation_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Request_Cancellation</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>SBQQ__Opportunity__r.Gross_License__c &gt; 0 &amp;&amp;
NOT(ISBLANK( Approval_Region__c)) &amp;&amp;
Approval_Region__c = &quot;EMEA&quot;</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Legal Approval EMEA</label>
        <name>Legal_Approval_EMEA</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Cancellation_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Request_Cancellation</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>SBQQ__Opportunity__r.Gross_License__c &gt; 0 &amp;&amp;
(
ISBLANK(Approval_Region__c) || Approval_Region__c = &quot;NAM&quot;
)</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Legal Approval North America</label>
        <name>Legal_Approval_North_America</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Cancellation_Legal</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Request_Cancellation</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>SBQQ__Opportunity__r.Gross_License__c &gt; 0 &amp;&amp;
NOT(ISBLANK( Approval_Region__c)) &amp;&amp;
Approval_Region__c = &quot;LATAM&quot;</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Legal Approval LATAM</label>
        <name>Legal_Approval_LATAM</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>NOT(ISBLANK( Approval_Region__c)) &amp;&amp;
Approval_Region__c = &quot;APAC&quot;</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Billing APAC</label>
        <name>Billing_APAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>NOT(ISBLANK( Approval_Region__c)) &amp;&amp;
Approval_Region__c = &quot;EMEA&quot;</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Billing EMEA</label>
        <name>Billing_EMEA</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>ISBLANK(Approval_Region__c) || Approval_Region__c = &quot;NAM&quot;</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Billing North America</label>
        <name>Billing_North_America</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>NOT(ISBLANK( Approval_Region__c)) &amp;&amp;
Approval_Region__c = &quot;LATAM&quot;</formula>
        </entryCriteria>
        <label>Billing LATAM</label>
        <name>Billing_LATAM</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <emailTemplate>unfiled$public/Request_Cancellation</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <formula>true</formula>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Approved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Request_Cancellation_Approved</name>
            <type>Alert</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Rejected</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Request_Cancellation_Rejected</name>
            <type>Alert</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Cancellation_Approval_Step_Enter</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>In_Progress</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Request_Cancellation</name>
            <type>Alert</type>
        </action>
    </initialSubmissionActions>
    <label>Request Cancellation v4</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Recalled</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
