public with sharing class MCSEnvironmentController {

    // Inner class for InvocableMethod input
    public class UpdateEnvironmentRequest {
        @InvocableVariable(label='Opportunity ID' description='The ID of the Closed Won Opportunity' required=true)
        public Id opportunityId;
    }

    @InvocableMethod(label='Update MCS Environment' description='Updates an MCS environment based on Opportunity and Contract details.' callout=true)
    public static void updateMCSEnvironment(List<UpdateEnvironmentRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            System.debug(LoggingLevel.WARN, 'updateMCSEnvironment: No requests provided.');
            return;
        }

        for (UpdateEnvironmentRequest req : requests) {
            System.debug('updateMCSEnvironment: Processing Opportunity ID ' + req.opportunityId);
            Opportunity opp = [SELECT Id, DSI__c, DSI__r.MCSEnvironmentName__c  FROM Opportunity WHERE Id = :req.opportunityId];
            String environmentId = opp.DSI__r?.MCSEnvironmentName__c;

            List<Contract> contracts = [
                SELECT Id, StartDate, EndDate, SBQQ__Opportunity__c
                FROM Contract
                WHERE SBQQ__Opportunity__c = :req.opportunityId
                  AND Status = 'Activated' 
                ORDER BY EndDate DESC, StartDate DESC
                LIMIT 1
            ];

            if (contracts.isEmpty()) {
                System.debug(LoggingLevel.ERROR, 'updateMCSEnvironment: No activated Contract found for Opportunity ID ' + req.opportunityId);
            }

            Contract latestContract = !contracts.isEmpty() ? contracts[0] : null;
            Date contractStartDate = latestContract != null ? latestContract.StartDate : null;
            Date contractEndDate = latestContract != null ? latestContract.EndDate : null;

            MCSEnvironmentUpdatePayload payload = new MCSEnvironmentUpdatePayload(contractStartDate, contractEndDate);
            String jsonBody = JSON.serialize(payload);
            System.debug('MCSEnvironmentUpdatePayload for Opp ID ' + req.opportunityId + ': ' + jsonBody);

            MCS_Config__mdt mcsConfig = MCS_Config__mdt.getInstance('Config');
            System.debug('mcsConfig-> ' + mcsConfig);

            HttpRequest request = new HttpRequest();
            // Endpoint: 'callout:NamedCredentialAPIName/path/{environmentId}'
            String endpointPath = mcsConfig.URL__c + '/' + EncodingUtil.urlEncode(environmentId, 'UTF-8');
            request.setEndpoint(endpointPath);
            request.setMethod('PATCH');
            request.setHeader('Content-Type', 'application/json;charset=UTF-8');
            request.setHeader('x-api-key', mcsConfig.X_MSTR_Key__c); 
            request.setHeader('Accepts', 'application/json');
            request.setTimeout(120000);
            request.setBody(jsonBody);
            System.debug('jsonBody ->'  + jsonBody);

            Http http = new Http();
            try {
                HttpResponse response = http.send(request);
                System.debug('MCS Environment Update Callout Status for Opp ID ' + req.opportunityId + ', Env ID ' + environmentId + ': ' + response.getStatusCode() + ' - ' + response.getStatus());
                System.debug('MCS Environment Update Callout Response Body: ' + response.getBody());

                if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
                    System.debug('Successfully updated MCS environment for Opp ID: ' + req.opportunityId + ', Env ID: ' + environmentId);
                } else {
                    System.debug(LoggingLevel.ERROR, 'Error during MCS Environment Update for Opp ID ' + req.opportunityId + ', Env ID ' + environmentId +
                                   '. Status: ' + response.getStatusCode() + '. Body: ' + response.getBody());  
                }
            } catch (System.CalloutException e) {
                System.debug(LoggingLevel.ERROR, 'CalloutException during MCS Environment Update for Opp ID ' + req.opportunityId + ', Env ID ' + environmentId + ': ' + e.getMessage() + ' Stacktrace: ' + e.getStackTraceString());
            }
        }
    }
}