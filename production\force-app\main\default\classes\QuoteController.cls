/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 06/01/2021.
 */

 public without sharing class QuoteController {

    //Recursion flag  
    private static Boolean firedFromQuoteExecution;

    public QuoteController(){
        firedFromQuoteExecution = false;
    }

    public static Boolean firedFromQuoteExecution(){
        return firedFromQuoteExecution;
    }
    
    public static void setFiredFromQuoteExecution(){
        firedFromQuoteExecution = true;
    }

    private static Map<Id, SBQQ__Quote__c> quoteMapToUpdate {
        get {
            if (quoteMapToUpdate == null) {
                quoteMapToUpdate = new Map<Id, SBQQ__Quote__c>();
            }
            return quoteMapToUpdate;
        }
        set;
    }

    private static Map<String, MSTR_Global_Configuation__mdt> bucketMetadataMap {
        get {
            if (bucketMetadataMap == null) {
                bucketMetadataMap = new Map<String, MSTR_Global_Configuation__mdt>();
                for (MSTR_Global_Configuation__mdt mtd : [SELECT Id, Value__c, DeveloperName FROM MSTR_Global_Configuation__mdt WHERE DeveloperName IN ('QuoteDraftStatuses', 'QuoteApprovedStatuses', 'QuoteRejectedStatuses')]) {
                    bucketMetadataMap.put(mtd.DeveloperName, mtd);
                }
            }
            return bucketMetadataMap;
        }
        set;
    }

    public static void quoteProcessCreate (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps) {
        System.debug(LoggingLevel.INFO, 'QuoteController.quoteProcessCreate');
        Opportunity queriedOpp;
        Set<Id>oppIds = new Set<Id>();
        for(Opportunity opp: queriedOpps.values()){
            oppIds.add(opp.Id);
        }
        List<Partner_Line__c>partnerLines = [SELECT Id, Partner_type__c, Account__c, Opportunity__c, Motion__c
        FROM Partner_Line__c
        WHERE Opportunity__c IN :oppIds AND ((Partner_type__c = 'Sales Partner' AND Motion__c LIKE '%Resell') OR Partner_type__c = 'Distributor') ORDER BY CreatedDate];

        for (SBQQ__Quote__c quote : quotes) {
            quote.Business_Approved__c = false;
            quote.CEOCFOApproved__c = false;
            quote.RecordTypeId = TriggersHelper.draftQuoteRecordTypeId;
            quote.SBCF_Approval_Status__c = 'Proposal generation with Account Team';
            quote.SEVP_Approved__c = false;
            quote.SBQQ__Status__c = 'Q01 SAE';
            quote.Business_Approved__c = false;
            quote.MxBusinessApproved__c = false;
            quote.SEVP_Approved__c = false;
            quote.ServicesOpsApproved__c = false;
            quote.Document_Execution_Requirement__c = null;
            quote.Internal_Comments__c = null;
            quote.Customer_Signer_Email__c = null;
            quote.CPAI_Template_Field__c = null;

            queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);

            quote.SBQQ__StartDate__c = quote.SBQQ__StartDate__c == null ? queriedOpp.CloseDate + 1 : quote.SBQQ__StartDate__c;


            if (queriedOpp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId || queriedOpp.RecordTypeId == TriggersHelper.modalOppRecordTypeId) {
                quote.SBQQ__Type__c = 'Renewal';
            }

            if (partnerLines != null && !partnerLines.isEmpty()) {
                Boolean distributorSet = false;
                Boolean resellerSet = false;
                for (Partner_Line__c pl : partnerLines) {
                    if (pl.Opportunity__c == queriedOpp.Id) {
                        if (!resellerSet && pl.Partner_type__c == 'Sales Partner' && pl.Motion__c != null && pl.Motion__c.contains('Resell')) {
                            quote.Reseller__c = pl.Account__c;
                            resellerSet = true;
                        } else if (!distributorSet && pl.Partner_type__c == 'Distributor') {
                            quote.SBQQ__Distributor__c = pl.Account__c;
                            distributorSet = true;
                        }
                        if (distributorSet && resellerSet)
                            break;
                    }
                }
                if(quote.SBQQ__Distributor__c != null){
                    quote.SBQQ__Partner__c = quote.SBQQ__Distributor__c;
                } else if(quote.Reseller__c != null){
                    quote.SBQQ__Partner__c = quote.Reseller__c;
                }
            }

            if ((quote.SBQQ__Type__c == 'Renewal' && queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__c != null && queriedOpp.SBQQ__RenewedContract__r.SBQQ__Opportunity__c != null)) {
                quote.Conditional_Language__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Conditional_Language__c;
                quote.Description_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Description_Header__c;
                quote.End_Date_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.End_Date_Header__c;
                quote.Est_Total_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Est_Total_Header__c;
                quote.Legal_Entity_Name__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Legal_Entity_Name__c;
                quote.Legal_Entity_Output_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Legal_Entity_Output_Header__c;
                quote.Legal_Representative__c = queriedOpp.Legal_Representative__c;
                quote.License_Type_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.License_Type_Header__c;
                quote.List_Hourly_Rate_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.List_Hourly_Rate_Header__c;
                quote.List_Price_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.List_Price_Header__c;
                quote.Net_Hourly_Rate_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Net_Hourly_Rate_Header__c;
                quote.Net_Price_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Net_Price_Header__c;
                quote.Number_of_Days__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Number_of_Days__c;
                quote.Price_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Price_Header__c;
                quote.Quantity_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Quantity_Header__c;
                quote.Rev_Rec_Representative__c = queriedOpp.Rev_Rec_Representative__c;
                quote.SBCF_Bill_to_Contact__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Bill_to_Contact__c;
                quote.SBCF_Cold_Failover_Disaster_Recovery__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Cold_Failover_Disaster_Recovery__c;
                quote.SBCF_Document_Language__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Document_Language__c;
                quote.SBCF_No_Name_Logo_Rights__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_No_Name_Logo_Rights__c;
                quote.SBCF_Non_Standard_Payment_Terms__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Non_Standard_Payment_Terms__c;
                quote.SBCF_Pre_approval_of_Travel_Expenses__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Pre_approval_of_Travel_Expenses__c;
                quote.SBCF_Reimbursement_Not_Required__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Reimbursement_Not_Required__c;
                quote.SBCF_Ship_to_Contact__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Ship_to_Contact__c;
                quote.SBQQ__PrimaryContact__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.SBQQ__PrimaryContact__c;
                quote.Sales_RVP__c = queriedOpp.RVP_Approver__c;
                quote.Services_RVP__c = queriedOpp.Services_RVP_Approver__c;
                quote.Start_Date_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Start_Date_Header__c;
                quote.Subtotal_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Subtotal_Header__c;
                quote.Term_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Term_Header__c;
                quote.Total_Header__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Total_Header__c;

                quote.PrevARR__c = queriedOpp.SBQQ__RenewedContract__r.PrevARR__c;
                quote.PrevTCV__c = queriedOpp.SBQQ__RenewedContract__r.PrevTCV__c;

                //Transfer Shipping/Billing Information
                quote.SBCF_Ship_to_Contact__c = queriedOpp.SBQQ__RenewedContract__r.Shipping_Contact__c;
                quote.Ship_To_Book__c = queriedOpp.SBQQ__RenewedContract__r.Ship_To_Book__c;
                quote.SBCF_Bill_to_Contact__c = queriedOpp.SBQQ__RenewedContract__r.Billing_Contact__c;
                quote.Bill_To_Book__c = queriedOpp.SBQQ__RenewedContract__r.Bill_To_Book__c;

                //Permissibly Modified Terms - handling multi-year mx commitment calculation
                if (queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.MY_MX__c) {
                    quote.MY_MX__c = queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.MY_MX__c;
                    quote.OrigMYQ__c = String.isNotBlank(queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.OrigMYQ__c) ?
                        queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.OrigMYQ__c : queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.Name;

                    //handle reduction of years selected in MXYears in renewal quote
                    Integer yearsRemaining;
                    try {
                        yearsRemaining = Integer.valueOf(queriedOpp.SBQQ__RenewedContract__r.SBQQ__Quote__r.MXYears__c.left(1));
                    } catch (Exception e) {
                        quote.OrigMYQ__c = 'Something went wrong, please check previous year';
                    }

                    //check if this is the last year. If so, remove flags and values
                    if (yearsRemaining != 1) {
                        quote.MXYears__c = yearsRemaining - 1 + (yearsRemaining - 1 == 1 ? ' year' : ' years');
                    } else {
                        quote.MY_MX__c = false;
                        quote.OrigMYQ__c = '';
                    }
                }
                if (quote.SBQQ__StartDate__c != null && quote.SBQQ__EndDate__c != null) {
                    quote.CTR__c = calculateCTR(quote);
                }

            }

            if (quote.SBQQ__Type__c != 'Renewal') {
                quote.SBQQ__PrimaryContact__c = queriedOpp.Business_Executive__c;
                quote.Legal_Representative__c = queriedOpp.Legal_Representative__c;
                quote.Rev_Rec_Representative__c = queriedOpp.Rev_Rec_Representative__c;
                quote.Sales_RVP__c = queriedOpp.RVP_Approver__c;
                quote.Services_RVP__c = queriedOpp.Services_RVP_Approver__c;
            } else if (quote.SBQQ__Type__c == 'Renewal' && queriedOpp.SBQQ__RenewedContract__r.SBQQ__Opportunity__c == null && queriedOpp.SBQQ__RenewedContract__r.SBQQ__RenewalQuoted__c) {
                quote.CPIC__c = queriedOpp.CPIC__c;
                quote.Legal_Representative__c = queriedOpp.Legal_Representative__c ;
                quote.Rev_Rec_Representative__c = queriedOpp.Rev_Rec_Representative__c ;
                quote.Sales_RVP__c = queriedOpp.RVP_Approver__c ;
                quote.Services_RVP__c = queriedOpp.Services_RVP_Approver__c ;
            }

            if (quote.SBQQ__Type__c == 'Amendment') {
                quote.Amended_Contract__c = queriedOpp.SBQQ__AmendedContract__c;
                quote.Amended_Order__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__c;
                quote.Amended_Quote__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Quote__c;
                quote.SBQQ__BillingFrequency__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Quote__r.SBQQ__BillingFrequency__c;
                quote.Billing_Frequency__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Quote__r.Billing_Frequency__c;
                quote.SBCF_Bill_to_Contact__c = queriedOpp.SBQQ__AmendedContract__r.Billing_Contact__c;//MYC BUG 320 Nogueira
                quote.Bill_To_Book__c = queriedOpp.SBQQ__AmendedContract__r.Bill_To_Book__c;//MYC BUG 320 Nogueira
                quote.SBCF_Ship_to_Contact__c = queriedOpp.SBQQ__AmendedContract__r.Shipping_Contact__c;//MYC BUG 320 Nogueira
                quote.Ship_To_Book__c = queriedOpp.SBQQ__AmendedContract__r.Ship_To_Book__c;//MYC BUG 320 Nogueira
                quote.SBCF_Document_Language__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Quote__r.SBCF_Document_Language__c;//MYC BUG 238 Wrong Territory of Sale in Amendments
                quote.Paperwork_Original_Quote_Name__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Quote__r.SBCF_Document_Quote_Name__c;
            }
        }
    }

    public static void stampRenewalEmailDays (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps) {
        Set<Id> relatedOppIds = new Set<Id>();
        for (SBQQ__Quote__c quote : quotes) {
            relatedOppIds.add(quote.SBQQ__Opportunity2__c);
        }
        Map<Id, Opportunity> filteredOpps = new Map<Id, Opportunity>();
        for (Opportunity opp : queriedOpps.values()) {
            if (RenewalCommunicationsHelper.renewalOppRTs.contains(opp.RecordType.Name) &&
                RenewalCommunicationsHelper.oppRenewalTypes.contains(opp.SBCF_Renewal_Type__c) &&
                RenewalCommunicationsHelper.oppTypes.contains(opp.Type)
            ) {
                filteredOpps.put(opp.Id, opp);
            }
        }

        if (filteredOpps.isEmpty()) {
            return;
        }

        Opportunity relatedOpportunity;
        Date today = Date.today();
        for (SBQQ__Quote__c quote : quotes) {
            if (!filteredOpps.containsKey(quote.SBQQ__Opportunity2__c)) {
                continue;
            }
            relatedOpportunity = filteredOpps.get(quote.SBQQ__Opportunity2__c);
            String emails = '';
            if (relatedOpportunity.Up_for_Renewal_Date__c > today.addDays(1) && relatedOpportunity.Type != 'Term Renewal' && relatedOpportunity.Type != 'Cloud Renewal') {
                emails += 'Initial;';
            }
            if (relatedOpportunity.Up_for_Renewal_Date__c > today.addDays(45)) {
                emails += '45;30;15';
            } else if (relatedOpportunity.Up_for_Renewal_Date__c > today.addDays(30) &&
                relatedOpportunity.Up_for_Renewal_Date__c <= today.addDays(45)
            ) {
                emails += '30;15';
            } else if (relatedOpportunity.Up_for_Renewal_Date__c > today.addDays(15) &&
                relatedOpportunity.Up_for_Renewal_Date__c <= today.addDays(30)
            ) {
                emails += '15';
            } else {
                quote.Prevent_Communication_Emails__c = true;
            }
            if (String.isNotBlank(emails)) {
                quote.Renewal_Emails_Pending__c = emails;
            }
        }
    }

    public static void setQuoteDescriptionHeader (List<SBQQ__Quote__c> quotes) {
        System.debug(LoggingLevel.INFO, 'QuoteController.setQuoteDescriptionHeader');

        Set<Id> corpIds = new Set<Id>();
        Map<String, Territory_of_Sale__mdt> territoryMap = new Map<String, Territory_of_Sale__mdt>();
        for (SBQQ__Quote__c quote : quotes) {
            if (quote.SBCF_Document_Language__c != null) {
                territoryMap.put(quote.SBCF_Document_Language__c, null);
            }
        }

        if (territoryMap.keySet().isEmpty()) {
            return;
        }

        for (Territory_of_Sale__mdt tos : [
            SELECT Id, MasterLabel, License_Type_Header__c, Description_Header__c, Term_Header__c, List_Price_Header__c,
                Net_Price_Header__c, Conditional_Language__c, Quantity_Header__c, Total_Header__c, Start_Date_Header__c,
                End_Date_Header__c, Price_Header__c, Subtotal_Header__c, List_Hourly_Rate_Header__c,
                Net_Hourly_Rate_Header__c, Est_Total_Header__c, Legal_Entity__c, Education_Header__c,
                Consulting_Header__c, Maintenance_Header__c, One_time_Transaction_Incentive_Header__c,
                Order_Grand_Total_Header__c, Order_Subtotal_Header__c, Other_Consulting_Header__c,
                Support_Header__c, Software_Total_Header__c, Standard_Support_Total_Header__c,
                Premium_Support_Total_Header__c, Education_Total_Header__c, Consulting_Total_Header__c
            FROM Territory_of_Sale__mdt
            WHERE MasterLabel IN :territoryMap.keySet()
        ]) {
            territoryMap.put(tos.MasterLabel, tos);

            if (tos.Legal_Entity__c != null) {
                corpIds.add(tos.Legal_Entity__c);
            }
        }

        Map<String, Account> corpMap = new Map<String, Account>([
            SELECT Id, Name, Output_Document_Header__c
            FROM Account
            WHERE Id IN :corpIds
            FOR VIEW
        ]);

        for (SBQQ__Quote__c quote : quotes) {
            if (!territoryMap.containsKey(quote.SBCF_Document_Language__c) ||
                territoryMap.get(quote.SBCF_Document_Language__c) == null
            ) {
                continue;
            }
            Territory_of_Sale__mdt tos = territoryMap.get(quote.SBCF_Document_Language__c);

            quote.Description_Header__c = tos.Description_Header__c;
            quote.License_Type_Header__c = tos.License_Type_Header__c;
            quote.Term_Header__c = tos.Term_Header__c;
            quote.List_Price_Header__c = tos.List_Price_Header__c;
            quote.Net_Price_Header__c = tos.Net_Price_Header__c;
            quote.Conditional_Language__c = tos.Conditional_Language__c;
            quote.Quantity_Header__c = tos.Quantity_Header__c;
            quote.Total_Header__c = tos.Total_Header__c;
            quote.Start_Date_Header__c = tos.Start_Date_Header__c;
            quote.End_Date_Header__c = tos.End_Date_Header__c;
            quote.Price_Header__c = tos.Price_Header__c;
            quote.Subtotal_Header__c = tos.Subtotal_Header__c;
            quote.Education_Header__c = tos.Education_Header__c;
            quote.Consulting_Header__c = tos.Consulting_Header__c;
            quote.Maintenance_Header__c = tos.Maintenance_Header__c;
            quote.One_Time_Transaction_Incentive_Header__c = tos.One_time_Transaction_Incentive_Header__c;
            quote.Order_Grand_Total_Header__c = tos.Order_Grand_Total_Header__c;
            quote.Order_Subtotal_Header__c = tos.Order_Subtotal_Header__c;
            quote.Other_Consulting_Header__c = tos.Other_Consulting_Header__c;
            quote.Support_Header__c = tos.Support_Header__c;
            quote.List_Hourly_Rate_Header__c = tos.List_Hourly_Rate_Header__c;
            quote.Net_Hourly_Rate_Header__c = tos.Net_Hourly_Rate_Header__c;
            quote.Est_Total_Header__c = tos.Est_Total_Header__c;
            quote.Software_Total_Header__c = tos.Software_Total_Header__c;
            quote.Standard_Support_Total_Header__c = tos.Standard_Support_Total_Header__c;
            quote.Premium_Support_Total_Header__c = tos.Premium_Support_Total_Header__c;
            quote.Education_Total_Header__c = tos.Education_Total_Header__c;
            quote.Consulting_Total_Header__c = tos.Consulting_Total_Header__c;
            if (tos.Legal_Entity__c != null && corpMap.containsKey(tos.Legal_Entity__c)) {
                quote.Legal_Entity_Name__c = corpMap.get(tos.Legal_Entity__c).Name;
                quote.Legal_Entity_Output_Header__c = corpMap.get(tos.Legal_Entity__c).Output_Document_Header__c;
            }

        }

    }

    //TODO: Make sure Quotes are getting unlocked
    public static void unlockQuotes (List<SBQQ__Quote__c> quotesToUnlock) {
        System.debug(LoggingLevel.INFO, 'QuoteController.unlockQuotes');
        Approval.unlock(quotesToUnlock, false);
    }

    public static void clearRenewalCommunicationFields (SBQQ__Quote__c quote) {
        System.debug(LoggingLevel.INFO, 'QuoteController.clearRenewalCommunicationFields');

        quote.Renewal_Emails_Pending__c = null;
        quote.Renewal_Emails_Sent__c = null;
        quote.Last_Renewal_Email_Sent__c = null;
        quote.Prevent_Communication_Emails__c = false;
    }

    public static void autoExecuteQuote (SBQQ__Quote__c quote) {
        System.debug(LoggingLevel.INFO, 'QuoteController.autoExecuteQuote');

        quote.SBCF_Approval_Status__c = 'Agreement Executed';
        quote.SBQQ__Status__c = 'Q09 EXE';
        quote.RecordTypeId = TriggersHelper.executedQuoteRecordTypeId;
    }

    public static void populateCountryId (List<SBQQ__Quote__c> quotes) {
        System.debug(LoggingLevel.INFO, 'QuoteController.populateCountryId');


        Map<String, Decimal> countryName2CountryId = new Map<String, Decimal>();
        for (Country_Map__mdt countryMap : [
            SELECT MasterLabel, Country_ID__c
            FROM Country_Map__mdt
        ]) {
            countryName2CountryId.put(countryMap.MasterLabel, countryMap.Country_ID__c);
        }
        for (SBQQ__Quote__c quote : quotes) {
            quote.BillTo_Country_ID__c = countryName2CountryId.get(quote.SBQQ__BillingCountry__c);
            quote.ShipTo_Country_ID__c = countryName2CountryId.get(quote.Ship_To_Contact_Country__c);
        }
    }

    public static void postToChatter (SBQQ__Quote__c quote, String fieldValue, String changedBy) {
        System.debug(LoggingLevel.INFO, 'QuoteController.postToChatter');
        ConnectApi.FeedItemInput feedItemInput = new ConnectApi.FeedItemInput();
        ConnectApi.MessageBodyInput messageBodyInput = new ConnectApi.MessageBodyInput();
        ConnectApi.TextSegmentInput textSegment = new ConnectApi.TextSegmentInput();
        ConnectApi.MentionSegmentInput mentionSegmentInput = new ConnectApi.MentionSegmentInput();

        messageBodyInput.messageSegments = new List<ConnectApi.MessageSegmentInput>();
        mentionSegmentInput.id = quote.OwnerId;
        textSegment.text = '\n\n' + changedBy + ' has updated the clarification notes on ' + quote.Name + ' to: \n' + fieldValue;
        messageBodyInput.messageSegments.add(mentionSegmentInput);
        messageBodyInput.messageSegments.add(textSegment);
        feedItemInput.subjectId = quote.SBQQ__Opportunity2__c;
        feedItemInput.body = messageBodyInput;
        feedItemInput.feedElementType = ConnectApi.FeedElementType.FeedItem;

        if (!Test.isRunningTest()) {
            ConnectApi.ChatterFeeds.postFeedElement(null, feedItemInput);
        }
    }

    public static Boolean checkQuoteBucket (String bucket, String status) {
        List<String> statuses = bucketMetadataMap.get(bucket).Value__c.split(',');
        return statuses.contains(status.substring(1, 3));
    }

    public static void setTextEnterpriseEntered (SBQQ__Quote__c newQuote) {
        System.debug(LoggingLevel.INFO, 'QuoteController.setTextEnterpriseEntered');

        newQuote.SBCF_Text_Enterprise_Terms_Entered__c = String.isNotBlank(newQuote.SBCF_License_Terms__c) ||
            String.isNotBlank(newQuote.SBCF_Technical_Support_Terms__c) || String.isNotBlank(newQuote.SBCF_Pricing_Payment_and_Invoice_Terms__c) ||
            String.isNotBlank(newQuote.SBCF_Other_Enterprise_Terms__c) || String.isNotBlank(newQuote.Services_Terms__c);
    }

    public static void avoidMultiplePrimaryQuotes (Map<Id, SBQQ__Quote__c> quotesToCheck, Map<Id, Opportunity> queriedOpps) {
        System.debug(LoggingLevel.INFO, 'QuoteController.avoidMultiplePrimaryQuotes');
        Map<Id, List<SBQQ__Quote__c>> oppsWithPrimaryQuotes = new Map<Id, List<SBQQ__Quote__c>>();
        Map<Id, List<SBQQ__Quote__c>> oppsWithPrimaryQuotesInContext = new Map<Id, List<SBQQ__Quote__c>>();


        for (SBQQ__Quote__c quote : quotesToCheck.values()) {
            if (quote.SBQQ__Primary__c) {
                if (oppsWithPrimaryQuotesInContext.containsKey(quote.SBQQ__Opportunity2__c)) {
                    oppsWithPrimaryQuotesInContext.get(quote.SBQQ__Opportunity2__c).add(quote);
                } else {
                    oppsWithPrimaryQuotesInContext.put(quote.SBQQ__Opportunity2__c, new List<SBQQ__Quote__c>{
                        quote
                    });
                }
            }
        }

        for (Opportunity opp : queriedOpps.values()) {
            for (SBQQ__Quote__c relatedQuote : opp.SBQQ__Quotes2__r) {
                if (quotesToCheck.keySet().contains(relatedQuote.Id)) {
                    continue;
                }
                if (oppsWithPrimaryQuotes.containsKey(relatedQuote.SBQQ__Opportunity2__c)) {
                    oppsWithPrimaryQuotes.get(relatedQuote.SBQQ__Opportunity2__c).add(relatedQuote);
                } else {
                    oppsWithPrimaryQuotes.put(relatedQuote.SBQQ__Opportunity2__c, new List<SBQQ__Quote__c>{
                        relatedQuote
                    });
                }
            }
        }
        SBQQ__Quote__c qToUpdate;
        if (!oppsWithPrimaryQuotes.isEmpty() && oppsWithPrimaryQuotes.size() > 0) {
            for (SBQQ__Quote__c objToCheck : quotesToCheck.values()) {
                if (!oppsWithPrimaryQuotes.isEmpty() && oppsWithPrimaryQuotes.containsKey(objToCheck.SBQQ__Opportunity2__c) && oppsWithPrimaryQuotes.get(objToCheck.SBQQ__Opportunity2__c).size() > 0) {
                    for (SBQQ__Quote__c objToUpdate : oppsWithPrimaryQuotes.get(objToCheck.SBQQ__Opportunity2__c)) {
                        if (oppsWithPrimaryQuotesInContext.get(objToCheck.SBQQ__Opportunity2__c).size() == 1) {
                            qToUpdate = getQuoteFromQuoteMapToUpdate(objToUpdate.Id);
                            qToUpdate.SBQQ__Primary__c = false;
                        } else if (oppsWithPrimaryQuotesInContext.get(objToCheck.SBQQ__Opportunity2__c).size() > 1) {
                            objToCheck.addError('An opportunity can have only 1 primary quote');
                        }
                    }

                }
            }
        } else if (!oppsWithPrimaryQuotesInContext.isEmpty() && oppsWithPrimaryQuotesInContext.size() > 0) {
            for (SBQQ__Quote__c objToCheck : quotesToCheck.values()) {
                if (oppsWithPrimaryQuotesInContext.containsKey(objToCheck.SBQQ__Opportunity2__c) && oppsWithPrimaryQuotesInContext.get(objToCheck.SBQQ__Opportunity2__c).size() > 1) {
                    objToCheck.addError('An opportunity can have only 1 primary quote');
                }
            }
        }
    }

    public static void stampStartDate (SBQQ__Quote__c quote) {
        quote.SBQQ__StartDate__c = System.today();
    }

    //Quote Line Group management on Renewals and Amendments
    public static Boolean insertMissingAmendmentRenewalLineGroupsRun = false;
    public static void qlgMGMT (Map<Id, SBQQ__Quote__c> newQuotesMap) {
        System.debug(LoggingLevel.INFO, 'QuoteController.qlgMGMT');

        if (!insertMissingAmendmentRenewalLineGroupsRun) {
            insertMissingAmendmentRenewalLineGroupsRun = true;
            QuoteGroupsMGMT.insertMissingAmendmentRenewalLineGroups(newQuotesMap, null,false);
        }
    }
    public static void setFieldRipReplace (List<Id> quotes) {
/* -> review Rip and Replace Module / Sebastian Suarez Nogueira Matias
        List<SBQQ__Quote__c> quotes2Update = new List<SBQQ__Quote__c>();
        List<Opportunity> opp2Update = new List<Opportunity>();
        for(SBQQ__Quote__c q : [SELECT Id, Replacement_Quote__c, SBQQ__Opportunity2__c, (SELECT Id, SBQQ__LineItemsGrouped__c , CurrencyIsoCode, SBQQ__PriceBook__c, SBQQ__PricebookId__c, SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.RegionPricebook__c, SBQQ__Opportunity2__r.Pricebook2Id, SBQQ__Opportunity2__r.CurrencyIsoCode, SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.DSI__c, SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.RFP_Picklist__c, SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Next_Step80__c,  SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Due_Date__c FROM cpqlabs__ChildQuotes__r) FROM SBQQ__Quote__c WHERE Id IN :quotes]){

            SBQQ__Quote__c qt = q.cpqlabs__ChildQuotes__r[0];
            if(q.Replacement_Quote__c == false){
                q.Replacement_Quote__c = true;
                q.CurrencyIsoCode = qt.CurrencyIsoCode;
                q.SBQQ__PriceBook__c = qt.SBQQ__PriceBook__c;
                q.SBQQ__PricebookId__c = qt.SBQQ__PricebookId__c;
                q.SBQQ__ContractingMethod__c = 'Single Contract';
                q.SBQQ__LineItemsGrouped__c = qt.SBQQ__LineItemsGrouped__c == true ? true : false;
                quotes2Update.add(q);

                Opportunity opp = new Opportunity(Id = q.SBQQ__Opportunity2__c);
                opp.RegionPricebook__c = qt.SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.RegionPricebook__c;
                opp.Pricebook2Id = qt.SBQQ__Opportunity2__r.Pricebook2Id;
                opp.CurrencyIsoCode = qt.SBQQ__Opportunity2__r.CurrencyIsoCode;
                opp.DSI__c = qt.SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.DSI__c;
                opp.RFP_Picklist__c = qt.SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.RFP_Picklist__c;
                opp.Next_Step80__c = qt.SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Next_Step80__c;
                opp.Due_Date__c = qt.SBQQ__Opportunity2__r.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Due_Date__c;
                opp2Update.add(opp);
            }
        }

        if(opp2Update.size() > 0){
            update opp2Update;
        }

        if(quotes2Update.size() > 0){
            SBQQ.TriggerControl.disable();
            update quotes2Update;
            SBQQ.TriggerControl.enable();
        }
        */
    }

    public static void setGroupsRipReplace (List<Id> quotes) {
/* -> review Rip and Replace Module / Sebastian Suarez Nogueira Matias
        Set<String> qlgName = new Set<String>();
        Decimal lastGroupNumber = 0;
        List<SBQQ__QuoteLineGroup__c> qlg2Insert = new List<SBQQ__QuoteLineGroup__c>();
        Map<Id, Id> groupById = new Map<Id, Id>();
        List<Id> q2Process = new List<Id>();
        for(SBQQ__Quote__c q : [SELECT Id, cpqlabs__Replacement_Quote__c, cpqlabs__Replacement_Quote__r.SBQQ__StartDate__c, (SELECT Original_Quote_Line__c, SBQQ__Group__c FROM SBQQ__LineItems__r), (SELECT Id, Name FROM SBQQ__LineItemGroups__r order by SBQQ__Number__c) FROM SBQQ__Quote__c WHERE Id IN :quotes]){

            q2Process.add(q.cpqlabs__Replacement_Quote__c);
            for(SBQQ__QuoteLine__c ql : q.SBQQ__LineItems__r){
                groupById.put(ql.Original_Quote_Line__c, ql.SBQQ__Group__c);
            }

            for(SBQQ__QuoteLineGroup__c qlg : q.SBQQ__LineItemGroups__r){

                SBQQ__QuoteLineGroup__c qlgNew = new SBQQ__QuoteLineGroup__c(
                        SBQQ__Quote__c = q.cpqlabs__Replacement_Quote__c,
                        Name = qlg.Name,
                        SBQQ__Source__c = qlg.Id,
                        SBQQ__Number__c = lastGroupNumber + 1
                );
                qlgNew.SBQQ__StartDate__c = q.cpqlabs__Replacement_Quote__r.SBQQ__StartDate__c;
                qlg2Insert.add(qlgNew);
                lastGroupNumber ++;
            }
        }

        if(qlg2Insert.size() > 0){
            insert qlg2Insert;
        }

        if(q2Process.size() > 0){

            Map<Id, Id> newGroupById = new Map<Id, Id>();
            List<SBQQ__QuoteLine__c> ql2Update = new List<SBQQ__QuoteLine__c>();
            for(SBQQ__Quote__c q : [SELECT Id, (SELECT Id, Original_Quote_Line__c, SBQQ__Group__c FROM SBQQ__LineItems__r), (SELECT Id, SBQQ__Source__c FROM SBQQ__LineItemGroups__r order by SBQQ__Number__c) FROM SBQQ__Quote__c WHERE Id IN :q2Process]){

                for(SBQQ__QuoteLineGroup__c qlg : q.SBQQ__LineItemGroups__r){
                    newGroupById.put(qlg.SBQQ__Source__c, qlg.Id);
                }

                for(SBQQ__QuoteLine__c ql : q.SBQQ__LineItems__r){

                    if(groupById.containsKey(ql.Original_Quote_Line__c)){

                        if(newGroupById.containsKey(groupById.get(ql.Original_Quote_Line__c))){
                            ql.SBQQ__Group__c = newGroupById.get(groupById.get(ql.Original_Quote_Line__c));
                            ql2Update.add(ql);
                        }
                    }
                }
            }

            if(ql2Update.size() > 0){
                update ql2Update;
            }
        }
 */
    }

    // Case 516152 - Nogueira Matias
    public static void validateAmendmentQuoteFields (SBQQ__Quote__c oldQuote, SBQQ__Quote__c newQuote) {
        System.debug(LoggingLevel.INFO, 'QuoteController.validateAmendmentQuoteFields');

        if (oldQuote.SBQQ__Type__c == 'Amendment' && newQuote.SBQQ__Type__c == 'Amendment') {

            if ((oldQuote.Billing_Frequency__c != newQuote.Billing_Frequency__c) &&
                !String.isEmpty(oldQuote.Billing_Frequency__c)
            ) {
                newQuote.addError('Billing frequency can not be changed in Amendment Quotes.');
            }
            if ((oldQuote.Ship_To_Book__c != newQuote.Ship_To_Book__c) ||
                (oldQuote.Bill_To_Book__c != newQuote.Bill_To_Book__c)) {
                newQuote.Billing_Shipping_Id_Updated__c = true;
            }

        }
    }

    //516151 - Dima
    public static void setNoChangesToConfig (List<SBQQ__Quote__c> quotes, Map<Id, List<SBQQ__QuoteLine__c>> qlByQuoteId) {
        System.debug(LoggingLevel.INFO, 'QuoteController.setNoChangesToConfig');

        Boolean noChanges = true;
        for (SBQQ__Quote__c q : quotes) {
            if (q.Quote_Lines__c == 0) {
                noChanges = false;
            }
            for (SBQQ__QuoteLine__c ql : qlByQuoteId.get(q.Id)) {
                if (ql.Original_Quote_Line__c == null) {
                    noChanges = false;
                } else {
                    if (ql.Original_Quote_Line__r.SBQQ__Quote__r.Quote_Lines__c != q.Quote_Lines__c ||
                        ql.Original_Quote_Line__r.SBQQ__Quantity__c != ql.SBQQ__Quantity__c) {
                        noChanges = false;
                    }
                }
                q.No_Changes_to_Config__c = noChanges;
            }
        }
    }

    //517434 - Nogueira Check for Cloning Error
    public static void checkCloningOrderedQuote (SBQQ__Quote__c quoteToCheck) {
        if (quoteToCheck.Cloned__c &&
            quoteToCheck.SBQQ__Ordered__c &&
            quoteToCheck.SBQQ__Status__c != 'Q09 EXE' &&
            quoteToCheck.SBCF_Approval_Status__c != 'Agreement Executed'
        ) {
            quoteToCheck.SBQQ__Ordered__c = false;
        }
    }

    //Case 517434 - Nogueira Matias Refactor
    public static void getOrderedQuotes (Map<Id, SBQQ__Quote__c> orderedQuotes, SBQQ__Quote__c oldQuote, SBQQ__Quote__c newQuote) {
        if (newQuote.SBQQ__Ordered__c && newQuote.SBQQ__Status__c == 'Q09 EXE') {
            if ((oldQuote.SBCF_Bill_to_Contact__c != newQuote.SBCF_Bill_to_Contact__c) ||
                (oldQuote.SBCF_Ship_to_Contact__c != newQuote.SBCF_Ship_to_Contact__c) ||
                (oldQuote.Ship_To_Book__c != newQuote.Ship_To_Book__c) ||
                (oldQuote.Bill_To_Book__c != newQuote.Bill_To_Book__c) ||
                (oldQuote.SBCF_Pricing_Payment_and_Invoice_Terms__c != newQuote.SBCF_Pricing_Payment_and_Invoice_Terms__c) ||
                (oldQuote.Billing_Frequency__c != newQuote.Billing_Frequency__c) ||
                (oldQuote.Multi_Year_Contract_Quote__c != newQuote.Multi_Year_Contract_Quote__c) ||
                (oldQuote.SBCF_Legal_Notes__c != newQuote.SBCF_Legal_Notes__c) ||
                (oldQuote.SBCF_Rev_Rec_Notes__c != newQuote.SBCF_Rev_Rec_Notes__c)
            ) {
                orderedQuotes.put(newQuote.Id, newQuote);
            }
        }
    }


    // Case 517434 - Nogueira Matias Refactor
    public static void updateOrdersFromQuotes (Map<Id, SBQQ__Quote__c> orderedQuotes) {
        System.debug(LoggingLevel.INFO, 'QuoteController.updateOrdersFromQuotes');

        Map<Id, Id> quoteAndOrder = new Map<Id, Id>();
        List<SBQQ__Quote__c> orderedQuotesAllFields = [
            SELECT Id, Billing_Frequency__c, SBCF_Bill_to_Contact__c, SBCF_Ship_to_Contact__c, Ship_To_Book__c,
                Ship_To_Book__r.JDE_Id__c, SBCF_Ship_to_Contact__r.Email, SBCF_Ship_to_Contact__r.Account.Name,
                SBCF_Ship_to_Contact__r.MailingStreet, SBCF_Ship_to_Contact__r.MailingCity, SBCF_Ship_to_Contact__r.MailingState,
                SBCF_Ship_to_Contact__r.MailingPostalCode, SBCF_Ship_to_Contact__r.MailingCountry, Bill_To_Book__c, SBCF_Pricing_Payment_and_Invoice_Terms__c,
                Multi_Year_Contract_Quote__c, SBCF_Legal_Notes__c, SBCF_Rev_Rec_Notes__c, (
                SELECT Id, End_User_Id__c, EndUserContactId__c, SBQQ__Quote__c
                FROM SBQQ__Orders__r
            )
            FROM SBQQ__Quote__c
            WHERE Id IN :orderedQuotes.keySet()
        ];
        List<Order> ordersToUpdate = new List<Order>();

        for (SBQQ__Quote__c quote : orderedQuotesAllFields) {
            for (Order currentOrder : quote.SBQQ__Orders__r) {
                String endUserAddress = '';
                quoteAndOrder.put(currentOrder.Id, quote.Id);

                currentOrder.Billing_Frequency__c = quote.Billing_Frequency__c;
                currentOrder.BillToContactId = quote.SBCF_Bill_to_Contact__c;
                currentOrder.ShipToContactId = quote.SBCF_Ship_to_Contact__c;
                currentOrder.Shipping_ID__c = quote.Ship_To_Book__c;
                currentOrder.Billing_ID__c = quote.Bill_To_Book__c;
                currentOrder.Pricing_Payment_and_Invoicing_Terms__c = quote.SBCF_Pricing_Payment_and_Invoice_Terms__c;
                currentOrder.Multi_Year_Contract_Order__c = quote.Multi_Year_Contract_Quote__c;
                currentOrder.Legal_Notes__c = quote.SBCF_Legal_Notes__c;
                currentOrder.Rev_Rec_Notes__c = quote.SBCF_Rev_Rec_Notes__c;
                //518132 - Nogueira Default End User Values
                if (currentOrder.End_User_Id__c == null) {
                    currentOrder.End_User_Id__c = quote.Ship_To_Book__c;
                    currentOrder.End_User_JDE_Id__c = quote.Ship_To_Book__r.JDE_Id__c;
                }
                if (currentOrder.EndUserContactId__c == null) {
                    currentOrder.EndUserContactId__c = quote.SBCF_Ship_to_Contact__c;
                    currentOrder.End_User_Email__c = quote.SBCF_Ship_to_Contact__r.Email;
                    currentOrder.End_User_Company__c = quote.SBCF_Ship_to_Contact__r.Account.Name;
                    String mailingStreet = String.isNotBlank(String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingStreet)) ? String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingStreet) + ', ' : '';
                    String mailingCity = String.isNotBlank(String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingCity)) ? String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingCity) + ', ' : '';
                    String mailingState = String.isNotBlank(String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingState)) ? String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingState) + ', ' : '';
                    String mailingPostalCode = String.isNotBlank(String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingPostalCode)) ? String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingPostalCode) + ', ' : '';
                    String mailingCountry = String.isNotBlank(String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingCountry)) ? String.valueOf(quote.SBCF_Ship_to_Contact__r.MailingCountry) : '';
                    endUserAddress = mailingStreet + mailingCity + mailingState + mailingPostalCode + mailingCountry;
                    currentOrder.End_User_Address__c = endUserAddress;
                }
            }

            Database.SaveResult[] ordersToUpdateResults = Database.update(ordersToUpdate, false);
            for (Integer i = 0; i < ordersToUpdate.size(); i++) {
                if (!ordersToUpdateResults[i].isSuccess()) {
                    String errors = '';
                    for (Database.Error er : ordersToUpdateResults[i].getErrors()) {
                        errors += er.getMessage().contains('You cannot make changes to an Order after Integration Status.') ? 'You cannot make changes to the Quote once the Order is Integrated or Activated.\n' : er.getMessage() + '\n';
                    }
                    orderedQuotes.get(quoteAndOrder.get(ordersToUpdate[i].Id)).addError(errors);
                }
            }
        }
    }

    // Case 509575 - Gómez - Validation for Billing Frequency: Quarterly, Case 538739 Nogueira
    public static void validateQuarterlyQuotes (List<SBQQ__Quote__c> quarterlyQuotes) {
        System.debug(LoggingLevel.INFO, 'QuoteController.validateQuarterlyQuotes');

        List<PermissionSetAssignment> revOpsUser = [SELECT PermissionSetId FROM PermissionSetAssignment WHERE AssigneeId = :UserInfo.getUserId() AND PermissionSet.Name = 'RevOps_Team'];
        List<Profile> ps = [SELECT Id FROM Profile WHERE Name IN ('System Administrator','Renewals v2')];
        Set<Id> psIds = new Set<Id>();
        for(Profile currentP : ps){
            psIds.add(currentP.Id);
        }
        for (SBQQ__Quote__c quote : quarterlyQuotes) {
            if (revOpsUser.isEmpty() && !psIds.contains(UserInfo.getProfileId()) ) {
                quote.Billing_Frequency__c.addError('Please contact RevOps team if a Quarterly Billing Frequency is needed.');
            }
        }
    }

    /***********************************************************************
     * : method to return true if the Quote is a new sale.
     *
     * @param qt
     *
     * @return
     **********************************************************************/
    public static Boolean isNewSale (SBQQ__Quote__c qt) {
        return qt.Opportunity_Record_Type__c == CPQConstants.QT_OPP_RT_SALESOPPNEW;
    }

    /**********************************************************************
     * : method to return true if the Quote is an Amendment.
     *
     * @param qt
     *
     * @return
     **********************************************************************/
    public static Boolean isAmendment (SBQQ__Quote__c qt) {
        return qt.SBQQ__Type__c == CPQConstants.QUOTETYPE_AMENDMENT;
    }


    /****************************************************************************
     * : method to auto select template on preview Document page
     *
     * @param quotesToBePreviewed
     *****************************************************************************/
    public static void setTemplateIdForPreview (List<SBQQ__Quote__c> quotesToBePreviewed) {
        System.debug(LoggingLevel.INFO, 'QuoteController.setTemplateIdForPreview');

        Map<String, Id> templateNameToIdMap = new Map<String, Id>();
        for (SBQQ__QuoteTemplate__c template : [SELECT Id, Name FROM SBQQ__QuoteTemplate__c]) {
            templateNameToIdMap.put(template.Name, template.Id);
        }
        for (SBQQ__Quote__c q : quotesToBePreviewed) {
            String templateName;
            if (isAmendment(q)) {
                switch on q.SBCF_Document_Language__c{
                    when 'Taiwan'{
                        templateName = CPQConstants.AMENDMENT_TEMPLATE_TC;
                    }
                    when 'China'{
                        templateName = CPQConstants.AMENDMENT_TEMPLATE_SC;
                    }
                    when 'Japan'{
                        templateName = CPQConstants.AMENDMENT_TEMPLATE_JP;
                    }
                    when 'Korea'{
                        templateName = CPQConstants.AMENDMENT_TEMPLATE_KR;
                    }
                    when else{
                        templateName = CPQConstants.AMENDMENT_TEMPLATE;
                    }
                }
            } else if (isNewSale(q)) {
                if(q.SBQQ__LineItemsGrouped__c){
                    switch on q.SBCF_Document_Language__c{
                        when 'Taiwan'{
                            templateName = CPQConstants.NEWSALE_TEMPLATE_MYC_TC;
                        }
                        when 'China'{
                            templateName = CPQConstants.NEWSALE_TEMPLATE_MYC_SC;
                        }
                        when 'Japan'{
                            templateName = CPQConstants.NEWSALE_TEMPLATE_MYC_JP;
                        }
                        when 'Korea'{
                            templateName = CPQConstants.NEWSALE_TEMPLATE_MYC_KR;
                        }
                        when else{
                            templateName = CPQConstants.NEWSALE_TEMPLATE_MYC;
                        }
                    }
                } else{
                    switch on q.SBCF_Document_Language__c{
                        when 'Taiwan'{
                            templateName = CPQConstants.NONMYC_TEMPLATE_TC;
                        }
                        when 'China'{
                            templateName = CPQConstants.NONMYC_TEMPLATE_SC;
                        }
                        when 'Japan'{
                            templateName = CPQConstants.NONMYC_TEMPLATE_JP;
                        }
                        when 'Korea'{
                            templateName = CPQConstants.NONMYC_TEMPLATE_KR;
                        }
                        when else{
                            templateName = CPQConstants.NONMYC_TEMPLATE;
                        }
                    }
                }
            } else {
                if(q.SBQQ__LineItemsGrouped__c){
                    switch on q.SBCF_Document_Language__c{
                        when 'Taiwan'{
                            templateName = CPQConstants.RENEWAL_TEMPLATE_MYC_TC;
                        }
                        when 'China'{
                            templateName = CPQConstants.RENEWAL_TEMPLATE_MYC_SC;
                        }
                        when 'Japan'{
                            templateName = CPQConstants.RENEWAL_TEMPLATE_MYC_JP;
                        }
                        when 'Korea'{
                            templateName = CPQConstants.RENEWAL_TEMPLATE_MYC_KR;
                        }
                        when else{
                            templateName = CPQConstants.RENEWAL_TEMPLATE_MYC;
                        }
                    }
                } else{
                    switch on q.SBCF_Document_Language__c{
                        when 'Taiwan'{
                            templateName = CPQConstants.NONMYC_TEMPLATE_TC;
                        }
                        when 'China'{
                            templateName = CPQConstants.NONMYC_TEMPLATE_SC;
                        }
                        when 'Japan'{
                            templateName = CPQConstants.NONMYC_TEMPLATE_JP;
                        }
                        when 'Korea'{
                            templateName = CPQConstants.NONMYC_TEMPLATE_KR;
                        }
                        when else{
                            templateName = CPQConstants.NONMYC_TEMPLATE;
                        }
                    }
                }
            }

            q.SBQQ__QuoteTemplateId__c = templateNameToIdMap.get(templateName);
        }
    }

    public static void stampDatesOnQuote (List<SBQQ__Quote__c> quotes, Map<Id, List<SBQQ__QuoteLine__c>> qlByQuoteId) {
        System.debug(LoggingLevel.INFO, 'QuoteController.stampDatesOnQuote');

        Integer totNumberOfDays;
        Integer leapYears;
        for (SBQQ__Quote__c q : quotes) {
            totNumberOfDays = 0;
            leapYears = 0;
            q.SBQQ__StartDate__c = q.MinStDt__c;
            q.SBQQ__EndDate__c = q.MxEndDt__c;

            Date startDate = q.SBQQ__StartDate__c ;
            Date endDate = q.SBQQ__EndDate__c;

            if (endDate != null && startDate != null) {
                q.CTR__c = calculateCTR(q);

                //MYC Flag and Total Number of days are tracked --> Update only if needed
                if (q.SBQQ__StartDate__c != null && q.SBQQ__EndDate__c != null) {
                    totNumberOfDays = q.SBQQ__StartDate__c.daysBetween(q.SBQQ__EndDate__c) + 1;
                    //Substract leap year days                      
                    if (Date.isLeapYear(q.SBQQ__StartDate__c.year()) && q.SBQQ__StartDate__c <= Date.newInstance(q.SBQQ__StartDate__c.year(), 2, 29)){
                        leapYears += 1;
                    }
                    if (Date.isLeapYear(q.SBQQ__EndDate__c.year()) && q.SBQQ__EndDate__c >= Date.newInstance(q.SBQQ__EndDate__c.year(), 2, 29)){
                        leapYears += 1;
                    }
                    //If it's MultiYear, evaluate all the years in the range 
                    if(q.SBQQ__StartDate__c.year() + 1 != q.SBQQ__EndDate__c.year()){
                        for (Integer year = q.SBQQ__StartDate__c.year()+1; year < q.SBQQ__EndDate__c.year(); year++) {
                            if (Date.isLeapYear(year)){
                                leapYears += 1;                    
                            }
                        }
                    }
                    totNumberOfDays -= leapYears; 
                    if (q.Total_Number_of_Days__c != totNumberOfDays) {
                        q.Total_Number_of_Days__c = totNumberOfDays;
                    }
                    if (q.Multi_Year_Contract_Quote__c != (q.Total_Number_of_Days__c > 365)) {
                        q.Multi_Year_Contract_Quote__c = q.Total_Number_of_Days__c > 365;
                    }
                } else {
                    q.Total_Number_of_Days__c = null;
                    q.Multi_Year_Contract_Quote__c = false;
                }
            }

            if (q.SBQQ__Type__c == 'Amendment') {
                //Rollup to filter not-Amended QLs
                Date amendmentStartDate;
                Date amendmentEndDate;
                for (SBQQ__QuoteLine__c ql : qlByQuoteId.get(q.Id)) {
                    if (ql.SBQQ__Quote__c == q.Id 
                        && (ql.SBQQ__UpgradedSubscription__c == null  //New QL on Amended Quote
                        	|| (ql.SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__c != null //Amended QL (Quantity or CPI)
                            	&& (ql.SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__r.SBQQ__Quantity__c != ql.SBQQ__Quantity__c
                            		|| ql.SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__r.SBCF_CPI_Uplift__c != ql.SBCF_CPI_Uplift__c)))) {
                        amendmentStartDate = amendmentStartDate == null || (ql.SBQQ__StartDate__c != null && amendmentStartDate > ql.SBQQ__StartDate__c) ? ql.SBQQ__StartDate__c : amendmentStartDate;
                        amendmentEndDate = amendmentEndDate == null || (ql.SBQQ__EndDate__c != null && amendmentEndDate < ql.SBQQ__EndDate__c) ? ql.SBQQ__EndDate__c : amendmentEndDate;
                    }
                }
                if (amendmentStartDate != null && amendmentEndDate != null) {
                    leapYears = 0;
                    totNumberOfDays = amendmentStartDate.daysBetween(amendmentEndDate) + 1;
                    //Substract leap year days                      
                    if (Date.isLeapYear(amendmentStartDate.year()) && amendmentStartDate <= Date.newInstance(amendmentStartDate.year(), 2, 29)){
                        leapYears += 1;
                    }
                    if (Date.isLeapYear(amendmentEndDate.year()) && amendmentEndDate >= Date.newInstance(amendmentEndDate.year(), 2, 29)){
                        leapYears += 1;
                    }
                    //If it's MultiYear, evaluate all the years in the range 
                    if(amendmentStartDate.year() + 1 != amendmentEndDate.year()){
                        for (Integer year = amendmentStartDate.year()+1; year < amendmentEndDate.year(); year++) {
                            if (Date.isLeapYear(year)){
                                leapYears += 1;                    
                            }
                        }
                    }
                    totNumberOfDays -= leapYears; 
                    if (q.Total_Amended_Number_of_Days__c != totNumberOfDays) {
                        q.Total_Amended_Number_of_Days__c = totNumberOfDays;
                    }
                }
                else{
                    q.Total_Amended_Number_of_Days__c = null;
                }
            }
        }
    }

    public static void updateAssetsForAmendedLines (List<SBQQ__QuoteLine__c> lines, Map<Id, Opportunity> queriedOpps) {
        Set<Id> quoteIds = new Set<Id>();
        Set<Id> assetIds = new Set<Id>();
        for (SBQQ__QuoteLine__c line : lines) {
            assetIds.add(line.SBQQ__RenewedAsset__c);
            quoteIds.add(line.SBQQ__Quote__c);
        }

        Map<Id, Asset> relatedAssets = new Map<Id, Asset>([
            SELECT Id, Quantity, SBQQ__Subscription__c, Exception_multi__c, Key_Group_multi__c, Name, Product2Id, Aggregation_Comments__c,
                ProductCode, Sale_Opportunity__c, SBCF_DSI__c, SBQQ__CombineKey__c, SBQQ__LatestQuoteLine__c, SBQQ__CurrentSubscription__c,
                AccountId, ContactId, Price, SBQQ__ListPrice__c, SBQQ__RegularPrice__c, SBQQ__Discount__c, SBQQ__BundledQuantity__c, Is_Zero_Quantity__c
            FROM Asset
            WHERE Id IN :assetIds
        ]);

        Set<Id> renewedContractIds = new Set<Id>();
        for (SBQQ__QuoteLine__c ql : lines) {
            renewedContractIds.add(queriedOpps.get(ql.SBQQ__Quote__r.SBQQ__Opportunity2__c).SBQQ__RenewedContract__c);
        }
        Map<Id, SBQQ__Subscription__c> subscriptionsByContracts = new Map<Id, SBQQ__Subscription__c>();
        for (SBQQ__Subscription__c subscription : [SELECT Id, SBQQ__Contract__c FROM SBQQ__Subscription__c WHERE SBQQ__Contract__c IN :renewedContractIds]) {
            if (!subscriptionsByContracts.containsKey(subscription.SBQQ__Contract__c)) {
                subscriptionsByContracts.put(subscription.SBQQ__Contract__c, subscription);
            }
        }

        List<Asset> assetsToUpdate = new List<Asset>();
        List<Asset> assetsToInsert = new List<Asset>();
        List<Asset> zeroQuantityAssets = new List<Asset>();
        Asset relatedAsset;
        Asset newAsset;
        for (SBQQ__QuoteLine__c line : lines) {
            if (!relatedAssets.containsKey(line.SBQQ__RenewedAsset__c)) {
                continue;
            }

            Opportunity queriedOpp = queriedOpps.get(line.SBQQ__Quote__r.SBQQ__Opportunity2__c);
            relatedAsset = relatedAssets.get(line.SBQQ__RenewedAsset__c);

            if (line.SBQQ__Quantity__c == 0) {
                relatedAsset.Key_Group_multi__c = 'N/A';
                relatedAsset.Exception_multi__c = line.Amendment_Type__c;
                relatedAsset.Aggregation_Comments__c = line.Amendment_Type__c +
                    '\nQuote: ' + line.SBQQ__Quote__r.Name;
            } else if (line.SBQQ__Quantity__c > 0 && relatedAsset.Quantity > line.SBQQ__Quantity__c && !relatedAsset.Is_Zero_Quantity__c) {
                newAsset = relatedAsset.clone(false, true, false, false);
                newAsset.Quantity = relatedAsset.Quantity - line.SBQQ__Quantity__c;
                newAsset.Key_Group_multi__c = 'N/A';
                newAsset.Exception_multi__c = line.Amendment_Type__c;
                newAsset.Aggregation_Comments__c = line.Amendment_Type__c +
                    '\nQuote: ' + line.SBQQ__Quote__r.Name;
                newAsset.SBQQ__LatestQuoteLine__c = line.Id;
                relatedAsset.Terminated__c = line.Amendment_Type__c == 'Terminated';
                assetsToInsert.add(newAsset);
            } else if (relatedAsset.Is_Zero_Quantity__c && line.SBQQ__Quantity__c > 0) {
                relatedAsset.SBQQ__CurrentSubscription__c = subscriptionsByContracts.get(queriedOpp.SBQQ__RenewedContract__c).Id;
                relatedAsset.Key_Group_multi__c = 'Prod Key 1';
                relatedAsset.Exception_multi__c = '';
                zeroQuantityAssets.add(relatedAsset);
            }

            if (relatedAsset.Key_Group_multi__c != 'N/A') {
                relatedAsset.SBQQ__ListPrice__c = line.SBQQ__ListPrice__c;
                relatedAsset.SBQQ__RegularPrice__c = line.SBQQ__ListPrice__c;
                relatedAsset.Price = line.SBQQ__ListPrice__c;
            }

            if (relatedAsset.SBQQ__BundledQuantity__c == null) {
                relatedAsset.SBQQ__BundledQuantity__c = relatedAsset.Quantity;
            }

            if (line.SBQQ__Quantity__c > 0) {
                relatedAsset.Quantity = line.SBQQ__Quantity__c;
            }

            relatedAsset.Is_Zero_Quantity__c = line.SBQQ__Quantity__c == 0;
            assetsToUpdate.add(relatedAsset);

        }

        if (assetsToUpdate.isEmpty()) {
            return;
        }
        update assetsToUpdate;

        if (assetsToInsert.isEmpty()) {
            return;
        }
        insert assetsToInsert;

        List<SBQQ__SubscribedAsset__c> subscribedAssets = new List<SBQQ__SubscribedAsset__c>();
        for (Asset insertedAsset : assetsToInsert) {
            subscribedAssets.add(new SBQQ__SubscribedAsset__c(SBQQ__Asset__c = insertedAsset.Id, SBQQ__Subscription__c = insertedAsset.SBQQ__CurrentSubscription__c));
        }
        for (Asset updatedAsset : zeroQuantityAssets) {
            subscribedAssets.add(new SBQQ__SubscribedAsset__c(SBQQ__Asset__c = updatedAsset.Id, SBQQ__Subscription__c = updatedAsset.SBQQ__CurrentSubscription__c, SBQQ__Active__c = true));
        }
        insert subscribedAssets;
    }

    public static void updateQuotes () {
        System.debug(LoggingLevel.INFO, 'QuoteController.updateQuotes');

        if (!quoteMapToUpdate.isEmpty()) {
            SBQQ.TriggerControl.disable();
            List<SBQQ__Quote__c> quotesToUpdate = quoteMapToUpdate.values().deepClone(true);
            quoteMapToUpdate.clear();
            update quotesToUpdate;
            SBQQ.TriggerControl.enable();
        }
    }

    private static SBQQ__Quote__c getQuoteFromQuoteMapToUpdate (Id quoteId) {
        SBQQ__Quote__c relatedQuote;
        if (quoteMapToUpdate.containsKey(quoteId)) {
            relatedQuote = quoteMapToUpdate.get(quoteId);
        } else {
            relatedQuote = new SBQQ__Quote__c(Id = quoteId);
            quoteMapToUpdate.put(relatedQuote.Id, relatedQuote);
        }
        return relatedQuote;
    }

    public static void updateDatesOnQuote(List<SBQQ__Quote__c> quotes){
        SBQQ__Quote__c qToUpdate;
        for (SBQQ__Quote__c q : quotes){
            if(q.SBQQ__StartDate__c != q.MinStDt__c || q.SBQQ__EndDate__c != q.MxEndDt__c) {
                qToUpdate = getQuoteFromQuoteMapToUpdate(q.Id);
                qToUpdate.SBQQ__StartDate__c = q.MinStDt__c;
                qToUpdate.SBQQ__EndDate__c = q.MxEndDt__c;
                quoteMapToUpdate.put(q.Id, qToUpdate);
            }
        }
    }

    public static Integer calculateCTR(SBQQ__Quote__c q) {
        Date startDate = q.SBQQ__StartDate__c ;
        Date endDate = q.SBQQ__EndDate__c;
        Integer tempYear;
        Integer tempMonth;
        Integer ctr;

        if (endDate != null && startDate != null) {
            tempYear = endDate.year();
            tempMonth = endDate.month();

            Date tempDate = ((Date.newInstance(tempYear, startDate.day() == 1 ? tempMonth : tempMonth + 1, 1)) - 1).day() < (startDate - 1).day() ?
                    ((Date.newInstance(tempYear, startDate.day() == 1 ? tempMonth : tempMonth + 1, 1)) - 1) :
                    Date.newInstance(tempYear, (startDate - 1).day() > endDate.day() ? tempMonth - 1 : tempMonth, Integer.valueOf((startDate - 1).day()));

            if ((endDate + 1).day() != startDate.day()) {

                ctr = ((endDate.year() - startDate.year()) * 12) + ((endDate).month() - (startDate).month()) + ((startDate).day() > endDate.day() ? -1 : 0) +
                        (tempDate.daysBetween(endDate) > 15 ? 1 : 0);

            } else {
                ctr = (((endDate + 1).year() - startDate.year()) * 12) + ((endDate + 1).month() - startDate.month());
            }
        }
        return ctr;
    }

    public static void setCPDM(List<SBQQ__Quote__c>quotes) {
        Set<Id>pbIds = new Set<Id>();
        List<SBQQ__Quote__c> noTierQuotes = new List<SBQQ__Quote__c>();

        for (SBQQ__Quote__c q : quotes) {
            if (q.Cloud_Tier__c != null && q.Cloud_Tier__c != 'N/A') {
                pbIds.add(q.SBQQ__PriceBook__c);
            } else {
                noTierQuotes.add(q);
            }
        }

        List<Cloud_Pricing_and_Discount_Mapping__c> cpdmList = [SELECT Id, Tier__c, Min_Threshold_for_Discount__c, Max_Threshold_for_Discount__c, Max_Discount_in_threshold__c, Pricebook__c, Minimum_Software_ARR_Amount__c, CurrencyIsoCode FROM Cloud_Pricing_and_Discount_Mapping__c WHERE Pricebook__c in :pbIds];

        for (SBQQ__Quote__c q : quotes) {
            if (!noTierQuotes.contains(q)) {
                for (Cloud_Pricing_and_Discount_Mapping__c cpdm : cpdmList) {
                    if (q.SBQQ__PriceBook__c == cpdm.Pricebook__c && q.Cloud_Tier__c == cpdm.Tier__c && q.Software_ARR__c >= cpdm.Min_Threshold_for_Discount__c && q.Software_ARR__c <= cpdm.Max_Threshold_for_Discount__c && q.CurrencyIsoCode == cpdm.CurrencyIsoCode) {
                        q.Maximum_Software_Discount__c = cpdm.Max_Discount_in_threshold__c;
                        q.Minimum_Software_ARR__c = cpdm.Minimum_Software_ARR_Amount__c;
                        q.CPDM_Id__c = cpdm.Id;
                        break;
                    } else if (q.SBQQ__PriceBook__c == cpdm.Pricebook__c && q.Cloud_Tier__c == cpdm.Tier__c && q.CurrencyIsoCode == cpdm.CurrencyIsoCode) {
                        q.Minimum_Software_ARR__c = cpdm.Minimum_Software_ARR_Amount__c;
                        if (q.Maximum_Software_Discount__c == null || q.Maximum_Software_Discount__c > cpdm.Max_Discount_in_threshold__c) {
                            q.Maximum_Software_Discount__c = cpdm.Max_Discount_in_threshold__c;
                            q.CPDM_Id__c = cpdm.Id;
                        }
                    }
                }
            } else {
                q.Maximum_Software_Discount__c = null;
                q.Minimum_Software_ARR__c = null;
                q.CPDM_Id__c = null;
            }
        }
    }

    public static void calculateSoftwareArr(List<SBQQ__Quote__c>quotes){
        Chat_Bot_API__mdt mc = Chat_Bot_API__mdt.getInstance('Config');
        for(SBQQ__Quote__c q : quotes) {
            if(mc.Release_Date__c != null && q.CreatedDate.date() >= mc.Release_Date__c || (q.Submitted_For_Approval__c != null && q.Submitted_For_Approval__c.date() >= mc.Release_Date__c)) {
                if((q.SBQQ__Type__c == 'Amendment' && q.Total_Amended_Number_of_Days__c != null) || (q.SBQQ__Type__c != 'Amendment' && q.Total_Number_of_Days__c != null)) {
                    q.Software_ARR__c = q.FX_Software__c / (q.SBQQ__Type__c == 'Amendment' ? q.Total_Amended_Number_of_Days__c : q.Total_Number_of_Days__c) * 365;
                } else {
                    q.Software_ARR__c = q.FX_Software__c;
                }
            } else {
                if ((q.Total_Number_of_Days__c != null || q.Total_Amended_Number_of_Days__c != null) && ((q.Total_Number_of_Days__c > 365 && q.SBQQ__Type__c != 'Amendment') || (q.Total_Amended_Number_of_Days__c > 365 && q.SBQQ__Type__c == 'Amendment'))) {
                    q.Software_ARR__c = q.FX_Software__c / (q.SBQQ__Type__c == 'Amendment' ? q.Total_Amended_Number_of_Days__c : q.Total_Number_of_Days__c) * 365;
                } else {
                    q.Software_ARR__c = q.FX_Software__c;
                }
            }
        }
    }

    public static void updateOpportunitySplits(List<SBQQ__Quote__c> containsAIChanged, Map<Id, Opportunity> queriedOpps){
        Map<Id, OpportunitySplit> opportunitySplitsToUpdate = new Map<Id, OpportunitySplit>();
        for(SBQQ__Quote__c q : containsAIChanged) {
            Opportunity relatedOpp = queriedOpps.get(q.SBQQ__Opportunity2__c);
            if(relatedOpp != null){
                if(relatedOpp.OpportunityTeamMembers.size() > 0){
                    //The Opportunity should have only one Primary Opportunity Owner
                    Id teamMemberId = relatedOpp.OpportunityTeamMembers.get(0).UserId; 
                    OpportunitySplit osToUpdate = null;
                    List<OpportunitySplit> relatedOsToUpdate = new List<OpportunitySplit>();
                    for(OpportunitySplit oS: relatedOpp.OpportunitySplits){
                        if(oS.SplitOwnerId == teamMemberId){
                            if(oS.SplitAmount > 0 || (oS.SplitAmount == 0 && oS.SplitTypeId == '1494W000000QCAaQAO')){
                                osToUpdate = oS;
                            }
                            relatedOsToUpdate.add(oS);
                        }
                    }
                    if(osToUpdate != null 
                        && (osToUpdate.Contains_AI__c == null || (osToUpdate.Contains_AI__c == 1 && !q.AIOnly__c) || (osToUpdate.Contains_AI__c == 0 && q.AIOnly__c))){
                        osToUpdate.Contains_AI__c = q.AIOnly__c? 1: 0;
                        opportunitySplitsToUpdate.put(osToUpdate.Id, osToUpdate);
                        for(OpportunitySplit relatedOs: relatedOsToUpdate){
                            if(relatedOs.Id != osToUpdate.Id && relatedOs.Contains_AI__c != null){
                                relatedOs.Contains_AI__c = null;
                                opportunitySplitsToUpdate.put(relatedOs.Id, relatedOs);
                            }
                    }
                }
            }
        }
        }
        if(!opportunitySplitsToUpdate.isEmpty()){
            update opportunitySplitsToUpdate.values();
        }
    }

     public static void deselectWatermarkShown(List<SBQQ__Quote__c>quotes){
         for(SBQQ__Quote__c quote : quotes){
             quote.SBQQ__WatermarkShown__c = false;
         }
     }

     public static void syncExpirationDateToCpai(List<SBQQ__Quote__c> quoteNewList) {
        List<SBQQ__Quote__c> quoteToSync = new List<SBQQ__Quote__c>();
        RecursionHelper.firstRunOfUpdate = false;
        for(SBQQ__Quote__c quoteObj : quoteNewList) {
            Map<String, Object> Params = new Map<String, Object>();
            String applicationType = quoteObj.SBQQ__Type__c == 'Amendment' ? '1573' : '1569';
            Params.put('quoteRecord', quoteObj);
            Params.put('applicationType', applicationType);
            Params.put('isExpirationDateSync', true);
            Flow.Interview flowInterview = Flow.Interview.createInterview('Quote_Sync_Address_To_CPAi', Params);
            flowInterview.start();
        }
    }
}