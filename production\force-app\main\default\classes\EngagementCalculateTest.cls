/************************************* MODIFICATION LOG ********************************************************************************************
*                --------------- Task Engagement Score changed --------------
* if  task  has a checkbox Engagement Qualified Task - i.e. Type contains Call - Engagement Score is calculated within a formula field,
* Task Trigger with handle update on Contact or Lead
*
*          -----------------Event Engagement Score Changes ----------------
* if event is a BDR Qualified Meeting record type - Engagement Score is calculated within a formula field,
* EventTrigger with handle update on Contact or Lead
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Alla Kiyashko             05/21/2018                        -- Original Version 
* Alla Kiyashko             05/24/2018                        -- Added test method to cover ManageRedReport class 
*/

@IsTest
public class EngagementCalculateTest {

    public static User sysAdmin {
        get {
            if (sysAdmin == null) {
                sysAdmin = [SELECT Id FROM User WHERE Profile.Name = 'System Administrator' AND IsActive = TRUE LIMIT 1];
            }
            return sysAdmin;
        }
        set;
    }

    @TestSetup
    static void setupMethod () {
        System.runAs(sysAdmin) {
            Id rOffer = Schema.SObjectType.Campaign.getRecordTypeInfosByName().get('Offer').getRecordTypeId();
            Id rRR = Schema.SObjectType.Campaign.getRecordTypeInfosByName().get('Red Report').getRecordTypeId();

            // List<RecordType> rTypes = [select Id from RecordTYpe Where SobjectType = 'Campaign' AND Name = 'Offer'];
            Campaign RR = new Campaign(Name = 'Red Report', IsActive = true, RecordTypeId = rRR, StartDate = Date.today());

            Campaign c1 = new Campaign(Name = 'Sales Line', Type = 'Corporate Event', Subtype__c = 'Track Show', IsActive = true, RecordTypeId = rOffer, StartDate = Date.today());

            Campaign c2 = new Campaign(Name = 'Sales Line1', Type = 'Website', Subtype__c = 'Collateral Download', IsActive = true, RecordTypeId = rOffer, StartDate = Date.today());
            insert new List<Campaign> {
                c1, c2, RR
            };

            List<CampaignMemberStatus > lResponses = new List<CampaignMemberStatus >();
            lResponses.add(new CampaignMemberStatus(CampaignId = c1.Id, Label = 'Attended', SortOrder = 3, IsDefault = true, HasResponded = true));
            lResponses.add(new CampaignMemberStatus(CampaignId = c1.Id, Label = 'Registered', SortOrder = 4, IsDefault = false, HasResponded = true));
            lResponses.add(new CampaignMemberStatus(CampaignId = c2.Id, Label = 'Downloaded', SortOrder = 5, IsDefault = false, HasResponded = true));
            insert lResponses;

            Lead L1 = new Lead(LastName = 'FirstTest', FirstName = 'Lead1', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'US');
            insert L1;

            Account acc = new Account(Name = 'Test2  Account', BillingCountry = 'United States', BillingCity = 'Test City',
                BillingPostalCode = '12345', BillingState = 'VA', BillingStreet = 'Test Street');
            insert acc;

            Contact Cont1 = new Contact(LastName = 'Test', FirstName = 'Contact1', AccountId = acc.Id, Email = '<EMAIL>');
            insert Cont1;

            CampaignMember cm1 = new CampaignMember(Status = 'Registered', LeadId = L1.Id, CampaignId = c1.Id);
            CampaignMember cm3 = new CampaignMember(Status = 'Downloaded', LeadId = L1.Id, CampaignId = c2.Id);
            CampaignMember cm2 = new CampaignMember(Status = 'Attended', ContactId = Cont1.Id, CampaignId = c1.Id);
            CampaignMember cm4 = new CampaignMember(Status = 'Downloaded', ContactId = Cont1.Id, CampaignId = c2.Id);
            insert new List<CampaignMember> {
                cm1, cm2, cm3, cm4
            };
        }
    }

    @IsTest
    static void ManageRedReportTest () {
        System.runAs(sysAdmin) {
            List<Lead> lLeads = [SELECT Id FROM Lead LIMIT 1];
            System.assertEquals(!lLeads.isEmpty(), true);
            List<Contact> lContacts = [SELECT Id FROM Contact LIMIT 1];
            System.assertEquals(!lContacts .isEmpty(), true);

            Test.startTest();

            Lead l = new Lead(Id = lLeads[0].Id, BD_Status__c = 'L1 - Lead');
            update l;
            manageRedReportAdd.CreateCampaignMembers(new List <Id> {
                l.Id
            });
            Contact c = new Contact (Id = lContacts[0].Id, Contact_Status__c = 'L1 - Lead');
            update c;
            manageRedReportAdd.CreateCampaignMembers(new List <Id> {
                c.Id
            });
            l.BD_Status__c = 'L9 - Asset';
            update l;

            c.Contact_Status__c = 'L9 - Asset';
            update c;
            manageRedReport.CleanUpCampaignMembers(new List <Id> {
                l.Id, c.Id
            });

            Test.stopTest();
        }
    }

    @IsTest
    static void UpdateLeadEngagementBatchTest1 () {
        System.runAs(sysAdmin) {
            Test.startTest();

            UpdateLeadEngagementBatch c = new UpdateLeadEngagementBatch('Lead');
            Id batchprocessid = Database.executeBatch(c);

            Test.stopTest() ;
        }
    }
    @IsTest
    static void UpdateLeadEngagementBatchTest2 () {
        System.runAs(sysAdmin) {
            Test.startTest();

            UpdateLeadEngagementBatch c = new UpdateLeadEngagementBatch('Contact', ' Engagement_Score__c >0 ');
            Id batchprocessid = Database.executeBatch(c);

            Test.stopTest();
        }
    }

    @IsTest
    static void UpdateLeadEngagementBatchTest3 () {
        System.runAs(sysAdmin) {
            Test.startTest();

            UpdateLeadEngagementBatch c = new UpdateLeadEngagementBatch();
            Id batchprocessid = Database.executeBatch(c);

            Test.stopTest();
        }
    }

    @IsTest
    static void updateEngagementScoreTask () {
        System.runAs(sysAdmin) {
            List<Lead> L1 = [SELECT Id FROM Lead LIMIT 1];
            List<Contact> Cont1 = [SELECT Id, AccountId FROM Contact LIMIT 1];
            Case c1 = new Case(RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('Internal - Contracts').getRecordTypeId(), AccountId = Cont1[0].AccountId, ContactId = Cont1[0].Id, SuppliedEmail = '<EMAIL>', Status = 'C1 - New', Origin = 'Web',Reason = 'Account Administration',
                               Subreason__c = 'License Key',Priority = 'P4 - Moderate Impact',
                               Subject = 'Test', Description = 'Test', Number_Affected_Users__c = 1, Version__c = '5');
            insert c1;

            Test.startTest();

            Task t1 = new Task(Type = 'Meeting - External', Status = 'In Progress', WhoId = L1[0].Id, Subject = 'Meeting', ActivityDate = Date.today() + 1);
            Task t2 = new Task(Type = 'Call', Status = 'Completed', WhoId = L1[0].Id, Subject = 'Call', ActivityDate = Date.today() - 1);
            Task t3 = new Task(Type = 'Meeting - External', Status = 'Completed', WhoId = Cont1[0].Id, Subject = 'Meeting', ActivityDate = Date.today() - 1);
            Task t4 = new Task(Type = 'Meeting - External', Status = 'In Progress', WhoId = Cont1[0].Id, Subject = 'Meeting', ActivityDate = Date.today() + 1);
            Task t5 = new Task(Type = 'Call - Conversation', Status = 'In Progress', WhoId = Cont1[0].Id, WhatId = c1.Id, Subject = 'Meeting', ActivityDate = Date.today() + 1);
            insert new List<Task> {
                t1, t2, t3, t4, t5
            };

            List<Lead> LN = [SELECT Id, Engagement_Score__c FROM Lead WHERE Id = :L1[0].Id LIMIT 1];
            System.assertNotEquals(LN, null);
            System.assertEquals(LN[0].Engagement_Score__c > 0, true);

            List<Contact> CN = [SELECT Id, Engagement_Score__c FROM Contact WHERE Id = :Cont1[0].Id LIMIT 1];
            System.assertNotEquals(CN, null);
            System.assertEquals(CN[0].Engagement_Score__c > 0, true);

            update new Task(Id = t1.Id, Type = 'Call');
            LN = [SELECT Id, Engagement_Score__c FROM Lead WHERE Id = :L1[0].Id LIMIT 1];
            System.assertEquals(LN[0].Engagement_Score__c > 0, true);
            Test.stopTest();
        }

    }

    @IsTest
    static void updateEngagementScoreEvent () {
        System.runAs(sysAdmin) {
            List<RecordType> rTypes = [SELECT Id FROM RecordType WHERE SobjectType = 'Event' AND Name = 'BDR Qualified Meeting'];

            List<Lead> L1 = [SELECT Id FROM Lead LIMIT 1];
            List<Contact> Cont1 = [SELECT Id FROM Contact LIMIT 1];

            Test.startTest();

            Event e1 = new Event (RecordTypeId = rTypes[0].Id, StartDateTime = System.now().addDays(1), DurationInMinutes = 10000, Type = 'E6 - Meeting', Lead_Qualification_Meeting_Status__c = 'Scheduled', WhoId = L1[0].Id, Subject = 'Meeting');
            Event e2 = new Event (RecordTypeId = rTypes[0].Id, StartDateTime = System.now().addDays(-11), DurationInMinutes = 10000, Type = 'E6 - Meeting', Lead_Qualification_Meeting_Status__c = 'Completed', Conclusion__c = 'Completed', WhoId = L1[0].Id, Subject = 'Call');
            Event e3 = new Event (RecordTypeId = rTypes[0].Id, StartDateTime = System.now(), DurationInMinutes = 10000, Type = 'E6 - Meeting', Lead_Qualification_Meeting_Status__c = 'Completed', Conclusion__c = 'Completed', WhoId = Cont1[0].Id, Subject = 'Meeting');
            Event e4 = new Event (RecordTypeId = rTypes[0].Id, StartDateTime = System.now(), DurationInMinutes = 10000, Type = 'E6 - Meeting', Lead_Qualification_Meeting_Status__c = 'In Review', WhoId = Cont1[0].Id, Subject = 'Meeting');
            insert new List<Event > {
                e1, e2, e3, e4
            };

            List<Lead> LN = [SELECT Id, Engagement_Score__c FROM Lead WHERE Id = :L1[0].Id LIMIT 1];
            System.assertNotEquals(LN, null);
            System.assertEquals(LN[0].Engagement_Score__c > 0, true);

            List<Contact> CN = [SELECT Id, Engagement_Score__c FROM Contact WHERE Id = :Cont1[0].Id LIMIT 1];
            System.assertNotEquals(CN, null);
            System.assertEquals(CN[0].Engagement_Score__c > 0, true);

            update new Event(Id = e3.Id, Lead_Qualification_Meeting_Status__c = 'In Review', Conclusion__c = 'Rejected');
            CN = [SELECT Id, Engagement_Score__c FROM Contact WHERE Id = :Cont1[0].Id LIMIT 1];
            System.assertEquals(CN[0].Engagement_Score__c > 0, true);
            Test.stopTest();
        }
    }
}