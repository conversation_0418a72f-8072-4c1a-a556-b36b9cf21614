({
    doInit : function(component, event, helper) {

    },
    ValidateRecord : function(component, event, helper){
        component.set("v.showSpinner", true);
        var quoteType = component.get("v.QuoteRecord.SBQQ__Type__c");
        var isES = component.get("v.QuoteRecord.SBCF_ES_Quote__c");
        var softwareQuotelines =component.get("v.QuoteRecord.Software_Quote_Lines__c");
        var serviceLineItems = component.get("v.QuoteRecord.Service_Line_Items__c");
        var SalesRVP = component.get("v.QuoteRecord.Sales_RVP__c");
        var RevRecRepsrentative = component.get("v.QuoteRecord.Rev_Rec_Representative__c");
        var ServicesRVP = component.get("v.QuoteRecord.Services_RVP__c");
        var legalRepresentative = component.get("v.QuoteRecord.Legal_Representative__c"); 
        var lineItemCount = component.get("v.QuoteRecord.SBQQ__LineItemCount__c");
        var SBCFAgreementClassification =  component.get("v.QuoteRecord.SBCF_Agreement_Classification__c");
        var BilltoContact =  component.get("v.QuoteRecord.SBCF_Bill_to_Contact__c");
        var ShipToContact =  component.get("v.QuoteRecord.SBCF_Ship_to_Contact__c");
        var ExpiresOn =  component.get("v.QuoteRecord.SBQQ__ExpirationDate__c");
        var SowUploaded = component.get("v.QuoteRecord.SOW_Uploaded__c");
        var InternalQuote = component.get("v.QuoteRecord.Internal_Quote__c");
        var SumofConsulting = component.get("v.QuoteRecord.Sum_of_Consulting__c");
        var isPrimary = component.get("v.QuoteRecord.SBQQ__Primary__c");
        var sumOfPkgServices = component.get("v.QuoteRecord.Sum_of_Pkg_Services__c");
        var oppDSI = component.get("v.QuoteRecord.SBQQ__Opportunity2__r.DSI__c");
        var oppRecType = component.get("v.QuoteRecord.SBQQ__Opportunity2__r.RecordType.Name");
        var quoteStatus = component.get("v.QuoteRecord.SBQQ__Status__c");
        var billingFrequency = component.get("v.QuoteRecord.Billing_Frequency__c");
        var PPIT = component.get("v.QuoteRecord.SBCF_Pricing_Payment_and_Invoice_Terms__c");
        var CAI = component.get("v.QuoteRecord.Has_Cloud_Architecture_or_Infrastructure__c");
        var maxSoftDiscount = component.get("v.QuoteRecord.Maximum_Software_Discount__c");
        var softDiscount = component.get("v.QuoteRecord.Software_Discount__c");
        var minSoftARR = component.get("v.QuoteRecord.Minimum_Software_ARR__c");
        var softARR = component.get("v.QuoteRecord.Software_ARR__c");
        var PE = component.get("v.QuoteRecord.Pricing_Exception__c");
        var cloudTier = component.get("v.QuoteRecord.Cloud_Tier__c");
        var reseller = component.get("v.QuoteRecord.Reseller__c");
        var distributor = component.get("v.QuoteRecord.SBQQ__Distributor__c");
        var typeOfSale = component.get("v.QuoteRecord.SBCF_Type_of_Sale__c");

        console.log('quoteStatus --- ', quoteStatus);
        console.log('billingFrequency --- ', billingFrequency);
       
        console.log('isES '+isES);
        if(lineItemCount == 0){
            var message ='Quotes cannot be submitted without Quote Lines. Verify the configuration and submit again';
            helper.showError(component, event,message);
            component.set("v.isValid",false);
     //   }else if(isPrimary === false){
      //      component.set("v.isValid",false);
      //      var message ='You cannot submit a quote that is not primary. Check the primary box before submitting for approval ';
      //      helper.showError(component, event,message);
        }else if(softwareQuotelines > 0 && SalesRVP == null){
            component.set("v.isValid",false);
            var message ='You cannot submit a quote without Sales RVP, please specify a Sales RVP.';
            helper.showError(component, event,message);
        }else if(serviceLineItems > 0 && ServicesRVP == null){
            component.set("v.isValid",false);
            var message ='You cannot submit a quote without Services RVP, please specify a Services RVP.';
            helper.showError(component, event,message);
        }else if(SBCFAgreementClassification != "Standard" && RevRecRepsrentative == null){
            component.set("v.isValid",false);
            var message ='You cannot submit a quote without Rev Rec Representative, please specify a Rev Rec Representative.';
            helper.showError(component, event,message);
            
        } else if(SBCFAgreementClassification != "Standard" && legalRepresentative == null){
            component.set("v.isValid",false);
            var message ='You cannot submit a quote without Legal Representative, please specify a Legal Representative.';
            helper.showError(component, event,message);
            
        }else if(!ExpiresOn){
            component.set("v.isValid",false);
            var message ='Expire Date is Required for Submission.';
            helper.showError(component, event,message);
            
        } else if(!BilltoContact){
            component.set("v.isValid",false);
            var message ='Billing Contact is Required for Submission.';
            helper.showError(component, event,message);
            
        }else if(!ShipToContact){
            component.set("v.isValid",false);
            var message ='Shipping Contact is Required for Submission.';
            helper.showError(component, event,message);
        }else if (!isES && oppDSI == null && oppRecType == "Sales Opportunity (New)") {
            component.set("v.isValid",false);
            var message ='Please select a DSI in the Opportuntiy to submit the quote.';
            helper.showError(component, event,message);            
        }else if(SowUploaded === false && InternalQuote === false && SumofConsulting > 0 && SumofConsulting != sumOfPkgServices){
            component.set("v.isValid",false);
            var message ='You cannot submit a quote that has Services products without uploading an SOW.';
            helper.showError(component, event,message);
        }else if(quoteStatus == 'Q01 SAE' && billingFrequency == null){
            component.set("v.isValid",false);
            var message ='Please populate Billing Frequency to submit the quote for approval.';
            helper.showError(component, event,message);
        }else if(billingFrequency == "Custom" && PPIT == null || PPIT == ''){
            component.set("v.isValid",false);
            var message ='If '+ "'Custom'"+ ' is selected for Billing Frequency, the "Pricing, Payment and Invoicing Terms" field should not be blank.';
            helper.showError(component, event,message);
        } else if (cloudTier !== 'N/A' && quoteType !== 'Amendment' && !PE && !CAI && (softARR < minSoftARR || softDiscount > maxSoftDiscount)){
            if(softARR < minSoftARR){
                if(softDiscount > maxSoftDiscount){
                    component.set("v.isValid",false);
                    var message ='Software ARR amount does not meet minimum requirements and quote exceeds maximum discount requirements, please adjust your quote or contact your Revops Leader';
                    helper.showError(component, event,message);
                } else{
                    component.set("v.isValid",false);
                    var message ='Software ARR amount does not meet minimum requirements, please adjust your discounts or contact your Revops Leader';
                    helper.showError(component, event,message);
                }
            } else if (softDiscount > maxSoftDiscount){
                component.set("v.isValid",false);
                var message ='This quote exceeds the maximum discount % based on Cloud Tier and Software ARR, please adjust your discounts or contact your Revops Leader';
                helper.showError(component, event,message);
            }
        } else if(typeOfSale == 'Partner Sale' && distributor == null && reseller == null){
            component.set("v.isValid",false);
            var message ='This quote is designated as a Partner Sale. Before submitting it for approval, please update the Opportunity Partner Information to include either a Sales Partner with a Resell motion or a Distributor.';
            helper.showError(component, event,message);
        } else if(component.get("v.isValid")){
            var action = component.get("c.onSubmitInLightnig");
            action.setParams({
                quoteId :component.get("v.recordId")    
            });
            action.setCallback(this, function(a) {
                var state = a.getState();
                //debugger;
                if (state === "SUCCESS") {
                    component.set("v.showSpinner",false);
                    var dismissActionPanel = $A.get("e.force:closeQuickAction");
                    dismissActionPanel.fire();
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "type": "Success",
                        "message": "Quote was submitted for approval." ,
                        "mode": "dismissable"
                    });
                    toastEvent.fire();
                    $A.get('e.force:refreshView').fire();
                }
                if(state === 'ERROR'){
                    var dismissActionPanel = $A.get("e.force:closeQuickAction");
                    dismissActionPanel.fire();
                    // helper.closeAction(component, event, helper);
                    var errors = a.getError();
                    let toastParams = {
                        title: "Error",
                        message: "Unknown error", // Default error message
                        type: "error"
                    };
                    // Pass the error message if any
                    if (errors && Array.isArray(errors) && errors.length > 0) {
                        toastParams.message = errors[0].message;
                        toastParams.mode ='sticky';
                    }
                    // Fire error toast
                    let toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams(toastParams);
                    toastEvent.fire();
                    
                }
            });
            $A.enqueueAction(action);
        }
        
    }
    
    
})