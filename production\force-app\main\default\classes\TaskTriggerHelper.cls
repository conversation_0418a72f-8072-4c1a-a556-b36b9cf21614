/************************************* MODIFICATION LOG ********************************************************************************************
*                --------------- CaseCommunication  --------------
* if  task  Status : “Completed” and  Type : 'Call - VM Inbound','Call - VM Outbound','Call - Conversation','Meeting - Screenshare','Call - Missed'
* then update  Last Communication(Last_Communication__c) : NOW() and Last Communication By(Last_Communication_By__c) : SVC User(Call - Missed),Text value of “Assigned To” User Name ($User.Full_Name__c)(inbound, outbound,conv,Call - Missed)
*       
* if task   Status : “Completed” and Type : 'Call - VM Inbound','Call - Conversation','Meeting - Screenshare','Call - Missed'
* then update  Customer Action Required(Customer_Action_Required__c) : FALSE
*
* if task  Status : “Completed” and Type : 'Call - VM Inbound','Call - Missed' and case.'Last Communication by Customer' = FALSE
* then update  Initial Inbound Message Received(Inbound_Message_Received__c) : NOW()
*
* if task  Status : “Completed” and Type : 'Call - Conversation','Call - VM Outbound','Meeting - Screenshare'
* then update Last TS Comm(Last_TS_Comm__c) : NOW()
*
* if task  Status : “Completed” and (Type : 'Call - Missed') or (Type : 'Call - VM Inbound AND Owner User Type = Customer Portal Manager(PowerCustomerSuccess)) 
* then update Inbound Email/call Recieved(Inbound_Email_Call_Received__c) : NOW()
*
*          ------------------ TaskMilestone -------------------
* If  Status : “Completed” and Type = 'Call - Conversation','Call - VM Outbound','Meeting - Screenshare'
* then update first response milestone Completion date = now()
*
*          -----------------Count Call - Conversation----------------
* If  Status : “Completed” and Type = 'Call - Conversation'
* then update #Convers to count of tasks for each case
*
*          -----------------Count Tasks on lead or contacts----------------
* If  Status : “Completed”
* then update #Tasks to count of tasks for each lead
* then update #Tasks to count of tasks for each contact
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Amritha Buddharaju            04/14/2016                        -- Original Version 
* Amritha Buddharaju            06/07/2016                        -- Added Last TS Comm in Case communication      
* Amritha Buddharaju            07/05/2016                        -- Added rollupConvs count tasks on a case  
* Amritha Buddharaju            07/15/2016                        -- Added rollupLeads count tasks on a lead
* Amritha Buddharaju            08/22/2016                        -- Added Milestone Type = task on milestone completion on a case 
* Amritha Buddharaju            11/19/2016                        -- Added two new task types Missed Call and WebEx    
* Amritha Buddharaju            12/19/2016                        -- Replaced task types Inbound Voicemail to 'Voicemail – Inbound' and Outbound Voicemail to 'Voicemail – Outbound'  
* Amritha Buddharaju            01/24/2017                        -- Updated task type name Voicemail - Inbound to Call - VM Inbound, Missed Call to Call - Missed,WebEx to Meeting - Screenshare, Conversation to Call - Conversation and Voicemail - Outbound to Call - VM Outbound                                    
* Amritha Buddharaju            01/26/2017                        -- Replaced High Volume Portal with Customer Portal Manager
* Alla Kiyashko                 05/21/3018                        -- Added logic to calculate Engagement Rollup on Lead or Contact (WhoId)
* Amritha Buddharaju            01/27/2020                        -- Added rollupLeads count tasks on a Contact for lean data(case 336581)
*/

public class TaskTriggerHelper {

    //------------------------------------Case Communication-------------------------------------
    public static void CaseCommunication (List<Task> tskList) {
        Map<Id, Id> caseIds = new Map<Id, Id>();
        Set<Id> ivCas = new Set<Id>();
        Set<Id> inCas = new Set<Id>();
        Set<Id> tsCommCas = new Set<Id>();
        Set<Id> inMc = new Set<Id>();
        List<User> usrList = [SELECT Id, Name FROM User WHERE Name = 'SVC_APP_INTEGRATION'];

        for (Task tskRc : tskList) {
            if ((tskRc.WhatId != null && String.valueOf(tskRc.WhatId).startsWith('500')) && (tskRc.Type != null &&
                (tskRc.Type.equalsIgnoreCase('Call - Conversation') ||
                    tskRc.Type.equalsIgnoreCase('Call - VM Outbound') ||
                    tskRc.Type.equalsIgnoreCase('Meeting - Screenshare') ||
                    tskRc.Type.equalsIgnoreCase('Call - Missed') ||
                    tskRc.Type.equalsIgnoreCase('Call - VM Inbound')))
                && tskRc.Status.equalsIgnoreCase('Completed')) {
                caseIds.put(tskRc.WhatId, tskRc.OwnerId);

                if ((tskRc.Type.equalsIgnoreCase('Call - Conversation') || tskRc.Type.equalsIgnoreCase('Call - VM Inbound') || tskRc.Type.equalsIgnoreCase('Meeting - Screenshare') ||
                    tskRc.Type.equalsIgnoreCase('Call - Missed')) && tskRc.Status.equalsIgnoreCase('Completed')) {
                    ivCas.add(tskRc.WhatId);

                }if ((tskRc.Type.equalsIgnoreCase('Call - VM Inbound') || tskRc.Type.equalsIgnoreCase('Call - Missed')) && tskRc.Status.equalsIgnoreCase('Completed')) {
                    inCas.add(tskRc.WhatId);
                }
                if ((tskRc.Type.equalsIgnoreCase('Call - Conversation') || tskRc.Type.equalsIgnoreCase('Call - VM Outbound') || tskRc.Type.equalsIgnoreCase('Meeting - Screenshare')) && tskRc.Status.equalsIgnoreCase('Completed')) {
                    tsCommCas.add(tskRc.WhatId);
                }
                if ((tskRc.Type.equalsIgnoreCase('Call - Missed') || (tskRc.Type.equalsIgnoreCase('Call - VM Inbound') && (tskRc.UserType__c == 'PowerCustomerSuccess'))) && tskRc.Status.equalsIgnoreCase('Completed')) {
                    inMc.add(tskRc.WhatId);
                }

            }
            if (!caseIds.isEmpty()) {
                List<Case> CMSList = new List<Case>();
                Map<Id, String> caseOwnerMap = getOwners(caseIds);
                for (Case c : [SELECT Id, Last_Communication__c, Last_TS_Comm__c, Inbound_Message_Received__c, Customer_Action_Required__c, Last_Communication_by_Customer__c, Inbound_Email_Call_Received__c FROM Case WHERE Id IN:caseIds.keySet()]) {
                    c.Last_Communication__c = System.now();

                    if (tskRc.Type.equalsIgnoreCase('Call - Missed')) {
                        c.Last_Communication_By__c = usrList != null && !usrList.isEmpty() ? usrList[0].Name : null;
                    }
                    if (!tskRc.Type.equalsIgnoreCase('Call - Missed')) {
                        c.Last_Communication_By__c = caseOwnerMap.containsKey(caseIds.get(c.Id)) ? caseOwnerMap.get(caseIds.get(c.Id)) : null;
                    }

                    if (ivCas.contains(c.Id)) {
                        c.Customer_Action_Required__c = false;

                    }if (inCas.contains(c.Id)) {
                        c.Inbound_Message_Received__c = c.Last_Communication_by_Customer__c == false ? System.now() : c.Inbound_Message_Received__c;
                    }

                    if (tsCommCas.contains(c.Id)) {
                        c.Last_TS_Comm__c = System.now();
                    }

                    if (inMc.contains(c.Id)) {
                        c.Inbound_Email_Call_Received__c = System.now();
                    }
                    CMSList.add(c);
                }
                if (CMSList.size() > 0)
                    update CMSList;
            }
        }

    }

    private static Map<Id, String> getOwners (Map<Id, Id> casMap) {
        Set<Id> recIds = new Set<Id>();
        recIds.addAll(casMap.values());
        Map<Id, String> temp = new Map<Id, String>();
        for (User u : [SELECT Id, Full_Name__c FROM User WHERE Id IN:recIds]) {
            temp.put(u.Id, u.Full_Name__c);
        }
        return temp;
    }

    //---------------------------------TaskMilestone ---------------------------
    public static void TaskMilestone (List<Task> tskList, Map<Id, Task> oldMap, Boolean isInsert, Boolean isUpdate) {
        Set<Id> caseIds = new Set<Id>();
        Set<Id> tskOwnrIds = new Set<Id>();

        for (Task tskRc : tskList) {
            if ((tskRc.WhatId != null && String.valueOf(tskRc.WhatId).startsWith('500')) && (tskRc.Type != null && (tskRc.Type.equalsIgnoreCase('Call - Conversation') || tskRc.Type.equalsIgnoreCase('Call - VM Outbound') || tskRc.Type.equalsIgnoreCase('Meeting - Screenshare'))) && tskRc.Status.equalsIgnoreCase('Completed')) {
                if (isInsert || (isUpdate && (tskRc.WhatId != oldMap.get(tskRc.Id).WhatId || tskRc.Type != oldMap.get(tskRc.Id).Type || tskRc.Status != oldMap.get(tskRc.Id).Status))) {
                    caseIds.add(tskRc.WhatId);
                    tskOwnrIds.add(tskRc.CreatedById);
                }
            }

        }
        if (caseIds.size() > 0) {
            Set<Id> caseRecIds = getCaseRecordsOfTaskOwner(tskOwnrIds, caseIds);
            MilestoneUtils.completeMilestone(caseRecIds, 'Task');
        }

    }

    //---------------------------------Rollup Count Call - Conversation-------------------------

    public static void rollupConvs (List<Task> tskList, Map<Id, Task> oldTaskMap, Boolean isUpdate, Boolean isInsert, Boolean isDelete) {
        Set<Id> allCaseIds = new Set<Id>();
        for (Task tskRc : tskList) {
            if (tskRc.WhatId != null && String.valueOf(tskRc.WhatId).startsWith('500')) {
                if (!isUpdate) {
                    if (tskRc.Type != null && tskRc.Type.equalsIgnoreCase('Call - Conversation')) {
                        allCaseIds.add(tskRc.WhatId);
                    }
                } else {
                    // For any type to Call - Conversation...
                    if (tskRc.Type == 'Call - Conversation' && oldTaskMap.get(tskRc.Id).Type != 'Call - Conversation') {
                        allCaseIds.add(tskRc.WhatId);
                    }
                    // For Call - Conversation to Any Other type ...
                    else if (tskRc.Type != 'Call - Conversation' && oldTaskMap.get(tskRc.Id).Type == 'Call - Conversation') {
                        allCaseIds.add(tskRc.WhatId);
                        allCaseIds.add(oldTaskMap.get(tskRc.Id).WhatId);
                    }
                }
            }
        }

        if (!allCaseIds.isEmpty()) {
            countConvs(allCaseIds);
        }
    }

    private static void countConvs (Set<Id> caseIds) {
        Map<Id, Integer> arMap = new Map<Id, Integer>();
        for (AggregateResult ar : [SELECT COUNT(Id) total, WhatId FROM Task WHERE WhatId IN:caseIds AND Type = 'Call - Conversation' GROUP BY WhatId]) {
            arMap.put((Id) ar.get('WhatId'), (Integer) ar.get('total'));
        }

        List<Case> updateCase = new List<Case>();
        for (Case c : [SELECT Id, convs__c FROM Case WHERE Id IN:caseIds]) {
            c.convs__c = arMap.containsKey(c.Id) ? arMap.get(c.Id) : null;
            updateCase.add(c);
        }

        if (!updateCase.isEmpty()) {
            update updateCase;
        }
    }

    private static Set<Id> getCaseRecordsOfTaskOwner (Set<Id> tOwnrIds, Set<Id> csIds) {
        Set<Id> tmpSet = new Set<Id>();
        for (Case c : [SELECT Id, OwnerId FROM Case WHERE Id IN:csIds AND OwnerId IN:tOwnrIds]) {
            tmpSet.add(c.Id);
        }
        return tmpSet;
    }

    //-------------------------------Rollup Count tasks for each lead/contact---------------------------

    public static void rollupLeads (List<Task> tskList, Map<Id, Task> oldTaskMap, Boolean isUpdate, Boolean isInsert, Boolean isDelete) {
        Set<Id> allTaskIds = new Set<Id>();
        List<Id> allTaskContactLeadIds = new List<Id>();
        System.debug('###taskList '+ tskList);
        for (Task tskRc : tskList) {
             System.debug('### first if '+ tskRc.WhoId);
            if (tskRc.WhoId != null && (String.valueOf(tskRc.WhoId).startsWith('00Q') || String.valueOf(tskRc.WhoId).startsWith('003'))) {
                if (isInsert && tskRc.Status == 'Completed') {
                    allTaskIds.add(tskRc.WhoId);
                } else if (isUpdate && (tskRc.Status == 'Completed' || oldTaskMap.get(tskRc.Id).Status == 'Completed')) {
                    allTaskIds.add(tskRc.WhoId);
                    allTaskIds.add(oldTaskMap.get(tskRc.Id).WhoId);
                } else if (isDelete) {
                    allTaskIds.add(tskRc.WhoId);
                }
            }
             if (tskRc.WhoId == null && tskRc.Status == 'Completed' && (isInsert || isUpdate)){
                allTaskContactLeadIds.add(tskRc.Id);
            }

            System.debug('###_--allTaskIds-' + allTaskIds);
            System.debug('##@@_--allTaskContactIds-' + allTaskContactLeadIds);

            System.debug('###_--allTaskIds-' + allTaskIds);
        }

        if (!allTaskIds.isEmpty()) {
            counttasks(allTaskIds);
        }
         if (!allTaskContactLeadIds.isEmpty()) {
            setWhoId(allTaskContactLeadIds);
        }
    }

    private static void counttasks (Set<Id> taskIds) {
        Map<Id, Integer> arMap = new Map<Id, Integer>();
        for (AggregateResult ar : [SELECT COUNT(Id) total, WhoId FROM Task WHERE WhoId IN:taskIds AND Status = 'Completed' GROUP BY WhoId]) {
            arMap.put((Id) ar.get('WhoId'), (Integer) ar.get('total'));
        }

        //---------------count tasks on a lead-----------
        List<Lead> updateLead = new List<Lead>();
        for (Lead c : [SELECT Id, Count_Tasks__c FROM Lead WHERE Id IN:taskIds]) {
            c.Count_Tasks__c = arMap.containsKey(c.Id) ? arMap.get(c.Id) : null;
            updateLead.add(c);
        }

        if (!updateLead.isEmpty()) {
            update updateLead;
        }

        //------------count tasks on a Contact-----------
        List<Contact> contacts = new List<Contact>();
        for (Contact c : [SELECT Id, Count_Tasks__c FROM Contact WHERE Id IN:taskIds]) {
            c.Count_Tasks__c = arMap.containsKey(c.Id) ? arMap.get(c.Id) : null;
            contacts.add(c);
        }

        if (!contacts.isEmpty()) {
            update contacts;
        }
    }

    public static void updateEngagementScore (List<Task> tskList, Map<Id, Task> oldTaskMap, Boolean isUpdate, Boolean isInsert, Boolean isDelete) {
        Set<Id> allTaskIds = new Set<Id>();

        if (FeatureSwitchManager.isSwitchDisabled('updateEngagementScore') && !Test.isRunningTest()) {
            System.debug(' disabled');
            return;
        }

        for (Task tskRc : tskList) {
            if (tskRc.WhoId != null) {
                if (isInsert || isDelete && tskRc.Engagement_Score_Qualified__c) {
                    allTaskIds.add(tskRc.WhoId);

                } else if (isUpdate && (tskRc.Engagement_Score_Qualified__c != oldTaskMap.get(tskRc.Id).Engagement_Score_Qualified__c || tskRc.Engagement_Score__c != oldTaskMap.get(tskRc.Id).Engagement_Score__c)) {
                    allTaskIds.add(tskRc.WhoId);
                    if (oldTaskMap.get(tskRc.Id).WhoId != null)
                        allTaskIds.add(oldTaskMap.get(tskRc.Id).WhoId);
                }
            }

            System.debug('###_--all items for Engagement calculation-' + allTaskIds);
        }

        if (!allTaskIds.isEmpty()) {
            EngagementCalculate.CalculateOneSet(new List<Id>(allTaskIds));
        }
    }

    public static Id defectRecordTypeId {
        get {
            if (defectRecordTypeId == null) {
                defectRecordTypeId = Schema.SObjectType.Task.getRecordTypeInfosByName().get('Defect').getRecordTypeId();
            }
            return defectRecordTypeId;
        }
        set;
    }

    public static void assignDefectToDeveloper (List<Task> newTasks) {
        Map<Id, List<Task>> bugsByTestScenarios = new Map<Id, List<Task>>();
        for (Task newTask : newTasks) {
            if (newTask.RecordTypeId == defectRecordTypeId && String.isNotBlank(newTask.WhatId) && newTask.WhatId.getSobjectType().getDescribe().getName() == 'Test_Scenarios__c') {
                if (bugsByTestScenarios.containsKey(newTask.WhatId)) {
                    bugsByTestScenarios.get(newTask.WhatId).add(newTask);
                } else {
                    bugsByTestScenarios.put(newTask.WhatId, new List<Task> {newTask});
                }
            }
        }

        if (!bugsByTestScenarios.isEmpty()) {
            for (Test_Scenarios__c testScenario : [SELECT Id, Case__r.Developer__c FROM Test_Scenarios__c WHERE Id IN :bugsByTestScenarios.keySet()]) {
                for (Task defect : bugsByTestScenarios.get(testScenario.Id)) {
                    if (testScenario.Case__c == null) {
                        defect.addError('Defect cannot be created for Test Scenario with blank Case.');
                    }
                    defect.OwnerId = testScenario.Case__r.Developer__c;
                    defect.Case__c = testScenario.Case__c;
                }
            }
        }
    }

    public static void preventDefectDeletion (List<Task> tasks) {
        for (Task oldTask : tasks) {
            if (oldTask.RecordTypeId == defectRecordTypeId) {
                oldTask.addError('Defects cannot be deleted.');
            }
        }
    }

    @AuraEnabled(Cacheable=true)
    public static List<DefectWrapper> getCaseRelatedDefects (Id caseId) {
        List<DefectWrapper> defectsData = new List<DefectWrapper>();

        List<Task> relatedDefects = [
            SELECT Id, Subject, Status, Priority, CreatedById, OwnerId, IsClosed, WhatId, CreatedDate
            FROM Task
            WHERE Case__c = :caseId
                AND RecordTypeId = :defectRecordTypeId
            ORDER BY Status, Priority, WhatId, OwnerId ASC
        ];

        Set<Id> relatedUserIds = new Set<Id>();
        Set<Id> relatedTSIds = new Set<Id>();
        for (Task defect : relatedDefects) {
            relatedUserIds.add(defect.OwnerId);
            relatedUserIds.add(defect.CreatedById);
            relatedTSIds.add(defect.WhatId);
        }
        Map<Id, User> relatedUsers = new Map<Id, User>([SELECT Id, Full_Name__c FROM User WHERE Id IN :relatedUserIds]);
        Map<Id, Test_Scenarios__c> relatedTestScenarios = new Map<Id, Test_Scenarios__c>([SELECT Id, Name FROM Test_Scenarios__c WHERE Id IN :relatedTSIds]);

        for (Task defect : relatedDefects) {
            defectsData.add(new DefectWrapper(defect, relatedUsers, relatedTestScenarios));
        }

        return defectsData;
    }

    public class DefectWrapper {
        @AuraEnabled
        public String id;
        @AuraEnabled
        public String defectUrl;
        @AuraEnabled
        public String ownerUrl;
        @AuraEnabled
        public String createdByUrl;
        @AuraEnabled
        public String subject;
        @AuraEnabled
        public String status;
        @AuraEnabled
        public String priority;
        @AuraEnabled
        public String ownerName;
        @AuraEnabled
        public String createdByName;
        @AuraEnabled
        public String iconName;
        @AuraEnabled
        public String relatedToName;
        @AuraEnabled
        public String relatedToURL;
        @AuraEnabled
        public Datetime createdDate;
        @AuraEnabled
        public Boolean isClosed;

        public DefectWrapper (Task defect, Map<Id, User> relatedUsers, Map<Id, Test_Scenarios__c> relatedTestScenarios) {
            String sfUrl = Url.getOrgDomainUrl().toExternalForm() + '/lightning/r/{0}/view';
            this.id = defect.Id;
            this.defectUrl = String.format(sfUrl, new List<String> {'Task/' + defect.Id});
            this.ownerUrl = String.format(sfUrl, new List<String> {'User/' + defect.OwnerId});
            this.createdByUrl = String.format(sfUrl, new List<String> {'User/' + defect.CreatedById});
            this.relatedToURL = String.format(sfUrl, new List<String> {'Test_Scenarios__c/' + defect.WhatId});
            this.subject = defect.Subject;
            this.status = defect.Status;
            this.priority = defect.Priority;
            this.ownerName = relatedUsers.get(defect.OwnerId).Full_Name__c;
            this.createdByName = relatedUsers.get(defect.CreatedById).Full_Name__c;
            this.iconName = defect.IsClosed ? defect.Status == 'DE00 - Rejected' ? 'action:reject' : 'action:approval' : 'action:bug';
            this.relatedToName = relatedTestScenarios.get(defect.WhatId).Name;
            this.createdDate = defect.CreatedDate;
            this.isClosed = defect.IsClosed;
        }
    }
    
     private static void setWhoId(List<Id> tskIds) {
        if (!tskIds.isEmpty()) {
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            String email = Test.isRunningTest() ?
                '<EMAIL>' :
            [SELECT Id, Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'Update_Contact_or_Leads_Status'].Value__c
                ;
            mail.setToAddresses(new List<String>{
                email
                    });
            mail.setPlainTextBody(JSON.serialize(tskIds));
            if (!Test.isRunningTest()) {
                Messaging.sendEmail(new Messaging.SingleEmailMessage[]{
                    mail
                        });
            }
        }
    }
    
    public static void updateTaskFields (List<Task> newTasks, Map<Id, Task> oldTaskMap, Boolean isInsert, Boolean isUpdate) {
        
        Map<String,String> prefixVsSObj = New Map<String,String>();
        Map<String,List<Id>> sObjVsSObjIds = New Map<String,List<Id>>();
        Map<String,Map<Id,sObject>> sObjVsSObjMap = New Map<String,Map<Id,sObject>>();
        Map<String,String> sObjVsSObjNameField = New Map<String,String>{'Case' => 'CaseNumber', 'ProcessException' => 'ProcessExceptionNumber', 
            'AssetRelationship' => 'AssetRelationshipNumber', 'Solution' => 'SolutionNumber'};
                String sObjName;
        String nameField;
        
        for (Schema.SObjectType sObjtype : Schema.getGlobalDescribe().values()) {
            Schema.DescribeSObjectResult result = sObjtype.getDescribe();
            prefixVsSObj.put(result.getKeyPrefix(),result.getName());
        }
        
        for (Task tsk : newTasks) {
            if ((isInsert && tsk.WhoId != null) || (isUpdate && TriggersHelper.valueChangedNotBlank(oldTaskMap.get(tsk.Id), tsk, Task.WhoId))) {
                sObjName = prefixVsSObj.get(String.valueOf(tsk.WhoId).substring(0,3));
                if (!sObjVsSObjIds.containsKey(sObjName))
                    sObjVsSObjIds.put(sObjName, New List<Id>());
                sObjVsSObjIds.get(sObjName).add(tsk.WhoId);
            }
            if ((isInsert && tsk.WhatId != null) || (isUpdate && TriggersHelper.valueChangedNotBlank(oldTaskMap.get(tsk.Id), tsk, Task.WhatId))) {
                sObjName = prefixVsSObj.get(String.valueOf(tsk.WhatId).substring(0,3));
                if (!sObjVsSObjIds.containsKey(sObjName))
                    sObjVsSObjIds.put(sObjName, New List<Id>());
                sObjVsSObjIds.get(sObjName).add(tsk.WhatId);
            }
        }
        
        for (String sObj : sObjVsSObjIds.keySet()) {
            List<Id> sObjIds = sObjVsSObjIds.get(sObj);
            nameField = sObjVsSObjNameField.containsKey(sObj) ? sObjVsSObjNameField.get(sObj) : 'Name';
            Map<Id,sObject> sObjList = new Map<Id,sObject>(Database.query('SELECT '+nameField+' FROM '+sObj+' WHERE Id IN :sObjIds'));
            sObjVsSObjMap.put(sObj,sObjList);
        }
        
        for (Task tsk : newTasks) {
            if ((isInsert && tsk.WhoId != null) || (isUpdate && TriggersHelper.valueChangedNotBlank(oldTaskMap.get(tsk.Id), tsk, Task.WhoId))) {
                sObjName = prefixVsSObj.get(String.valueOf(tsk.WhoId).substring(0,3));
                nameField = sObjVsSObjNameField.containsKey(sObjName) ? sObjVsSObjNameField.get(sObjName) : 'Name';
                tsk.Who_Name__c = String.valueOf(sObjVsSObjMap.get(sObjName).get(tsk.WhoId).get(nameField));
            }
            if ((isInsert && tsk.WhatId != null) || (isUpdate && TriggersHelper.valueChangedNotBlank(oldTaskMap.get(tsk.Id), tsk, Task.WhatId))) {
                sObjName = prefixVsSObj.get(String.valueOf(tsk.WhatId).substring(0,3));
                nameField = sObjVsSObjNameField.containsKey(sObjName) ? sObjVsSObjNameField.get(sObjName) : 'Name';
                tsk.What_Name__c = String.valueOf(sObjVsSObjMap.get(sObjName).get(tsk.WhatId).get(nameField));
            }
        }
    }
}
