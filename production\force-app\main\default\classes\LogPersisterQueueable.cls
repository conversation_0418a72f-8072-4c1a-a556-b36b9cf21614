/**
 * @description Queueable Apex class to persist a parent Transaction_Log__c record
 * and its child Integration_Error_Log__c records asynchronously.
 */
public class LogPersisterQueueable implements Queueable {

    private Transaction_Log__c parentTransactionLog;
    private List<Integration_Error_Log__c> childLogsToInsert;

    /**
     * @description Constructor that accepts the parent Transaction_Log__c SObject (not yet inserted)
     * and the list of child Integration_Error_Log__c SObjects.
     * @param parentLog The Transaction_Log__c SObject to be inserted.
     * @param childLogs A list of Integration_Error_Log__c SObjects to be inserted.
     */
    public LogPersisterQueueable(Transaction_Log__c parentLog, List<Integration_Error_Log__c> childLogs) {
        this.parentTransactionLog = parentLog;
        this.childLogsToInsert = childLogs;
    }

    /**
     * @description Executes the queueable job.
     * 1. Inserts the parent Transaction_Log__c.
     * 2. Associates child logs with the parent's new ID.
     * 3. Inserts the child Integration_Error_Log__c records.
     * @param context The QueueableContext interface provides context for the job.
     */
    public void execute(QueueableContext context) {
        if (parentTransactionLog == null) {
            System.debug(LoggingLevel.ERROR, 'LogPersisterQueueable: Parent Transaction Log is null. Cannot proceed.');
            return;
        }

        try {
            Database.SaveResult parentSaveResult = Database.insert(parentTransactionLog, false);

            if (parentSaveResult.isSuccess()) {
                Id newParentLogId = parentSaveResult.getId();

                if (childLogsToInsert != null && !childLogsToInsert.isEmpty()) {
                    for (Integration_Error_Log__c childLog : childLogsToInsert) {
                        childLog.Transaction_Log__c = newParentLogId;
                    }

                    Database.SaveResult[] childSaveResults = Database.insert(childLogsToInsert, false);

                    for (Integer i = 0; i < childSaveResults.size(); i++) {
                        if (!childSaveResults[i].isSuccess()) {
                            Database.Error error = childSaveResults[i].getErrors()[0];
                            System.debug(LoggingLevel.ERROR,
                                'LogPersisterQueueable: Failed to insert a child Integration_Error_Log__c record. ' +
                                'Parent Log ID: ' + newParentLogId +
                                '. Log Message (if available): ' + childLogsToInsert[i].Log_Message__c +
                                '. Status Code: ' + error.getStatusCode() +
                                '. Error Message: ' + error.getMessage() +
                                '. Fields: ' + String.join(error.getFields(), ', ')
                            );
                        }
                    }
                } else {
                    System.debug(LoggingLevel.INFO, 'LogPersisterQueueable: Parent log ' + newParentLogId + ' inserted. No child logs to process.');
                }
            } else {
                Database.Error error = parentSaveResult.getErrors()[0];
                System.debug(LoggingLevel.ERROR,
                    'LogPersisterQueueable: Failed to insert parent Transaction_Log__c. ' +
                    'Apex Class on Parent: ' + parentTransactionLog.Apex_Class_Name__c +
                    '. Status Code: ' + error.getStatusCode() +
                    '. Error Message: ' + error.getMessage() +
                    '. Fields: ' + String.join(error.getFields(), ', ') +
                    '. Child logs will not be inserted.'
                );
            }
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR,
                'LogPersisterQueueable: A critical exception occurred during log persistence. ' +
                'Exception Type: ' + e.getTypeName() +
                '. Message: ' + e.getMessage() +
                '. Stacktrace: ' + e.getStackTraceString()
            );
        }
    }
}
