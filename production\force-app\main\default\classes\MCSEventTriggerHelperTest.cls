/**
 * @description Test class for MCSEventTriggerHelper.
 * This class covers all future methods, including success paths for different logic branches,
 * callout failures, and general exception handling.
 */
@isTest
private class MCSEventTriggerHelperTest {

    /**
     * @description Sets up all necessary data for the tests.
     * This includes creating Users, Accounts, Contacts, DSI records,
     * and mock Custom Metadata records.
     */
    @testSetup
    static void makeData() {
        User slsUser = new User(
            ProfileId = UserInfo.getProfileId(), Alias = 'slsusr', Email='<EMAIL>',
            EmailEncodingKey='UTF-8', LastName='SLS', LanguageLocaleKey='en_US',
            LocaleSidKey='en_US', TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>' + System.currentTimeMillis(),
            Department = 'SLS', EmployeeNumber= '213', FirstName = 'asdf'
        );
        insert slsUser;

        Account slsAccount = new Account(Name='SLS Owner Account', OwnerId = slsUser.Id);
        insert slsAccount;

        Account defaultOwnerAccount = new Account(Name='Default Owner Account', OwnerId = UserInfo.getUserId());
        insert defaultOwnerAccount;

        // --- Contact and DSI Setup ---
        Contact con = new Contact(LastName = 'TestContact', AccountId = slsAccount.Id, Email='<EMAIL>', FirstName='asdf');
        insert con;

        DSI__c dsiForSlsAccount = new DSI__c(
            Name = 'Test DSI - SLS', Account__c = slsAccount.Id,
            Platform__c = 'MCS', MCSEnvironmentOwner__c = con.Id, Status__c = 'Trial',
            MCSActivationDateTime__c = System.now(), Entitlement_End_Date__c = System.today().addDays(30)
        );
        insert dsiForSlsAccount;

        DSI__c dsiForDefaultAccount = new DSI__c(
            Name = 'Test DSI - Default', Account__c = defaultOwnerAccount.Id,
            Platform__c = 'MCS', MCSEnvironmentOwner__c = con.Id, Status__c = 'Active'
        );
        insert dsiForDefaultAccount;
    }

    // --- Tests for updateAccountOwner ---

    @isTest
    static void testUpdateAccountOwner_Success_SLS() {
        Test.setMock(HttpCalloutMock.class, new MCSEventMock(200));
        Account acc = [SELECT Id FROM Account WHERE Name = 'SLS Owner Account'];

        Test.startTest();
        MCSEventTriggerHelper.updateAccountOwner(acc.Id);
        Test.stopTest();

    }

    @isTest
    static void testUpdateAccountOwner_Success_Default() {
        Test.setMock(HttpCalloutMock.class, new MCSEventMock(200));
        Account acc = [SELECT Id FROM Account WHERE Name = 'Default Owner Account'];

        Test.startTest();
        MCSEventTriggerHelper.updateAccountOwner(acc.Id);
        Test.stopTest();
	}

    @isTest
    static void testUpdateAccountOwner_CalloutFailure() {
        Test.setMock(HttpCalloutMock.class, new MCSEventMock(500));
        Account acc = [SELECT Id FROM Account WHERE Name = 'SLS Owner Account'];

        Test.startTest();
        MCSEventTriggerHelper.updateAccountOwner(acc.Id);
        Test.stopTest();

        // Verify a failure log was created
        System.assertEquals(1, [SELECT COUNT() FROM FF_Integration_Log__c]);
    }

    // --- Tests for updateEntitlementEndDate ---

    @isTest
    static void testUpdateEntitlementEndDate_Success_Trial() {
        Test.setMock(HttpCalloutMock.class, new MCSEventMock(200));
        DSI__c dsi = [SELECT Id FROM DSI__c LIMIT 1];

        Test.startTest();
        MCSEventTriggerHelper.updateEntitlementEndDate(dsi.Id);
        Test.stopTest();

    }

    @isTest
    static void testUpdateEntitlementEndDate_Success_Active() {
        Test.setMock(HttpCalloutMock.class, new MCSEventMock(200));
        DSI__c dsi = [SELECT Id FROM DSI__c LIMIT 1];

        Test.startTest();
        MCSEventTriggerHelper.updateEntitlementEndDate(dsi.Id);
        Test.stopTest();

    }

    @isTest
    static void testUpdateEntitlementEndDate_CalloutFailure() {
        Test.setMock(HttpCalloutMock.class, new MCSEventMock(404));
        DSI__c dsi = [SELECT Id FROM DSI__c LIMIT 1];

        Test.startTest();
        MCSEventTriggerHelper.updateEntitlementEndDate(dsi.Id);
        Test.stopTest();

    }

    // --- Tests for updateEnvironmentOwner ---

    @isTest
    static void testUpdateEnvironmentOwner_Success() {
        Test.setMock(HttpCalloutMock.class, new MCSEventMock(200));
        DSI__c dsi = [SELECT Id FROM DSI__c LIMIT 1];

        Test.startTest();
        MCSEventTriggerHelper.updateEnvironmentOwner(dsi.Id);
        Test.stopTest();

        // The logic inside the method inserts a log if status is not 200 OR if it's a test run.
        // So we expect a log here even on "success".
        System.assertEquals(1, [SELECT COUNT() FROM FF_Integration_Log__c]);
    }

    @isTest
    static void testUpdateEnvironmentOwner_CalloutFailure() {
        Test.setMock(HttpCalloutMock.class, new MCSEventMock(500));
        DSI__c dsi = [SELECT Id FROM DSI__c LIMIT 1];

        Test.startTest();
        MCSEventTriggerHelper.updateEnvironmentOwner(dsi.Id);
        Test.stopTest();

        System.assertEquals(1, [SELECT COUNT() FROM FF_Integration_Log__c]);
    }

    // --- General Exception Handling Test ---
    @isTest
    static void testFutureMethod_GeneralException() {
        String invalidId = '***************'; // An ID that doesn't exist

        Test.startTest();
        // Calling with an ID that will cause a QueryException
        MCSEventTriggerHelper.updateAccountOwner(invalidId);
        MCSEventTriggerHelper.updateEntitlementEndDate(invalidId);
        MCSEventTriggerHelper.updateEnvironmentOwner(invalidId);
        Test.stopTest();

    }


    /**
     * @description Mock class to simulate HTTP responses for all future method callouts.
     */
    public class MCSEventMock implements HttpCalloutMock {
        private Integer statusCode;

        public MCSEventMock(Integer statusCode) {
            this.statusCode = statusCode;
        }

        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setStatusCode(this.statusCode);
            res.setStatus(this.statusCode == 200 ? 'OK' : 'Error');
            res.setBody(this.statusCode == 200 ? '{"status":"success"}' : '{"status":"error"}');
            return res;
        }
    }
}
