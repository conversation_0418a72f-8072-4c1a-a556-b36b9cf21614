/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 06/01/2021.
 */

public without sharing class QuoteTriggerExecutionController implements TriggerHandler {

    public static Boolean useOldQuoteTrigger {
        get {
            if (useOldQuoteTrigger == null) {
                useOldQuoteTrigger = Boolean.valueOf([SELECT Id, Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'Use_Old_Quote_Trigger'].Value__c);
            }
            return useOldQuoteTrigger;
        }
        set;
    }

    private Set<Id> relatedOppIds;
    private Set<Id> currentQuotesIds;

    private List<SBQQ__Quote__c> newQuotesStampFields;
    private List<SBQQ__Quote__c> quotesStampRenewalEmailDays;
    private List<SBQQ__Quote__c> quotesChangeOppType;
    private List<SBQQ__Quote__c> quotesPlatformFlag;
    private List<SBQQ__Quote__c> quotesDocumentLanguage;
    private List<SBQQ__Quote__c> quotesApprovalStat<PERSON>Changed;
    private List<SBQQ__Quote__c> executedQuotes;
    private List<SBQQ__Quote__c> quotesLOEChanged;
    private List<SBQQ__Quote__c> quotesLegalNotes;
    private List<SBQQ__Quote__c> quotesRevRecNotes;
    private List<SBQQ__Quote__c> quotesLicenseForecast;
    private List<SBQQ__Quote__c> quotesClearCommunicationFields;
    private List<SBQQ__Quote__c> quotesToAutoExecute;
    private List<SBQQ__Quote__c> quotesToUnlock;
    private List<SBQQ__Quote__c> quotesPushOppRollups;
    private List<SBQQ__Quote__c> salesQuotesToStampMigrationType;
    private Map<Id, SBQQ__Quote__c> primaryQuotesToCheck;
    private Map<Id, SBQQ__Quote__c> quotesGroupMGMT;
    private List<Id> cloneQuotesGroupMGMT;
    private List<Id> setFieldRipReplace;
    private List<Id> setGroupsRipReplace;
    private List<SBQQ__Quote__c> quotesToCheckLines;
    private Map<Id, SBQQ__Quote__c> orderedQuotes;
    private List<SBQQ__Quote__c> quarterlyQuotes;
    private List<SBQQ__Quote__c> ripAndReplaceQuotes;
    private List<SBQQ__Quote__c> ripAndReplaceQuotesAfter;
    private Map<id, SBQQ__Quote__c> quotesRollupsMap;
    private List<SBQQ__Quote__c> quotesToBePreviewed;
    private List<SBQQ__Quote__c> quotes2StampDates;
    private List<SBQQ__Quote__c> quoteMYCFlagToOpp;
    private List<SBQQ__Quote__c> quotes2PopulateCountryId;
    private List<SBQQ__Quote__c> quotes2StampOppJDECorp;
    private List<SBQQ__Quote__c> quotes2oppToRecalculate;
    private Map<Id, SBQQ__Quote__c> primaryQuotesOppUpdate;
    private List<SBQQ__Quote__c> amendmentQuotesToUpdateOpps;
    private List<SBQQ__Quote__c> datesChange;
    private List<SBQQ__Quote__c> quotes2setCPDM;
    private List<SBQQ__Quote__c> quotes2setARR;
    private List<SBQQ__Quote__c> cloudQuotes;
    private List<SBQQ__Quote__c> containsAIChanged;
    private Map<Id, SBQQ__Quote__c> cpaiQuotes;
    private List<SBQQ__Quote__c> quotesToSyncToCPAi;
    private List<SBQQ__Quote__c> quotesReadyForSignature;

    public Integer getRecursionDepth () {
        return 0;
    }

    public Integer getMaxRecursionDepthAllowed () {
        return 0;
    }

    public Boolean isEnabled () {
        return true;
    }

    public void bulkBefore () {
        quotesToAutoExecute = new List<SBQQ__Quote__c>();
        quotesStampRenewalEmailDays = new List<SBQQ__Quote__c>();
        quotesClearCommunicationFields = new List<SBQQ__Quote__c>();
        quotesDocumentLanguage = new List<SBQQ__Quote__c>();
        newQuotesStampFields = new List<SBQQ__Quote__c>();
        primaryQuotesToCheck = new Map<Id, SBQQ__Quote__c>();
        quotesToCheckLines = new List<SBQQ__Quote__c>();
        orderedQuotes = new Map<Id, SBQQ__Quote__c>();
        quarterlyQuotes = new List<SBQQ__Quote__c>();
        ripAndReplaceQuotes = new List<SBQQ__Quote__c>();
        quotesRollupsMap = new Map<Id, SBQQ__Quote__c>();
        quotesToBePreviewed = new List<SBQQ__Quote__c>();
        quotes2StampDates = new List<SBQQ__Quote__c>();
        quotes2PopulateCountryId = new List<SBQQ__Quote__c>();
        relatedOppIds = new Set<Id>();
        currentQuotesIds = new Set<Id>();
        quotes2setCPDM = new List<SBQQ__Quote__c>();
        quotes2setARR = new List<SBQQ__Quote__c>();
        quotesReadyForSignature = new List<SBQQ__Quote__c>();
    }

    public void bulkAfter () {
        quotesLicenseForecast = new List<SBQQ__Quote__c>();
        quotesPlatformFlag = new List<SBQQ__Quote__c>();
        quotesLegalNotes = new List<SBQQ__Quote__c>();
        quotesRevRecNotes = new List<SBQQ__Quote__c>();
        quotesApprovalStatusChanged = new List<SBQQ__Quote__c>();
        quotesLOEChanged = new List<SBQQ__Quote__c>();
        quotesToUnlock = new List<SBQQ__Quote__c>();
        quotesPushOppRollups = new List<SBQQ__Quote__c>();
        executedQuotes = new List<SBQQ__Quote__c>();
        quotesChangeOppType = new List<SBQQ__Quote__c>();
        salesQuotesToStampMigrationType = new List<SBQQ__Quote__c>();
        quotes2oppToRecalculate = new List<SBQQ__Quote__c>();
        primaryQuotesOppUpdate = new Map<Id, SBQQ__Quote__c>();
        quotesGroupMGMT = new Map<Id, SBQQ__Quote__c>();
        cloneQuotesGroupMGMT = new List<Id>();
        setFieldRipReplace = new List<Id>();
        setGroupsRipReplace = new List<Id>();
        amendmentQuotesToUpdateOpps = new List<SBQQ__Quote__c>();
        orderedQuotes = new Map<Id, SBQQ__Quote__c>();
        ripAndReplaceQuotesAfter = new List<SBQQ__Quote__c>();
        quoteMYCFlagToOpp = new List<SBQQ__Quote__c>();
        relatedOppIds = new Set<Id>();
        currentQuotesIds = new Set<Id>();
        quotes2StampOppJDECorp = new List<SBQQ__Quote__c>();
        datesChange = new List<SBQQ__Quote__c>();
        cloudQuotes = new List<SBQQ__Quote__c>();
        containsAIChanged = new List<SBQQ__Quote__c>();
        cpaiQuotes = new Map<Id, SBQQ__Quote__c>();
        quotesToSyncToCPAi = new List<SBQQ__Quote__c>();
    }

    public void beforeInsert (SObject so) {

        SBQQ__Quote__c quote = (SBQQ__Quote__c) so;
        relatedOppIds.add(quote.SBQQ__Opportunity2__c);
        currentQuotesIds.add(quote.Id);

        if (quote.SBQQ__Opportunity2__c != null) {
            newQuotesStampFields.add(quote);
        }

        if (quote.Renewal_Emails_Pending__c != null || quote.Renewal_Emails_Sent__c != null ||
            quote.Last_Renewal_Email_Sent__c != null || quote.Prevent_Communication_Emails__c
        ) {
            QuoteController.clearRenewalCommunicationFields(quote);
        }

        if (quote.SBCF_Document_Language__c != null) {
            quotesDocumentLanguage.add(quote);
        }

        if (quote.SBQQ__BillingCountry__c != null || quote.Ship_To_Contact_Country__c != null) {
            quotes2PopulateCountryId.add(quote);
        }

        if (quote.SBQQ__Primary__c) {
            primaryQuotesToCheck.put(quote.Id, quote);
        }
        if (quote.SBQQ__Opportunity2__r.RecordTypeId == TriggersHelper.renewalOppRecordTypeId) {
            quotesToCheckLines.add(quote);
        }
        if (quote.Platform__c != null) {
            quote.Paperwork_Term_Header_Value__c = quote.Platform__c == CPQConstants.PLATFORM_MCE ?
                CPQConstants.MCE_SERVICE_TERM :
                CPQConstants.LICENSE_TERM;
        }

        if (quote.SBQQ__Type__c != null && quote.SBQQ__LineItemsGrouped__c != null) {
            quotesToBePreviewed.add(quote);
        }

        QuoteController.checkCloningOrderedQuote(quote);

        if (quote.MinStDt__c != null && quote.MxEndDt__c != null) {
            quotes2StampDates.add(quote);
        }
        if(quote.Software_ARR__c != null && quote.SBQQ__PriceBook__c != null){
            quotes2setCPDM.add(quote);
        }
        if((quote.Total_Number_of_Days__c != null || quote.Total_Amended_Number_of_Days__c != null) && quote.FX_Software__c != null){
            quotes2setARR.add(quote);
        }
        if(quote.CPAI_Template_Field__c == null) {
            CPAI_Template_Fields__c templateRec = new CPAI_Template_Fields__c();
            insert templateRec;
            quote.CPAI_Template_Field__c = templateRec.Id;
        }
    }

    public void beforeUpdate (SObject oldObject, SObject newObject) {
        if (OrderProductsRelatedListController.firedFromOI() != null && OrderProductsRelatedListController.firedFromOI()) {
            return;
        }

        SBQQ__Quote__c newQuote = (SBQQ__Quote__c) newObject;
        SBQQ__Quote__c oldQuote = (SBQQ__Quote__c) oldObject;
        
        if(newQuote.SBQQ__Status__c != oldQuote.SBQQ__Status__c && newQuote.SBQQ__Status__c == 'Q09 EXE' && (QuoteController.firedFromQuoteExecution() == null || !QuoteController.firedFromQuoteExecution())){
            QuoteController.setFiredFromQuoteExecution();
        }

        relatedOppIds.add(newQuote.SBQQ__Opportunity2__c);
        currentQuotesIds.add(newQuote.Id);

        if (newQuote.SBQQ__Opportunity2__c != null && newQuote.SBQQ__Primary__c) {

            if (!newQuote.Prevent_Communication_Emails__c &&
                (oldQuote.SBQQ__Status__c != newQuote.SBQQ__Status__c) &&
                newQuote.SBCF_Agreement_Classification__c == 'Standard' &&
                String.isBlank(newQuote.Renewal_Emails_Pending__c) &&
                !RenewalCommunicationsHelper.excludedCountries.contains(newQuote.SBCF_Document_Language__c) &&
                newQuote.SBQQ__Status__c.contains('Q07')
            ) {
                quotesStampRenewalEmailDays.add(newQuote);
            }


            if ((oldQuote.SBCF_Legal_Notes__c != newQuote.SBCF_Legal_Notes__c) ||
                (oldQuote.SBCF_Rev_Rec_Notes__c != newQuote.SBCF_Rev_Rec_Notes__c)
            ) {
                QuoteOpportunityController.setCAFComments(newQuote);
            }

            if (oldQuote.SBQQ__Primary__c != newQuote.SBQQ__Primary__c) {
                primaryQuotesToCheck.put(newQuote.Id, newQuote);
            }
        }

        if ((oldQuote.SBCF_License_Terms__c != newQuote.SBCF_License_Terms__c) ||
            (oldQuote.SBCF_Technical_Support_Terms__c != newQuote.SBCF_Technical_Support_Terms__c) ||
            (oldQuote.SBCF_Pricing_Payment_and_Invoice_Terms__c != newQuote.SBCF_Pricing_Payment_and_Invoice_Terms__c) ||
            (oldQuote.SBCF_Other_Enterprise_Terms__c != newQuote.SBCF_Other_Enterprise_Terms__c) ||
            (oldQuote.Services_Terms__c != newQuote.Services_Terms__c)
        ) {
            QuoteController.setTextEnterpriseEntered(newQuote);
        }
        if ((oldQuote.SBQQ__BillingCountry__c != newQuote.SBQQ__BillingCountry__c) ||
            (oldQuote.Ship_To_Contact_Country__c != newQuote.Ship_To_Contact_Country__c) ||
            (oldQuote.ShipTo_Country_ID__c != newQuote.ShipTo_Country_ID__c) ||
            (oldQuote.BillTo_Country_ID__c != newQuote.BillTo_Country_ID__c)
        ) {
            quotes2PopulateCountryId.add(newQuote);
        }

        if (newQuote.Auto_Execute__c && newQuote.Is_BC_Quote__c &&
            (oldQuote.Auto_Execute__c != newQuote.Auto_Execute__c) &&
                FeatureManagement.checkPermission('CPQSystemExecution')
        ) {
            QuoteController.autoExecuteQuote(newQuote);
        }

        if (oldQuote.SBCF_Document_Language__c != newQuote.SBCF_Document_Language__c) {
            quotesDocumentLanguage.add(newQuote);
        }

        if (newQuote.SBQQ__Status__c == 'Q09 EXE' && newQuote.SBQQ__StartDate__c == null &&
            (oldQuote.SBQQ__Status__c != newQuote.SBQQ__Status__c)
        ) {
            QuoteController.stampStartDate(newQuote);
        }

        if (newQuote.SBCF_Approval_Status__c == 'Agreement Executed' &&
            newQuote.SBQQ__Status__c == 'Q09 EXE' &&
            (oldQuote.SBQQ__Status__c != newQuote.SBQQ__Status__c) &&
            (oldQuote.SBCF_Approval_Status__c != newQuote.SBCF_Approval_Status__c) &&
            !oldQuote.SBQQ__Ordered__c && !newQuote.SBQQ__Ordered__c
        ) {
            newQuote.SBQQ__ContractingMethod__c = 'Single Contract';
            //HDR 745172 - Nogueira
            if(!newQuote.Is_BC_Quote__c){
                newQuote.SBQQ__Ordered__c = true;
            }
        }

        if (newQuote.SBQQ__Type__c == 'Amendment') {
            QuoteController.validateAmendmentQuoteFields(oldQuote, newQuote);
        }

        if (newQuote.SBQQ__Type__c == 'Renewal') {
            quotesToCheckLines.add(newQuote);
        }

        QuoteController.checkCloningOrderedQuote(newQuote);

        if (newQuote.SBQQ__Type__c != 'Amendment' && newQuote.Billing_Frequency__c == 'Quarterly' &&
            (oldQuote.Billing_Frequency__c != newQuote.Billing_Frequency__c && newQuote.Billing_Frequency__c != null)
        ) {
            quarterlyQuotes.add(newQuote);
        }

        if (newQuote.Quote_Line_Groups__c > 1) {
            newQuote.MYC__c = true;
        } else {
            newQuote.MYC__c = false;
        }

        if (!oldQuote.SBQQ__LineItemsGrouped__c && newQuote.SBQQ__LineItemsGrouped__c) {
            newQuote.Has_Groups__c = true;
        } else if (!newQuote.Has_Groups__c && !newQuote.MYC__c && newQuote.SBQQ__LineItemsGrouped__c) {
            newQuote.Has_Groups__c = true;
        }

        //516155 - Mayur Chimurkar
        if (newQuote.Platform__c != null && oldQuote.Platform__c != newQuote.Platform__c) {
            newQuote.Paperwork_Term_Header_Value__c = newQuote.Platform__c == CPQConstants.PLATFORM_MCE ?
                CPQConstants.MCE_SERVICE_TERM :
                CPQConstants.LICENSE_TERM;
        }
        //516155 - Mayur Chimurkar
        if (QuoteController.firedFromQuoteExecution() == null || !QuoteController.firedFromQuoteExecution()){
            quotesRollupsMap.put(newQuote.Id, newQuote);
        }

        //Mayur Chimurkar
        if ((oldQuote.SBQQ__LineItemsGrouped__c != newQuote.SBQQ__LineItemsGrouped__c) ||
            (newQuote.SBQQ__Type__c != null && oldQuote.SBQQ__Type__c != newQuote.SBQQ__Type__c) ||
            (newQuote.SBCF_Document_Language__c != oldQuote.SBCF_Document_Language__c)
        ) {
            quotesToBePreviewed.add(newQuote);
        }

        if (newQuote.SBQQ__Type__c == 'Amendment' || (newQuote.MinStDt__c != null && newQuote.MxEndDt__c != null
            && (newQuote.MinStDt__c != newQuote.SBQQ__StartDate__c || newQuote.MxEndDt__c != newQuote.SBQQ__EndDate__c))
        ) {
            quotes2StampDates.add(newQuote);
        }
/*
        Transferred workflows:
        Quote Rec Rev last modified
        Quote Legal Notes last modified
*/
        if (oldQuote.SBCF_Rev_Rec_Notes__c != newQuote.SBCF_Rev_Rec_Notes__c) {
            newQuote.RevRecNotesLastModified__c = Datetime.now();
        }
        if (oldQuote.SBCF_Legal_Notes__c != newQuote.SBCF_Legal_Notes__c) {
            newQuote.LegalNotesLastModified__c = Datetime.now();
        }
        if(newQuote.Software_ARR__c != null && newQuote.SBQQ__PriceBook__c != null && newQuote.SBQQ__Status__c == 'Q01 SAE'){
            quotes2setCPDM.add(newQuote);
        }
        if(newQuote.SBQQ__Status__c == 'Q01 SAE' && newQuote.FX_Software__c != null){
            quotes2setARR.add(newQuote);
        }
        if((newQuote.SBQQ__Status__c.contains('Q02') || newQuote.SBQQ__Status__c.contains('Q01')) &&newQuote.CPAI_Template_Field__c == null) {
            CPAI_Template_Fields__c templateRec = new CPAI_Template_Fields__c();
            insert templateRec;
            newQuote.CPAI_Template_Field__c = templateRec.Id;
        }
        if(newQuote.SBCF_Approval_Status__c != oldQuote.SBCF_Approval_Status__c &&(newQuote.SBCF_Approval_Status__c == 'Ready for Signature' || newQuote.SBCF_Approval_Status__c == 'Signature(s) Completed')){
            quotesReadyForSignature.add(newQuote);
        }
    }

    public void beforeDelete (SObject so) {
        SBQQ__Quote__c quote = (SBQQ__Quote__c) so;

        relatedOppIds.add(quote.SBQQ__Opportunity2__c);
        currentQuotesIds.add(quote.Id);

        if (quote.SBCF_Approval_Status__c == 'Agreement Executed') {
            quote.addError('Quotes in status "Agreement Executed" cannot be deleted.');
        }
    }

    public void afterInsert (SObject so) {
        SBQQ__Quote__c newQuote = (SBQQ__Quote__c) so;

        relatedOppIds.add(newQuote.SBQQ__Opportunity2__c);
        currentQuotesIds.add(newQuote.Id);

        if (newQuote.SBQQ__Primary__c && newQuote.SBQQ__Opportunity2__c != null) {
            quotesPlatformFlag.add(newQuote);
            quotesLicenseForecast.add(newQuote);
            primaryQuotesOppUpdate.put(newQuote.Id, newQuote);

            if (newQuote.Ship_To_Book__c != null) {
                quotes2StampOppJDECorp.add(newQuote);
            }

            if (newQuote.SBQQ__Type__c == 'Renewal') {
                quotesChangeOppType.add(newQuote);
            }
        }

        if (newQuote.SBQQ__Opportunity2__c != null) {
            quotes2oppToRecalculate.add(newQuote);

            if (newQuote.SBQQ__Type__c == 'Amendment') {
                amendmentQuotesToUpdateOpps.add(newQuote);
            }
        }
    }

    @SuppressWarnings('ApexUnresolvableReference')
    public void afterUpdate (SObject oldSo, SObject so) {
        if (OrderProductsRelatedListController.firedFromOI() != null && OrderProductsRelatedListController.firedFromOI()) {
            return;
        }
        SBQQ__Quote__c oldQuote = (SBQQ__Quote__c) oldSo;
        SBQQ__Quote__c newQuote = (SBQQ__Quote__c) so;

        relatedOppIds.add(newQuote.SBQQ__Opportunity2__c);
        currentQuotesIds.add(newQuote.Id);
        if (newQuote.SBQQ__Primary__c) {
            containsAIChanged.add(newQuote);
        }

        if (newQuote.SBQQ__Primary__c && newQuote.SBQQ__Opportunity2__c != null) {

            if ((oldQuote.Platform__c != newQuote.Platform__c) ||
                (oldQuote.SBQQ__Primary__c != newQuote.SBQQ__Primary__c)
            ) {
                quotesPlatformFlag.add(newQuote);
            }

            if ((oldQuote.SBQQ__Primary__c != newQuote.SBQQ__Primary__c && newQuote.SBQQ__Primary__c != null) ||
                (oldQuote.Platform__c != newQuote.Platform__c && newQuote.Platform__c != null) ||
                (oldQuote.PLA__c != newQuote.PLA__c && newQuote.PLA__c != null) ||
                (oldQuote.AASV__c != newQuote.AASV__c && newQuote.AASV__c != null) ||
                (oldQuote.Tx_Incentive__c != newQuote.Tx_Incentive__c && newQuote.Tx_Incentive__c != null) ||
                (oldQuote.Net_Analytics__c != newQuote.Net_Analytics__c && newQuote.Net_Analytics__c != null) ||
                (oldQuote.Net_Security__c != newQuote.Net_Security__c && newQuote.Net_Security__c != null) ||
                (oldQuote.Net_Mobility__c != newQuote.Net_Mobility__c && newQuote.Net_Mobility__c != null) ||
                (oldQuote.Number_of_Days__c != newQuote.Number_of_Days__c && newQuote.Number_of_Days__c != null) ||
                (oldQuote.CTR__c != newQuote.CTR__c && newQuote.CTR__c != null) ||
                (oldQuote.LF2__c != newQuote.LF2__c && newQuote.LF2__c != null)
            ) {
                quotesLicenseForecast.add(newQuote);
            }
            if ((oldQuote.Ship_To_Book__c != newQuote.Ship_To_Book__c && newQuote.Ship_To_Book__c != null) ||
                (oldQuote.SBQQ__Primary__c != newQuote.SBQQ__Primary__c) ||
                (newQuote.SBQQ__Type__c == 'Renewal' && newQuote.SBQQ__Opportunity2__c != null && newQuote.SBQQ__Status__c == 'Q01 SAE')
            ) {
                quotes2StampOppJDECorp.add(newQuote);
            }
            if ((oldQuote.SBQQ__Primary__c != newQuote.SBQQ__Primary__c) ||
                (oldQuote.SBQQ__Opportunity2__c != newQuote.SBQQ__Opportunity2__c && newQuote.SBQQ__Opportunity2__c != null) ||
                (oldQuote.Quote_Offer_Type__c != newQuote.Quote_Offer_Type__c && newQuote.Quote_Offer_Type__c != null)
            ) {
                quotesPushOppRollups.add(newQuote);
            }
            for (Integer i = 0; i < TriggersHelper.oppRollups.size() && !quotesPushOppRollups.contains(newQuote); i++) {
                if (oldQuote.get(TriggersHelper.oppRollups.get(i).Target_Value__c) != newQuote.get(TriggersHelper.oppRollups.get(i).Target_Value__c)) {
                    quotesPushOppRollups.add(newQuote);
                }
            }

            if (newQuote.SBQQ__Type__c == 'Renewal' && (
                (oldQuote.SBQQ__Primary__c != newQuote.SBQQ__Primary__c) ||
                    (oldQuote.Platform__c != newQuote.Platform__c) ||
                    (oldQuote.Sum_of_Term__c != newQuote.Sum_of_Term__c) ||
                    (oldQuote.SBCF_Sum_Of_Cloud__c != newQuote.SBCF_Sum_Of_Cloud__c) ||
                    (oldQuote.SBQQ__Opportunity2__c != newQuote.SBQQ__Opportunity2__c) ||
                    (oldQuote.Quote_Offer_Type__c != newQuote.Quote_Offer_Type__c) ||
                    (oldQuote.License_Type__c != newQuote.License_Type__c)
            )) {
                quotesChangeOppType.add(newQuote);
            }

            if (newQuote.SBQQ__Type__c != 'Renewal' && (
                (oldQuote.License_Type__c != newQuote.License_Type__c) ||
                    (oldQuote.Platform__c != newQuote.Platform__c)
            )) {
                salesQuotesToStampMigrationType.add(newQuote);
            }
            if ((oldQuote.MinStDt__c != newQuote.MinStDt__c) ||
                (oldQuote.MxEndDt__c != newQuote.MxEndDt__c)) {
                datesChange.add(newQuote);
            }
        }

        if (newQuote.SBQQ__Opportunity2__c != null &&
            (oldQuote.SBQQ__Status__c != newQuote.SBQQ__Status__c) && (
            (QuoteController.checkQuoteBucket('QuoteDraftStatuses', newQuote.SBQQ__Status__c) && !QuoteController.checkQuoteBucket('QuoteDraftStatuses', oldQuote.SBQQ__Status__c)) || //moved to a draft status
                (QuoteController.checkQuoteBucket('QuoteApprovedStatuses', newQuote.SBQQ__Status__c) && !QuoteController.checkQuoteBucket('QuoteApprovedStatuses', oldQuote.SBQQ__Status__c)) || //moved to an approved status
                (QuoteController.checkQuoteBucket('QuoteRejectedStatuses', newQuote.SBQQ__Status__c) && !QuoteController.checkQuoteBucket('QuoteRejectedStatuses', oldQuote.SBQQ__Status__c)) //moved to a rejected status
        )) {
            quotes2oppToRecalculate.add(newQuote);
        }

        if ((oldQuote.Al__c != newQuote.Al__c) ||
            (oldQuote.SBCF_Approval_Status__c != newQuote.SBCF_Approval_Status__c) &&
            Approval.isLocked(newQuote)
        ) {
            quotesToUnlock.add(newQuote);
        }

        if (newQuote.SBQQ__Opportunity2__c != null) {

            if (oldQuote.LC_LOE__c != newQuote.LC_LOE__c) {
                quotesLOEChanged.add(newQuote);
            }

            if ((oldQuote.Approval_Step_Status__c != newQuote.Approval_Step_Status__c) ||
                (oldQuote.SBCF_Approval_Status__c != newQuote.SBCF_Approval_Status__c) ||
                (oldQuote.AP__c != newQuote.AP__c)
            ) {
                quotesApprovalStatusChanged.add(newQuote);
            }

            if (newQuote.SBCF_Approval_Status__c == 'Agreement Executed' &&
                (oldQuote.SBCF_Approval_Status__c != newQuote.SBCF_Approval_Status__c)
            ) {
                executedQuotes.add(newQuote);
            }

            if ((oldQuote.Legal_Clarification_Notes__c != newQuote.Legal_Clarification_Notes__c)) {
                QuoteController.postToChatter(newQuote, newQuote.Legal_Clarification_Notes__c, 'Legal');
            }

            if ((oldQuote.Rev_Rec_Clarification_Notes__c != newQuote.Rev_Rec_Clarification_Notes__c)) {
                QuoteController.postToChatter(newQuote, newQuote.Legal_Clarification_Notes__c, 'Rev Rec');
            }

            if (oldQuote.Multi_Year_Contract_Quote__c != newQuote.Multi_Year_Contract_Quote__c) {
                quoteMYCFlagToOpp.add(newQuote);
            }
            if(String.isNotBlank(newQuote.Platform__c) && (newQuote.Platform__c.contains('MCE') || newQuote.Platform__c.contains('MCG'))){
                cloudQuotes.add(newQuote);
            }
        }

        //First Calculation
        if (oldQuote.SBQQ__LastCalculatedOn__c == null &&
            (oldQuote.SBQQ__LastCalculatedOn__c != newQuote.SBQQ__LastCalculatedOn__c)
        ) {
            if (newQuote.SBQQ__Type__c == 'Amendment' && !newQuote.Has_Groups__c) {
                quotesGroupMGMT.put(newQuote.Id, newQuote);
            } if (newQuote.Model_Quote__c <> null) {
                cloneQuotesGroupMGMT.add(newQuote.Id);
            }
        }

        /* -> review Rip and Replace Module / Sebastian Suarez Nogueira Matias
        Boolean isMergeQuote = newQuote.cpqlabs__Replacement_Quote__c <> null;
        isMergeQuote = isMergeQuote && newQuote.cpqlabs__Replacement_Quote__c <> oldQuote.cpqlabs__Replacement_Quote__c;

        if (isMergeQuote == true) {
            setFieldRipReplace.add(newQuote.cpqlabs__Replacement_Quote__c);
        }
        */

        if (oldQuote.Quote_Calculation_Trigger__c != newQuote.Quote_Calculation_Trigger__c) {
            setGroupsRipReplace.add(newQuote.Id);
        }

        if(newQuote.CPAI_Template_Field__c != null && ((oldQuote.SBQQ__Status__c != newQuote.SBQQ__Status__c && newQuote.SBQQ__Status__c.contains('Q02')) || newQuote.SBQQ__Status__c.contains('Q01'))){
            cpaiQuotes.put(newQuote.Id, newQuote);
        }
        
        if(newQuote.SBQQ__ExpirationDate__c != oldQuote.SBQQ__ExpirationDate__c && newQuote.Is_SF_Approval_Completed__c == true) {
            quotesToSyncToCPAi.add(newQuote);
        }

        //Case 517434 - Nogueira Matias Refactor
        QuoteController.getOrderedQuotes(orderedQuotes, oldQuote, newQuote);

    }

    public void afterDelete (SObject so) {
        SBQQ__Quote__c oldQuote = (SBQQ__Quote__c) so;

        relatedOppIds.add(oldQuote.SBQQ__Opportunity2__c);
        currentQuotesIds.add(oldQuote.Id);

        if (oldQuote.SBQQ__Opportunity2__c != null) {
            quotes2oppToRecalculate.add(oldQuote);
        }
    }

    public void afterUndelete (SObject so) {
        SBQQ__Quote__c oldQuote = (SBQQ__Quote__c) so;

        relatedOppIds.add(oldQuote.SBQQ__Opportunity2__c);
        currentQuotesIds.add(oldQuote.Id);

        if (oldQuote.SBQQ__Opportunity2__c != null) {
            quotes2oppToRecalculate.add(oldQuote);
        }
    }

    public void andFinally () {
        if (OrderProductsRelatedListController.firedFromOI() != null && OrderProductsRelatedListController.firedFromOI()) {
            return;
        }

        Map<Id, Opportunity> relatedOpps = queryRelatedOpps(this.relatedOppIds);
        Map<Id, List<SBQQ__QuoteLine__c>> qlByQuoteId = getRelatedQL(this.currentQuotesIds);
        Map<Id, pse__Grp__c> groupMap = new Map<Id, pse__Grp__c>();
        getRelatedsObjects(groupMap);

        //---------------------------------------------------
        //Before Triggers
        //---------------------------------------------------

        //TODO: Test to change
        //Trigger action: Before Insert
        if (newQuotesStampFields != null && !newQuotesStampFields.isEmpty()) {
            QuoteController.quoteProcessCreate(newQuotesStampFields, relatedOpps);
        }

        //Trigger action: Before Insert/Update
        if (quotes2PopulateCountryId != null && !quotes2PopulateCountryId.isEmpty()) {
            QuoteController.populateCountryId(quotes2PopulateCountryId);
        }

        //TODO: Test to change
        //Trigger action: Before Update
        if (quotesStampRenewalEmailDays != null && !quotesStampRenewalEmailDays.isEmpty()) {
            QuoteController.stampRenewalEmailDays(quotesStampRenewalEmailDays, relatedOpps);
        }

        //Trigger action: Before Insert/Update. Account Query
        if (quotesDocumentLanguage != null && !quotesDocumentLanguage.isEmpty()) {
            QuoteController.setQuoteDescriptionHeader(quotesDocumentLanguage);
        }

        //Trigger action: Before Insert/Update
        if (primaryQuotesToCheck != null && !primaryQuotesToCheck.isEmpty()) {
            QuoteController.avoidMultiplePrimaryQuotes(primaryQuotesToCheck, relatedOpps);
        }

/*
        if (setFieldRipReplace != null && !setFieldRipReplace.isEmpty()) {
            QuoteController.setFieldRipReplace(setFieldRipReplace);
        }

        if (setGroupsRipReplace != null && !setGroupsRipReplace.isEmpty()) {
            QuoteController.setGroupsRipReplace(setGroupsRipReplace);
        }
*/

        //Trigger action: Before Insert/Update
        if (quotesToCheckLines != null && !quotesToCheckLines.isEmpty()) {
            QuoteController.setNoChangesToConfig(quotesToCheckLines, qlByQuoteId);
        }

        //Trigger action: Before Update
        if (quarterlyQuotes != null && !quarterlyQuotes.isEmpty()) {
            QuoteController.validateQuarterlyQuotes(quarterlyQuotes);
        }

        //Trigger action: Before Update
        if (quotesRollupsMap != null && !quotesRollupsMap.isEmpty()) {
            QuoteRollupService.doRollup(quotesRollupsMap, qlByQuoteId);
        }

        //Trigger action: Before Insert/Update
        if (quotesToBePreviewed != null && !quotesToBePreviewed.isEmpty()) {
            QuoteController.setTemplateIdForPreview(quotesToBePreviewed);
        }

        //Trigger action: Before Insert/Update
        if (quotes2StampDates != null && !quotes2StampDates.isEmpty()) {
            QuoteController.stampDatesOnQuote(quotes2StampDates, qlByQuoteId);
        }

        if(quotes2setARR!= null && !quotes2setARR.isEmpty()){
            QuoteController.calculateSoftwareArr(quotes2setARR);
        }

        if(quotes2setCPDM!= null && !quotes2setCPDM.isEmpty()){
            QuoteController.setCPDM(quotes2setCPDM);
        }

        if(quotesReadyForSignature != null && !quotesReadyForSignature.isEmpty()){
            QuoteController.deselectWatermarkShown(quotesReadyForSignature);
        }
        //---------------------------------------------------
        //After Triggers
        //---------------------------------------------------

        //Trigger action: After Update
        if (salesQuotesToStampMigrationType != null && !salesQuotesToStampMigrationType.isEmpty()) {
            QuoteOpportunityController.updateSalesOppMigrationType(salesQuotesToStampMigrationType);
        }

        //TODO: Test to change
        //Trigger action: After Insert/Update
        if (quotesChangeOppType != null && !quotesChangeOppType.isEmpty()) {
            QuoteOpportunityController.updateOppType(quotesChangeOppType, relatedOpps);
        }

        //Trigger action: After Update
        if (quotesPushOppRollups != null && !quotesPushOppRollups.isEmpty()) {
            QuoteOpportunityController.pushOppRollups(quotesPushOppRollups, relatedOpps);
        }

        //TODO: Test to change
        //Trigger action: After Update
        if (executedQuotes != null && !executedQuotes.isEmpty()) {
            QuoteOpportunityController.updateOppClosedWon(executedQuotes, relatedOpps, qlByQuoteId);
        }

        // 1 Quote Query
        // AFTER INSERT/UPDATE
        //Trigger action: After Insert/Update
        if (quotesLicenseForecast != null && !quotesLicenseForecast.isEmpty()) {
            QuoteOpportunityController.updateOppForecasts(quotesLicenseForecast, relatedOpps);
        }

        //Trigger action: After Insert/Update
        if (quotesPlatformFlag != null && !quotesPlatformFlag.isEmpty()) {
            QuoteOpportunityController.updateOppPlatformFlag(quotesPlatformFlag);
        }

        //Trigger action: After Update
        if (quotesApprovalStatusChanged != null && !quotesApprovalStatusChanged.isEmpty()) {
            QuoteOpportunityController.updateOppApprovalStatusFields(quotesApprovalStatusChanged);
        }

        //Trigger action: After Insert/Update
        if (quotes2StampOppJDECorp != null && !quotes2StampOppJDECorp.isEmpty()) {
            QuoteOpportunityController.updateOppJDECorporation(quotes2StampOppJDECorp, relatedOpps, groupMap);
        }

        //Trigger action: After Insert/Update/Delete/Undelete
        if (quotes2oppToRecalculate != null && !quotes2oppToRecalculate.isEmpty()) {
            QuoteOpportunityController.updateOppRollups(quotes2oppToRecalculate, relatedOpps);
        }

        //Trigger action: After Update
        if (quotesToUnlock != null && !quotesToUnlock.isEmpty()) {
            QuoteController.unlockQuotes(quotesToUnlock);
        }

        //Trigger action: After Update
        if (quotesLOEChanged != null && !quotesLOEChanged.isEmpty()) {
            QuoteOpportunityController.updateOppTotalLOE(quotesLOEChanged, relatedOpps);
        }

        //Trigger action: After Insert
        if (primaryQuotesOppUpdate != null && !primaryQuotesOppUpdate.isEmpty()) {
            QuoteOpportunityController.populatePrimaryOnOpp(primaryQuotesOppUpdate);
        }

        //1 QuoteLine query
        //1 QuoteLineGroup query
        // AFTER UPDATE
        if (quotesGroupMGMT != null && !quotesGroupMGMT.isEmpty()) {
            QuoteController.qlgMGMT(quotesGroupMGMT);
        }

        //2 QuoteLine query
        // AFTER UPDATE
        if (cloneQuotesGroupMGMT != null && !cloneQuotesGroupMGMT.isEmpty()) {
            QuoteGroupsClone.setGroup(cloneQuotesGroupMGMT);
        }

        //Trigger action: After Insert
        if (amendmentQuotesToUpdateOpps != null && !amendmentQuotesToUpdateOpps.isEmpty()) {
            QuoteOpportunityController.updateAmendmentOpps(amendmentQuotesToUpdateOpps, relatedOpps, groupMap);
        }

        //Trigger action: After Update
        if (orderedQuotes != null && !orderedQuotes.isEmpty()) {
            QuoteController.updateOrdersFromQuotes(orderedQuotes);
        }

        //Trigger action: After Update
        if (quoteMYCFlagToOpp != null && !quoteMYCFlagToOpp.isEmpty()) {
            QuoteOpportunityController.updateOppMYCFlag(quoteMYCFlagToOpp);
        }

        if (datesChange != null && !datesChange.isEmpty()) {
            QuoteController.updateDatesOnQuote(datesChange);
        }

        if(cloudQuotes != null && !cloudQuotes.isEmpty()){
            QuoteOpportunityController.calculateCloudForecast(cloudQuotes, relatedOpps);
        }

        if(cpaiQuotes != null && !cpaiQuotes.isEmpty()){
            CpaiTemplateController.populateTemplateInfo(cpaiQuotes, getRelatedQL(cpaiQuotes.keySet()));
        }
        
        if(quotesToSyncToCPAi != null && !quotesToSyncToCPAi.isEmpty() && RecursionHelper.firstRunOfUpdate){
            QuoteController.syncExpirationDateToCpai(quotesToSyncToCPAi);
        }

        QuoteController.updateQuotes();

        QuoteOpportunityController.updateRelatedOpportunities();

        if (containsAIChanged != null && !containsAIChanged.isEmpty()) {
            QuoteController.updateOpportunitySplits(containsAIChanged, relatedOpps);
        }
    }

    @TestVisible
    private Map<Id, Opportunity> queryRelatedOpps (Set<Id> relatedOppIds) {
        String oppFields = '';

        //Trigger action: Before Insert. Fields for: QuoteController.quoteProcessCreate
        if (newQuotesStampFields != null && !newQuotesStampFields.isEmpty() || Test.isRunningTest()) {
            oppFields += 'Id,RecordTypeId,Business_Executive__c,End_User_Customer__c,SBQQ__RenewedContract__r.SBQQ__Quote__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Opportunity__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Conditional_Language__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Description_Header__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.End_Date_Header__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Est_Total_Header__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Legal_Entity_Name__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Legal_Entity_Output_Header__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Legal_Representative__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.License_Type_Header__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.List_Hourly_Rate_Header__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.List_Price_Header__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Net_Hourly_Rate_Header__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Net_Price_Header__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.No_Dispute_Timeline__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Number_of_Days__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Price_Header__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Quantity_Header__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Rev_Rec_Representative__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Bill_to_Contact__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Broaden_License_Grant__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Cold_Failover_Disaster_Recovery__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Document_Language__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_MSA2__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_No_Name_Logo_Rights__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_No_Press_Rights__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Non_Standard_Payment_Terms__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Pre_approval_of_Travel_Expenses__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Reimbursement_Not_Required__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.SBCF_Ship_to_Contact__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.SBQQ__Partner__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.SBQQ__PrimaryContact__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Sales_RVP__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Services_RVP__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Start_Date_Header__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Subtotal_Header__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.Term_Header__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Total_Header__c,SBQQ__RenewedContract__r.SBQQ__RenewalQuoted__c,CPIC__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Opportunity__r.Term_Opportunity__c,Legal_Representative__c,Rev_Rec_Representative__c,' +
                'RVP_Approver__c,Services_RVP_Approver__c,SBQQ__RenewedContract__r.PrevARR__c,SBQQ__RenewedContract__r.PrevTCV__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.MY_MX__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.MXYears__c,SBQQ__RenewedContract__r.SBQQ__Quote__r.OrigMYQ__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Quote__r.Name,' +
                'SBQQ__RenewedContract__r.SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.Not_PIF__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.MXIT__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.CTR__c,' +
                'SBQQ__RenewedContract__r.Billing_Contact__c,SBQQ__RenewedContract__r.Bill_To_Book__c,' +
                'SBQQ__RenewedContract__r.Shipping_Contact__c,SBQQ__RenewedContract__r.Ship_To_Book__c,' +
                'SBQQ__AmendedContract__c,' +
                'SBQQ__AmendedContract__r.Bill_To_Book__c,' + //MYC BUG 320 Nogueira
                'SBQQ__AmendedContract__r.Billing_Contact__c,' + //MYC BUG 320 Nogueira
                'SBQQ__AmendedContract__r.Ship_To_Book__c,' + //MYC BUG 320 Nogueira
                'SBQQ__AmendedContract__r.Shipping_Contact__c,' + //MYC BUG 320 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Order__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Quote__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Quote__r.SBQQ__BillingFrequency__c,SBQQ__AmendedContract__r.SBQQ__Quote__r.Billing_Frequency__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Quote__r.SBCF_Bill_to_Contact__c,SBQQ__AmendedContract__r.SBQQ__Quote__r.Ship_To_Book__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Quote__r.Bill_To_Book__c,SBQQ__AmendedContract__r.SBQQ__Quote__r.SBCF_Ship_to_Contact__c,SBQQ__PrimaryQuote__r.SBCF_Document_Language__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Quote__r.SBCF_Document_Language__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Quote__r.SBCF_Document_Quote_Name__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.PartnerAccountId,' +//MYC BUG 329 Nogueira
                'CloseDate,';
        }

        //Trigger action: Before Update. Fields for: QuoteController.stampRenewalEmailDays
        if (quotesStampRenewalEmailDays != null && !quotesStampRenewalEmailDays.isEmpty() || Test.isRunningTest()) {
            oppFields += 'Up_for_Renewal_Date__c,Type,RecordType.Name,SBCF_Renewal_Type__c,';
        }

        //Trigger action: After Insert/Update. Fields for: QuoteOpportunityController.updateOppType
        if (quotesChangeOppType != null && !quotesChangeOppType.isEmpty() || Test.isRunningTest()) {
            oppFields += 'Type,SBQQ__RenewedContract__r.SBQQ__Opportunity__r.Platform__c,' +
                'SBQQ__RenewedContract__r.SBQQ__Opportunity__r.LicenseType__c,';
        }

        //Trigger action: After Insert/Update. Fields for: QuoteOpportunityController.pushOppRollups
        if (quotesPushOppRollups != null && !quotesPushOppRollups.isEmpty() || Test.isRunningTest()) {
            oppFields += 'SBQQ__RenewedContract__c,Enterprise_Support__c,StageName,' +
                'SBCF_Renewal_Type__c,Name,RecordTypeId,Type,Platform__c,' +
                'SBQQ__RenewedContract__r.SBQQ__RenewalQuoted__c,MYC__c,' +
                'DSI__c,DSI__r.Platform__c,DSI__r.Num_Subscriptions__c,DSI__r.Num_Assets__c,' +
                'Account.Platform__c,';
        }

        //Trigger action: After Update. Fields for: QuoteOpportunityController.updateOppClosedWon
        if (executedQuotes != null && !executedQuotes.isEmpty() || Test.isRunningTest()) {
            oppFields += 'SBQQ__RenewedContract__c,';
        }

        //Trigger action: After Insert/Update. Fields for: QuoteOpportunityController.updateOppForecasts
        if (quotesLicenseForecast != null && !quotesLicenseForecast.isEmpty() || Test.isRunningTest()) {
            oppFields += 'Prevent_Forecast_Override__c,Cloud_Migration_Forecast_Lock__c,';
        }

        //Trigger action: After Insert/Update. Fields for: QuoteOpportunityController.updateOppJDECorporation
        if (quotes2StampOppJDECorp != null && !quotes2StampOppJDECorp.isEmpty() || Test.isRunningTest()) {
            oppFields += 'SBQQ__PrimaryQuote__r.Ship_To_Book__c,SBQQ__PrimaryQuote__r.Ship_To_Book__r.Country__c,';
        }

        //Trigger action: After Insert. Fields for: QuoteOpportunityController.updateAmendmentOpps
        if (amendmentQuotesToUpdateOpps != null && !amendmentQuotesToUpdateOpps.isEmpty() || Test.isRunningTest()) {
            oppFields += 'SBQQ__AmendedContract__c,SBQQ__AmendedContract__r.SBQQ__Quote__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Order__c,SBQQ__AmendedContract__r.DSI_SFID__c,' +
                'SBQQ__AmendedContract__r.SBQQ__Quote__r.Name,SBQQ__AmendedContract__r.SBQQ__Order__r.OrderNumber,' +
                'SBQQ__AmendedContract__r.SBQQ__Order__r.JDE_Corporation__c,' + //MYC BUG 324 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Order__r.Consulting_JDE_BU__c,' + //MYC BUG 324 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Order__r.Education_JDE_BU__c,' + //MYC BUG 324 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Order__r.License_JDE_BU__c,' + //MYC BUG 324 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Order__r.Maintenance_JDE_BU__c,' + //MYC BUG 324 Nogueira;
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.SBCF_Legal_Entity__c,' + //MYC BUG 324 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Consulting_JDE_BU_ID__c,' + //MYC BUG 324 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Education_JDE_BU_ID__c,' + //MYC BUG 324 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.License_JDE_BU_ID__c,' + //MYC BUG 324 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Maintenance_JDE_BU_ID__c,' + //MYC BUG 324 Nogueira;
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Partner_Contact__c,' + //MYC BUG 329 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Partner_AE__c,' + //MYC BUG 329 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.GA_Representative__c,' + //MYC BUG 329 Nogueira
                'SBQQ__AmendedContract__r.SBQQ__Opportunity__r.SBCF_Type_of_Sale__c,'; //MYC BUG 329 Nogueira
        }
        if(cloudQuotes != null && !cloudQuotes.isEmpty()){
            oppFields += 'Cloud_Incremental_Forecast__c, License_Forecast__c, CloseDate, Cloud_Migration_Forecast__c, On_Prem_Forecast__c,';
        }

        Set<String> oppFieldsSet = new Set<String>(oppFields.split(','));
        oppFieldsSet.remove('Id');
        oppFieldsSet.remove(null);
        oppFields = String.join(new List<String>(oppFieldsSet), ',');

        //Trigger action: Before/After Insert/Update/Delete/Undelete
        //Fields for: QuoteController.avoidMultiplePrimaryQuotes
        //Fields for: QuoteOpportunityController.updateOppRollups
        //Fields for: QuoteOpportunityController.updateOppTotalLOE
        if ((primaryQuotesToCheck != null && !primaryQuotesToCheck.isEmpty()) ||
            (quotes2oppToRecalculate != null && !quotes2oppToRecalculate.isEmpty()) ||
            (quotesLOEChanged != null && !quotesLOEChanged.isEmpty()) ||
            Test.isRunningTest()
        ) {
            oppFields = String.isBlank(oppFields) ? ' Id' : oppFields;
            oppFields += ',(SELECT Id,SBQQ__Opportunity2__c,SBQQ__Primary__c,SBQQ__Status__c,LC_LOE__c' +
                ' FROM SBQQ__Quotes2__r)';
        }

        //Trigger action: After Update
        //Fields for: QuoteController.updateOpportunitySplits
        if (containsAIChanged != null && !containsAIChanged.isEmpty()){
            oppFields = String.isBlank(oppFields) ? ' Id' : oppFields;
            oppFields += ',(SELECT Id, SplitOwnerId, Contains_AI__c, SplitAmount, SplitTypeId'+ 
                ' FROM OpportunitySplits WHERE (SplitTypeId = \'1494W000000QCAZQA4\' OR SplitTypeId = \'1494W000000QCAaQAO\') ORDER BY SplitAmount ASC),'+
                '(SELECT Id, UserId FROM OpportunityTeamMembers WHERE Primary__c = true and TeamMemberRole = \'Opportunity Owner\')';
        }
        

        if (String.isBlank(oppFields)) {
            return new Map<Id, Opportunity>();
        }
        return new Map<Id, Opportunity>((List<Opportunity>) Database.query('SELECT ' + oppFields + ' ' +
            'FROM Opportunity ' +
            'WHERE Id IN :relatedOppIds'
        ));

    }

    @TestVisible
    private Map<Id, List<SBQQ__QuoteLine__c>> getRelatedQL (Set<Id> currentQuotesIds) {
        String qlFields = '';

        //Trigger action: Before Insert/Update. Fields for: QuoteController.setNoChangesToConfig
        if (quotesToCheckLines != null && !quotesToCheckLines.isEmpty() || Test.isRunningTest()) {
            qlFields += 'SBQQ__Quote__c,SBQQ__Quantity__c,Original_Quote_Line__c,' +
                'Original_Quote_Line__r.SBQQ__Quote__r.Quote_Lines__c,Original_Quote_Line__r.SBQQ__Quantity__c,';
        }

        //Trigger action: Before Insert/Update. Fields for: QuoteController.stampDatesOnQuote
        if (quotes2StampDates != null && !quotes2StampDates.isEmpty() || Test.isRunningTest()) {
            qlFields += 'SBQQ__Quote__c,SBQQ__StartDate__c,SBQQ__EndDate__c,SBQQ__UpgradedSubscription__c,' +
                'SBQQ__Quantity__c, SBCF_CPI_Uplift__c,' +
                'SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__r.SBQQ__Quantity__c,' +
                'SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__r.SBCF_CPI_Uplift__c,';
        }

        //Trigger action: Before Update. Fields for: QuoteRollupService.doRollup
        if (quotesRollupsMap != null && !quotesRollupsMap.isEmpty() || Test.isRunningTest()) {
            qlFields += 'SBQQ__Quote__c,Id,PGT__c,interval__c,ProductFamilyLabel__c,Product_Category__c,' +
                'SBQQ__Quote__r.SBQQ__Opportunity2__r.Agreement_Description__c,SBQQ__EffectiveQuantity__c,';
        }

        //Trigger action: After Update. Fields for: QuoteOpportunityController.updateOppClosedWon
        if (executedQuotes != null && !executedQuotes.isEmpty() || Test.isRunningTest()) {
            qlFields += 'SBQQ__RenewedAsset__c,SBQQ__Quantity__c,SBQQ__Quote__r.Name,SBQQ__ListPrice__c,' +
                'Amendment_Type__c,SBQQ__Quote__c,Is_Amended__c,SBQQ__Quote__r.SBQQ__Opportunity2__c,';
        }

        if(cpaiQuotes != null && !cpaiQuotes.isEmpty() || Test.isRunningTest()){
            qlFields += 'PC__c,ProductFamilyLabel__c,SBQQ__Quote__c,Group_Number__c,Group_Name__c,SBQQ__Quantity__c,SBQQ__UpgradedSubscription__c,SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__c,SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__r.SBQQ__Quantity__c';
        }

        if (String.isBlank(qlFields)) {
            return null;
        }

        Set<String> qlFieldsSet = new Set<String>(qlFields.split(','));
        qlFieldsSet.remove(null);
        qlFields = String.join(new List<String>(qlFieldsSet), ',');

        List<SBQQ__QuoteLine__c> relatedQLs = Database.query('SELECT ' + qlFields + ' ' +
            'FROM SBQQ__QuoteLine__c ' +
            'WHERE SBQQ__Quote__c IN :currentQuotesIds'
        );

        Map<Id, List<SBQQ__QuoteLine__c>> qlByQuoteId = new Map<Id, List<SBQQ__QuoteLine__c>>();

        for (Id quoteId : currentQuotesIds) {
            qlByQuoteId.put(quoteId, new List<SBQQ__QuoteLine__c>());
        }

        for (SBQQ__QuoteLine__c ql : relatedQLs) {
            qlByQuoteId.get(ql.SBQQ__Quote__c).add(ql);
        }
        return qlByQuoteId;
    }

    @TestVisible
    private void getRelatedsObjects (Map<Id, pse__Grp__c> groupMap) {
        if (quotes2StampOppJDECorp != null && !quotes2StampOppJDECorp.isEmpty() 
            || amendmentQuotesToUpdateOpps != null && !amendmentQuotesToUpdateOpps.isEmpty() 
            || Test.isRunningTest()) {
            for (pse__Grp__c groupRec : TriggersHelper.JDEgroups.values()) {
                groupMap.put(groupRec.Id, groupRec);
            }
        }
    }

}