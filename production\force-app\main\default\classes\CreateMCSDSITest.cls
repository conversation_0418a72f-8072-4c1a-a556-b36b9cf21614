/*
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER						DATE									REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Mahipal Jat		          	22/05/2025                       		- Original Version - 557760 - SF/CPQ - MCS - Auto Opp Creation
* ---------------------------------------------------------------------------------------------------------------------------------------------------
*
*/

@IsTest
public with sharing class CreateMCSDSITest {
    private static User sysAdmin {
        get {
            if (sysAdmin == null) {
                sysAdmin = [
                    SELECT Id, Email, FirstName, LastName
                    FROM User
                    WHERE IsActive = TRUE AND Profile.Name = 'System Administrator'
                    LIMIT 1
                ];
            }
            return sysAdmin;
        }
        set;
    }

    @TestSetup
    static void initData() {
        System.runAs(sysAdmin) {
            PSA_Project_Creation_Settings__c customSetting = new PSA_Project_Creation_Settings__c(
                Name = 'test',
                Resource_Request_Stage__c = 'Close'
            );
            insert customSetting;

            Business_Unit__c bu = new Business_Unit__c();
            bu.Name = 'Geo-Coding';
            insert bu;

            Territory__c te = new Territory__c();
            te.Name = 'Testtes';
            te.Business_Unit__c = bu.Id;
            insert te;

            District__c dst = new District__c();
            dst.Name = 'test';
            dst.RevRec_User__c = UserInfo.getUserId();
            dst.Legal_User__c = UserInfo.getUserId();
            dst.Territory__c = te.Id;
            dst.Business_Unit__c = bu.Id;
            dst.Business_Hours__c = [SELECT Id FROM BusinessHours LIMIT 1].Id;
            insert dst;

            Account acc = new Account();
            acc.Name = 'Test Account Quote';
            acc.BillingStreet = 'Timesquare';
            acc.BillingCity = 'New york';
            acc.BillingState = 'VA';
            acc.BillingPostalCode = '33444';
            acc.BillingCountry = 'USA';
            acc.District__c = dst.Id;
            acc.OwnerId = sysAdmin.Id;
            insert acc;

            Contact c = TestHelper.createContact(false, false);
            c.AccountId = acc.Id;
            c.Email = sysAdmin.Email;
            c.pse__Salesforce_User__c = sysAdmin.Id;
            insert c;

            DSI__c dsi = TestHelper.createDSI(false, false);
            dsi.Name = 'United';
            dsi.Account__c = acc.Id;
            dsi.DSI_ID__c = '123456';
            dsi.Platform__C = 'MCS';
            insert dsi;

            String rtIdP = [SELECT Id FROM RecordType WHERE Name = 'Product' AND SobjectType = 'Product2'].Id;

            Product2 prodct = new Product2();
            prodct.IsActive = true;
            prodct.Software_Type_ID__c = 3;
            prodct.Name = 'Test';
            prodct.RMS_Product_ID__c = 608;
            prodct.Version_ID__c = 104;
            prodct.ProductCode = '12323';
            prodct.RecordTypeId = rtIdP;
            prodct.Interval__c = 'Perpetual';
            insert prodct;

            Product2 prodct2 = new Product2();
            prodct2.IsActive = true;
            prodct2.Software_Type_ID__c = 3;
            prodct2.Name = 'Test';
            prodct2.RMS_Product_ID__c = 608;
            prodct2.Version_ID__c = 104;
            prodct2.ProductCode = '*********';
            prodct2.Previous_SKU__c = prodct.Id;
            prodct2.RecordTypeId = rtIdP;
            prodct2.Interval__c = 'Term';
            insert prodct2;

            List<Asset> assetList = new List<Asset>();
            Asset asset1 = new Asset();
            asset1.Name = 'Test Asset';
            asset1.AccountId = acc.Id;
            asset1.Quantity = 1;
            asset1.SBCF_DSI__c = dsi.Id;
            asset1.Product2Id = prodct.Id;
            asset1.Key_Group_multi__c = 'Prod Key 1';
            assetList.add(asset1);

            Asset asset2 = new Asset();
            asset2.Name = 'Test Asset';
            asset2.Quantity = 1;
            asset2.SBCF_DSI__c = dsi.Id;
            asset2.AccountId = acc.Id;
            asset2.Product2Id = prodct.Id;
            asset2.Key_Group_multi__c = 'Prod Key 2';
            assetList.add(asset2);

            Asset asset3 = new Asset();
            asset3.Name = 'Test Asset';
            asset3.Quantity = 1;
            asset3.SBCF_DSI__c = dsi.Id;
            asset3.AccountId = acc.Id;
            asset3.Product2Id = prodct.Id;
            asset3.Key_Group_multi__c = 'Prod Key 1';
            assetList.add(asset3);

            Asset asset4 = new Asset();
            asset4.Name = 'Test Asset';
            asset4.Quantity = 1;
            asset4.SBCF_DSI__c = dsi.Id;
            asset4.AccountId = acc.Id;
            asset4.Product2Id = prodct.Id;
            asset4.Key_Group_multi__c = 'Prod Key 1';
            asset4.Translated__c = true;
            assetList.add(asset4);

            insert assetList;

            SBQQ__Subscription__c subs1 = new SBQQ__Subscription__c();
            subs1.SBQQ__Account__c = acc.Id;
            subs1.SBQQ__Product__c = prodct2.Id;
            subs1.SBQQ__Quantity__c = 1;
            subs1.SBCF_DSI__c = dsi.Id;
            subs1.Key_Group_multi__c = 'Prod Key 1';
            insert subs1;

            MSTR_Placeholder_Values__c placeholder = MSTR_Placeholder_Values__c.getOrgDefaults();
            SlaProcess MCSSLA = [SELECT Id, Name, BusinessHoursId FROM SlaProcess WHERE Name = 'Cloud MCS' LIMIT 1];
            Entitlement ent = new Entitlement();
            ent.DSI__c = dsi.ID;
            ent.AccountId = c.accountId;
            ent.Support_Level__c = 'Cloud MCS';
            ent.Name = dsi.Name + ' - Cloud MCS';
            ent.slaProcessId = MCSSLA.Id;
            ent.BusinessHoursId = placeholder.Default_Business_Hours__c;
            ent.LastUpdateddate__c = System.now();
            ent.Type = 'Email Support';
            ent.StartDate = System.Today();
            ent.EndDate = System.Today().addDays(90);
            insert ent;

            Product_Key__c key = new Product_Key__c();
            key.DSI__c = dsi.Id;
            insert key;
            
            //Case 446953 - Priyank Start
            Product_Key_Request__c tempRequest = new Product_Key_Request__c();
            
            tempRequest.Account__c = acc.id;
            tempRequest.DSI__c = dsi.id;
            tempRequest.Manual_Key__c = false;
            tempRequest.Version_List__c = '10.4.0';
            //tempRequest.Ship_Contact__c=temp.Ship_To_Contact_SFID__c;
            //tempRequest.Opportunity__c=temp.Id;
            tempRequest.QuoteId__c = 104;
            
            insert tempRequest;
            //Case 446953 - Priyank End
        }
    }

    @IsTest
    static void testDoPostWrongData() {
        System.runAs(sysAdmin) {
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/doPost';
            req.httpMethod = 'POST';
            req.requestBody = Blob.valueOf(JSON.serialize(new CreateMCSDSI.RequestWrapper()));

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.ResponseWrapper response = CreateMCSDSI.doPost();
            Test.stopTest();

            System.assertEquals('Missing Required Fields', response.errorMessage);
        }
    }

    @IsTest
    static void testDoPostApexError() {
        System.runAs(sysAdmin) {
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/doPost';
            req.httpMethod = 'POST';

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.ResponseWrapper response = CreateMCSDSI.doPost();
            Test.stopTest();

            System.assertEquals('Argument cannot be null.', response.errorMessage);
        }
    }

    @IsTest
    static void testDoPostSuccess() {
        System.runAs(sysAdmin) {
            Test.setMock(HttpCalloutMock.class, new McRestRequestMock());

            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.mstrVersion = '2021';
            wrapper.productPackage = 'MCS';
            wrapper.environmentId = 'Test';
            wrapper.user = new CreateMCSDSI.ContactUser();
            wrapper.user.email = sysAdmin.Email;
            wrapper.user.firstName = sysAdmin.FirstName;
            wrapper.user.lastName = sysAdmin.LastName;
            wrapper.user.scopusID = 'test';

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/doPost';
            req.httpMethod = 'POST';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.doPost();
            Test.stopTest();
        }
    }

    @IsTest
    static void testDoDeleteSuccess() {
        System.runAs(sysAdmin) {
            Test.setMock(HttpCalloutMock.class, new McRestRequestMock());
            DSI__c dsi = [SELECT id, DSIID__c FROM DSI__c LIMIT 1];

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/' + dsi.DSIID__c;
            req.httpMethod = 'DELETE';

            RestContext.request = req;
            RestContext.response = res;
            Test.startTest();

            CreateMCSDSI.deleteMCSEnvironment();

            Test.stopTest();
        }
    }

    @IsTest
    static void testNoDSIDeleteFailure() {
        System.runAs(sysAdmin) {
            Test.setMock(HttpCalloutMock.class, new McRestRequestMock());
            DSI__c dsi = [SELECT id, DSIID__c FROM DSI__c LIMIT 1];

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/' + dsi.DSIID__c;
            req.httpMethod = 'DELETE';

            RestContext.request = req;
            RestContext.response = res;
            Test.startTest();

            CreateMCSDSI.deleteMCSEnvironment();

            Test.stopTest();
        }
    }

    @IsTest
    static void testNoDSIFoundFailure() {
        System.runAs(sysAdmin) {
            Test.setMock(HttpCalloutMock.class, new McRestRequestMock());
            DSI__c dsi = [SELECT id, DSIID__c FROM DSI__c LIMIT 1];

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/123123123';
            req.httpMethod = 'DELETE';

            RestContext.request = req;
            RestContext.response = res;
            Test.startTest();

            CreateMCSDSI.deleteMCSEnvironment();

            Test.stopTest();
        }
    }

    @IsTest
    static void testUpdateDSISuccess() {
        System.runAs(sysAdmin) {
            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.mstrVersion = '2021';
            wrapper.productPackage = 'MCS';
            wrapper.environmentId = 'Test';
            wrapper.user = new CreateMCSDSI.ContactUser();
            wrapper.user.email = sysAdmin.Email;
            wrapper.user.firstName = sysAdmin.FirstName;
            wrapper.user.lastName = sysAdmin.LastName;
            wrapper.user.scopusID = 'test';
            wrapper.dsiId = [SELECT Id, DSIID__c FROM DSI__c WHERE DSIID__c != NULL LIMIT 1].DSIID__c;

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/updateDSI';
            req.httpMethod = 'PATCH';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.PatchResponseWrapper response = CreateMCSDSI.updateDSI();
            Test.stopTest();

            //System.assert(String.isBlank(response.errorMessage));
        }
    }

    @IsTest
    static void testUpdateDSIWrongData() {
        System.runAs(sysAdmin) {
            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.mstrVersion = '2021';
            wrapper.productPackage = 'MCS';
            wrapper.environmentId = 'Test';
            wrapper.user = new CreateMCSDSI.ContactUser();
            wrapper.user.email = sysAdmin.Email;
            wrapper.user.firstName = sysAdmin.FirstName;
            wrapper.user.lastName = sysAdmin.LastName;
            wrapper.user.scopusID = 'test';
            wrapper.dsiId = '9999';

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/updateDSI';
            req.httpMethod = 'PATCH';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.PatchResponseWrapper response = CreateMCSDSI.updateDSI();
            Test.stopTest();

            System.assertEquals('Something went wrong: Invalid id: 9999', response.errorMessage);
        }
    }

    @IsTest
    static void testUpdateDSIWrongJSON() {
        System.runAs(sysAdmin) {
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/updateDSI';
            req.httpMethod = 'PATCH';
            req.requestBody = Blob.valueOf(JSON.serialize(new CreateMCSDSI.RequestWrapper()));

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.PatchResponseWrapper response = CreateMCSDSI.updateDSI();
            Test.stopTest();

            System.assertEquals('Missing Required Fields', response.errorMessage);
        }
    }

    @IsTest
    static void testValidatePatchRequest() {
        System.runAs(sysAdmin) {
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/dsi/updateDSI';
            req.httpMethod = 'PATCH';
            //req.requestBody = Blob.valueOf(JSON.serialize(new CreateMCSDSI.RequestWrapper()));
            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.dsiId = '9999';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.PatchResponseWrapper response = CreateMCSDSI.updateDSI();

            wrapper.user = new CreateMCSDSI.ContactUser();
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));
            response = CreateMCSDSI.updateDSI();
            Test.stopTest();

            System.assertEquals('Something went wrong: Invalid id: 9999', response.errorMessage);
        }
    }

    @IsTest
    static void testCreateDSISuccess() {
        System.runAs(sysAdmin) {
            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.mstrVersion = '2021';
            wrapper.productPackage = 'MCS';
            wrapper.environmentId = 'Test';
            wrapper.user = new CreateMCSDSI.ContactUser();
            wrapper.user.email = sysAdmin.Email;
            wrapper.user.firstName = sysAdmin.FirstName;
            wrapper.user.lastName = sysAdmin.LastName;
            wrapper.user.scopusID = 'test';
            wrapper.dsiId = [SELECT Id, DSIID__c FROM DSI__c WHERE DSIID__c != NULL LIMIT 1].DSIID__c;

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/sfdcInternalMCS/createDSI';
            req.httpMethod = 'POST';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.ResponseWrapper response = mcsRestRequest.createDSI();
            Test.stopTest();

            System.assert(String.isBlank(response.errorMessage));
        }
    }

    @IsTest
    static void testAccountOwnerMCSUpdateSuccess() {
        System.runAs(sysAdmin) {
            DSI__c dsi = [SELECT Id FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
            Contact c = [SELECT Id, AccountId FROM Contact LIMIT 1];

            Test.startTest();
            EventBus.publish(
                new List<MCS_Event__e>{ new MCS_Event__e(contact__c = c.Id), new MCS_Event__E(Account__c = c.AccountId) }
            );
            Test.stopTest();
        }
    }

    @IsTest
    static void testGetMCSInfoSuccess() {
        System.runAs(sysAdmin) {
            DSI__c dsi = [SELECT Id, Name FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
            Contact c = [SELECT Id, email, AccountId FROM Contact LIMIT 1];
            Account a = [SELECT Id FROM Account LIMIT 1];

            dsi.Platform__c = 'MCS';
            dsi.MCSEnvironmentOwner__c = c.Id;
            dsi.MCSActivationDateTime__c = System.now();
            dsi.MCSExpirationDatetime__c = System.now();
            update dsi;

            MSTR_Placeholder_Values__c placeholder = MSTR_Placeholder_Values__c.getOrgDefaults();
            SlaProcess MCSSLA = [SELECT Id, Name, BusinessHoursId FROM SlaProcess WHERE Name = 'Cloud MCS' LIMIT 1];

            //Entitlement
            Entitlement ent = new Entitlement();
            ent.DSI__c = dsi.ID;
            ent.AccountId = c.AccountId;
            ent.Support_Level__c = 'Cloud Standard';
            ent.StartDate = System.Today();
            ent.EndDate = System.Today().addDays(90); //subscriptionenddate or 90 days from today
            ent.Name = dsi.Name + ' - Cloud MCS';
            ent.slaProcessId = MCSSLA.Id;
            ent.BusinessHoursId = placeholder.Default_Business_Hours__c;
            insert ent;

            EntitlementContact entContact = new EntitlementContact();
            entContact.ContactId = c.id;
            entContact.EntitlementId = ent.Id;
            insert entContact;

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/environments?email=' + c.email;
            req.addParameter('email', c.email);
            req.httpMethod = 'GET';

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            GetMCSInfo.ResponseWrapper result = GetMCSInfo.doGet();
            Test.stopTest();

            //system.assert(String.isNotBlank(result.errorMessage));
            system.assertEquals(result.user.email, c.Email, 'Emails do not match:');
        }
    }

    @IsTest
    static void testGetMCSInfoNoEmail() {
        System.runAs(sysAdmin) {
            DSI__c dsi = [SELECT Id FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
            Contact c = [SELECT Id, email FROM Contact LIMIT 1];

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/environments?email=' + c.email;
            req.httpMethod = 'GET';

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            GetMCSInfo.ResponseWrapper result = GetMCSInfo.doGet();
            Test.stopTest();

            system.assert(String.isNotBlank(result.errorMessage));
        }
    }

    @IsTest
    static void testGetMCSInfoNoFoundEmail() {
        System.runAs(sysAdmin) {
            DSI__c dsi = [SELECT Id FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
            Contact c = [SELECT Id, email FROM Contact LIMIT 1];

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/MCS/environments?email=notrealemail';
            req.addParameter('email', 'notarealemail');
            req.httpMethod = 'GET';

            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            GetMCSInfo.ResponseWrapper result = GetMCSInfo.doGet();
            Test.stopTest();

            system.assert(String.isNotBlank(result.errorMessage));
        }
    }
	//Case 446953 - Priyank Start
    /*@IsTest
    static void testMCSDependencyBatch() {
        String userId = UserInfo.getUserId();
        System.runAs(sysAdmin) {
            DSI__c dsi = [SELECT Id, MCS_Type__c FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
            Contact c = [SELECT Id, email, AccountId FROM Contact LIMIT 1];
            dsi.MCSBatchStatus__c = 'Pending';
            dsi.MCSEnvironmentOwner__c = c.Id;
            update dsi;

            Test.startTest();
            Database.executeBatch(new MCSDependencyBatch());
            Test.stopTest();

            Contact c2 = TestHelper.createContact(false, false);
            c2.AccountId = c.AccountId;
            c2.Email = 'mockContact2' + sysAdmin.Email;
            c2.pse__Salesforce_User__c = userId;
            insert c2;

            dsi.MCSEnvironmentOwner__c = c2.id;
            dsi.MCSBatchStatus__c = 'Pending';
            update dsi;

            Database.executeBatch(new MCSDependencyBatch(' MCSBatchStatus__c = \'Pending\' '));

            List<SBQQ__Quote__c> quotesToInsert = new List<SBQQ__Quote__c>();
            Id draftRecordTypeId = Schema.SObjectType.SBQQ__Quote__c.getRecordTypeInfosByName()
                .get('Draft Quote')
                .getRecordTypeId();
            for (Opportunity opp : [SELECT Id, AccountId, CloseDate, OwnerId, CurrencyIsoCode FROM Opportunity]) {
                SBQQ__Quote__c quote = new SBQQ__Quote__c();
                quote.SBQQ__Account__c = opp.AccountId;
                quote.SBQQ__Opportunity2__c = opp.Id;
                quote.SBQQ__StartDate__c = opp.CloseDate;
                quote.SBQQ__Primary__c = false;
                quote.Is_MCS_DCI__c = true;
                quote.SBQQ__SubscriptionTerm__c = 12;
                quote.RecordTypeId = draftRecordTypeId;
                quote.AccountOwner__c = opp.OwnerId;
                quote.OwnerId = opp.OwnerId;
                quote.CurrencyIsoCode = opp.CurrencyIsoCode;
                quotesToInsert.add(quote);
            }
            MCSDependencyEmailService.handleInboundEmail(
                String.valueOf(SBQQ__Quote__c.getSObjectType()),
                JSON.serialize(quotesToInsert)
            );
            MCSDependencyEmailService.updateErrorMap(
                dsi.Id,
                new Map<Id, FF_Integration_Log__c>(),
                'test',
                new List<Database.Error>()
            );
        }
    }

    @IsTest
    static void testMCSDependencyBatchForEmployees() {
        String userId = UserInfo.getUserId();
        System.runAs(sysAdmin) {
            DSI__c dsi = [SELECT Id, MCS_Type__c FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
            Contact c = [SELECT Id, email, AccountId FROM Contact LIMIT 1];
            Account acc = [SELECT Id, Name FROM Account LIMIT 1];

            acc.Name = 'MicroStrategy';
            update acc;

            dsi.MCSBatchStatus__c = 'Pending';
            dsi.MCSEnvironmentOwner__c = c.Id;
            update dsi;

            Test.startTest();
            Database.executeBatch(new MCSDependencyBatch());
            Test.stopTest();

            Contact c2 = TestHelper.createContact(false, false);
            c2.AccountId = c.AccountId;
            c2.Email = 'mockContact2' + sysAdmin.Email;
            c2.pse__Salesforce_User__c = userId;
            insert c2;

            dsi.MCSEnvironmentOwner__c = c2.id;
            dsi.MCSBatchStatus__c = 'Pending';
            update dsi;

            Database.executeBatch(new MCSDependencyBatch(' MCSBatchStatus__c = \'Pending\' '));

            List<SBQQ__Quote__c> quotesToInsert = new List<SBQQ__Quote__c>();
            Id draftRecordTypeId = Schema.SObjectType.SBQQ__Quote__c.getRecordTypeInfosByName()
                .get('Draft Quote')
                .getRecordTypeId();
            for (Opportunity opp : [SELECT Id, AccountId, CloseDate, OwnerId, CurrencyIsoCode FROM Opportunity]) {
                SBQQ__Quote__c quote = new SBQQ__Quote__c();
                quote.SBQQ__Account__c = opp.AccountId;
                quote.SBQQ__Opportunity2__c = opp.Id;
                quote.SBQQ__StartDate__c = opp.CloseDate;
                quote.SBQQ__Primary__c = false;
                quote.Is_MCS_DCI__c = true;
                quote.SBQQ__SubscriptionTerm__c = 12;
                quote.RecordTypeId = draftRecordTypeId;
                quote.AccountOwner__c = opp.OwnerId;
                quote.OwnerId = opp.OwnerId;
                quote.CurrencyIsoCode = opp.CurrencyIsoCode;
                quotesToInsert.add(quote);
            }
            MCSDependencyEmailService.handleInboundEmail(
                String.valueOf(SBQQ__Quote__c.getSObjectType()),
                JSON.serialize(quotesToInsert)
            );
            MCSDependencyEmailService.updateErrorMap(
                dsi.Id,
                new Map<Id, FF_Integration_Log__c>(),
                'test',
                new List<Database.Error>()
            );
        }
    }

    @IsTest
    static void testMCSDependencyBatchWithoutDefaultOwner() {
        String userId = UserInfo.getUserId();
        System.runAs(sysAdmin) {
            DSI__c dsi = [SELECT Id, MCS_Type__c FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
            Contact c = [SELECT Id, email, AccountId FROM Contact LIMIT 1];

            dsi.MCSBatchStatus__c = 'Pending';
            dsi.MCSEnvironmentOwner__c = c.Id;
            update dsi;

            sysAdmin.Department = 'SLS';
            update sysAdmin;
            Entitlement ent = new Entitlement(
                Name = 'Test Entitlement',
                AccountId = c.AccountId,
                DSI__c = dsi.id,
                Support_Level__c = 'Cloud Elite',
                StartDate = Date.today().addDays(-5),
                EndDate = Date.today().addDays(65)
            );
            insert ent;

            EntitlementContact ec1 = new EntitlementContact();
            ec1.ContactId = c.Id;
            ec1.EntitlementId = ent.Id;
            insert ec1;

            Test.startTest();
            Database.executeBatch(new MCSDependencyBatch());
            Test.stopTest();

            Contact c2 = TestHelper.createContact(false, false);
            c2.AccountId = c.AccountId;
            c2.Email = 'mockContact2' + sysAdmin.Email;
            c2.pse__Salesforce_User__c = userId;
            insert c2;

            dsi.MCSEnvironmentOwner__c = c2.id;
            dsi.MCSBatchStatus__c = 'Pending';
            update dsi;

            Database.executeBatch(new MCSDependencyBatch(' MCSBatchStatus__c = \'Pending\' '));

            List<SBQQ__Quote__c> quotesToInsert = new List<SBQQ__Quote__c>();
            Id draftRecordTypeId = Schema.SObjectType.SBQQ__Quote__c.getRecordTypeInfosByName()
                .get('Draft Quote')
                .getRecordTypeId();
            for (Opportunity opp : [SELECT Id, AccountId, CloseDate, OwnerId, CurrencyIsoCode FROM Opportunity]) {
                SBQQ__Quote__c quote = new SBQQ__Quote__c();
                quote.SBQQ__Account__c = opp.AccountId;
                quote.SBQQ__Opportunity2__c = opp.Id;
                quote.SBQQ__StartDate__c = opp.CloseDate;
                quote.SBQQ__Primary__c = false;
                quote.Is_MCS_DCI__c = true;
                quote.SBQQ__SubscriptionTerm__c = 12;
                quote.RecordTypeId = draftRecordTypeId;
                quote.AccountOwner__c = opp.OwnerId;
                quote.OwnerId = opp.OwnerId;
                quote.CurrencyIsoCode = opp.CurrencyIsoCode;
                quotesToInsert.add(quote);
            }
            MCSDependencyEmailService.handleInboundEmail(
                String.valueOf(SBQQ__Quote__c.getSObjectType()),
                JSON.serialize(quotesToInsert)
            );
            MCSDependencyEmailService.updateErrorMap(
                dsi.Id,
                new Map<Id, FF_Integration_Log__c>(),
                'test',
                new List<Database.Error>()
            );
        }
    }
	*/
    //Case 446953 - Priyank End
    
    // @IsTest
    // static void testMCSDependencyBatchFailure() {
    //     String userId = UserInfo.getUserId();
    //     System.runAs(sysAdmin) {
    //         DSI__c dsi = [SELECT Id, MCS_Type__c FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
    //         Contact c = [SELECT Id, email, AccountId FROM Contact LIMIT 1];

    //         dsi.MCSBatchStatus__c = 'Pending';
    //         dsi.MCSEnvironmentOwner__c = c.Id;
    //         update dsi;

    //         Entitlement ent = new Entitlement(
    //             Name = 'Test Entitlement',
    //             AccountId = c.AccountId,
    //             DSI__c = dsi.id,
    //             Support_Level__c = 'Cloud Elite',
    //             StartDate = Date.today().addDays(-5),
    //             EndDate = Date.today().addDays(65)
    //         );
    //         insert ent;

    //         EntitlementContact ec1 = new EntitlementContact();
    //         ec1.ContactId = c.Id;
    //         ec1.EntitlementId = ent.Id;
    //         insert ec1;

    //         Test.startTest();
    //         Database.executeBatch(new MCSDependencyBatch());
    //         Test.stopTest();

    //         //     Contact c2 = TestHelper.createContact(false, false);
    //         //     c2.AccountId = c.AccountId;
    //         //     c2.Email = 'mockContact2' + sysAdmin.Email;
    //         //     c2.pse__Salesforce_User__c = userId;
    //         //     insert c2;

    //         //     dsi.MCSEnvironmentOwner__c = c2.id;
    //         //     dsi.MCSBatchStatus__c = 'Pending';
    //         //     update dsi;

    //         //     Database.executeBatch(new MCSDependencyBatch(' MCSBatchStatus__c = \'Pending\' '));

    //         //     List<SBQQ__Quote__c> quotesToInsert = new List<SBQQ__Quote__c>();
    //         //     Id draftRecordTypeId = Schema.SObjectType.SBQQ__Quote__c.getRecordTypeInfosByName()
    //         //         .get('Draft Quote')
    //         //         .getRecordTypeId();
    //         //     for (Opportunity opp : [SELECT Id, AccountId, CloseDate, OwnerId, CurrencyIsoCode FROM Opportunity]) {
    //         //         SBQQ__Quote__c quote = new SBQQ__Quote__c();
    //         //         quote.SBQQ__Account__c = opp.AccountId;
    //         //         quote.SBQQ__Opportunity2__c = opp.Id;
    //         //         quote.SBQQ__StartDate__c = opp.CloseDate;
    //         //         quote.SBQQ__Primary__c = false;
    //         //         quote.Is_MCS_DCI__c = true;
    //         //         quote.SBQQ__SubscriptionTerm__c = 12;
    //         //         quote.RecordTypeId = draftRecordTypeId;
    //         //         quote.AccountOwner__c = opp.OwnerId;
    //         //         quote.OwnerId = opp.OwnerId;
    //         //         quote.CurrencyIsoCode = opp.CurrencyIsoCode;
    //         //         quotesToInsert.add(quote);
    //         //     }
    //         //     MCSDependencyEmailService.handleInboundEmail(
    //         //         String.valueOf(SBQQ__Quote__c.getSObjectType()),
    //         //         JSON.serialize(quotesToInsert)
    //         //     );
    //         //     MCSDependencyEmailService.updateErrorMap(
    //         //         dsi.Id,
    //         //         new Map<Id, FF_Integration_Log__c>(),
    //         //         'test',
    //         //         new List<Database.Error>()
    //         //     );
    //     }
    // }

    @IsTest
    static void testFailureLog() {
        String userId = UserInfo.getUserId();
        System.runAs(sysAdmin) {
            MCSDependencyBatch testBatch = new MCSDependencyBatch();
            Map<id, FF_Integration_Log__c> errMap = new Map<id, FF_Integration_Log__c>();
            testBatch.updateErrorMap(userId, errMap, 'testString', new List<Database.Error>{});
            testBatch.updateErrorMap(userId, errMap, 'testString2', new List<Database.Error>{});
            System.assert(errMap.containsKey(userId));
        }
    }

    @IsTest
    static void testMCSTriggerHelperTest() {
        String userId = UserInfo.getUserId();
        System.runAs(sysAdmin) {
            Test.setMock(HttpCalloutMock.class, new McRestRequestMock());
            DSI__c dsi = [SELECT id, DSIID__c FROM DSI__c WHERE Platform__c = 'MCS' LIMIT 1];

            Test.startTest();

            MCSEventTriggerHelper.updateAccountOwner(dsi.Id);

            Test.stopTest();
        }
    }
    
    @IsTest
    static void testUpdateMCSAccountInfo() {
        String userId = UserInfo.getUserId();
        System.runAs(sysAdmin) {
            Test.setMock(HttpCalloutMock.class, new McRestRequestMock());
            DSI__c dsi = [SELECT id, DSIID__c FROM DSI__c WHERE Platform__c = 'MCS' LIMIT 1];

            Test.startTest();

            dsi.Entitlement_End_Date__c = Date.today() + 2;
            update dsi;

            Test.stopTest();
        }
    }

    public class McRestRequestMock implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest request) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json; charset=UTF-8');
            if (request.getEndpoint().contains('/services/apexrest/sfdcInternalMCS/createDSI')) {
                res.setBody(
                    JSON.serialize(
                        new CreateMCSDSI.ResponseWrapper([SELECT Id, DSIID__c FROM DSI__c WHERE DSIID__c != NULL LIMIT 1], '')
                    )
                );
            } else if (request.getEndpoint().contains('https://cdkeydev.microstrategy.com/KeyGeneratorAPI/API/KeyGen/GetCDKey')) {
                res.setBody(JSON.serialize([SELECT Id, Body FROM StaticResource WHERE Name = 'CDKeyMock'].Body));
            } else if (request.getEndpoint().contains('/api/v1/_internal/environments/DSI/')) {
            }
            res.setStatusCode(200);
            return res;
        }
    }
    
    @isTest
    static void testUpdateDSINoContactFound() {
        System.runAs(sysAdmin) {
            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.dsiId = [SELECT DSIID__c FROM DSI__c LIMIT 1].DSIID__c;
            wrapper.user = new CreateMCSDSI.ContactUser();
            wrapper.user.email = '<EMAIL>'; // Email that doesn't exist

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/mcs/dsi/updateDSI';
            req.httpMethod = 'PATCH';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));
            RestContext.request = req;
            RestContext.response = res;
            
            Test.startTest();
            CreateMCSDSI.PatchResponseWrapper response = CreateMCSDSI.updateDSI();
            Test.stopTest();
            
            System.assertEquals('No matching Salesforce Contact found', response.errorMessage);
            System.assertEquals(400, RestContext.response.statusCode);
        }
    }

    @isTest
    static void testUpdateDSIDefaultAccountOwner() {
        System.runAs(sysAdmin) {
            // Ensure the Account Owner is not in Sales department
            User testUser = [SELECT Id, Department FROM User WHERE Id = :UserInfo.getUserId()];
            testUser.Department = 'Support';
            update testUser;
            
            Account acc = [SELECT Id, OwnerId FROM Account LIMIT 1];
            acc.OwnerId = testUser.Id;
            acc.Compass_ID__c = 320;
            update acc;

            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.dsiId = [SELECT DSIID__c FROM DSI__c LIMIT 1].DSIID__c;
            wrapper.user = new CreateMCSDSI.ContactUser();
            wrapper.user.email = [SELECT Email FROM Contact LIMIT 1].Email;
            
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/mcs/dsi/updateDSI';
            req.httpMethod = 'PATCH';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));
            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.PatchResponseWrapper response = CreateMCSDSI.updateDSI();
            Test.stopTest();

            System.assertEquals(null, response.errorMessage, 'Should not have an error message');
            System.assertNotEquals(null, response.account.accountExecutive.email, 'Default AE email should be populated.');
        }
    }

    @isTest
    static void testDeleteDSIMissingIdInUri() {
        System.runAs(sysAdmin) {
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            // URI is missing the final DSI ID part
            req.requestURI = '/services/apexrest/v1/mcs/dsi/';
            req.httpMethod = 'DELETE';
            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            CreateMCSDSI.PatchResponseWrapper response = CreateMCSDSI.deleteMCSEnvironment();
            Test.stopTest();

            System.assertEquals('No DSI Provided', response.errorMessage);
            System.assertEquals(400, RestContext.response.statusCode);
        }
    }

    @isTest
    static void testInternalCreateDSIException() {
        System.runAs(sysAdmin) {
            // This wrapper is missing productPackage, which will cause an exception
            // when querying for MSTR_Global_Configuation__mdt
            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.mstrVersion = '2021';
            wrapper.environmentId = 'Test-Env-789';

            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/sfdcInternalMCS/createDSI';
            req.httpMethod = 'POST';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));
            RestContext.request = req;
            RestContext.response = res;

            Test.startTest();
            Boolean exceptionThrown = false;
            try {
                MCSRestRequest.createDSI();
            } catch (MCSRestRequest.WSException e) {
                exceptionThrown = true;
                System.assert(e.getMessage().contains('Something went Wrong'), 'Exception message should indicate a failure.');
            }
            Test.stopTest();

            System.assert(exceptionThrown, 'A WSException should have been thrown.');
        }
    }
     @IsTest
    static void testDoPost_And_GenerateKey_Success() {
        System.runAs(sysAdmin) {
            // Setup a mock that simulates successful responses for both the internal call and the key generator call.
            Test.setMock(HttpCalloutMock.class, new McRestRequestMock_ForGenerateKey());
            
            Contact c = [SELECT Email, FirstName, LastName FROM Contact LIMIT 1];
            
            // Prepare a valid request that will pass validation.
            CreateMCSDSI.RequestWrapper wrapper = new CreateMCSDSI.RequestWrapper();
            wrapper.mstrVersion = '2022';
            wrapper.productPackage = 'MCS';
            wrapper.environmentId = 'GenerateKey-Test-Env';
            wrapper.user = new CreateMCSDSI.ContactUser();
            wrapper.user.email = c.Email;
            wrapper.user.firstName = c.FirstName;
            wrapper.user.lastName = c.LastName;
            
            // Set up the RestContext
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();
            req.requestURI = '/services/apexrest/v1/mcs/dsi/doPost';
            req.httpMethod = 'POST';
            req.requestBody = Blob.valueOf(JSON.serialize(wrapper));
            
            RestContext.request = req;
            RestContext.response = res;
            
            Test.startTest();
            // Execute the main method. This will trigger the callouts and the call to generateKey.
            CreateMCSDSI.ResponseWrapper response = CreateMCSDSI.doPost();
            Test.stopTest();
            
          
        }
    }

    /**
     * @description A dedicated mock class for testing the generateKey flow.
     * It handles two separate callouts that occur in the doPost transaction:
     * 1. The internal callout to MCSRestRequest to create the DSI.
     * 2. The external callout made by KeyGeneratorController to get a key.
     */
    public class McRestRequestMock_ForGenerateKey implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest request) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json; charset=UTF-8');
            
            // Mock for the internal DSI creation call
            if (request.getEndpoint().contains('/services/apexrest/sfdcInternalMCS/createDSI')) {
                DSI__c mockDSI = [SELECT Id, DSIID__c, MCSActivationDateTime__c, MCSExpirationDatetime__c FROM DSI__c WHERE DSIID__c != NULL LIMIT 1];
                CreateMCSDSI.ResponseWrapper respWrapper = new CreateMCSDSI.ResponseWrapper(mockDSI, null);
                res.setBody(JSON.serialize(respWrapper));
                res.setStatusCode(200);
            } 
            // Mock for the KeyGeneratorController's callout
            else if (request.getEndpoint().contains('KeyGeneratorAPI')) {
                // This mock simulates a successful key generation callout.
                res.setBody('{"status":"success", "key":"MOCK-KEY-123"}');
                res.setStatusCode(200);
            } 
            else {
                res.setStatusCode(404);
                res.setStatus('Not Found');
            }
            return res;
        }
    }
}