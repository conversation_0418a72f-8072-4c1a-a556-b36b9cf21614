/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 06.04.2021.
 */

 public without sharing class SubscriptionTriggerExecutionController implements TriggerHandler {


    private Set<Id> subscriptionsEduCode;
    private List<SBQQ__Subscription__c> subscriptions;
    private List<SBQQ__Subscription__c> subs2StampKeyGroup;
    private Map<Id, SBQQ__Subscription__c> ql2Subs;
    private Set<Id> ctList;
    private List<Id> subs;
    private List<SBQQ__Subscription__c> subs2stampNextKeyShipmentDateOnDSI;
    private Set<Id> contracts;
    private Set<Id> dsiSet;
    private Set<Id> subs2StampRelatedContracts;
    private Set<Id> subsRelatedContracts;
    private Set<Id> dsiIdsKeyShipment;
    private Set<Id> dsiSetForAI;
    private Set<Id> dsiSetForAggregateAI;
    Map<Id, Double> dsiIdToAggregateValue;
    private Set<Id> AIAccounts;
    private Set<String> AISKUs = new Set<String>{'16091', '16094', '89408', '89406', '89407'};

    public Integer getRecursionDepth() {
        return 0;
    }

    public Integer getMaxRecursionDepthAllowed() {
        return 0;
    }
    public Boolean isEnabled() {
        return true;
    }

    public void bulkBefore() {
        subscriptions = new List<SBQQ__Subscription__c>();
        subs2StampKeyGroup = new List<SBQQ__Subscription__c>();
        subs2StampRelatedContracts = new Set<Id>();
    }

    public void bulkAfter() {
        subscriptionsEduCode = new Set<Id>();
        subscriptions = new List<SBQQ__Subscription__c>();
        ql2Subs = new Map<Id, SBQQ__Subscription__c>();
        ctList = new Set<Id>();
        subs = new List<Id>();
        subs2stampNextKeyShipmentDateOnDSI = new List<SBQQ__Subscription__c>();
        contracts = new Set<Id>();
        dsiSet = new Set<Id>();
        subsRelatedContracts = new Set<Id>();
        dsiIdsKeyShipment = new Set<Id>();
        dsiSetForAI = new Set<Id>();
        dsiSetForAggregateAI = new Set<Id>();
        dsiIdToAggregateValue = new Map<Id, Double>();
        AIAccounts = new Set<Id>();
    }

    public void beforeInsert(SObject so) {
        SBQQ__Subscription__c newSubscription = (SBQQ__Subscription__c) so;
        if (newSubscription.SBQQ__OriginalQuoteLine__c != null) {
            subscriptions.add(newSubscription);
        }
        if (newSubscription.Edu_CouponCode__c.contains('ANL') || newSubscription.Edu_CouponCode__c.contains('ARC')) {
            newSubscription.Notification_Trigger_Date_dtm__c = System.now();
        }
        if (newSubscription.Quote_Line_Group__c != null && newSubscription.Quote_Line_Group__c != 0 && !newSubscription.SBQQ__ProductName__c.containsIgnoreCase('Education')) {
            subs2StampKeyGroup.add(newSubscription);
            if (newSubscription.SBQQ__Contract__c != null) {
                subs2StampRelatedContracts.add(newSubscription.SBQQ__Contract__c);
            }
        }
        if(newSubscription.Product_Code__c != NULL && newSubscription.Product_Code__c == '16091') {
            newSubscription.AI__c = true;
            newSubscription.AI_Questions_Entitlement__c = 20000 * newSubscription.SBQQ__Quantity__c;
        }
    }

    public void afterInsert(SObject so) {
        SBQQ__Subscription__c newSubscription = (SBQQ__Subscription__c) so;
        if (newSubscription.Edu_CouponCode__c.contains('ANL') || newSubscription.Edu_CouponCode__c.contains('ARC')) {
            subscriptionsEduCode.add(newSubscription.Id);
        }
        if (newSubscription.SBQQ__QuoteLine__c != null) {
            ql2Subs.put(newSubscription.SBQQ__QuoteLine__c, newSubscription);
        }

        if (newSubscription.SBQQ__Contract__c <> null) {
            ctList.add(newSubscription.SBQQ__Contract__c);
            contracts.add(newSubscription.SBQQ__Contract__c);
        }
        //HDR 745172 - Nogueira Filtered out if No DSI is present
        if (newSubscription.SBQQ__StartDate__c != null && newSubscription.SBCF_DSI__c != null) {
            subs2stampNextKeyShipmentDateOnDSI.add(newSubscription);
            if (newSubscription.SBCF_DSI__c != null) {
                dsiIdsKeyShipment.add(newSubscription.SBCF_DSI__c);
            }
        }
        if(newSubscription.AI__c == true) {
            dsiSetForAI.add(newSubscription.SBCF_DSI__c);
        }
        if(newSubscription.Product_Code__c != null && AISKUs.contains(newSubscription.Product_Code__c)
            && ((newSubscription.Depleted_date__c == null || newSubscription.Depleted_date__c >= Date.today()) && newSubscription.SBQQ__EndDate__c >= Date.today())) {
                AIAccounts.add(newSubscription.SBQQ__Account__c);
        }

        dsiIdToAggregateValue = getDSIAggregateMap(newSubscription, dsiIdToAggregateValue);

        subs.add(newSubscription.Id);
        if(newSubscription.SBQQ__Contract__c != null){ //&& newSubscription.SBQQ__Contract__c != 'null') { //GG: Commented since it was blocking Contract creation 
            subsRelatedContracts.add(newSubscription.SBQQ__Contract__c);
        }
    }

    public void beforeUpdate(SObject oldObject, SObject newObject) {
        SBQQ__Subscription__c newSubscription = (SBQQ__Subscription__c) newObject;
        SBQQ__Subscription__c oldSubscription = (SBQQ__Subscription__c) oldObject;
        if (newSubscription.SBQQ__OriginalQuoteLine__c != null) {
            subscriptions.add(newSubscription);
        }
        if(newSubscription.AI_questions_used__c != oldSubscription.AI_questions_used__c && 
        newSubscription.AI_Questions_Entitlement__c != null &&
        newSubscription.AI_Questions_Entitlement__c - newSubscription.AI_questions_used__c == 0) {
            newSubscription.Depleted_date__c = Date.today();
        }
    }

    public void afterUpdate(SObject oldSo, SObject so) {
        SBQQ__Subscription__c oldSubscription = (SBQQ__Subscription__c) oldSo;
        SBQQ__Subscription__c newSubscription = (SBQQ__Subscription__c) so;

        if (oldSubscription.KeyShipped__c != newSubscription.KeyShipped__c || oldSubscription.SBQQ__StartDate__c != newSubscription.SBQQ__StartDate__c
            || oldSubscription.SBQQ__EndDate__c != newSubscription.SBQQ__EndDate__c || oldSubscription.SBQQ__TerminatedDate__c != newSubscription.SBQQ__TerminatedDate__c
            || oldSubscription.SBCF_DSI__c != newSubscription.SBCF_DSI__c) {
            subs2stampNextKeyShipmentDateOnDSI.add(newSubscription);
            if (newSubscription.SBCF_DSI__c != null) {
                dsiIdsKeyShipment.add(newSubscription.SBCF_DSI__c);
            }
            if(oldSubscription.SBCF_DSI__c!= null && oldSubscription.SBCF_DSI__c != newSubscription.SBCF_DSI__c){
                dsiIdsKeyShipment.add(oldSubscription.SBCF_DSI__c);
            }
        }

        Boolean datesChanged = newSubscription.SBQQ__Contract__c <> null;
        datesChanged &= oldSubscription.SBQQ__SubscriptionEndDate__c <> newSubscription.SBQQ__SubscriptionEndDate__c || oldSubscription.SBQQ__SubscriptionStartDate__c <> newSubscription.SBQQ__SubscriptionStartDate__c;


        if (datesChanged == true) {
            contracts.add(newSubscription.SBQQ__Contract__c);
        }

        Boolean subTerminated = oldSubscription.SBQQ__TerminatedDate__c <> newSubscription.SBQQ__TerminatedDate__c;
        subTerminated &= newSubscription.SBQQ__TerminatedDate__c <> null && newSubscription.SBCF_DSI__c <> null;
        if (subTerminated == true) {
            dsiSet.add(newSubscription.SBCF_DSI__c);
        }

        if(newSubscription.AI_questions_used__c != null && newSubscription.AI_questions_used__c != oldSubscription.AI_questions_used__c &&
        newSubscription.SBCF_DSI__c != null) {
            dsiSetForAggregateAI.add(newSubscription.SBCF_DSI__c);
        }
        if(newSubscription.Product_Code__c != null && AISKUs.contains(newSubscription.Product_Code__c)
            && (newSubscription.Depleted_date__c != null && newSubscription.Depleted_date__c <= Date.today() || newSubscription.SBQQ__EndDate__c < Date.today())) {
                AIAccounts.add(newSubscription.SBQQ__Account__c);
        }
    }

    public void beforeDelete(SObject so) {

    }

    public void afterDelete(SObject so) {

    }

    public void afterUndelete(SObject so) {

    }

    public void andFinally() {
        //Variables: Related queried sObjects and related sObjects to perform DMLs
        Map<Id, OrderItem> OIsToUpdate = new Map<Id, OrderItem>();
        Map<Id, Contract> contractsToUpdate = new Map<Id, Contract>();
        Map<Id, SBQQ__Subscription__c> subscriptionsToUpdate = new Map<Id, SBQQ__Subscription__c>();
        Map<Id, DSI__c> DSIsToUpdate = new Map<Id, DSI__c>();
        Map<Id, Entitlement> entitlementsToUpdate = new Map<Id, Entitlement>();
        Map<Id, DSI__c> dsisToUpdateForAI = new Map<Id, DSI__c>();
        List<DSI__c> dsiList = new List<DSI__c>();
        List<AggregateResult> aggrDSI = new List<AggregateResult>();
        List<Account> accountsToUpdate = new List<Account>();

        //Dynamic queries
        Map<Id, SBQQ__Subscription__c> subscriptionRelatedObjects = new Map<Id, SBQQ__Subscription__c>();
        String subsQueryFields = 'SELECT Id';
        String subsQueryCondition = 'FROM SBQQ__Subscription__c WHERE ';
        String subsQueryOrder = 'ORDER BY ';
        Map<Id, OrderItem> queriedOIs = new Map<Id, OrderItem>();
        String OIQueryFields = 'SELECT Id';
        String OIQueryCondition = 'FROM OrderItem WHERE ';
        Map<Id, DSI__c> queriedDSIs = new Map<Id, DSI__c>();
        String DSIQueryFields = 'SELECT Id';
        String DSIQueryCondition = 'FROM DSI__c WHERE ';

        /************************************************************************************************************
            Subscriptions and their Related sObjects 
        *************************************************************************************************************/
        //Before Insert / Before Update
        if (subscriptions != null && !subscriptions.isEmpty()) {
            subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBQQ__Opportunity2__r.RecordType.Name'; //updateOriginalQuoteLine
            subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__r.SBCF_Co_termed__c, SBQQ__QuoteLine__r.SBQQ__EndDate__c, SBQQ__QuoteLine__c';
            subsQueryCondition += 'OR Id IN: subscriptions ';
        }
        //Before Insert
        if (subs2StampKeyGroup != null && !subs2StampKeyGroup.isEmpty() && subs2StampRelatedContracts != null && !subs2StampRelatedContracts.isEmpty()) {
            subsQueryFields += ',SBQQ__StartDate__c, Quote_Line_Group__c, Key_Group_multi__c, SBQQ__Product__c, SBQQ__ProductName__c, KeyShipped__c'; //stampKeyGroup
            subsQueryFields += ',SBQQ__Quantity__c, SBQQ__Contract__c';
            subsQueryCondition += 'OR SBQQ__Contract__c IN: subs2StampRelatedContracts ';
        }

        //After Insert
        if (subscriptionsEduCode != null && !subscriptionsEduCode.isEmpty()) {
            subsQueryFields += ',Edu_CouponCode__c, Product_Code__c,SBQQ__EndDate__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.MYC__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBQQ__Type__c '; //publishPlatformEvents
            if(!subsQueryFields.contains(', SBQQ__ProductName__c')){
                subsQueryFields += ', SBQQ__ProductName__c';
            }
            if (!subsQueryFields.contains('SBQQ__QuoteLine__r.SBQQ__Quote__c')) {
                subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__c';
            }
            subsQueryFields += ',SBQQ__Account__r.Name, SBQQ__Account__r.Owner.Email';
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__Quote__c, SBQQ__Contract__r.SBQQ__Quote__r.Name, SBQQ__QuoteLine__r.SBQQ__Quote__r.Name';
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__Quote__r.SBCF_Ship_to_Contact__r.FirstName, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBCF_Ship_to_Contact__r.FirstName';
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__Quote__r.SBCF_Ship_To_Contact_Email__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBCF_Ship_To_Contact_Email__c';
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__Opportunity__r.Owner.Email, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBQQ__Opportunity2__r.Owner.Email';
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__Opportunity__r.Business_Executive__r.Email, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBQQ__Opportunity2__r.Business_Executive__r.Email';
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__Opportunity__r.OwnerId, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBQQ__Opportunity2__r.OwnerId';
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__Opportunity__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBQQ__Opportunity2__c';
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__Opportunity__r.SBCF_Type_of_Sale__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBQQ__Opportunity2__r.SBCF_Type_of_Sale__c';
            if (!subsQueryFields.contains('SBQQ__Contract__c')) {
                subsQueryFields += ',SBQQ__Contract__c';
            }
            if (!subsQueryFields.contains('SBQQ__Quantity__c')) {
                subsQueryFields += ',SBQQ__Quantity__c';
            }
            subsQueryCondition += 'OR Id IN: subscriptionsEduCode ';
        }
        if (ctList != null && !ctList.isEmpty()) {
            if (!subsQueryFields.contains('SBQQ__Contract__c')) {
                subsQueryFields += ',SBQQ__Contract__c';
            }
            subsQueryFields += ',SBQQ__Contract__r.SBQQ__RenewalQuoted__c'; //reTriggerRenewalCreation
            if (!subsQueryFields.contains('SBQQ__QuoteLine__r.SBQQ__Quote__c')) { //setContractFields
                subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__c';
            }
            subsQueryFields += ',SBQQ__QuoteLine__c';
            subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__r.Net_License__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.Net_Education__c'; //setContractFields: Quote query
            subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__r.Net_Consulting__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBCF_Bill_to_Contact_Address__c';
            subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__r.Bill_To_Book__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.SBCF_Bill_to_Contact__c';
            subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__r.SBCF_Ship_To_Contact_Address__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.Ship_To_Book__c';
            subsQueryFields += ',SBQQ__QuoteLine__r.SBQQ__Quote__r.SBCF_Ship_to_Contact__c';

            subsQueryCondition += 'OR SBQQ__Contract__c IN: ctList ';
        }
        if (AIAccounts != null && !AIAccounts.isEmpty()) {
            if (!subsQueryFields.contains('SBQQ__EndDate__c')) { 
                subsQueryFields += ',SBQQ__EndDate__c';
            }
            if (!subsQueryFields.contains('Product_Code__c')) { //updateAIAccounts
                subsQueryFields += ',Product_Code__c';
            }
            subsQueryFields += ',SBQQ__Account__c, SBQQ__Account__r.AI_Expiration_Date__c, Depleted_date__c'; //updateAIAccounts

            subsQueryCondition += 'OR SBQQ__Account__c IN: AIAccounts ';
        }
        if (subs != null && !subs.isEmpty()) {
            if (!subsQueryFields.contains('SBQQ__Contract__c')) {
                subsQueryFields += ',SBQQ__Contract__c';
            }
            subsQueryFields += ',SBQQ__SubscriptionEndDate__c, SBQQ__Contract__r.EndDate, SBQQ__RenewalQuantity__c'; //contractExtension
            subsQueryCondition += 'OR Id IN: subs ';

            if (subsRelatedContracts != null && !subsRelatedContracts.isEmpty()) {
                subsQueryFields += ',SBQQ__Contract__r.StartDate'; //contractExtension: Contract query
                if (!subsQueryFields.contains('SBQQ__Quantity__c')) {
                    subsQueryFields += ',SBQQ__Quantity__c';
                }
                subsQueryCondition += 'OR SBQQ__Contract__c IN: subsRelatedContracts ';
                subsQueryOrder += 'SBQQ__SubscriptionEndDate__c DESC';
            }
        }

        if(dsiSetForAggregateAI != null && !dsiSetForAggregateAI.isEmpty()) {
            aggrDSI = [
                SELECT SBCF_DSI__c, SUM(Remaining_AI_Questions__c) 
                FROM SBQQ__Subscription__c 
                WHERE SBCF_DSI__c IN:dsiSetForAggregateAI 
                AND (Depleted_date__c > TODAY
                OR Depleted_date__c = NULL)
                AND SBQQ__EndDate__c > TODAY
                AND Remaining_AI_Questions__c != NULL
                Group BY SBCF_DSI__c
                ];
        }

        //After Update
        if(!aggrDSI.isEmpty()) {
            Map<Id, Double> dsiIdWithAggregateValue = new Map<Id, Double>();
            for(AggregateResult ar : aggrDSI) {
                dsiIdWithAggregateValue.put((Id)ar.get('SBCF_DSI__c'), (Double)ar.get('expr0'));
            }
            for(DSI__c dsi : [SELECt Id, AI_Remaining__c FROM DSI__c WHERE Id IN :dsiIdWithAggregateValue.keySet()]) {
                dsi.AI_Remaining__c = dsiIdWithAggregateValue.get(dsi.Id);
                dsiList.add(dsi);
            }
        }

        //After Insert
        if(dsiSetForAI != NULL && !dsiSetForAI.isEmpty()) {
            dsisToUpdateForAI = new Map<Id, DSI__c>([SELECt Id, AI__c, AI_Status__c FROM DSI__c WHERE Id IN :dsiSetForAI AND AI__c = FALSE]);
            for(DSI__c dsi : dsisToUpdateForAI.values()) {
                dsi.AI__c = true;
                dsi.AI_Status__c = 'Active';
            }
        }

        //After Insert
        if (dsiIdToAggregateValue != null) {
            for(DSI__c dsi : [SELECT Id, AI_Remaining__c FROM DSI__c WHERE Id IN:dsiIdToAggregateValue.keySet()]) {
                dsi.AI_Remaining__c = dsi.AI_Remaining__c != null ? dsi.AI_Remaining__c + dsiIdToAggregateValue.get(dsi.Id) : dsiIdToAggregateValue.get(dsi.Id); 
                dsiList.add(dsi);
            }
        }

        //After Insert / After Update
        if (subs2stampNextKeyShipmentDateOnDSI != null && !subs2stampNextKeyShipmentDateOnDSI.isEmpty()) {
            subsQueryFields +=',SBQQ__StartDate__c, KeyShipped__c, SBCF_DSI__c, SBCF_DSI__r.Next_Key_Shipment_Date__c, SBQQ__TerminatedDate__c'; //UpdateNextKeyShipment
            subsQueryFields += ',SBQQ__Product__r.Product_Selection_Group__c';
            if(!subsQueryFields.contains(', SBQQ__ProductName__c')){
                subsQueryFields += ', SBQQ__ProductName__c';
            }
            if(!subsQueryFields.contains('SBQQ__Product__c')){
                subsQueryFields += ',SBQQ__Product__c';
            }
            if (!subsQueryFields.contains('Key_Group_multi__c')) {
                subsQueryFields += ',Key_Group_multi__c';
            }
            subsQueryCondition += 'OR SBCF_DSI__c IN :dsiIdsKeyShipment ';
        }

        if (contracts != null && !contracts.isEmpty()) {
            if (!subsQueryFields.contains('SBQQ__Contract__c')) { //subRenewal: Contract query
                subsQueryFields += ',SBQQ__Contract__c';
            }
            if (!subsQueryFields.contains('SBQQ__Contract__r.StartDate')) {
                subsQueryFields += ',SBQQ__Contract__r.StartDate';
            }
            if (!subsQueryFields.contains('SBQQ__Contract__r.EndDate')) {
                subsQueryFields += ',SBQQ__Contract__r.EndDate';
            }
            subsQueryFields += ',SBQQ__Contract__r.ContractTerm';
            subsQueryFields += ',SBQQ__SubscriptionStartDate__c';
            if (!subsQueryFields.contains('SBQQ__SubscriptionEndDate__c')) {
                subsQueryFields += ',SBQQ__SubscriptionEndDate__c';
            }
            if (!subsQueryFields.contains('SBQQ__RenewalQuantity__c')) {
                subsQueryFields += ',SBQQ__RenewalQuantity__c';
            }
            if (!subsQueryFields.contains('SBQQ__Quantity__c')) {
                subsQueryFields += ',SBQQ__Quantity__c';
            }
            if(!subsQueryFields.contains('SBQQ__QuoteLine__r.SBQQ__EndDate__c')){
                subsQueryFields += ', SBQQ__QuoteLine__r.SBQQ__EndDate__c';
            }
            subsQueryCondition += 'OR SBQQ__Contract__c IN: contracts ';
            subsQueryOrder += String.isEmpty(subsQueryOrder) ? 'SBQQ__SubscriptionEndDate__c DESC' : '';
        }

        //Join Subscription Query parameters
        if ((subsQueryFields + subsQueryCondition).containsIgnoreCase('WHERE OR')) {
            String tempQuery = subsQueryFields + ' ' + subsQueryCondition;
            tempQuery = tempQuery.replace('WHERE OR', 'WHERE');
            if (subsQueryOrder.containsIgnoreCase('DESC')) {
                tempQuery += subsQueryOrder;
            }

                tempQuery += ' LIMIT 40000';

            subscriptionRelatedObjects = new Map<Id, SBQQ__Subscription__c>((List<SBQQ__Subscription__c>) Database.query(tempQuery));
        }


        /************************************************************************************************************
           Related Order Items 
        *************************************************************************************************************/
        //After Insert
        if (ql2Subs != null && !ql2Subs.isEmpty()) {
            Set<ID> subsKeyset = ql2Subs.keySet();
            OIQueryFields += ',SBQQ__QuoteLine__c'; //populateSubsOnOrderItems
            OIQueryCondition += 'OR SBQQ__QuoteLine__c IN:  subsKeyset ';
        }

        //Join Order Item Query parameters
        if ((OIQueryFields + OIQueryCondition).containsIgnoreCase('WHERE OR')) {
            String tempQuery = OIQueryFields + ' ' + OIQueryCondition;
            tempQuery = tempQuery.replace('WHERE OR', 'WHERE');
            queriedOIs = new Map<Id, OrderItem>((List<OrderItem>) Database.query(tempQuery));
        }


        /************************************************************************************************************
           Related DSI
        *************************************************************************************************************/
        //After Update
        if (dsiSet != null && !dsiSet.isEmpty()) {
            DSIQueryFields += ',(SELECT Id, SBQQ__TerminatedDate__c FROM Subscriptions__r)'; //entitlementTermination
            DSIQueryFields += ',(SELECT Id, EndDate FROM Entitlements__r)';
            DSIQueryCondition += 'OR Id IN: dsiSet ';
        }

        //Join DSI Query parameters
        if ((DSIQueryFields + DSIQueryCondition).containsIgnoreCase('WHERE OR')) {
            String tempQuery = DSIQueryFields + ' ' + DSIQueryCondition;
            tempQuery = tempQuery.replace('WHERE OR', 'WHERE');
            queriedDSIs = new Map<Id, DSI__c>((List<DSI__c>) Database.query(tempQuery));
        }

        /************************************************************************************************************
            Before Insert
        *************************************************************************************************************/
        //Also used in Before Update
        if (subscriptions != null && !subscriptions.isEmpty()) {
            SubscriptionController.updateOriginalQuoteLine(subscriptions, subscriptionRelatedObjects);
        }
        if (subs2StampKeyGroup != null && !subs2StampKeyGroup.isEmpty()) {
            SubscriptionController.stampKeyGroup(subs2StampKeyGroup, subscriptionsToUpdate, subscriptionRelatedObjects);
        }

        /************************************************************************************************************
            After Insert
        *************************************************************************************************************/
        if (ql2Subs != null && !ql2Subs.isEmpty()) {
            SubscriptionController.populateSubsOnOrderItems(ql2Subs, OIsToUpdate, queriedOIs);
        }
        if (subscriptionsEduCode != null && !subscriptionsEduCode.isEmpty()) {
            SubscriptionController.publishPlatformEvents(subscriptionsEduCode, subscriptionsToUpdate, subscriptionRelatedObjects);
        }
        if (ctList != null && !ctList.isEmpty()) {
            SubscriptionController.setContractFields(ctList, contractsToUpdate, subscriptionRelatedObjects);
        }
        //Also used in After Update
        if (subs2stampNextKeyShipmentDateOnDSI != null && !subs2stampNextKeyShipmentDateOnDSI.isEmpty()) {
            SubscriptionController.UpdateNextKeyShipment(subs2stampNextKeyShipmentDateOnDSI, DSIsToUpdate, subscriptionRelatedObjects);
        }
        if (contracts != null && !contracts.isEmpty()) {
            SubscriptionController.subRenewal(contracts, contractsToUpdate, subscriptionsToUpdate, subscriptionRelatedObjects);
        }
        
        if (subs != null && !subs.isEmpty()) {
            SubscriptionController.contractExtension(subs, contractsToUpdate, subscriptionsToUpdate, subscriptionRelatedObjects);
        }
        
        if (ctList != null && !ctList.isEmpty()) {
            SubscriptionController.reTriggerRenewalCreation(ctList, contractsToUpdate, subscriptionRelatedObjects);
        }

        /************************************************************************************************************
            After Insert / After Update
        *************************************************************************************************************/
        if (AIAccounts != null && !AIAccounts.isEmpty()) {
            SubscriptionController.updateAIAccounts(AIAccounts, accountsToUpdate, subscriptionRelatedObjects);
        }

        /************************************************************************************************************
            After Update
        *************************************************************************************************************/
        if (dsiSet != null && !dsiSet.isEmpty()) {
            SubscriptionController.entitlementTermination(dsiSet, entitlementsToUpdate, queriedDSIs);
        }

        /************************************************************************************************************
            DMLs
        *************************************************************************************************************/
        if (OIsToUpdate != null && !OIsToUpdate.isEmpty()) {
            Database.update(OIsToUpdate.values());
        }
        if (contractsToUpdate != null && !contractsToUpdate.isEmpty()) {
            Database.update(contractsToUpdate.values());
        }
        if (subscriptionsToUpdate != null && !subscriptionsToUpdate.isEmpty()) {
            Database.update(subscriptionsToUpdate.values());
        }
        if (DSIsToUpdate != null && !DSIsToUpdate.isEmpty()) {
            Database.update(DSIsToUpdate.values());
        }
        if (entitlementsToUpdate != null && !entitlementsToUpdate.isEmpty()) {
            Database.update(entitlementsToUpdate.values());
        }
        if (dsisToUpdateForAI != null && !dsisToUpdateForAI.isEmpty()) {
            Database.update(dsisToUpdateForAI.values());
        }
        if (dsiList != null && !dsiList.isEmpty()) {
            Database.update(dsiList, false);
        }
        if (accountsToUpdate != null && !accountsToUpdate.isEmpty()) {
            Database.update(accountsToUpdate);
        }
    }

    public Map<Id, Double> getDSIAggregateMap(SBQQ__Subscription__c subs, Map<Id, Double> dsiIdToAggregateValue) {
        if(Test.isRunningTest()) {
            return null;
        }
        if(((subs.Depleted_date__c != NULL && subs.Depleted_date__c > Date.today()) || 
        subs.Depleted_date__c == NULL) && subs.SBQQ__EndDate__c != NULL && subs.SBQQ__EndDate__c > Date.today() &&
        subs.SBCF_DSI__c != null && subs.Remaining_AI_Questions__c != null) {
            if (!dsiIdToAggregateValue.containsKey(subs.SBCF_DSI__c)) {
                dsiIdToAggregateValue.put(subs.SBCF_DSI__c, subs.Remaining_AI_Questions__c);
            } else {
                dsiIdToAggregateValue.put(subs.SBCF_DSI__c, dsiIdToAggregateValue.get(subs.SBCF_DSI__c) + subs.Remaining_AI_Questions__c);
            }
            return dsiIdToAggregateValue;
        }
        return null;
    }
}