/**
 * Created by <PERSON><PERSON><PERSON> on 04/06/2024.
 * test class - PartnerLineTriggerTest
 */

public with sharing class PartnerLineController {

    public static void handleUpdate(Map<Id, Partner_Line__c>newPlMap, Map<Id, Partner_Line__c>oldPlMap) {
        Set<Id>oppIds = new Set<Id>();
        List<SBQQ__Quote__c> quotes = new List<SBQQ__Quote__c>();
        Set<SBQQ__Quote__c> quotes2update = new Set<SBQQ__Quote__c>();
        Map<Id, List<Partner_Line__c>> opp2queriedPartners = new Map<Id, List<Partner_Line__c>>();
        Map<Id, List<Partner_Line__c>> opp2updatedPartners = new Map<Id, List<Partner_Line__c>>();

        for (Partner_Line__c pl : newPlMap.values()) {
            if (pl.Account__c != oldPlMap.get(pl.Id).Account__c ||
                    pl.Partner_Type__c != oldPlMap.get(pl.Id).Partner_Type__c ||
                    pl.Motion__c != oldPlMap.get(pl.Id).Motion__c) {
                if (!opp2updatedPartners.containsKey(pl.Opportunity__c))
                    opp2updatedPartners.put(pl.Opportunity__c, new List<Partner_Line__c>());
                opp2updatedPartners.get(pl.Opportunity__c).add(pl);
                oppIds.add(pl.Opportunity__c);
            }
        }

        if (!oppIds.isEmpty()) {
            List<Opportunity> queriedOpps = [SELECT Id FROM Opportunity WHERE Id IN :oppIds];
            quotes = [SELECT Id, SBQQ__Partner__c, SBQQ__Opportunity2__c, SBQQ__Distributor__c, Reseller__c, SBQQ__Status__c FROM SBQQ__Quote__c WHERE SBQQ__Opportunity2__c IN :oppIds];

            for (Partner_Line__c partner : [SELECT Id, Opportunity__c, Account__c, Partner_Type__c, Motion__c, CreatedDate FROM Partner_Line__c WHERE Opportunity__c IN :oppIds AND ((Partner_Type__c = 'Sales Partner' AND Motion__c LIKE '%Resell') OR Partner_Type__c = 'Distributor') ORDER BY CreatedDate]) {
                if (!opp2queriedPartners.containsKey(partner.Opportunity__c)) {
                    opp2queriedPartners.put(partner.Opportunity__c, new List<Partner_Line__c>());
                }
                opp2queriedPartners.get(partner.Opportunity__c).add(partner);
            }

            if (!queriedOpps.isEmpty()) {
                for (Opportunity opp : queriedOpps) {
                    Partner_Line__c reseller;
                    Partner_Line__c distributor;
                    Partner_Line__c primarySalesPartner;
                    Boolean resellerSet = false;
                    Boolean distributorSet = false;

                    for (Partner_Line__c pl : opp2updatedPartners.get(opp.Id)) {
                        if (!resellerSet && pl.Partner_Type__c == 'Sales Partner' && pl.Motion__c != null && pl.Motion__c.contains('Resell')) {
                            resellerSet = true;
                            reseller = pl;
                        } else if (!distributorSet && pl.Partner_Type__c == 'Distributor') {
                            distributorSet = true;
                            distributor = pl;
                        }
                    }

                    if (opp2queriedPartners.get(opp.Id) != null) {
                        for (Partner_Line__c pl : opp2queriedPartners.get(opp.Id)) {
                            if (pl.Partner_Type__c == 'Sales Partner' && (newPlMap.get(pl.Id) == null || newPlMap.get(pl.Id).Partner_Type__c == 'Sales Partner')) {
                                if (pl.Motion__c != null && pl.Motion__c.contains('Resell') && (newPlMap.get(pl.Id) == null || (newPlMap.get(pl.Id).Motion__c != null && newPlMap.get(pl.Id).Motion__c.contains('Resell')))) {
                                    if(resellerSet){
                                        reseller = pl.CreatedDate < reseller.CreatedDate ? pl : reseller;
                                    } else {
                                        reseller = (newPlMap.get(pl.Id) == null) ? pl : newPlMap.get(pl.Id);
                                        resellerSet = true;
                                    }
                                }
                            } else if (pl.Partner_Type__c == 'Distributor' && (newPlMap.get(pl.Id) == null || newPlMap.get(pl.Id).Partner_Type__c == 'Distributor')) {
                                if(distributorSet){
                                    distributor = pl.CreatedDate < distributor.CreatedDate ? pl : distributor;
                                } else {
                                    distributor = (newPlMap.get(pl.Id) == null) ? pl : newPlMap.get(pl.Id);
                                    distributorSet = true;
                                }
                            }
                        }
                    }
                    if (reseller != null || distributor != null) {
                        if (!quotes.isEmpty()) {
                            for (SBQQ__Quote__c q : quotes) {
                                if(distributor != null) {
                                    primarySalesPartner = distributor;
                                } else {
                                    primarySalesPartner = reseller;
                                }
                                if (q.SBQQ__Opportunity2__c == opp.Id && (q.SBQQ__Partner__c == null || q.SBQQ__Partner__c != primarySalesPartner.Account__c) && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))) {
                                    q.SBQQ__Partner__c = primarySalesPartner.Account__c;
                                    quotes2update.add(q);
                                }
                            }
                        }
                    } else {
                        for (SBQQ__Quote__c q : quotes) {
                            if (q.SBQQ__Opportunity2__c == opp.Id && q.SBQQ__Partner__c != null && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))) {
                                q.SBQQ__Partner__c = null;
                                quotes2update.add(q);
                            }
                        }
                    }
                    if (distributor != null) {
                        if (!quotes.isEmpty()) {
                            for (SBQQ__Quote__c q : quotes) {
                                if (q.SBQQ__Opportunity2__c == opp.Id && q.SBQQ__Distributor__c != distributor.Account__c && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))) {
                                    q.SBQQ__Distributor__c = distributor.Account__c;
                                    quotes2update.add(q);
                                }
                            }
                        }
                    } else {
                        if (!quotes.isEmpty()) {
                            for (SBQQ__Quote__c q : quotes) {
                                if(q.SBQQ__Opportunity2__c == opp.Id && (q.SBQQ__Distributor__c == null || q.SBQQ__Distributor__c != null) && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))){
                                    q.SBQQ__Distributor__c = null;
                                    quotes2update.add(q);
                                }
                            }
                        }
                    }
                    if (reseller != null) {
                        if (!quotes.isEmpty()) {
                            for (SBQQ__Quote__c q : quotes) {
                                if (q.SBQQ__Opportunity2__c == opp.Id && (q.Reseller__c == null || q.Reseller__c != reseller.Account__c) && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))) {
                                    q.Reseller__c = reseller.Account__c;
                                    quotes2update.add(q);
                                }
                            }
                        }
                    } else {
                        if (!quotes.isEmpty()) {
                            for (SBQQ__Quote__c q : quotes) {
                                if(q.SBQQ__Opportunity2__c == opp.Id && q.Reseller__c != null && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))){
                                    q.Reseller__c = null;
                                    quotes2update.add(q);
                                }
                            }
                        }
                    }


                }
            }

            List<SBQQ__Quote__c>quoteList2update = new List<SBQQ__Quote__c>();
            if (!quotes2update.isEmpty()) {
                for(SBQQ__Quote__c q : quotes2update){
                    if(!quoteList2update.contains(q)){
                        quoteList2update.add(q);
                    }
                }
            }
            if(!quoteList2update.isEmpty())
                update quoteList2update;
        }
    }

    public static void handleInsert(Map<Id, Partner_Line__c>partnerLines) {
        Set<Id>oppIds = new Set<Id>();
        List<Opportunity> opportunities = new List<Opportunity>();
        List<SBQQ__Quote__c> quotes = new List<SBQQ__Quote__c>();
        Map<Id, List<Partner_Line__c>> opp2queriedPartners = new Map<Id, List<Partner_Line__c>>();
        Set<SBQQ__Quote__c> quotes2update = new Set<SBQQ__Quote__c>();

        for (Partner_Line__c pl : partnerLines.values()) {
            if (pl.Partner_Type__c == 'Sales Partner' || pl.Partner_Type__c == 'Distributor') {
                oppIds.add(pl.Opportunity__c);
            }
        }

        if (!oppIds.isEmpty()) {
            opportunities = [SELECT Id FROM Opportunity WHERE Id IN :oppIds];
            quotes = [SELECT Id, SBQQ__Partner__c, SBQQ__Opportunity2__c, SBQQ__Distributor__c, Reseller__c, SBQQ__Status__c FROM SBQQ__Quote__c WHERE SBQQ__Opportunity2__c IN :oppIds];

            for (Partner_Line__c partner : [SELECT Id, Opportunity__c, Account__c, Partner_Type__c, Motion__c FROM Partner_Line__c WHERE Opportunity__c IN :oppIds AND (Partner_Type__c = 'Sales Partner' OR Partner_Type__c = 'Distributor') ORDER BY CreatedDate]) {
                if (!opp2queriedPartners.containsKey(partner.Opportunity__c))
                    opp2queriedPartners.put(partner.Opportunity__c, new List<Partner_Line__c>());
                opp2queriedPartners.get(partner.Opportunity__c).add(partner);
            }

            if (!opportunities.isEmpty()) {
                for (Opportunity opp : opportunities) {
                    if (opp2queriedPartners.get(opp.Id) != null && !opp2queriedPartners.get(opp.Id).isEmpty()) {
                        for (SBQQ__Quote__c q : quotes) {
                            Boolean distributorSet = false;
                            Boolean resellerSet = false;
                            if (q.SBQQ__Opportunity2__c == opp.Id && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))) {
                                for (Partner_Line__c pl : opp2queriedPartners.get(opp.Id)) {
                                    if (!distributorSet && pl.Partner_Type__c == 'Distributor') {
                                        distributorSet = true;
                                        if (q.SBQQ__Distributor__c != pl.Account__c) {
                                            q.SBQQ__Distributor__c = pl.Account__c;
                                            quotes2update.add(q);
                                        }
                                    } else if (!resellerSet && pl.Partner_Type__c == 'Sales Partner' && pl.Motion__c != null && pl.Motion__c.contains('Resell')) {
                                        resellerSet = true;
                                        if (q.Reseller__c != pl.Account__c) {
                                            q.Reseller__c = pl.Account__c;
                                            quotes2update.add(q);
                                        }
                                    }
                                    if(q.SBQQ__Distributor__c != null && q.SBQQ__Partner__c != q.SBQQ__Distributor__c){
                                        q.SBQQ__Partner__c = q.SBQQ__Distributor__c;
                                        quotes2update.add(q);
                                    } else if(q.SBQQ__Distributor__c == null && q.Reseller__c != null && q.SBQQ__Partner__c != q.Reseller__c){
                                        q.SBQQ__Partner__c = q.Reseller__c;
                                        quotes2update.add(q);
                                    }
                                    if (distributorSet && resellerSet)
                                        break;
                                }
                            }
                        }
                    }
                }

                List<SBQQ__Quote__c>quoteList2update = new List<SBQQ__Quote__c>();
                if (!quotes2update.isEmpty()) {
                    for(SBQQ__Quote__c q : quotes2update){
                        if(!quoteList2update.contains(q)){
                            quoteList2update.add(q);
                        }
                    }
                }
                if(!quoteList2update.isEmpty())
                    update quoteList2update;
            }
        }
    }


    public static void handleDelete(Map<Id, Partner_Line__c>partnerLineMap) {
        Set<Id>oppIds = new Set<Id>();
        List<Opportunity> opportunities = new List<Opportunity>();
        List<SBQQ__Quote__c> quotes = new List<SBQQ__Quote__c>();
        Set<SBQQ__Quote__c> quotes2update = new Set<SBQQ__Quote__c>();
        Map<Id, List<Partner_Line__c>> opp2partners = new Map<Id, List<Partner_Line__c>>();

        for (Partner_Line__c pl : partnerLineMap.values()) {
            if (pl.Partner_Type__c == 'Sales Partner' || pl.Partner_Type__c == 'Distributor') {
                oppIds.add(pl.Opportunity__c);
            }
        }

        if (!oppIds.isEmpty()) {
            opportunities = [SELECT Id  FROM Opportunity WHERE Id IN :oppIds];
            quotes = [SELECT Id, SBQQ__Partner__c, SBQQ__Opportunity2__c, SBQQ__Distributor__c, Reseller__c, SBQQ__Status__c FROM SBQQ__Quote__c WHERE SBQQ__Opportunity2__c IN :oppIds];

            List<Partner_Line__c>partnerLines = [SELECT Id, Opportunity__c, Account__c, Partner_Type__c, Motion__c FROM Partner_Line__c WHERE Opportunity__c IN :oppIds AND (Partner_Type__c = 'Sales Partner' OR Partner_Type__c = 'Distributor') ORDER BY CreatedDate];

            if (!opportunities.isEmpty()) {
                if (partnerLines != null && !partnerLines.isEmpty()) {
                    for (Opportunity opp : opportunities) {
                        for (Partner_Line__c pl : partnerLines) {
                            if (pl.Opportunity__c == opp.Id) {
                                if (opp2partners.get(opp.Id) == null)
                                    opp2partners.put(opp.Id, new List<Partner_Line__c>());
                                opp2partners.get(opp.Id).add(pl);
                            }
                        }
                    }

                    for (Opportunity opp : opportunities) {

                        Id previousSalesPartner;
                        Id previousDistributor;
                        Id previousReseller;
                        for (SBQQ__Quote__c q : quotes) {
                            Boolean distributorSet = false;
                            Boolean resellerSet = false;
                            if (q.SBQQ__Opportunity2__c == opp.Id && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))) {
                                previousSalesPartner = q.SBQQ__Partner__c;
                                previousReseller = q.Reseller__c;
                                previousDistributor = q.SBQQ__Distributor__c;

                                for(Partner_Line__c pl : opp2partners.get(opp.Id)){
                                    Boolean lastIteration = pl == opp2partners.get(opp.Id).get(opp2partners.get(opp.Id).size() - 1);
                                    if(pl.Partner_Type__c == 'Sales Partner'){
                                        if(!resellerSet && pl.Motion__c != null && pl.Motion__c.contains('Resell') && previousReseller != null){
                                            resellerSet = true;
                                            if(previousReseller != pl.Id){
                                                q.Reseller__c = pl.Account__c;
                                                quotes2update.add(q);
                                            }
                                        }
                                    } else if(pl.Partner_Type__c == 'Distributor' && !distributorSet && previousDistributor != null){
                                        distributorSet = true;
                                        if(previousDistributor != pl.Id){
                                            q.SBQQ__Distributor__c = pl.Account__c;
                                            quotes2update.add(q);
                                        }
                                    }
                                    if(lastIteration && !resellerSet && !distributorSet){
                                        if(q.SBQQ__Partner__c != null){
                                            q.SBQQ__Partner__c = null;
                                            quotes2update.add(q);
                                        }
                                    }
                                    if(lastIteration && !resellerSet){
                                        if(q.Reseller__c != null){
                                            q.Reseller__c = null;
                                            quotes2update.add(q);
                                        }
                                    }
                                    if(lastIteration && !distributorSet){
                                        if(q.SBQQ__Distributor__c != null){
                                            q.SBQQ__Distributor__c = null;
                                            quotes2update.add(q);
                                        }
                                    }
                                    if(q.SBQQ__Distributor__c != null && q.SBQQ__Partner__c != q.SBQQ__Distributor__c){
                                        q.SBQQ__Partner__c = q.SBQQ__Distributor__c;
                                        quotes2update.add(q);
                                    } else if(q.SBQQ__Distributor__c == null && q.Reseller__c != null && q.SBQQ__Partner__c != q.Reseller__c){
                                        q.SBQQ__Partner__c = q.Reseller__c;
                                        quotes2update.add(q);
                                    }
                                    if (distributorSet && resellerSet)
                                        break;
                                }
                            }
                        }

                    }

                } else {
                    for (Opportunity opp : opportunities) {
                        for (SBQQ__Quote__c q : quotes) {
                            if (q.SBQQ__Opportunity2__c == opp.Id && (q.SBQQ__Status__c.contains('Q01') || q.SBQQ__Status__c.contains('Q02'))){
                                if(q.SBQQ__Partner__c != null || q.SBQQ__Distributor__c != null || q.Reseller__c != null) {
                                    q.SBQQ__Partner__c = null;
                                    q.SBQQ__Distributor__c = null;
                                    q.Reseller__c = null;
                                    quotes2update.add(q);
                                }
                            }
                        }
                    }
                }
                List<SBQQ__Quote__c>quoteList2update = new List<SBQQ__Quote__c>();
                if (!quotes2update.isEmpty()) {
                    for(SBQQ__Quote__c q : quotes2update){
                        if(!quoteList2update.contains(q)){
                            quoteList2update.add(q);
                        }
                    }
                }
                if(!quoteList2update.isEmpty())
                    update quoteList2update;
            }


        }
    }

    public static void validateSourcedPartner(List<Partner_Line__c>partnerLines) {
        Set<Id> oppIds = new Set<Id>();
        for (Partner_Line__c pl : partnerLines) {
            if (pl.Sourced__c)
                oppIds.add(pl.Opportunity__c);
        }
        if(!oppIds.isEmpty()) {
            List<Partner_Line__c>sourcedPartners = [SELECT Id, Opportunity__c FROM Partner_Line__c WHERE Opportunity__c IN :oppIds AND Sourced__c = TRUE];
            if (sourcedPartners != null && !sourcedPartners.isEmpty()) {
                Map<Id, Boolean>opps2existingSPL = new Map<Id, Boolean>();
                for (Partner_Line__c pl : sourcedPartners) {
                    opps2existingSPL.put(pl.Opportunity__c, true);
                }

                for (Partner_Line__c pl : partnerLines) {
                    if (opps2existingSPL.get(pl.Opportunity__c)) {
                        pl.addError('Failed to create Partner Line. Please save your changes before creating Sourced Partner');
                    }
                }
            }
        }
    }


}