public class CpaiTemplateController {

    public static void populateTemplateInfo(Map<Id, SBQQ__Quote__c> quoteMap, Map<Id, List<SBQQ__QuoteLine__c>> quoteLineMap) {
        Set<Id>templates = new Set<Id>();

        for (SBQQ__Quote__c quote : quoteMap.values()) {
            templates.add(quote.CPAI_Template_Field__c);
        }
        Map<Id, CPAI_Template_Fields__c> templateMap = new Map<Id, CPAI_Template_Fields__c>([SELECT Id, Paperwork_Section_Headers__c FROM CPAI_Template_Fields__c WHERE Id IN :templates]);
        List<String> ppwHeaderValues;
        Integer softwareCounter;
        Integer serviceCounter;
        Integer paasCounter;
        Integer consultingCounter;
        Integer educationCounter;
        for (SBQQ__Quote__c quote : quoteMap.values()) {
            ppwHeaderValues = new List<String>();
            Boolean quoteMCE = quote.is_MCE__c ? true : quote.Platform__c == 'MCE' ? true : false;
            softwareCounter = 0;
            serviceCounter = 0;
            paasCounter = 0;
            consultingCounter = 0;
            educationCounter = 0;
            for (SBQQ__QuoteLine__c ql : quoteLineMap.get(quote.Id)) {
                if (quote.SBQQ__Type__c != 'Amendment' || (quote.SBQQ__Type__c == 'Amendment' && (
                        ql.SBQQ__UpgradedSubscription__c == null || (ql.SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__c != null
                                && ql.SBQQ__UpgradedSubscription__r.SBQQ__QuoteLine__r.SBQQ__Quantity__c != ql.SBQQ__Quantity__c)))) {
                    if (ql.PC__c == 'Software') {
                        softwareCounter++;
                    }
                    if (ql.PC__c == 'Service') {
                        if (ql.ProductFamilyLabel__c == 'MCE' || ql.ProductFamilyLabel__c == 'Cloud' || ql.ProductFamilyLabel__c == 'MCE Sup') {
                            paasCounter++;
                        } else if (ql.ProductFamilyLabel__c.containsIgnoreCase('education') || ql.ProductFamilyLabel__c == 'Event') {
                            educationCounter++;
                        } else if (ql.ProductFamilyLabel__c.containsIgnoreCase('consulting') || ql.ProductFamilyLabel__c == 'Expense') {
                            consultingCounter++;
                        } else {
                            serviceCounter++;
                        }
                    }
                }
            }

            if (softwareCounter > 0) {
                ppwHeaderValues.add(quoteMCE ? 'MCE Service - Software' : 'Software');
            }
            if (paasCounter > 0) {
                ppwHeaderValues.add(quoteMCE ? 'MCE Service - Additional PaaS Components' : 'Additional PaaS Components');
            }
            if (serviceCounter > 0 || educationCounter > 0 || consultingCounter > 0) {
                ppwHeaderValues.add(quoteMCE ? 'Other Services' : 'Services');
            }
            
            templateMap.get(quote.CPAI_Template_Field__c).Paperwork_Section_Headers__c = String.join(ppwHeaderValues, ';');

        }

        update templateMap.values();
    }

}