/**
 * Created by <PERSON><PERSON><PERSON> on 16/10/2024.
 */

@IsTest
private class CpaiTemplateControllerTest {
    @TestSetup
    static void initData(){
        List<Opportunity> opp = DataFactory.createOpportunities(0,1,true,null);
        List<SBQQ__Quote__c> quote = CPQTestDataFactory.CreateQuoteforOpptys(true, opp);

        List<Product2> createdProducts = CPQTestDataFactory.CreateProducts(true, new List<Product2>{
                new Product2(Product_Category__c = 'Software', Name = 'Software Product'),
                new Product2(Product_Category__c = 'Service', Family = 'Education', Name = ' Service Product'),
                new Product2(Product_Category__c = 'Service', Family = 'MCE', Name = 'PaaS Product')
        });

        Quote_Line_Security__c settings = Quote_Line_Security__c.getOrgDefaults();
        settings.All_Unlocked__c = true;
        settings.Unlocked_Statuses__c = 'Proposal generation with Account Team';
        upsert settings Quote_Line_Security__c.Id;
        SBQQ__QuoteLine__c ql = new SBQQ__QuoteLine__c(
                SBQQ__Quote__c = quote.get(0).Id,
                SBQQ__Product__c = createdProducts.get(0).Id,
                SBQQ__Quantity__c = 1
        );
        insert ql;

    }
    @IsTest
    static void testBehavior() {
        SBQQ__Quote__c quote = [SELECT Id, CPAI_Template_Field__c, Paperwork_Sections__c, SBQQ__Status__c FROM SBQQ__Quote__c LIMIT 1];
        List<Product2> products = [SELECT Id, Product_Category__c, Family FROM Product2];
        SBQQ__QuoteLine__c quoteLine = [SELECT Id, SBQQ__Quote__c, SBQQ__Product__c FROM SBQQ__QuoteLine__c WHERE SBQQ__Quote__c = :quote.Id LIMIT 1];

        System.assert(quote.CPAI_Template_Field__c != null);

        Test.startTest();

        CPAI_Template_Fields__c template = [SELECT Id, Paperwork_Section_Headers__c FROM CPAI_Template_Fields__c WHERE Id = :quote.CPAI_Template_Field__c];

        System.assertEquals('Software', template.Paperwork_Section_Headers__c);

        quoteLine.SBQQ__Product__c = products.get(1).Id;
        update quoteLine;
        update quote;
        template = [SELECT Id, Paperwork_Section_Headers__c FROM CPAI_Template_Fields__c WHERE Id = :quote.CPAI_Template_Field__c];

        System.assertEquals('Services', template.Paperwork_Section_Headers__c);

        quoteLine.SBQQ__Product__c = products.get(2).Id;
        update quoteLine;
        update quote;
        template = [SELECT Id, Paperwork_Section_Headers__c FROM CPAI_Template_Fields__c WHERE Id = :quote.CPAI_Template_Field__c];

        System.assertEquals('Additional PaaS Components', template.Paperwork_Section_Headers__c);
    }
}