/**
 * @description Test class for MCSEnvironmentController, MCSEnvironmentUpdatePayload 
 * This class covers success scenarios, error conditions, and edge cases for the
 * updateMCSEnvironment invocable method.
 */
@isTest
private class MCSEnvironmentControllerTest {

    /**
     * @description Sets up the necessary data for all test methods.
     * This includes creating an Account, Contact, DSI, Opportunity, Contract,
     * and a mock Custom Metadata record.
     */
    @testSetup
    static void makeData(){
       
        Account acc = new Account(Name='Test Account');
        insert acc;

        Contact con = new Contact(
            LastName='Test',
            FirstName='Contact',
            Email='<EMAIL>',
            AccountId=acc.Id
        );
        insert con;

        DSI__c dsi = new DSI__c(
            Name = 'Test DSI',
            Account__c = acc.Id,
            MCSEnvironmentName__c = 'env-test-123'
        );
        insert dsi;

        Opportunity opp = new Opportunity(
            Name = 'Test Opportunity',
            AccountId = acc.Id,
            StageName = 'Closed Won',
            CloseDate = System.today(),
            DSI__c = dsi.Id
        );
        insert opp;

        Contract cont = new Contract(
            AccountId = acc.Id,
            StartDate = System.today(),
            ContractTerm = 12,
            SBQQ__Opportunity__c = opp.Id
        );
        insert cont;
    }

    /**
     * @description Tests the successful execution path of the updateMCSEnvironment method.
     * Verifies that the callout is made correctly with a valid payload.
     */
    @isTest
    static void testUpdateEnvironment_SuccessPath() {
        // Set the mock for the HTTP callout
        Test.setMock(HttpCalloutMock.class, new MCSEnvironmentMock(200));

        Opportunity opp = [SELECT Id FROM Opportunity LIMIT 1];

        // Prepare the request for the invocable method
        MCSEnvironmentController.UpdateEnvironmentRequest request = new MCSEnvironmentController.UpdateEnvironmentRequest();
        request.opportunityId = opp.Id;
        List<MCSEnvironmentController.UpdateEnvironmentRequest> requests = new List<MCSEnvironmentController.UpdateEnvironmentRequest>{request};

        Test.startTest();
            // Call the method to be tested
            MCSEnvironmentController.updateMCSEnvironment(requests);
        Test.stopTest();

        // No explicit assert is needed here for the callout itself in this simple case.
        // The fact that the test runs without throwing an exception and the mock is set to 200
        // confirms the success path was executed. You can add more specific asserts
        // by enhancing the mock if needed.
    }

    /**
     * @description Tests the scenario where the API callout fails (e.g., returns a 500 error).
     * Verifies that the failure is handled gracefully.
     */
    @isTest
    static void testUpdateEnvironment_CalloutFailure() {
        // Set a mock that simulates a server error
        Test.setMock(HttpCalloutMock.class, new MCSEnvironmentMock(500));

        Opportunity opp = [SELECT Id FROM Opportunity LIMIT 1];

        MCSEnvironmentController.UpdateEnvironmentRequest request = new MCSEnvironmentController.UpdateEnvironmentRequest();
        request.opportunityId = opp.Id;
        List<MCSEnvironmentController.UpdateEnvironmentRequest> requests = new List<MCSEnvironmentController.UpdateEnvironmentRequest>{request};

        Test.startTest();
            // Call the method
            MCSEnvironmentController.updateMCSEnvironment(requests);
        Test.stopTest();

        // The test verifies that even with a 500 error from the mock,
        // the Apex code does not throw an unhandled exception.
        // We can check system logs to ensure the error was logged correctly.
    }
    
    /**
     * @description Tests the scenario where no activated contract is found for the opportunity.
     * Verifies that the method exits gracefully without making a callout.
     */
    @isTest
    static void testUpdateEnvironment_NoContractFound() {
         // Set the mock for the HTTP callout
        Test.setMock(HttpCalloutMock.class, new MCSEnvironmentMock(200));

        
        // Delete the contract created in testSetup to simulate its absence
        delete [SELECT Id FROM Contract];

        Opportunity opp = [SELECT Id FROM Opportunity LIMIT 1];

        MCSEnvironmentController.UpdateEnvironmentRequest request = new MCSEnvironmentController.UpdateEnvironmentRequest();
        request.opportunityId = opp.Id;
        List<MCSEnvironmentController.UpdateEnvironmentRequest> requests = new List<MCSEnvironmentController.UpdateEnvironmentRequest>{request};

        Test.startTest();
            // Call the method
            MCSEnvironmentController.updateMCSEnvironment(requests);
        Test.stopTest();

        // Since no mock was set, this test will fail if a callout is attempted.
        // Successfully passing this test proves that no callout was made,
        // which is the correct behavior when no contract is found.
    }

    /**
     * @description Tests the initial null/empty check of the method.
     * Verifies that the method handles empty input without errors.
     */
    @isTest
    static void testUpdateEnvironment_NullRequest() {
        Test.startTest();
            // Call the method with a null list
            MCSEnvironmentController.updateMCSEnvironment(null);
            // Call the method with an empty list
            MCSEnvironmentController.updateMCSEnvironment(new List<MCSEnvironmentController.UpdateEnvironmentRequest>());
        Test.stopTest();

        // The assertion is that no DML or Callout exception was thrown,
        // which proves the initial null/empty check is working correctly.
        System.assert(true, 'Method should handle null or empty lists gracefully.');
    }

    /**
     * @description Mock class to simulate HTTP responses from the MCS environment API.
     */
    public class MCSEnvironmentMock implements HttpCalloutMock {
        private Integer statusCode;

        public MCSEnvironmentMock(Integer statusCode) {
            this.statusCode = statusCode;
        }

        public HttpResponse respond(HttpRequest req) {
            // Create a mock response
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            
            if (this.statusCode == 200) {
                res.setBody('{"status": "success", "message": "Environment updated"}');
            } else {
                res.setBody('{"status": "error", "message": "An internal server error occurred"}');
            }
            
            res.setStatusCode(this.statusCode);
            res.setStatus(this.statusCode == 200 ? 'OK' : 'Server Error');
            
            return res;
        }
    }
    
    /**
     * @description Tests the MCSEnvironmentUpdatePayload wrapper class.
     * Verifies that the constructor correctly handles both null and non-null date inputs.
     */
    @isTest
    static void testPayloadWrapper_ConstructorLogic() {
        // --- Path 1: Test with valid start and end dates ---
        Date startDate = Date.newInstance(2025, 6, 11);
        Date endDate = Date.newInstance(2026, 6, 10);

        // Instantiate the payload class
        MCSEnvironmentUpdatePayload payloadWithDates = new MCSEnvironmentUpdatePayload(startDate, endDate);

        // Assert that all properties are set correctly when dates are provided
        System.assertEquals('paid', payloadWithDates.plan, 'Plan should be set to paid.');
        System.assertEquals(50, payloadWithDates.numberOfUsers, 'Number of users should be 50.');
        System.assertNotEquals(null, payloadWithDates.subscription, 'Subscription object should be instantiated.');
        System.assertEquals('2025-06-11T00:00:00Z', payloadWithDates.subscription.activationDate, 'Activation date should be formatted correctly as GMT.');
        System.assertEquals('2026-06-10T00:00:00Z', payloadWithDates.subscription.expirationDate, 'Expiration date should be formatted correctly as GMT.');


        // --- Path 2: Test with null dates ---
        // This covers the 'else' branch of the constructor's 'if' statement.
        MCSEnvironmentUpdatePayload payloadWithoutDates = new MCSEnvironmentUpdatePayload(null, null);

        // Assert that the subscription object is null when dates are not provided
        System.assertEquals('paid', payloadWithoutDates.plan, 'Plan should default to paid even if dates are null.');
        System.assertEquals(50, payloadWithoutDates.numberOfUsers, 'Users should default to 50 even if dates are null.');
        System.assertEquals(null, payloadWithoutDates.subscription, 'Subscription object should be null when dates are null.');
    }
}
