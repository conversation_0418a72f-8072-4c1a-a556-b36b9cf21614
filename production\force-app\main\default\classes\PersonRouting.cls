/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 18/06/2021.
 */

 public without sharing class PersonRouting {

    public static void execute (List<SObject> personRecords, Set<Id> MQLPersonRecords) {
        if (personRecords != null && !personRecords.isEmpty() && !Boolean.valueOf([SELECT Id, Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'Person_Routing_Disabled'].Value__c)) {
            PersonRoutingController controller = new PersonRoutingController(personRecords, MQLPersonRecords);
            controller.run();
            controller.commitChanges();
        }
    }

    public static void processRecordsLeadChange (Set<Id> leadIds, Set<Id> MQLPersonRecords) {
        List<SObject> personRecords = [
            SELECT Id, Account_2__c, OwnerId, Country, State, Account_2__r.BillingCountry, Account_2__r.BillingState, Account_2__r.OwnerId, Most_Recent_Offer__c, Cadence_Name__c, Account_2__r.AccountCategory__c,
            		Account_2__r.Name, Account_2__r.Owner.ManagerId, Account_2__r.Customer_Success_Manager__c, Account_2__r.AccountStrategy__c, Status, Name, Title
            FROM Lead
            WHERE Id IN :leadIds AND Status != 'Converted' AND ConvertedContactId = NULL AND Prevent_Person_Routing__c != TRUE
        ];
        execute(personRecords, MQLPersonRecords);
    }

    public static void processRecordsContactChange (Set<Id> contactIds, Set<Id> MQLPersonRecords) {
        List<SObject> personRecords = [
            SELECT Id, AccountId, OwnerId, MailingCountry, MailingState, Account.BillingCountry, Account.BillingState, Account.OwnerId, Most_Recent_Offer__c, Cadence_Name__c, Account.AccountCategory__c,
            		Account.Name, Account.Owner.ManagerId, Account.Customer_Success_Manager__c, Account.AccountStrategy__c, L_Status__c, Name, Title
            FROM Contact
            WHERE Id IN :contactIds AND RecordType.Name = 'Sales Contact' AND Prevent_Person_Routing__c != TRUE
        ];
        execute(personRecords, MQLPersonRecords);
    }
    
    @InvocableMethod(label='Person Routing Flow')
    public static void personRoutingFlow(List<InputVariables> InputVariablesList) {
        Set<Id> personIds = new Set<Id>();
        Set<Id> MQLIds = new Set<Id>();
        for (InputVariables InputVariable : InputVariablesList) {
            personIds.add(InputVariable.recordId);
            if(InputVariable.status == 'L3 - Engaged'){
            	MQLIds.add(InputVariable.recordId);
            }
            if(InputVariable.isLead){
                processRecordsLeadChange(personIds, MQLIds);
            }
            else{
                processRecordsContactChange(personIds, MQLIds);
            }
        }
    }
    
        
    public class InputVariables{ 
        @InvocableVariable
        public Boolean isLead; 
        
        @InvocableVariable
        public Id recordId;  
        
        @InvocableVariable
        public String status;
    }
}