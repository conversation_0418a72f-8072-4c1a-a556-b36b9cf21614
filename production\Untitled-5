Test Classes That Need to Be Created:
4. SubscriptionTriggerExecutionControllerTest
Target Class: SubscriptionTriggerExecutionController (currently 50% → need 75%+)
Status: Needs to be created
5. KeyGeneratorAPICalloutTest
Target Class: KeyGeneratorAPICallout (currently 51% → need 75%+)
Status: Needs to be created
6. EntitlementContactExecutionControllerTest
Target Class: EntitlementContactExecutionController (currently 70% → need 75%+)
Status: Needs to be created
7. EntitlementContactTriggerHelperTest
Target Class: EntitlementContactTriggerHelper (currently 31% → need 75%+)
Status: Needs to be created
8. CampaignMemberTriggerHelperTest
Target Class: CampaignMemberTriggerHelper (currently 9% → need 75%+)
Status: Needs to be created
9. CampaignMemberTriggerExecutionControllerTest
Target Class: CampaignMemberTriggerExecutionController (currently 54% → need 75%+)
Status: Needs to be created