<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Show_Dynamic_Contract_Language__c</fullName>
    <description>Dynamic Contract Language Boolean</description>
    <formula>OR(
				SBCF_Cold_Failover_Disaster_Recovery__c,
				SBCF_No_Name_Logo_Rights__c,
				SBCF_Non_Standard_Payment_Terms__c,
				SBCF_No_Press_Rights__c,
				SBCF_Reimbursement_Not_Required__c,
				No_Dispute_Timeline__c,
				SBCF_Payment_from_Receipt_of_Invoice__c,
				SBCF_Broaden_License_Grant__c,
				SBCF_Pre_approval_of_Travel_Expenses__c,
				Prepaid_Consulting_Terms__c ,
				Prior_Order_Incorporated_Terms__c 
)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>Show Dynamic Contract Language</label>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Checkbox</type>
</CustomField>
