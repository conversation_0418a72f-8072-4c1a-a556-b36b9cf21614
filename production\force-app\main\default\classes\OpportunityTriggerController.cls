/************************************* MODIFICATION LOG ********************************************************************************************
* OpportunityTriggerController
*
* DESCRIPTION : Contains supporting actions for Opportunity triggers
*
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* Public Method Name            Inputs                                  Description
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* checkPrimaryQuote             Trigger.New                             Checks if Primary Quote exists on Quote Details when the
Opportunity stage is 'Closed Won'.
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                  REASON
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* Priyanka Pallepati            02/18/2014          - Original Version
* Amritha Buddharaju            01/06/2016          - Added logic to Update the next opportunity field of the parent opportunity
*
*/
public without sharing class OpportunityTriggerController {
    public static Boolean onBoardingCasesCreated = false;
    public static Boolean isMciTrialDeactivated = false;
    public static Boolean executionFirstRun = true;
    public static Boolean unexecutionFirstRun = true;

    /*public static void checkPrimaryQuote(List<Opportunity> opportunityList)
{
List<Opportunity> closedWonOpportunityList = new List<Opportunity>();
List<QuoteDetails__c> quoteDetailsList = new List<QuoteDetails__c>();
List<QuoteDetails__c> primaryQuoteDetailsList = new List<QuoteDetails__c>();
for(Opportunity opportunity :opportunityList)
{
if(opportunity.Id !=null && opportunity.StageName=='Closed Won')
{
closedWonOpportunityList.add(opportunity);
}
}

if(closedWonOpportunityList.size()>0)
{
quoteDetailsList = [Select Primary_Quote__c from QuoteDetails__c where Opportunity__c in :closedWonOpportunityList];
if(quoteDetailsList.isEmpty())
{
//throw new GenericException('ERROR: Quote Required for an Opportunity');
}
else
{
for(QuoteDetails__c quoteDetails :quoteDetailsList)
{
if(quoteDetails.Primary_Quote__c)
{
primaryQuoteDetailsList.add(quoteDetails);
}
}
if(primaryQuoteDetailsList.isEmpty())
{
//throw new GenericException('ERROR: A Primary Quote need to be selected for an Opportunity');
}
}

}

}*/

    //    public static void checkNumContactRoles(Map<Id, Opportunity> newMap, Map<Id, Opportunity> oldMap) {
    //        Map<Id, List<OpportunityContactRole>> crMap = new Map<Id, List<OpportunityContactRole>>();
    //        MSTRSettings__c mstrSettings = MSTRSettings__c.getOrgDefaults();
    //        Set<Id> oppIds = new Set<Id>();
    //
    //        if (mstrSettings.Required_Probability__c != null) {
    //            Id salesOpp = UtilityHelper.getRecordTypeId('Opportunity', 'Sales Opportunity');
    //
    //            for (Opportunity opp : newMap.values()) {
    //                Opportunity old = oldMap != null ? oldMap.get(opp.Id) : new Opportunity();
    //
    //                if (opp.RecordTypeId == salesOpp && (old.StageName != opp.StageName || old.Probability != opp.Probability) && opp.Probability > mstrSettings.Required_Probability__c) {
    //                    oppIds.add(opp.Id);
    //                }
    //            }
    //
    //            if (!oppIds.isEmpty()) {
    //                List<OpportunityContactRole> ocrs = [SELECT Id, OpportunityId FROM OpportunityContactRole WHERE OpportunityId IN :oppIds];
    //
    //                if (!ocrs.isEmpty()) {
    //                    for (OpportunityContactRole cr : ocrs) {
    //                        List<OpportunityContactRole> crs = crMap.containsKey(cr.OpportunityId) ? crMap.get(cr.OpportunityId) : new List<OpportunityContactRole>();
    //                        crs.add(cr);
    //                        crMap.put(cr.OpportunityId, crs);
    //                    }
    //                }
    //
    //                for (Id oppId : oppIds) {
    //                    Opportunity opp = newMap.get(oppId);
    //                    List<OpportunityContactRole> crs = crMap.containsKey(opp.Id) ? crMap.get(opp.Id) : new List<OpportunityContactRole>();
    //
    //                    if (crs.size() == 0) {
    //                        opp.addError('You need to have at least one contact role before changing this opportunity\'s probability to be greater than ' + mstrSettings.Required_Probability__c.format() + '%.');
    //                    }
    //                }
    //            }
    //        }
    //    }
    //
    //    // THis method will update the next opportunity field of the parent opportunity ...
    //    public static void updateNextOpportunity(Map<Id, Opportunity> oppMap) {
    //        Map<Id, Id> parentChildOppIdMap = new Map<Id, Id>();
    //
    //        for (Opportunity opp : oppMap.values()) {
    //            if (opp.Previous_Opportunity__c != null) {
    //                parentChildOppIdMap.put(opp.Previous_Opportunity__c, opp.Id);
    //            }
    //        }
    //
    //        if (!parentChildOppIdMap.isEmpty()) {
    //            List<Opportunity> updateOppList = new List<Opportunity>();
    //            for (Opportunity opp : [select Id from Opportunity Where Id IN:parentChildOppIdMap.keySet()]) {
    //                opp.Next_Opportunity__c = parentChildOppIdMap.get(opp.Id);
    //                updateOppList.add(opp);
    //            }
    //
    //            if (!updateOppList.isEmpty()) {
    //                update updateOppList;
    //            }
    //        }
    //
    //    }

    /*public static void checkOwnerBusinessUnit(List<Opportunity> opportunityList, Map<Id, Opportunity> oldMap) {
Set<Id> syncBUIds = new Set<Id>();
List<Opportunity> syncBUopps = new List<Opportunity>();

for(Opportunity opp : opportunityList) {
Opportunity old = oldMap != null ? oldMap.get(opp.Id) : new Opportunity();

if(opp.OwnerId != old.OwnerId || opp.Business_Unit__c != old.Business_Unit__c) {
syncBUIds.add(opp.OwnerId);
syncBUopps.add(opp);
}
}

if(!syncBUIds.isEmpty()) {
Map<Id, User> ownerMap = new Map<Id, User>([SELECT Id, Business_Unit_ID__c FROM User WHERE Id IN :syncBUIds]);

for(Opportunity opp : syncBUopps) {
User owner = ownerMap.get(opp.OwnerId);
opp.Business_Unit__c = owner.Business_Unit_ID__c; // switch to Kimble Business Unit
}
}
}*/

    public static void RollupPartnerAccounts (Set<Id> partnerAccounts, Map <Id, Account> accountsToUpdate, List<Account> queriedAccounts) {
        Map<String, Decimal> next4QurtsMap = getNext4Quarters(partnerAccounts);
        Map<String, Decimal> last4QurtsMap = getLast4Quarters(partnerAccounts);

        List<Account> updateAccounts = new List<Account>();
        //        for (Account acc : [SELECT Id, Partner_Bookings_L4Q__c, Partner_Bookings_N4Q__c FROM Account WHERE Id IN:partnerAccounts]) {
        for (Account acc : queriedAccounts) {
            Decimal nxt4Qrts = next4QurtsMap.containsKey(acc.Id) ? next4QurtsMap.get(acc.Id) : null;
            Decimal lst4Qrts = last4QurtsMap.containsKey(acc.Id) ? last4QurtsMap.get(acc.Id) : null;

            if (acc.Partner_Bookings_N4Q__c != nxt4Qrts || acc.Partner_Bookings_L4Q__c != lst4Qrts) {
                if (accountsToUpdate.containsKey(acc.Id)) {
                    accountsToUpdate.get(acc.Id).Partner_Bookings_N4Q__c = nxt4Qrts;
                    accountsToUpdate.get(acc.Id).Partner_Bookings_L4Q__c = lst4Qrts;
                } else {
                    acc.Partner_Bookings_N4Q__c = nxt4Qrts;
                    acc.Partner_Bookings_L4Q__c = lst4Qrts;
                    accountsToUpdate.put(acc.Id, acc);

                }
            }

        }
    }

    private static Map<String, Decimal> getNext4Quarters (Set<Id> accIds) {
        Map<String, Decimal> tempmap = new Map<String, Decimal>();
        for (AggregateResult ar : [
            SELECT SUM(Pipe_K__c) total, End_User_Customer__c
            FROM Opportunity
            WHERE End_User_Customer__c IN:accIds
            AND Next_4_Qtrs__c = TRUE
            GROUP BY End_User_Customer__c
        ]) {
            tempmap.put((String) ar.get('End_User_Customer__c'), (Decimal) ar.get('total'));
        }
        return tempmap;
    }

    private static Map<String, Decimal> getLast4Quarters (Set<Id> accIds) {
        Map<String, Decimal> tempmap = new Map<String, Decimal>();
        for (AggregateResult ar : [
            SELECT SUM(Pipe_K__c) total, End_User_Customer__c
            FROM Opportunity
            WHERE End_User_Customer__c IN:accIds
            AND Last_4_Qtrs__c = TRUE
            GROUP BY End_User_Customer__c
        ]) {
            tempmap.put((String) ar.get('End_User_Customer__c'), (Decimal) ar.get('total'));
        }
        return tempmap;
    }

    public static void UpdateOriginalCampaign (Set<Id> oppIds, Map<Id, Opportunity> oppsToUpdate) {
        Map<String, String> oppContactMap = new Map<String, String>();
        List <SSO_Contact__c> oppContactNew = new List<SSO_Contact__c>();

        List<Lead> leads = [
            SELECT Id, ConvertedOpportunityId, ConvertedContactId
            FROM Lead
            WHERE ConvertedOpportunityId IN :oppIds
            AND IsConverted = TRUE
        ];

        if (leads != null && !leads.isEmpty()) {
            for (Lead l : leads) {
                oppContactMap.put(l.ConvertedOpportunityId, l.ConvertedContactId);
            }

            if (oppContactMap.size() > 0) {
                Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id, Original_Campaign_Source__c FROM Contact WHERE Id IN :oppContactMap.values()]);
                List<Opportunity> oppList = [SELECT Id, Original_Campaign_Source__c FROM Opportunity WHERE Id IN :oppContactMap.keySet()];
                for (Opportunity o : oppList) {
                    String contactId = oppContactMap.get(o.Id);
                    if (contactMap.containsKey(contactId)) {
                        if (oppsToUpdate.containsKey(o.Id)) {
                            oppsToUpdate.get(o.Id).Original_Campaign_Source__c = contactMap.get(contactId).Original_Campaign_Source__c;
                        } else {
                            o.Original_Campaign_Source__c = contactMap.get(contactId).Original_Campaign_Source__c;
                            oppsToUpdate.put(o.Id, o);
                        }
                    }
                }
            }
        }

        List<OpportunityContactRole> oppContactRoles = [
            SELECT Id, OpportunityId, Role, ContactId, Contact.Original_Campaign_Source__c
            FROM OpportunityContactRole
            WHERE OpportunityId IN :oppIds
            AND IsPrimary = TRUE
        ];

        oppContactMap = new Map<String, String>();
        if (oppContactRoles.size() > 0) {
            for (OpportunityContactRole o : oppContactRoles) {
                oppContactMap.put(o.OpportunityId, o.Contact.Original_Campaign_Source__c);

                SSO_Contact__c temp = new SSO_Contact__c();
                temp.Contact__c = o.ContactId;
                temp.Opportunity__c = o.OpportunityId;
                temp.Contact_Role__c = o.Role ;
                temp.OpportunityContactRoleID__c = o.Id;
                oppContactNew.add(temp);
            }

            List<Opportunity> oppList = [SELECT Id, Original_Campaign_Source__c FROM Opportunity WHERE Id IN :oppContactMap.keySet()];
            for (Opportunity o : oppList) {
                if (oppContactMap.containsKey(o.Id)) {
                    if (oppsToUpdate.containsKey(o.Id)) {
                        oppsToUpdate.get(o.Id).Original_Campaign_Source__c = oppContactMap.get(o.Id);
                    } else {
                        o.Original_Campaign_Source__c = oppContactMap.get(o.Id);
                        oppsToUpdate.put(o.Id, o);
                    }
                    // o.Original_Campaign_Source__c = oppContactMap.get(o.Id);
                }
            }

            //            if (oppList.size() > 0) {
            //                update oppList;
            //            }
            if (oppContactNew.size() > 0) {
                insert oppContactNew;
            }
        }
    }

    public static void CreateTNI (Map<Id, Opportunity> opps) {
        List<OpportunityLineItem> oliList = new List<OpportunityLineItem>();
        Map<String, String> pbeMap = new Map<String, String>();
        Set<Id> oppIdSet = new Set<Id>();
        oppIdSet = opps.keySet();

        if (!oppIdSet.isEmpty()) {
            for (PricebookEntry pbe : [
                SELECT Id, Pricebook2Id, CurrencyIsoCode
                FROM PricebookEntry
                WHERE Product2.Name = 'Transaction Incentive'
            ]) {
                pbeMap.put(pbe.Pricebook2Id + pbe.CurrencyIsoCode, pbe.Id);
            }

            Map<String, OpportunityLineItem > existingOppLineItems = new Map<String, OpportunityLineItem >();
            Set<String> pbeids = new Set<String>(); pbeids.addAll(pbeMap.values());
            for (OpportunityLineItem olt : [SELECT Id, PricebookEntryId, Opportunity_Product__c FROM OpportunityLineItem WHERE Opportunity_Product__c IN:oppIdSet AND PricebookEntry.Product2.Name = 'Transaction Incentive']) {
                String key = olt.Opportunity_Product__c + '' + olt.PricebookEntryId;
                existingOppLineItems.put(key, olt);
            }

            List<OpportunityLineItem > delOLI = new List<OpportunityLineItem>();
            for (String s : oppIdSet) {
                Opportunity op = opps.get(s);
                String pbeId = pbeMap.get(op.Pricebook2Id + op.CurrencyIsoCode);
                String key = op.Id + '' + pbeId ;
                if (op.Tx_Incentive__c != null) {
                    OpportunityLineItem obj = new OpportunityLineItem(OpportunityId = s,
                                                                      Quantity__c = 1,
                                                                      Num_Periods__c = 1,
                                                                      Discount = 0,
                                                                      Opportunity_Product__c = op.Id,
                                                                      PricebookEntryId = pbeId,
                                                                      UnitPrice = (op.Tx_Incentive__c) * -1,
                                                                      Opp_Record_Type__c = 'Product'
                                                                     );
                    if (existingOppLineItems.containsKey(key)) {
                        obj.Id = existingOppLineItems.get(key).Id;
                    }

                    oliList.add(obj);
                } else if (existingOppLineItems.containsKey(key)) {
                    delOLI.add(new OpportunityLineItem(Id = existingOppLineItems.get(key).Id));
                }
            }

            if (oliList.size() > 0) {
                upsert oliList;
            }
            if (!delOLI.isEmpty()) {
                delete delOLI;
            }
        }
    }

    public static void updateOppContactsonExecChange (Map<Id, Opportunity> oppMap) {

        Map<String, String> contactToRole = new Map<String, String>();
        Set<Id> contacts = new Set<Id>();
        Set<Id> oppIds = oppMap.keySet();

        for (Opportunity opp : oppMap.values()) {
            if (opp.Business_Executive__c != null && !contactToRole.containsKey(opp.Business_Executive__c + '' + opp.Id)) {
                contactToRole.put(opp.Business_Executive__c + '' + opp.Id, 'Business Executive');
            }
            if (opp.Technical_Executive__c != null && !contactToRole.containsKey(opp.Technical_Executive__c + '' + opp.Id)) {
                contactToRole.put(opp.Technical_Executive__c + '' + opp.Id, 'Technical Executive');
            }
            if (opp.Business_Executive__c != null && !contacts.contains(opp.Business_Executive__c)) {
                contacts.add(opp.Business_Executive__c);
            }
            if (opp.Technical_Executive__c != null && !contacts.contains(opp.Technical_Executive__c)) {
                contacts.add(opp.Technical_Executive__c);
            }
        }

        Set<String> existingContacts = new Set<String>();

        for (SSO_Contact__c con : [SELECT Opportunity__c, Contact__c FROM SSO_Contact__c WHERE Opportunity__c IN:oppIds AND Contact__c IN:contacts]) {
            if (!existingContacts.contains(con.Contact__c + '' + con.Opportunity__c)) {
                existingContacts.add(con.Contact__c + '' + con.Opportunity__c);
            }
        }

        List<SSO_Contact__c> newOppContacts = new List<SSO_Contact__c>();

        for (String con : contactToRole.keySet()) {
            if (!existingContacts.contains(con) || Test.isRunningTest()) {
                SSO_Contact__c temp = new SSO_Contact__c();
                //break compound key
                temp.Contact__c = con.left(18);
                temp.Opportunity__c = con.right(18);
                temp.Contact_Role__c = contactToRole.get(con);
                newOppContacts.add(temp);
            }
        }

        if (newOppContacts.size() > 0) {
            insert newOppContacts;
        }
    }

    public static void recallQuotesForClosedOpp (Set<Id> closedOppIds) {
        if (!closedOppIds.isEmpty()) {
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            String email = Test.isRunningTest() ?
                '<EMAIL>' :
                MSTR_Global_Configuation__mdt.getInstance('Recall_Quotes_Email_Service').Value__c
                ;
            mail.setToAddresses(new List<String>{
                email
                    });
            mail.setPlainTextBody(JSON.serialize(closedOppIds));
            if (!Test.isRunningTest()) {
                Messaging.sendEmail(new Messaging.SingleEmailMessage[]{
                    mail
                        });
            }
        }
    }

    public static void createDSILineItems (Set<Id> dsiIds, Set<Id> pqIds, Set<Id> oppIds, Map<Id, Opportunity> newOpportunities, Map<Id, Opportunity> oppsToUpdate) {
        if (!pqIds.isEmpty()) {
            for (SBQQ__QuoteLine__c ql : [
                SELECT SBCF_DSI__c
                FROM SBQQ__QuoteLine__c
                WHERE SBQQ__Quote__c IN :pqIds
                AND SBCF_DSI__c != NULL
            ]) {
                dsiIds.add(ql.SBCF_DSI__c);
            }
        }

        if (!oppIds.isEmpty()) {
            CreateDSILineItemsInContext(newOpportunities.values(), oppIds, dsiIds, oppsToUpdate);
        }
    }

    public static void createOnBoardingCases (List<Case> newOnBoardingCases, List<Contact> queriedContacts) {
        if (!onBoardingCasesCreated) {
            if (!newOnBoardingCases.isEmpty()) {
                Id runningUserContactId = queriedContacts[0].Id;
                for (Case newCase : newOnBoardingCases) {
                    newCase.ContactId = runningUserContactId;
                }
                if(!Test.isRunningTest()) {
                    insert newOnBoardingCases;
                }
                onBoardingCasesCreated = true;
            }
        }
    }

    public static void syncOppStage (List<Opportunity> opportunitiesNewStage, Map<Id, Opportunity_Contact_Member__c> ocmToUpdate) {
        Map<Id, List<Opportunity_Contact_Member__c>> ocmsByOppId = new Map<Id, List<Opportunity_Contact_Member__c>>();
        for (Opportunity_Contact_Member__c ocm : [
            SELECT Id, Opportunity__c, Contact__c, Opportunity_Stage__c
            FROM Opportunity_Contact_Member__c
            WHERE Opportunity__c IN :opportunitiesNewStage
        ]) {
            if (ocmsByOppId.containsKey(ocm.Opportunity__c)) {
                ocmsByOppId.get(ocm.Opportunity__c).add(ocm);
            } else {
                ocmsByOppId.put(ocm.Opportunity__c, new List<Opportunity_Contact_Member__c>{
                    ocm
                        });
            }
        }

        for (Opportunity newOpp : opportunitiesNewStage) {
            if (ocmsByOppId.containsKey(newOpp.Id)) {
                List<Opportunity_Contact_Member__c> relatedOCM = ocmsByOppId.get(newOpp.Id);
                for (Opportunity_Contact_Member__c ocm : relatedOCM) {
                    if (ocmToUpdate.containsKey(ocm.Id)) {
                        ocmToUpdate.get(ocm.Id).Opportunity_Stage__c = newOpp.StageName;
                    } else {
                        ocm.Opportunity_Stage__c = newOpp.StageName;
                        ocmToUpdate.put(ocm.Id, ocm);
                    }
                }
                //  ocmToUpdate.addAll(relatedOCM);
            }
        }
    }

    public static void updateAccountCustomerSince (Map<Id, Account> accountsToUpdate, List<Account> queriedAccounts) {
        // List<Account> accList = [SELECT Id, CustomerSince__c FROM Account WHERE Id IN :accIdSet AND CustomerSince__c = NULL];
        if (queriedAccounts.size() > 0) {
            for (Account acc : queriedAccounts) {
                acc.CustomerSince__c = Date.today();
                if (accountsToUpdate.containsKey(acc.Id)) {
                    accountsToUpdate.get(acc.Id).CustomerSince__c = Date.today();
                } else {
                    accountsToUpdate.put(acc.Id, acc);
                }
            }
        }
    }

    public static void updateConversionRate (List<Opportunity> toProcessOptyList) {
        Set<String> CurrencyIsoCodes = new Set<String>();
        for (Opportunity opp : toProcessOptyList) {
            CurrencyIsoCodes.add(opp.CurrencyIsoCode);
        }
        // Getting the currency conversion rates ...
        List<CurrencyType> cts = [SELECT IsoCode, ConversionRate FROM CurrencyType WHERE IsoCode IN :CurrencyIsoCodes];

        Map<String, Double> cRateMap = new Map<String, Double>();
        for (CurrencyType ct : cts) {
            cRateMap.put(ct.IsoCode, ct.ConversionRate);
        }

        for (Opportunity opportunity : toProcessOptyList) {
            //Get the corresponding Conversion rate for the currency code of the Opportunity - assign the Opportunity
            opportunity.ConversionRate__c = cRateMap.get(opportunity.CurrencyIsoCode);
        }
    }

    public static void setMSTRManagedCloudAndPO (List<Opportunity> newOpportunities, Set<Id> accountIds, List<Account> accounts) {
        Map<Id, Account> accountMap = new Map<Id, Account>();
        for (Account acc : accounts) {
            accountMap.put(acc.Id, acc);
        }
        //        for (Opportunity opp : newOpportunities) {
        //            if (String.isNotBlank(opp.DSI__c) && (opp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId || opp.RecordTypeId == TriggersHelper.salesOppRecordTypeId)) {
        //                accountIds.add(opp.DSI__c);
        //            }
        //        }

        Map<Id, DSI__c> dsiMap = new Map<Id, DSI__c>([SELECT Id, MSTR_Managed_Cloud__c FROM DSI__c WHERE Id IN :accountIds]);
        // Map<Id, Account> accountMap = new Map<Id, Account>([SELECT Id, PO_required__c FROM Account WHERE Id IN :accountIds]);
        for (Opportunity opp : newOpportunities) {
            if (String.isNotBlank(opp.DSI__c) && (opp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId || opp.RecordTypeId == TriggersHelper.salesOppRecordTypeId) && dsiMap.containsKey(opp.DSI__c)) {
                opp.MSTR_Managed_Cloud__c = dsiMap.get(opp.DSI__c).MSTR_Managed_Cloud__c;
            }
            if (String.isNotBlank(opp.AccountId) && (opp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId || opp.RecordTypeId == TriggersHelper.salesOppRecordTypeId) && dsiMap.containsKey(opp.AccountId)) {
                opp.PO_required__c = accountMap.get(opp.AccountId).PO_required__c;
            }
        }
    }

    public static void populateApproversAndGeographyFields (List<Opportunity> toProcessOptyList, Set<Id> districtIDSet) {
        Map<Id, District__c> districtMap = new Map<Id, District__c>();
        Map<String, Approvers__mdt> approversMetaMap = new Map<String, Approvers__mdt>();
        Map<String, User> userMap = new Map<String, User>();
        Set<String> regionNames = new Set<String>();
        Set<String> theatreNames = new Set<String>();

        if (toProcessOptyList.size() > 0 ) {
            Set<String> userIDSet = new Set<String>();

            if (districtIDSet.size() > 0) {
                for (Opportunity opp : toProcessOptyList) {
                    districtIDSet.add(opp.District__c);
                }
                districtIDSet.remove(null);
                for (District__c DistrictRec : [SELECT Id, Name, Legal_User__c, RevRec_User__c, Services_RVP_Approver__c, Region__c, Theatre__c, Territory__c, Territory__r.Name FROM District__c WHERE Id IN :districtIDSet]) {
                    districtMap.put(DistrictRec.Id, DistrictRec);
                    userIDSet.add(DistrictRec.Legal_User__c);
                    userIDSet.add(DistrictRec.RevRec_User__c);
                    userIDSet.add(DistrictRec.Services_RVP_Approver__c);
                    regionNames.add(DistrictRec.Region__c);
                    theatreNames.add(DistrictRec.Theatre__c);
                }
            }

            for (Approvers__mdt metaRec : [SELECT Id, SalesRVPID__c, DefaultLegalRep__c, DefaultRevRec__c, DefaultServicesRVP__c FROM Approvers__mdt]) {
                approversMetaMap.put(metaRec.SalesRVPID__c, metaRec);
                userIDSet.add(metaRec.SalesRVPID__c);
                userIDSet.add(metaRec.DefaultLegalRep__c);
                userIDSet.add(metaRec.DefaultRevRec__c);
                userIDSet.add(metaRec.DefaultServicesRVP__c);
            }

            if (userIDSet.size() > 0) {
                for (User userRec : [SELECT Id, Name, IsActive FROM User WHERE Id IN :userIDSet]) {
                    userMap.put(userRec.Id, userRec);
                }
            }
            //As for some reason Region and Theatre are text fields and not lookups, we need 2 separate queries to get their names
            Map<String, Region__c> regionMap = new Map<String, Region__c>();
            for (Region__c region : [
                SELECT Id, Name
                FROM Region__c
                WHERE Name IN :regionNames
            ]) {
                regionMap.put(region.Name, region);
            }
            Map<String, Theatre__c> theatreMap = new Map<String, Theatre__c>();
            for (Theatre__c theatre : [
                SELECT Id, Name
                FROM Theatre__c
                WHERE Name IN :theatreNames
            ]) {
                theatreMap.put(theatre.Name, theatre);
            }

            District__c relatedDistrict;
            Region__c relatedRegion;
            Theatre__c relatedTheatre;
            for (Opportunity Opty : toProcessOptyList) {
                //Legal and RevRec When RVP is Rick Nelson or Ruppel

                //** LEGAL, REV REC STARTS HERE ***//
                //Legal and RevRec When RVP IS Rick Nelson or Ruppel  i.e Custom metadata contains Opty.RVP_Approver__c ID.
                if (Opty.RVP_Approver__c != null && approversMetaMap.get(Opty.RVP_Approver__c) != null && userMap.get(Opty.RVP_Approver__c) != null && userMap.get(Opty.RVP_Approver__c).IsActive) {
                    if (approversMetaMap.get(Opty.RVP_Approver__c).DefaultLegalRep__c != null) {
                        Opty.Legal_Representative__c = approversMetaMap.get(Opty.RVP_Approver__c).DefaultLegalRep__c;
                    }
                    if (approversMetaMap.get(Opty.RVP_Approver__c).DefaultRevRec__c != null) {
                        Opty.Rev_Rec_Representative__c = approversMetaMap.get(Opty.RVP_Approver__c).DefaultRevRec__c;
                    }
                } else {
                    // NOTLegal and RevRec When RVP is NOT Rick Nelson or Ruppel
                    if (Opty.District__c != null && districtMap.get(Opty.District__c) != null && districtMap.get(Opty.District__c).Legal_User__c != null && userMap.get(districtMap.get(Opty.District__c).Legal_User__c) != null && userMap.get(districtMap.get(Opty.District__c).Legal_User__c).IsActive) {
                        Opty.Legal_Representative__c = districtMap.get(Opty.District__c).Legal_User__c;
                    } else {
                        Opty.Legal_Representative__c = approversMetaMap.get('Other').DefaultLegalRep__c;
                    }

                    if (Opty.District__c != null && districtMap.get(Opty.District__c) != null && districtMap.get(Opty.District__c).RevRec_User__c != null && userMap.get(districtMap.get(Opty.District__c).RevRec_User__c) != null && userMap.get(districtMap.get(Opty.District__c).RevRec_User__c).IsActive) {
                        Opty.Rev_Rec_Representative__c = districtMap.get(Opty.District__c).RevRec_User__c;
                    } else {
                        Opty.Rev_Rec_Representative__c = approversMetaMap.get('Other').DefaultRevRec__c;
                    }
                }
                //** LEGAL, REV REC ENDS HERE ***//

                //**** Services_RVP_Approver__    STARTS HERE ****  //
                if (Opty.District__c != null && districtMap.get(Opty.District__c) != null && districtMap.get(Opty.District__c).Services_RVP_Approver__c != null && userMap.get(districtMap.get(Opty.District__c).Services_RVP_Approver__c) != null && userMap.get(districtMap.get(Opty.District__c).Services_RVP_Approver__c).IsActive) {
                    Opty.Services_RVP_Approver__c = districtMap.get(Opty.District__c).Services_RVP_Approver__c;
                } else if (Opty.RVP_Approver__c != null && approversMetaMap.get(Opty.RVP_Approver__c) != null && approversMetaMap.get(Opty.RVP_Approver__c).DefaultServicesRVP__c != null) {
                    Opty.Services_RVP_Approver__c = approversMetaMap.get(Opty.RVP_Approver__c).DefaultServicesRVP__c;
                } else {
                    Opty.Services_RVP_Approver__c = approversMetaMap.get('Other').DefaultServicesRVP__c;
                }

                if (String.isNotBlank(Opty.District__c)) {
                    //Get related district, region and theatre using maps from above
                    if (districtMap.containsKey(Opty.District__c)) {
                        //Set all Opportunity Geography fields using maps above
                        relatedDistrict = districtMap.get(Opty.District__c);
                        Opty.Territory_Lookup__c = relatedDistrict.Territory__c;
                        Opty.Territory__c = relatedDistrict.Territory__r.Name;

                        if (regionMap.containsKey(relatedDistrict.Region__c)) {
                            relatedRegion = regionMap.get(relatedDistrict.Region__c);
                            Opty.Region_Lookup__c = relatedRegion.Id;
                            Opty.Region__c = relatedRegion.Name;
                        }

                        if (theatreMap.containsKey(relatedDistrict.Theatre__c)) {
                            relatedTheatre = theatreMap.get(relatedDistrict.Theatre__c);
                            Opty.Theatre_Lookup__c = relatedTheatre.Id;
                            Opty.Theatre__c = relatedTheatre.Name;
                        }
                    }
                }
            } // END FOR
        } // END IF toProcessOptyList size > 0
    }

    public static void populateDistrictSalesApprover (List<Opportunity> toProcessOptyList, List<Account> queriedAccounts, Boolean isAccountUpdate, Set<Id> districtIDSet) {
        //Set <Id> districtIDSet = new Set <Id>();
        Map<Id, Account> AccountMap = new Map<Id, Account>();

        if (toProcessOptyList.size() > 0) {
            // if (accountIDSet.size() > 0) {
            //  for (Account acc : [SELECT Id, Owner.RVP_Approver__c, Owner.RVP_Approver__r.IsActive, District__c FROM Account WHERE Id IN :accountIDSet]) {
            for (Account acc : queriedAccounts) {
                AccountMap.put(acc.Id, acc);
            }
            //}

            String DefaultSalesRVP = MSTR_Global_Configuation__mdt.getInstance('DefaultSalesRVP').Value__c;

            for (Opportunity optyRec : toProcessOptyList) {
                if (AccountMap != null && AccountMap.containsKey(optyRec.AccountId)) {
                    districtIDSet.add(optyRec.District__c);
                    if (isAccountUpdate != null && isAccountUpdate) {
                        optyRec.OwnerId = AccountMap.get(optyRec.AccountId).OwnerId;
                        optyRec.Name = AccountMap.get(optyRec.AccountId).Name + ' - ' + optyRec.CPQ_UAT_Name__c + ' - ' + optyRec.SBCF_QuoteNumber__c;
                        optyRec.Primary_SE__c = null;
                        optyRec.Billing_Contact__c = null;
                    }
                    if (optyRec.AccountId != null && AccountMap.get(optyRec.AccountId) != null && AccountMap.get(optyRec.AccountId).Owner.RVP_Approver__c != null && AccountMap.get(optyRec.AccountId).Owner.RVP_Approver__r.IsActive) {
                        optyRec.RVP_Approver__c = AccountMap.get(optyRec.AccountId).Owner.RVP_Approver__c;
                    } else if (DefaultSalesRVP != null) {
                        optyRec.RVP_Approver__c = DefaultSalesRVP;
                    }

                    if (optyRec.AccountId != null && AccountMap.get(optyRec.AccountId) != null && AccountMap.get(optyRec.AccountId).District__c != null) {
                        optyRec.District__c = AccountMap.get(optyRec.AccountId).District__c;
                    }
                }
            } // END FOR
        } // END IF

        populateApproversAndGeographyFields(toProcessOptyList, districtIDSet);
    }

    public static void setBillingContactFields (List<Opportunity> opportunitiesToProcess, Set<Id> contactIds, Map<Id, Contact> contactMap) {
        // Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id, Email, FirstName, LastName FROM Contact WHERE Id IN :contactIds]);
        for (Opportunity opp : opportunitiesToProcess) {
            if (String.isNotBlank(opp.Billing_Contact__c)) {
                Contact c = contactMap.get(opp.Billing_Contact__c);
                opp.BCEmail__c = c.Email;
                opp.BCFName__c = c.FirstName;
                opp.BCLName__c = c.LastName;
            }
        }
    }

    public static void setGeographyFields (List<Opportunity> opportunitiesToProcess, Set<Id> districtIds) {
        if (!opportunitiesToProcess.isEmpty() && !districtIds.isEmpty()) {
            Set<String> regionNames = new Set<String>();
            Set<String> theatreNames = new Set<String>();
            //Query for related districts
            Map<Id, District__c> districtMap = new Map<Id, District__c>([
                SELECT Id, Name, Territory__c, Territory__r.Name, Region__c, Theatre__c
                FROM District__c
                WHERE Id IN :districtIds
            ]);

            for (District__c district : districtMap.values()) {
                regionNames.add(district.Region__c);
                theatreNames.add(district.Theatre__c);
            }

            //As for some reason Region and Theatre are text fields and not lookups, we need 2 separate queries to get their names
            Map<String, Region__c> regionMap = new Map<String, Region__c>();
            for (Region__c region : [
                SELECT Id, Name
                FROM Region__c
                WHERE Name IN :regionNames
            ]) {
                regionMap.put(region.Name, region);
            }
            Map<String, Theatre__c> theatreMap = new Map<String, Theatre__c>();
            for (Theatre__c theatre : [
                SELECT Id, Name
                FROM Theatre__c
                WHERE Name IN :theatreNames
            ]) {
                theatreMap.put(theatre.Name, theatre);
            }

            District__c relatedDistrict;
            Region__c relatedRegion;
            Theatre__c relatedTheatre;
            for (Opportunity newOpportunity : opportunitiesToProcess) {
                if (String.isNotBlank(newOpportunity.District__c)) {
                    //Get related district, region and theatre using maps from above
                    if (districtMap.containsKey(newOpportunity.District__c)) {
                        //Set all Opportunity Geography fields using maps above
                        relatedDistrict = districtMap.get(newOpportunity.District__c);
                        newOpportunity.Territory_Lookup__c = relatedDistrict.Territory__c;
                        newOpportunity.Territory__c = relatedDistrict.Territory__r.Name;

                        if (regionMap.containsKey(relatedDistrict.Region__c)) {
                            relatedRegion = regionMap.get(relatedDistrict.Region__c);
                            newOpportunity.Region_Lookup__c = relatedRegion.Id;
                            newOpportunity.Region__c = relatedRegion.Name;
                        }

                        if (theatreMap.containsKey(relatedDistrict.Theatre__c)) {
                            relatedTheatre = theatreMap.get(relatedDistrict.Theatre__c);
                            newOpportunity.Theatre_Lookup__c = relatedTheatre.Id;
                            newOpportunity.Theatre__c = relatedTheatre.Name;
                        }
                    }
                }
            }
        }
    }

    // Case - 325684 - pick Currency, Pricebook based on Region Picklist value selected
    public static void regionCurrencyMatcher (List<Opportunity> toProcessOptyList) {
        Map<String, Sale_Regions__mdt> regionCustomMetaMap = new Map<String, Sale_Regions__mdt>();
        if (toProcessOptyList.size() > 0) {
            for (Sale_Regions__mdt metaDataRec : [SELECT Id, DeveloperName, MasterLabel, CurrencyISOCode__c, Pricebook__c FROM Sale_Regions__mdt]) {
                regionCustomMetaMap.put(metaDataRec.MasterLabel, metaDataRec);
            }
            for (Opportunity opty : toProcessOptyList) {
                if (opty.RegionPricebook__c != null && regionCustomMetaMap.get(opty.RegionPricebook__c) != null) {
                    opty.CurrencyIsoCode = regionCustomMetaMap.get(opty.RegionPricebook__c).CurrencyISOCode__c;
                    opty.Pricebook2Id = regionCustomMetaMap.get(opty.RegionPricebook__c).Pricebook__c;
                }
            }
        }
    }

    public static void SetRPGs (List < Opportunity > listOpportunities, Set <String> usrIds, Map <String, String> accUserMap, Boolean isUpdate, Map<Id, Opportunity> oldOpportunities, List<Account> queriedAccounts) {

        if (!accUserMap.isEmpty()) {
            //            for (Account acc: [select Id, Primary_Services_Director__c from Account Where Id In: accUserMap.KeySet() AND
            //            Primary_Services_Director__c != null
            //            ]) {
            for (Account acc : queriedAccounts) {
                usrIds.add(acc.Primary_Services_Director__c);
                accUserMap.put(acc.Id, acc.Primary_Services_Director__c);
            }
        }

        Map < String, Contact > usrContMap = new Map < String, Contact > ();
        Map<Id, pse__Grp__c> groupMap = new Map<Id, pse__Grp__c>();
        Id groupId;
        if (usrIds != null && !usrIds.isEmpty()) {
            usrIds.remove('');
            usrIds.remove(null);
            TriggersHelper.setRPGHelper(usrIds);
            Map<Id, Contact> contactsSetRPG = TriggersHelper.contactsSetRPG;
            for (Contact c : contactsSetRPG.values()) {
                usrContMap.put(c.pse__Salesforce_User__c, c);
            }
            for (Opportunity opp : listOpportunities) {
                Contact cont = null;
                if (opp.Primary_Services_Director__c != null && usrContMap.containsKey(opp.Primary_Services_Director__c)) {
                    cont = usrContMap.get(opp.Primary_Services_Director__c);
                } else if (accUserMap.containsKey(opp.AccountId) && accUserMap.get(opp.AccountId) != null && usrContMap.containsKey(accUserMap.get(opp.AccountId))) {
                    cont = usrContMap.get(accUserMap.get(opp.AccountId));
                } else if (usrContMap.containsKey(opp.OwnerId)) {
                    cont = usrContMap.get(opp.OwnerId);
                }
                if (cont != null) {
                    opp.pse__Region__c = cont.pse__Region__c;
                    opp.pse__Practice__c = cont.pse__Practice__c;
                    opp.pse__Group__c = cont.pse__Group__c;
                }
                if (isUpdate != null && isUpdate && cont == null) {
                    opp.pse__Region__c = null;
                    opp.pse__Practice__c = null;
                    opp.pse__Group__c = null;
                }
                if (opp.RecordTypeId == TriggersHelper.salesOppRecordTypeId 
                    && ((opp.SBQQ__PrimaryQuote__c == null && oldOpportunities != null
                            && (opp.License_JDE_BU_ID__c == null || opp.Maintenance_JDE_BU_ID__c == null || opp.Consulting_JDE_BU_ID__c == null || opp.Education_JDE_BU_ID__c == null))
                        || (oldOpportunities != null && !oldOpportunities.isEmpty() && oldOpportunities.containsKey(opp.Id) 
                            && ((opp.OwnerId != oldOpportunities.get(opp.Id).OwnerId) || (opp.SBQQ__PrimaryQuote__c == null && opp.SBQQ__PrimaryQuote__c != oldOpportunities.get(opp.Id).SBQQ__PrimaryQuote__c)))) 
                    && usrContMap.containsKey(opp.OwnerId)) {
                    groupMap = TriggersHelper.JDEgroups;
                    groupId = usrContMap.get(opp.OwnerId).pse__Group__c;
                    opp.License_JDE_BU_ID__c = (groupId != null && groupMap.get(groupId) != null && groupMap.get(groupId).JDE_ID__c != null)? Integer.valueOf(groupMap.get(groupId).JDE_ID__c): null;
                    groupId = usrContMap.get(opp.OwnerId).Maintenance_JDE_BU__c;
                    opp.Maintenance_JDE_BU_ID__c = (groupId != null && groupMap.get(groupId) != null && groupMap.get(groupId).JDE_ID__c != null)? Integer.valueOf(groupMap.get(groupId).JDE_ID__c): null;
                    groupId = usrContMap.get(opp.OwnerId).Consulting_JDE_BU__c;
                    opp.Consulting_JDE_BU_ID__c = (groupId != null && groupMap.get(groupId) != null && groupMap.get(groupId).JDE_ID__c != null)? Integer.valueOf(groupMap.get(groupId).JDE_ID__c): null;
                    groupId = usrContMap.get(opp.OwnerId).Education_JDE_BU__c;
                    opp.Education_JDE_BU_ID__c = (groupId != null && groupMap.get(groupId) != null && groupMap.get(groupId).JDE_ID__c != null)? Integer.valueOf(groupMap.get(groupId).JDE_ID__c): null;
                    opp.SBCF_Legal_Entity__c = usrContMap.get(opp.OwnerId).Legal_Entity__c;
                }
            }
        }
    }

    public static void OpportunityCPQRenewal (List<Opportunity> optyNewList, Set<Id> parentContractIdSet, Set<Id> parentAccIDSet, List<Account> querriedAccounts) {
        Map<Id, Account> parentAccMap = new Map<Id, Account>();
        Map<Id, Contract> parentContractMap = new Map<Id, Contract>();
        String RenewalOpportunityNewRecordtypeID = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Renewal Opportunity (New)').getRecordTypeId();

        if (parentContractIdSet.size() > 0) {
            // Sync Opportunity Inflight - Legal Rev Rec PB
            for (Contract parentContractRec : [
                SELECT
                Id, EndDate, Account.Owner.RVP_Approver__c, SBQQ__Opportunity__r.ALF__c, SBQQ__Opportunity__c, SBQQ__Opportunity__r.Term_Opportunity__c,SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.Platform__c, SBQQ__Opportunity__r.Cloud_Opportunity__c, SBQQ__Opportunity__r.Business_Executive__c, SBQQ__Opportunity__r.CloseDate, SBQQ__Opportunity__r.DSI__c, SBQQ__Opportunity__r.District__c, SBQQ__Opportunity__r.Due_Date__c, SBQQ__Opportunity__r.End_User_Customer__c, SBQQ__Opportunity__r.Legal_Representative__c,
                SBQQ__Opportunity__r.RVP_Approver__r.Id, SBQQ__Opportunity__r.Rev_Rec_Representative__c, SBQQ__Opportunity__r.Consulting_JDE_BU_ID__c, SBQQ__Opportunity__r.Education_JDE_BU_ID__c, SBQQ__Opportunity__r.License_JDE_BU_ID__c, SBQQ__Opportunity__r.Maintenance_JDE_BU_ID__c, SBQQ__Opportunity__r.SBCF_Type_of_Sale__c, SBQQ__Opportunity__r.Services_RVP_Approver__c, SBQQ__Opportunity__r.pse__Group__c, SBCF_IsEvergreen__c, SBQQ__Opportunity__r.ARR__c, SBQQ__Opportunity__r.SBCF_Legal_Entity__c,SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.CTR__c,
                SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.MXIT__c, SBQQ__Opportunity__r.RegionPricebook__c,Account.District__r.Legal_User__c,Account.District__r.RevRec_User__c,Account.District__r.Services_RVP_Approver__c,QuoteNumber__c
                FROM Contract 
                WHERE Id IN :parentContractIdSet
            ]) { 
                parentContractMap.put(parentContractRec.Id, parentContractRec);
            }

            if (parentAccIDSet.size() > 0) {
                //for (Account acc : [SELECT Id, SBCF_PO_Required__c FROM Account WHERE Id IN :parentAccIDSet]) {
                for (Account acc : querriedAccounts) {
                    parentAccMap.put(acc.Id, acc);
                }
            }

            String DefaultSalesRVP = MSTR_Global_Configuation__mdt.getInstance('DefaultSalesRVP').Value__c;

            for (Opportunity optyRec : optyNewList) {
                if (optyRec.SBQQ__Renewal__c) {
                    If (optyRec.SBQQ__RenewedContract__r.SBQQ__Opportunity__c != null && optyRec.Type != 'Royalty Renewal' &&
                        optyRec.Platform__c == 'MCG'){
                        optyRec.Name =  'MCG'+'-'+optyRec.SBCF_QuoteNumber__c+'-'+parentContractMap.get(optyRec.SBQQ__RenewedContract__c).QuoteNumber__c;}
                    else if(optyRec.Type != 'Education Renewal' && !optyRec.Name.startsWith('EDU-')){
                        optyRec.Name = optyRec.SBCF_OpportunityNameFormula__c;
                    }
                    optyRec.RFP_Picklist__c = 'No';
                    optyRec.StageName = 'S5 - Negotiate/Purchase Solution';
                    if (optyRec.AccountId != null && parentAccMap.get(optyRec.AccountId) != null) {
                        optyRec.SBCF_PO_Required__c = parentAccMap.get(optyRec.AccountId).SBCF_PO_Required__c;
                    }
                }
                
                if (optyRec.SBQQ__RenewedContract__c != null) {

                    Contract parentContractRec = parentContractMap.get(optyRec.SBQQ__RenewedContract__c);
                    optyRec.CloseDate = parentContractRec.EndDate;
                    optyRec.SBCF_Renewal_Type__c = parentContractRec.SBCF_IsEvergreen__c ? 'Evergreen' : 'Standard';
                    optyRec.SBCF_Legal_Entity__c = parentContractRec.SBQQ__Opportunity__r.SBCF_Legal_Entity__c;
                    optyRec.Consulting_JDE_BU_ID__c = parentContractRec.SBQQ__Opportunity__r.Consulting_JDE_BU_ID__c;
                    optyRec.Education_JDE_BU_ID__c = parentContractRec.SBQQ__Opportunity__r.Education_JDE_BU_ID__c;
                    optyRec.License_JDE_BU_ID__c = parentContractRec.SBQQ__Opportunity__r.License_JDE_BU_ID__c;
                    optyRec.Maintenance_JDE_BU_ID__c = parentContractRec.SBQQ__Opportunity__r.Maintenance_JDE_BU_ID__c;
                    optyRec.End_User_Customer__c = parentContractRec.SBQQ__Opportunity__r.End_User_Customer__c;
                    optyRec.RegionPricebook__c = parentContractRec.SBQQ__Opportunity__r.RegionPricebook__c;

                    if (optyRec.SBQQ__Renewal__c && parentContractRec != null && parentContractRec.SBQQ__Opportunity__c != null && !parentContractRec.SBQQ__Opportunity__r.Cloud_Opportunity__c && parentContractRec.SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.Platform__c != 'MCG') {
                        //NOT Cloud
                        optyRec.Business_Executive__c = parentContractRec.SBQQ__Opportunity__r.Business_Executive__c;
                        optyRec.DSI__c = parentContractRec.SBQQ__Opportunity__r.DSI__c;
                        optyRec.Late_Close_Date__c = null;
                        optyRec.PALF__c = parentContractRec.SBQQ__Opportunity__r.ALF__c;

                        if (parentContractRec.Account.Owner.RVP_Approver__c != null) {
                            optyRec.RVP_Approver__c = parentContractRec.Account.Owner.RVP_Approver__c;
                        } else {
                            optyRec.RVP_Approver__c = DefaultSalesRVP;
                        }

                        optyRec.SBCF_Type_of_Sale__c = parentContractRec.SBQQ__Opportunity__r.SBCF_Type_of_Sale__c;
                        optyRec.Type = 'Maintenance Renewal';
                        optyRec.pse__Group__c = parentContractRec.SBQQ__Opportunity__r.pse__Group__c;

                        // optyRec.CloseDate                   =  (parentContractRec.SBQQ__Opportunity__r.CloseDate == null )  ?  date.today().addMonths(12) : parentContractRec.SBQQ__Opportunity__r.CloseDate.addMonths(12);
                        optyRec.Due_Date__c = (parentContractRec.SBQQ__Opportunity__r.Due_Date__c == null) ? Date.today().addMonths(12) : parentContractRec.SBQQ__Opportunity__r.Due_Date__c.addMonths(12);
                    } else if (parentContractRec != null && parentContractRec.SBQQ__Opportunity__c != null && (parentContractRec.SBQQ__Opportunity__r.Cloud_Opportunity__c || parentContractRec.SBQQ__Opportunity__r.SBQQ__PrimaryQuote__r.Platform__c == 'MCG')) {

                        optyRec.Type = 'License Renewal';
                        optyRec.PALF__c = parentContractRec.SBQQ__Opportunity__r.ALF__c;
                        optyRec.DSI__c = parentContractRec.SBQQ__Opportunity__r.DSI__c;
                    } else if (parentContractRec != null && parentContractRec.SBQQ__Opportunity__c != null) {
                        optyRec.PALF__c = parentContractRec.SBQQ__Opportunity__r.ALF__c;

                    } else if (parentContractRec != null && parentContractRec.SBQQ__Opportunity__c == null && optyRec.AccountId != null){ // Sync Opportunity Inflight - Legal Rev Rec PB
                        optyRec.Legal_Representative__c = parentContractRec.Account.District__r.Legal_User__c != null ? parentContractRec.Account.District__r.Legal_User__c : null;
                        optyRec.RVP_Approver__c = parentContractRec.Account.Owner.RVP_Approver__c != null ? parentContractRec.Account.Owner.RVP_Approver__c : null;
                        optyRec.Rev_Rec_Representative__c = parentContractRec.Account.District__r.RevRec_User__c != null ? parentContractRec.Account.District__r.RevRec_User__c : null;
                        optyRec.Services_RVP_Approver__c = parentContractRec.Account.District__r.Services_RVP_Approver__c != null ? parentContractRec.Account.District__r.Services_RVP_Approver__c : null;
                    }
                }
            }
        } // End parentCotractIdSet  > 0
    } // End OpportunityCPQRenewal

    //Case - 342297 - Naming Convention For Maintenance Renewal Opportunities
    //BEFORE INSERT
    public static void namingMaintenanceRenewalOptys (List<Opportunity> optyNewList, Set<Id> contractIDSet) {
        Set<Id> accIDSet = new Set<Id>();
        Map<Id, Id> accountToCSMMap = new Map<Id, Id>();

        if (contractIDSet.size() > 0) {
            Id CSMDefaultOwnerID = MSTR_Global_Configuation__mdt.getInstance('CSMDefaultOwner').Value__c;

            Map<Id, Contract> contractMap = new Map<Id, Contract>();
            Set<Id> termFamilyContractIDSet = new Set<Id>();
            List<Opportunity> termFamilyOpportunityList = new List<Opportunity>();
            for (Contract contractRec : [SELECT Id, AccountId, Account.OwnerId, SBQQ__Opportunity__c, /* SBQQ__Opportunity__r.Cloud_Opportunity__c,*/ SBQQ__Opportunity__r.Platform__c, SBQQ__Opportunity__r.Type,SBQQ__Quote__c, SBQQ__Quote__r.SBQQ__Primary__c, SBQQ__Quote__r.Rlty__c, SBQQ__Quote__r.License_Type__c FROM Contract WHERE Id IN :contractIDSet]) {
                contractMap.put(contractRec.Id, contractRec);
                accIDSet.add(contractRec.AccountId);
            }

            if (accIDSet.size() > 0) {
                for (AccountTeamMember accTeamMem : [SELECT Id, AccountId, TeamMemberRole, UserId FROM AccountTeamMember WHERE AccountId IN :accIDSet AND TeamMemberRole = 'Customer Success Manager']) {
                    accountToCSMMap.put(accTeamMem.AccountId, accTeamMem.UserId);
                }
            }

            Map<Id, String> contractIDtoOptyCategoryMap = new Map<Id, String>();

            for (Opportunity newOpp : optyNewList) {
                if (newOpp.SBQQ__RenewedContract__c != null && contractMap.get(newOpp.SBQQ__RenewedContract__c) != null) {
                    Contract renewedContract = contractMap.get(newOpp.SBQQ__RenewedContract__c);
                    newOpp.Name = newOpp.Name.replace('MX-', 'MX-QuoteNumber');
                    // IF CLOUD RENEWAL
                    if (renewedContract.SBQQ__Opportunity__c != null /*&&  renewedContract.SBQQ__Opportunity__r.Cloud_Opportunity__c == True*/ && (renewedContract.SBQQ__Opportunity__r.Platform__c == 'MCE' || renewedContract.SBQQ__Opportunity__r.Platform__c == 'CMC' || renewedContract.SBQQ__Opportunity__r.Platform__c == 'MCG' || 
                        renewedContract.SBQQ__Opportunity__r.Platform__c == 'MCS') && renewedContract.SBQQ__Opportunity__r.Type != 'Education Renewal') {
                        newOpp.Name = renewedContract.SBQQ__Opportunity__r.Platform__c == 'MCE' ? newOpp.Name.replace('MX', 'MCE') : 
                        renewedContract.SBQQ__Opportunity__r.Platform__c == 'CMC' ? newOpp.Name.replace('MX', 'CMC') : 
                        renewedContract.SBQQ__Opportunity__r.Platform__c == 'MCG' ? newOpp.Name.replace('MX', 'MCG') : 
                        newOpp.Name.replace('MX', 'MCS');
                        newOpp.Type = 'Cloud Renewal';

                        if (contractMap.get(newOpp.SBQQ__RenewedContract__c).Account.OwnerId != null) {
                            newOpp.OwnerId = contractMap.get(newOpp.SBQQ__RenewedContract__c).Account.OwnerId;
                        }
                    } else if (renewedContract.SBQQ__Quote__r.SBQQ__Primary__c && renewedContract.SBQQ__Quote__r.Rlty__c > 0) {
                        //ROYALTY RENEWAL
                        newOpp.Name = newOpp.Name.replace('MX', 'RR');
                        newOpp.Type = 'Royalty Renewal';
                        if (contractMap.get(newOpp.SBQQ__RenewedContract__c).Account.OwnerId != null) {
                            newOpp.OwnerId = contractMap.get(newOpp.SBQQ__RenewedContract__c).Account.OwnerId;
                        }
                    } else {
                        termFamilyContractIDSet.add(newOpp.SBQQ__RenewedContract__c);
                        termFamilyOpportunityList.add(newOpp);
                    }
                }
            } // END FOR   Opty List

            if (termFamilyContractIDSet.size() > 0) {
                Map<Id, List<SBQQ__Subscription__c>> contractToSubscriptionsMap = new Map<Id, List<SBQQ__Subscription__c>>();
                for (SBQQ__Subscription__c subscRec : [SELECT SBQQ__Product__r.Interval__c, SBQQ__Product__r.Family, SBQQ__RenewalPrice__c, SBQQ__Contract__c FROM SBQQ__Subscription__c WHERE SBQQ__Contract__c IN :termFamilyContractIDSet]) {
                    List<SBQQ__Subscription__c> tempList = (contractToSubscriptionsMap.get(subscRec.SBQQ__Contract__c) != null) ? contractToSubscriptionsMap.get(subscRec.SBQQ__Contract__c) : new List<SBQQ__Subscription__c>();
                    tempList.add(subscRec);
                    contractToSubscriptionsMap.put(subscRec.SBQQ__Contract__c, tempList);
                }

                Map<Id,Integer> mapOfContractIdVsSupportCount = new Map<Id,Integer>();
                for (Id contractID : termFamilyContractIDSet) {
                    List<Decimal> sumList;

                    Decimal maintenanceSum = 0.0;
                    Decimal supportSum = 0.0;
                    Decimal termIntervalSum = 0.0;
                    Decimal educationSum = 0.0;
                    
                    Integer countOfMaintenance = 0;
                    Integer countOfSupport = 0;
                    Integer countOfTerms = 0;
                    Integer countOfEDU = 0;

                    if (contractToSubscriptionsMap.get(contractID) != null) {
                        for (SBQQ__Subscription__c subscRec : contractToSubscriptionsMap.get(contractID)) {
                            //if(subscRec.SBQQ__RenewalPrice__c != null ){

                            if (subscRec.SBQQ__Product__r.Family == 'Maintenance') {
                                if (subscRec.SBQQ__RenewalPrice__c != null) {
                                    maintenanceSum = maintenanceSum + subscRec.SBQQ__RenewalPrice__c;
                                }
                                countOfMaintenance++;
                            } else if (subscRec.SBQQ__Product__r.Family == 'Support') {
                                if (subscRec.SBQQ__RenewalPrice__c != null) {
                                    supportSum = supportSum + subscRec.SBQQ__RenewalPrice__c;
                                }

                                countOfSupport++;
                            } else if (subscRec.SBQQ__Product__r.Interval__c == 'Term') {
                                if (subscRec.SBQQ__RenewalPrice__c != null) {
                                    termIntervalSum = termIntervalSum + subscRec.SBQQ__RenewalPrice__c;
                                }
                                countOfTerms++;
                            } else if (subscRec.SBQQ__Product__r.Family == 'Education') {
                                if (subscRec.SBQQ__RenewalPrice__c != null) {
                                    educationSum = educationSum + subscRec.SBQQ__RenewalPrice__c;
                                }

                                countOfEDU++;
                            }
                            //}
                        } // END FOR
                        mapOfContractIdVsSupportCount.put(contractID,countOfSupport);
                        sumList = new List<Decimal>{
                            termIntervalSum, supportSum, maintenanceSum, educationSum
                                };
                                    sumList.sort();

                        // MAINTENANCE RENEWAL
                        if (sumList[sumList.size() - 1] == maintenanceSum && countOfMaintenance > 0) {
                            contractIDtoOptyCategoryMap.put(contractID, 'Maintenance');
                        } else if (sumList[sumList.size() - 1] == supportSum && countOfSupport > 0) {
                            // SUPPORT RENEWAL
                            contractIDtoOptyCategoryMap.put(contractID, 'Support');
                        } else if (sumList[sumList.size() - 1] == termIntervalSum && countOfTerms > 0) {
                            // TERM RENEWAL
                            contractIDtoOptyCategoryMap.put(contractID, 'Term');
                        } else if (sumList[sumList.size() - 1] == educationSum && countOfEDU > 0) {
                            // EDUCATION RENEWAL
                            contractIDtoOptyCategoryMap.put(contractID, 'Education');
                        }
                    }
                } // END FOR termFamilyContractIDSet ID
                for (Opportunity newOpp : termFamilyOpportunityList) {
                    Id CSMID;
                    Id contrAccountOwnerID;
                    if (newOpp.AccountId != null && accountToCSMMap.get(newOpp.AccountId) != null) {
                        CSMID = accountToCSMMap.get(newOpp.AccountId);
                    } else {
                        CSMID = CSMDefaultOwnerID;
                    }
                    if (newOpp.SBQQ__RenewedContract__c != null && contractMap.get(newOpp.SBQQ__RenewedContract__c).Account.OwnerId != null) {
                        contrAccountOwnerID = contractMap.get(newOpp.SBQQ__RenewedContract__c).Account.OwnerId;
                    }
                    // Term License type
                    if (newOpp.SBQQ__RenewedContract__c != null && contractMap.containsKey(newOpp.SBQQ__RenewedContract__c)
                        && contractMap.get(newOpp.SBQQ__RenewedContract__c).SBQQ__Quote__r.License_Type__c == 'Term' && 
                        mapOfContractIdVsSupportCount.containsKey(newOpp.SBQQ__RenewedContract__c) && 
                        mapOfContractIdVsSupportCount.get(newOpp.SBQQ__RenewedContract__c) > 0) {
                            newOpp.Name = newOpp.Name.replace('MX', 'TX');
                            newOpp.OwnerId = contrAccountOwnerID;
                            newOpp.Type = 'Term Renewal';
                        }
                    // Perpetual License type
                    else if (newOpp.SBQQ__RenewedContract__c != null && contractMap.containsKey(newOpp.SBQQ__RenewedContract__c)
                             && contractMap.get(newOpp.SBQQ__RenewedContract__c).SBQQ__Quote__r.License_Type__c == 'Perpetual' && 
                             mapOfContractIdVsSupportCount.containsKey(newOpp.SBQQ__RenewedContract__c) && 
                             mapOfContractIdVsSupportCount.get(newOpp.SBQQ__RenewedContract__c) > 0) {
                                 newOpp.OwnerId = CSMID;
                                 newOpp.Type = 'Maintenance Renewal';
                             }
                    // system.debug('newOpp.SBCF_OpportunityNameFormula__c ###*** '+newOpp.SBCF_OpportunityNameFormula__c);
                    else if (newOpp.SBQQ__RenewedContract__c != null && contractIDtoOptyCategoryMap.get(newOpp.SBQQ__RenewedContract__c) != null) {
                        if (contractIDtoOptyCategoryMap.get(newOpp.SBQQ__RenewedContract__c) == 'Maintenance') {
                            //newOpp.Name  = newOpp.Name.replace('MX', 'MX' );
                            newOpp.OwnerId = CSMID;
                            newOpp.Type = 'Maintenance Renewal';
                        } else if (contractIDtoOptyCategoryMap.get(newOpp.SBQQ__RenewedContract__c) == 'Term') {
                            newOpp.Name = newOpp.Name.replace('MX', 'TX');
                            newOpp.OwnerId = contrAccountOwnerID;
                            newOpp.Type = 'Term Renewal';
                        } else if (contractIDtoOptyCategoryMap.get(newOpp.SBQQ__RenewedContract__c) == 'Support') {
                            // newOpp.Name  = newOpp.Name.replace('MX', 'MX' );
                            newOpp.OwnerId = CSMID;
                            newOpp.Type = 'Support Renewal';
                        } else if (contractIDtoOptyCategoryMap.get(newOpp.SBQQ__RenewedContract__c) == 'Education') {
                            newOpp.Name = newOpp.Name.replace('MX', 'EDU');
                            // newOpp.ownerID = contrAccountOwnerID;
                            newOpp.OwnerId = CSMID;
                            newOpp.Type = 'Education Renewal';
                            newOpp.StageName = 'S3 - Evaluate Options/Proposals';
                        }
                    }
                }
            } // END termFamilyContractIDSet > 0
        } // END IF contractID set > 0
    }
    //
    //    public static void updateEntitlementEndDateFOrTrialMCIDSI(Set<Id> dsiIds, Map<Id, Entitlement> entsToUpdate, Map<Id, SBQQ__Subscription__c> subsToUpdate, Map<Id, DSI__c> dsiToUpdate) {
    //        List<DSI__c> mciDsis = new List<DSI__c>();
    //        if (!dsiIds.isEmpty()) {
    //            List<SBQQ__Subscription__c> subs = [SELECT Id, SBCF_DSI__r.Entitlement_End_Date__c, SBQQ__StartDate__c, SBQQ__SubscriptionEndDate__c FROM SBQQ__Subscription__c WHERE SBCF_DSI__c IN :dsiIds AND SBCF_DSI__r.Platform__c = 'MCI' AND SBCF_DSI__r.Status__c = 'Trial'];
    //            List<Entitlement> entitlements = [SELECT Id, StartDate, EndDate, DSI__c FROM Entitlement WHERE DSI__c IN :dsiIds];
    //            if (subs != null && !subs.isEmpty()) {
    //                for (SBQQ__Subscription__c obj : subs) {
    //                    if (obj.SBQQ__SubscriptionEndDate__c < obj.SBQQ__StartDate__c.addDays(90)) {
    //                        if (subsToUpdate.containsKey(obj.Id)) {
    //                            subsToUpdate.get(obj.Id).SBQQ__SubscriptionEndDate__c = obj.SBQQ__StartDate__c.addDays(90);
    //                        } else {
    //                            obj.SBQQ__SubscriptionEndDate__c = obj.SBQQ__StartDate__c.addDays(90);
    //                            subsToUpdate.put(obj.Id, obj);
    //                        }
    //                        if (dsiToUpdate.containsKey(obj.SBCF_DSI__c)) {
    //                            dsiToUpdate.get(obj.SBCF_DSI__c).Entitlement_End_Date__c = obj.SBQQ__SubscriptionEndDate__c;
    //                        } else {
    //                            dsiToUpdate.put(obj.SBCF_DSI__c, new DSI__c(Id = obj.SBCF_DSI__c, Entitlement_End_Date__c = obj.SBQQ__SubscriptionEndDate__c));
    //                        }
    //                    }
    //                }
    ////                if (subs != null && !subs.isEmpty()) {
    ////                    Database.update(subs);
    ////                }
    //                if (mciDsis != null && !mciDsis.isEmpty()) {
    //                    Database.update(mciDsis);
    //                }
    //            }
    //            if (entitlements != null && entitlements.size() > 0) {
    //                for (Entitlement ent : entitlements) {
    //                    if (ent.EndDate < ent.StartDate.addDays(90)) {
    //                        if (entsToUpdate.containsKey(ent.Id)) {
    //                            entsToUpdate.get(ent.Id).EndDate = ent.StartDate.addDays(90);
    //                        } else {
    //                            ent.EndDate = ent.StartDate.addDays(90);
    //                            entsToUpdate.put(ent.Id, ent);
    //                        }
    //                    }
    //                }
    ////                if (entitlements != null && !entitlements.isEmpty()) {
    ////                    Database.update(entitlements);
    ////                }
    //            }
    //        }
    //    }
    //
    //    public static void terminateMCIDSITrials(Set<Id> dsiIds) {
    //        if (!dsiIds.isEmpty()) {
    //            List<DSI__c> dsis = [SELECT Id, Name, DSIID__c FROM DSI__c WHERE Id IN :dsiIds AND Platform__c = 'MCI' AND Status__c = 'Trial'];
    //            if (dsis != null && !dsis.isEmpty()) {
    //                isMciTrialDeactivated = true;
    //                terminateDSI(dsis[0].DSIID__c, dsis[0].Name);
    //            }
    //        }
    //    }
    //
    //    @future(callout=true)
    //    public static void terminateDSI(String dsiId, String dsiName) {
    //        List<FF_Integration_Log__c> failureLogList = new List<FF_Integration_Log__c>();
    //        Http h = new Http();
    //        HttpRequest terminateEnv = new HttpRequest();
    //        if (TriggersHelper.runningInASandbox) {
    //            // terminateEnv.setEndpoint('https://dev-mci-api.hypernow.microstrategy.com/api/v1/_internal/environments/DSI/' + dsiId);
    //            terminateEnv.setEndpoint('callout:MCIsandboxdev/api/v1/_internal/environments/DSI/' + dsiId);
    //
    //        } else {
    //
    //        }
    //        terminateEnv.setHeader('Content-Type', 'application/json; charset=UTF-8');
    //        terminateEnv.setHeader('Authorization', '{!$Credential.Password}');
    //        terminateEnv.setMethod('DELETE');
    //        HttpResponse resp = h.send(terminateEnv);
    //
    //        if (resp.getStatusCode() != 200 || Test.isRunningTest()) {
    //            //Failed call to MCI - Create Error Log for Investigation
    //            FF_Integration_Log__c errorReport = new FF_Integration_Log__c();
    //            errorReport.Debug_Message__c = 'MCI Account Update Failed: \n' + resp.getStatusCode() + '\n' + resp.getBody();
    //            errorReport.Type__c = 'Error';
    //            errorReport.Reference_Id__c = dsiName;
    //            failureLogList.add(errorReport);
    //            SlackProcessPublisher.SlackPayLoad newPost = new SlackProcessPublisher.SlackPayLoad();
    //            newPost.publishText = 'Env termination failed for :' + dsiName;
    //            newPost.publishText2 = errorReport.Debug_Message__c;
    //            newPost.slackDurl = '*******************************************************************************';
    //            List<SlackProcessPublisher.SlackPayLoad> slackPostList = new List<SlackProcessPublisher.SlackPayLoad>{
    //                    newPost
    //            };
    //
    //            if (!Test.isRunningTest()) {
    //                SlackProcessPublisher.postToSlack(slackPostList);
    //            }
    //        }
    //    }

    //Called in OpportunityTrigger after update
    //Run though updates opportunities and check if Business_Executive__c changed, if yes we need to delete old Opportunity_Contact_Member__c
    //for previous Business_Executive__c and create new records for new Business_Executive__c.
    public static void checkForOCMChange (Opportunity oldOpp, Opportunity newOpp, Set<Id> oppIds, Set<Id> oldRelatedContactIds, Set<Id> newRelatedContactIds, List<Opportunity> opportunitiesOldOCM, List<Opportunity> opportunitiesNewOCM) {
        //3 set collections to pass to removeOCMRecords and createOCMRecords methods not to loop twice though records
        //Compare old and new records to check if Business_Executive__c changed
        if (oldOpp == null || oldOpp.Business_Executive__c != newOpp.Business_Executive__c) {
            oppIds.add(newOpp.Id);

            //Only remove old records if previous Business_Executive__c is not blank
            if (String.isNotBlank(oldOpp.Business_Executive__c)) {
                oldRelatedContactIds.add(oldOpp.Business_Executive__c);
                opportunitiesOldOCM.add(oldOpp);
            }

            //Only create new records if new Business_Executive__c is not blank
            if (String.isNotBlank(newOpp.Business_Executive__c)) {
                newRelatedContactIds.add(newOpp.Business_Executive__c);
                opportunitiesNewOCM.add(newOpp);
            }
        }

        if (oldOpp == null || oldOpp.Technical_Executive__c != newOpp.Technical_Executive__c) {
            oppIds.add(newOpp.Id);

            //Only remove old records if previous Technical_Executive__c is not blank
            if (String.isNotBlank(oldOpp.Technical_Executive__c)) {
                oldRelatedContactIds.add(oldOpp.Technical_Executive__c);
                opportunitiesOldOCM.add(oldOpp);
            }

            //Only create new records if new Technical_Executive__c is not blank
            if (String.isNotBlank(newOpp.Technical_Executive__c)) {
                newRelatedContactIds.add(newOpp.Technical_Executive__c);
                if (!opportunitiesNewOCM.contains(newOpp)) {
                    opportunitiesNewOCM.add(newOpp);
                }
            }
        }
    }

    //Called in checkForOCMChange and OpportunityTriggers after insert
    public static void createOCMRecords (List<Opportunity> opportunities, Set<Id> oppIds, Set<Id> businessExecutiveIds) {
        //build a set of keys for Opportunity_Contact_Member__c so we know if record already exists, not to create duplicates.
        //Can happen only if is called from checkForOCMChange, after insert there is no chance duplicate exists
        Set<String> existingOCMKeys = new Set<String>();
        if (oppIds != null && !oppIds.isEmpty() && businessExecutiveIds != null && !businessExecutiveIds.isEmpty()) {
            for (Opportunity_Contact_Member__c ocm : [
                SELECT Id, Opportunity__c, Contact__c
                FROM Opportunity_Contact_Member__c
                WHERE Contact__c IN :businessExecutiveIds
                AND Opportunity__c IN :oppIds
            ]) {
                existingOCMKeys.add(ocm.Opportunity__c + '' + ocm.Contact__c);
            }
        }

        List<Opportunity_Contact_Member__c> ocmToCreate = new List<Opportunity_Contact_Member__c>();
        for (Opportunity opp : opportunities) {
            if (String.isNotBlank(opp.Business_Executive__c) && !existingOCMKeys.contains(opp.Id + '' + opp.Business_Executive__c)) {
                ocmToCreate.add(new Opportunity_Contact_Member__c(Contact__c = opp.Business_Executive__c, Opportunity__c = opp.Id, Opportunity_Stage__c = opp.StageName));
            }

            if (String.isNotBlank(opp.Technical_Executive__c) && !existingOCMKeys.contains(opp.Id + '' + opp.Technical_Executive__c) && opp.Business_Executive__c != opp.Technical_Executive__c) {
                ocmToCreate.add(new Opportunity_Contact_Member__c(Contact__c = opp.Technical_Executive__c, Opportunity__c = opp.Id, Opportunity_Stage__c = opp.StageName));
            }
        }
        insert ocmToCreate;
    }

    public static void removeOCMRecords (List<Opportunity> opportunities, Set<Id> oppIds, Set<Id> businessExecutiveIds) {
        Map<Id, Opportunity_Contact_Member__c> ocmToDelete = new Map<Id, Opportunity_Contact_Member__c>();
        //Query for old Opportunity_Contact_Member__c and map them by key Opp Id + Contact Id so we know what exactly to remove
        Map<String, Opportunity_Contact_Member__c> relatedOCM = new Map<String, Opportunity_Contact_Member__c>();
        for (Opportunity_Contact_Member__c ocm : [
            SELECT Id, Opportunity__c, Contact__c
            FROM Opportunity_Contact_Member__c
            WHERE Contact__c IN :businessExecutiveIds
            AND Opportunity__c IN :oppIds
        ]) {
            relatedOCM.put(ocm.Opportunity__c + '' + ocm.Contact__c, ocm);
        }

        for (Opportunity oldOpp : opportunities) {
            //If key matches save record to remove
            if (relatedOCM.containsKey(oldOpp.Id + '' + oldOpp.Business_Executive__c)) {
                ocmToDelete.put(relatedOCM.get(oldOpp.Id + '' + oldOpp.Business_Executive__c).Id, relatedOCM.get(oldOpp.Id + '' + oldOpp.Business_Executive__c));
            }

            if (relatedOCM.containsKey(oldOpp.Id + '' + oldOpp.Technical_Executive__c) && oldOpp.Business_Executive__c != oldOpp.Technical_Executive__c) {
                ocmToDelete.put(relatedOCM.get(oldOpp.Id + '' + oldOpp.Technical_Executive__c).Id, relatedOCM.get(oldOpp.Id + '' + oldOpp.Technical_Executive__c));
            }
        }

        delete ocmToDelete.values();
    }

    //    public static void updateMCIAccountId(List<Contact> conUpdateList) {
    //
    //        List<Entitlement> toUpdateEntList = new List<Entitlement> ();
    //        List<DSI__c> toUpdateDSIList = new List<DSI__c> ();
    //        List<Opportunity> toUpdateOppList = new List<Opportunity>();
    //        List<SBQQ__Quote__c> toUpdateQuoteList = new List<SBQQ__Quote__c>();
    //        List<SBQQ__Subscription__c> toUpdateSubList = new List<SBQQ__Subscription__c>();
    //
    //        Map<Id, Id > conToAccntIDsMap = new Map<Id, Id > ();
    //        Map<Id, Set<Id> > conToEntIDsMap = new Map<Id, Set<Id> > ();
    //        Map<Id, Set<Id> > conToDSIIDsMap = new Map<Id, Set<Id> > ();
    //        Map<Id, Id > oppIdToConIdMap = new Map<Id, Id > ();
    //
    //        for (Contact con : [SELECT Id, AccountId FROM Contact WHERE Id IN :conUpdateList]) {
    //            conToAccntIDsMap.put(con.Id, con.AccountId);
    //        }
    //
    //        for (OpportunityContactRole optconJunc : [SELECT ContactId, OpportunityId FROM OpportunityContactRole WHERE ContactId IN :conUpdateList]) {
    //            oppIdToConIdMap.put(optconJunc.OpportunityId, optconJunc.ContactId);
    //        }
    //        for (Opportunity opp : [SELECT Id, AccountId FROM Opportunity WHERE Id IN :oppIdToConIdMap.keySet() AND StageName != 'S6 - Closed Won' AND StageName != 'S0 - Closed Lost' AND Platform__c = 'MCI']) {
    //            if (oppIdToConIdMap.get(opp.Id) != null && conToAccntIDsMap.get(oppIdToConIdMap.get(opp.Id)) != null) {
    //                opp.AccountId = conToAccntIDsMap.get(oppIdToConIdMap.get(opp.Id));
    //                opp.CPQ_UAT_Name__c = 'Education';
    //                toUpdateOppList.add(opp);
    //            }
    //        }
    //
    //        if (toUpdateOppList.size() > 0) {
    //            update toUpdateOppList;
    //            for (SBQQ__Quote__c qte : [SELECT Id, SBQQ__Opportunity2__r.AccountId, SBQQ__Account__c FROM SBQQ__Quote__c WHERE SBQQ__Opportunity2__c IN :toUpdateOppList AND SBQQ__Status__c = 'Q01 SAE' AND Platform__c = 'MCI']) {
    //                qte.SBQQ__Account__c = qte.SBQQ__Opportunity2__r.AccountId;
    //                toUpdateQuoteList.add(qte);
    //            }
    //            if (toUpdateQuoteList.size() > 0) {
    //                update toUpdateQuoteList;
    //            }
    //        }
    //
    //        for (EntitlementContact juncRec : [
    //                SELECT Id, ContactId, EntitlementId, Entitlement.DSI__c
    //                FROM EntitlementContact
    //                WHERE ContactId IN :conUpdateList
    //                AND Entitlement.Product__r.ProductCode IN ('89900')
    //        ]) {
    //            Set<Id> tempEntIDSet = conToEntIDsMap.get(juncRec.ContactId) != null ? conToEntIDsMap.get(juncRec.ContactId) : new Set<Id> () ;
    //
    //            tempEntIDSet.add(juncRec.EntitlementId);
    //            conToEntIDsMap.put(juncRec.ContactId, tempEntIDSet);
    //
    //            Set<Id> tempDSIIDSet = conToDSIIDsMap.get(juncRec.ContactId) != null ? conToDSIIDsMap.get(juncRec.ContactId) : new Set<Id> () ;
    //            tempDSIIDSet.add(juncRec.Entitlement.DSI__c);
    //            conToDSIIDsMap.put(juncRec.ContactId, tempDSIIDSet);
    //        }
    //
    //        for (Contact con : conUpdateList) {
    //            if (conToEntIDsMap.get(con.Id) != null && conToEntIDsMap.get(con.Id).size() > 0) {
    //                for (Entitlement ent : [SELECT Id, AccountId FROM Entitlement WHERE Id IN :conToEntIDsMap.get(con.Id) AND Entitlement_Status__c = 'Active']) {
    //                    ent.AccountId = conToAccntIDsMap.get(con.Id);
    //                    toUpdateEntList.add(ent);
    //                }
    //            }
    //
    //            if (conToDSIIDsMap.get(con.Id) != null && conToDSIIDsMap.get(con.Id).size() > 0) {
    //                for (DSI__c dsiRec : [SELECT Id, Account__c FROM DSI__c WHERE Id IN :conToDSIIDsMap.get(con.Id) AND Status__c = 'Active']) {
    //                    dsiRec.Account__c = conToAccntIDsMap.get(con.Id);
    //                    toUpdateDSIList.add(dsiRec);
    //                }
    //            }
    //        }
    //
    //        if (toUpdateEntList.size() > 0) {
    //            update toUpdateEntList;
    //        }
    //        if (toUpdateDSIList.size() > 0) {
    //            update toUpdateDSIList;
    //            for (SBQQ__Subscription__c sub : [SELECT Id, SBQQ__Account__c, SBCF_DSI__r.Account__c FROM SBQQ__Subscription__c WHERE SBCF_DSI__c IN :toUpdateDSIList]) {
    //                sub.SBQQ__Account__c = sub.SBCF_DSI__r.Account__c;
    //                toUpdateSubList.add(sub);
    //            }
    //
    //            if (toUpdateSubList.size() > 0) {
    //                update toUpdateSubList;
    //            }
    //        }
    //    }

    /**Case 336643  - CPQ - Opportunity - Trigger to avoid deletion and amount changes in Opportunity and OLI
AFTER UPDATE - Opty stage changed from S6  */
    public static void optyReopenedAlert (List<Opportunity> reopenedOptyList) {
        if (reopenedOptyList.size() > 0 && unexecutionFirstRun == true) {
            unexecutionFirstRun = false;

            List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
            EmailTemplate emailTemplate = [SELECT Id, Subject, HtmlValue, Body FROM EmailTemplate WHERE DeveloperName = 'Opportunity_Reopened_Notification_Custom'];
            Set<String> toEmailSet;
            String htmlBody;
            String plainBody;
            String subjectLine;
            MSTR_Global_Configuation__mdt metaDataRec = MSTR_Global_Configuation__mdt.getInstance('OpportunityReopenedEmailList');
            if (metaDataRec != null && metaDataRec.Value__c != null) {
                String[] splited = metaDataRec.Value__c.split(';');
                toEmailSet = new Set<String>(splited);
            }

            for (Opportunity reOpenedOpty : [
                SELECT
                Id, Name, Region_Lookup__r.Name, Territory_Lookup__r.Name, Account.Name, Amount, Gross_Amount__c, Gross_License__c, License_Forecast_Formula__c, License_Discount_Percent__c, Net_Analytics__c, Net_Mobility__c, Net_Security__c, Support__c, Net_Consulting__c, Net_Education__c, Primary_Competitor__c, Primary_SE__r.Name, Executive_Sponsor_user_lookup__r.Name
                FROM Opportunity
                WHERE Id IN :reopenedOptyList
            ]) {
                htmlBody = emailTemplate.HtmlValue;
                plainBody = emailTemplate.Body;
                subjectLine = emailTemplate.Subject;
                htmlBody = htmlBody.replace(']]>', '');
                // Create the account URL from the base URL.
                String opportunityURL = Url.getOrgDomainUrl().toExternalForm() + '/' + reOpenedOpty.Id;

                subjectLine = subjectLine.replace('OpportunityName', reOpenedOpty.Name);
                htmlBody = htmlBody.replace('{!Opportunity.Name}', '<a href=' + opportunityURL + '>' + reOpenedOpty.Name + '</a>');
                plainBody = plainBody.replace('{!Opportunity.Name}', '<a href=' + opportunityURL + '>' + reOpenedOpty.Name + '</a>');

                htmlBody = htmlBody.replace('{!Opportunity.Region_Lookup__c}', reOpenedOpty.Region_Lookup__r.Name != null ? String.valueOf(reOpenedOpty.Region_Lookup__r.Name) : '');
                htmlBody = htmlBody.replace('{!Opportunity.Territory_Lookup__c}', reOpenedOpty.Territory_Lookup__r.Name != null ? String.valueOf(reOpenedOpty.Territory_Lookup__r.Name) : '');

                htmlBody = htmlBody.replace('{!Opportunity.Account}', reOpenedOpty.Account.Name != null ? String.valueOf(reOpenedOpty.Account.Name) : '0.00');

                htmlBody = htmlBody.replace('AmountVal', reOpenedOpty.Amount != null ? String.valueOf(reOpenedOpty.Amount) : '0.00');
                htmlBody = htmlBody.replace('GrossAmtVal', reOpenedOpty.Gross_Amount__c != null ? String.valueOf(reOpenedOpty.Gross_Amount__c) : '0.00');
                htmlBody = htmlBody.replace('GrossLicenseVal', reOpenedOpty.Gross_License__c != null ? String.valueOf(reOpenedOpty.Gross_License__c) : '0.00');
                htmlBody = htmlBody.replace('NetLicenseVal', reOpenedOpty.License_Forecast_Formula__c != null ? String.valueOf(reOpenedOpty.License_Forecast_Formula__c) : '0.00');
                htmlBody = htmlBody.replace('LicenseDiscountVal', reOpenedOpty.License_Discount_Percent__c != null ? String.valueOf(reOpenedOpty.License_Discount_Percent__c) : '0.00');

                htmlBody = htmlBody.replace('{!Opportunity.Net_Analytics__c}', reOpenedOpty.Net_Analytics__c != null ? String.valueOf(reOpenedOpty.Net_Analytics__c) : '0.00');
                htmlBody = htmlBody.replace('{!Opportunity.Net_Mobility__c}', reOpenedOpty.Net_Mobility__c != null ? String.valueOf(reOpenedOpty.Net_Mobility__c) : '0.00');
                htmlBody = htmlBody.replace('{!Opportunity.Net_Security__c}', reOpenedOpty.Net_Security__c != null ? String.valueOf(reOpenedOpty.Net_Security__c) : '0.00');
                htmlBody = htmlBody.replace('SupportVal', reOpenedOpty.Support__c != null ? String.valueOf(reOpenedOpty.Support__c) : '0.00');

                htmlBody = htmlBody.replace('{!Opportunity.Net_Consulting__c}', reOpenedOpty.Net_Consulting__c != null ? String.valueOf(reOpenedOpty.Net_Consulting__c) : '0.00');
                htmlBody = htmlBody.replace('{!Opportunity.Net_Education__c}', reOpenedOpty.Net_Education__c != null ? String.valueOf(reOpenedOpty.Net_Education__c) : '0.00');

                htmlBody = htmlBody.replace('{!Opportunity.Primary_Competitor__c}', reOpenedOpty.Primary_Competitor__c != null ? String.valueOf(reOpenedOpty.Primary_Competitor__c) : '');
                htmlBody = htmlBody.replace('{!Opportunity.Primary_SE__c}', reOpenedOpty.Primary_SE__r.Name != null ? String.valueOf(reOpenedOpty.Primary_SE__r.Name) : '');
                htmlBody = htmlBody.replace('{!Opportunity.Executive_Sponsor_user_lookup__c}', reOpenedOpty.Executive_Sponsor_user_lookup__r.Name != null ? String.valueOf(reOpenedOpty.Executive_Sponsor_user_lookup__r.Name) : '');
                htmlBody = htmlBody.replace('{!Opportunity.Link}', opportunityURL != null ? opportunityURL : '');

                Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                mail.saveAsActivity = false;
                mail.subject = subjectLine;
                mail.setToAddresses(new List<String>(toEmailSet));

                mail.setHtmlBody(htmlBody);
                mail.setPlainTextBody(plainBody);
                emails.add(mail);
            }

            if (emails.size() > 0) {
                //try{
                if (!Test.isRunningTest()) {
                    Messaging.sendEmail(emails);
                }

                //}
                // catch (Exception e) {

                //}
            }
        }
    }

    public static void updateRelatedOpportunityTeamMembers (Set<Id> opportunitiesWithNewStatus) {
        if (!opportunitiesWithNewStatus.isEmpty()) {
            OpportunityTriggerHelper.teamMembersUpdateFromOpportunity = true;
            update OpportunityTeamTriggerHandler.populateCompRoleField(
                [SELECT Id, UserId FROM OpportunityTeamMember WHERE OpportunityId IN :opportunitiesWithNewStatus]
            );
            OpportunityTriggerHelper.teamMembersUpdateFromOpportunity = false;
        }
    }

    //--------------keyGeneration------------------
    public static void keyGeneration (List<Opportunity> opportunities) {
        List<Product_Key_Request__c> requestsToInsert = new List<Product_Key_Request__c>();
        List<Version_Map__mdt> versionList = [SELECT Label, Latest_KeyGen__c FROM Version_Map__mdt WHERE Latest_KeyGen__c = TRUE];
        Version_Map__mdt version = new Version_Map__mdt();
        if (versionList.size() > 0) {
            version = versionList[0];
        }
        for (Opportunity temp : opportunities) {
            Product_Key_Request__c tempRequest = new Product_Key_Request__c();
            tempRequest.Account__c = temp.AccountId;
            tempRequest.DSI__c = temp.DSI__c;
            tempRequest.Manual_Key__c = false;
            tempRequest.Version_List__c = String.valueOf(version.Label);  //'10.4.0'; commented to change from static to dynamic
            tempRequest.Ship_Contact__c = temp.Ship_To_Contact_SFID__c;
            tempRequest.Opportunity__c = temp.Id;
            tempRequest.QuoteID__c = Integer.valueOf(temp.get('SBCF_QuoteNumber__c'));

            requestsToInsert.add(tempRequest);
            //}
            //}
        }

        if (requestsToInsert.size() > 0) {
            insert requestsToInsert;
            List<Id> requestIds = new List<Id>((new Map<Id, SObject>(requestsToInsert)).keySet());
            //            KeyGeneratorController.generateDSIKeyInvocable(requestIds);
            if (!Test.isRunningTest()) {
                Id jobID = System.enqueueJob(new KeyGeneratorServiceQueue(requestIds));
            }
        }
    }

    // 446697 - SF - MCI - An MCI DSI account should be updated when the opportunity account is changed
    public static void updateRelatedObjectsOnAccountChange (List<Opportunity> opportunitiesToUpdate, Map<Id, Entitlement> entsToUpdate, Map<Id, DSI__c> dsisToUpdate, Map<Id, SBQQ__Quote__c> quotesToUpdateMap) {
        Map<Id, Id> opportunityToNewAccountId = new Map<Id, Id>();
        Map<Id, Id> dsiToUpdateIdsToAccount = new Map<Id, Id>();
        List<DSI__c> dsiToUpdate = new List<DSI__c>();
        Map<Id, Id> quotesToUpdateIdsToAccount = new Map<Id, Id>();
        List<SBQQ__Quote__c> quotesToUpdate = new List<SBQQ__Quote__c>();
        List<Entitlement> entitlementsToUpdate = new List<Entitlement>();

        if (!opportunitiesToUpdate.isEmpty()) {
            for (Opportunity opp : opportunitiesToUpdate) {
                quotesToUpdateIdsToAccount.put(opp.SBQQ__PrimaryQuote__c, opp.AccountId);
                dsiToUpdateIdsToAccount.put(opp.DSI__c, opp.AccountId);
            }

            for (DSI__c dsi : [SELECT Id, Account__c, (SELECT Id, AccountId FROM Entitlements__r) FROM DSI__c WHERE Id IN :dsiToUpdateIdsToAccount.keySet()]) {
                if (dsisToUpdate.containsKey(dsi.Id)) {
                    dsisToUpdate.get(dsi.Id).Account__c = dsiToUpdateIdsToAccount.get(dsi.Id);
                } else {
                    dsi.Account__c = dsiToUpdateIdsToAccount.get(dsi.Id);
                    dsisToUpdate.put(dsi.Id, dsi);
                }
                dsiToUpdate.add(dsi);

                for (Entitlement entitlement : dsi.Entitlements__r) {
                    if (entsToUpdate.containsKey(entitlement.Id)) {
                        entsToUpdate.get(entitlement.Id).AccountId = dsiToUpdateIdsToAccount.get(dsi.Id);
                    } else {
                        entitlement.AccountId = dsiToUpdateIdsToAccount.get(dsi.Id);
                        entsToUpdate.put(entitlement.Id, entitlement);
                    }
                    entitlementsToUpdate.add(entitlement);
                }
            }

            for (SBQQ__Quote__c primaryQuote : [SELECT Id, SBQQ__Account__c FROM SBQQ__Quote__c WHERE Id IN :quotesToUpdateIdsToAccount.keySet()]) {
                if (quotesToUpdateMap.containsKey(primaryQuote.Id)) {
                    quotesToUpdateMap.get(primaryQuote.Id).SBQQ__Account__c = quotesToUpdateIdsToAccount.get(primaryQuote.Id);
                } else {
                    primaryQuote.SBQQ__Account__c = quotesToUpdateIdsToAccount.get(primaryQuote.Id);
                    quotesToUpdateMap.put(primaryQuote.Id, primaryQuote);
                }
                quotesToUpdate.add(primaryQuote);
            }

            //            update dsiToUpdate;
            //            update quotesToUpdate;
            //            update entitlementsToUpdate;
        }
    }

    // Case - 373713  -  AFTER UPDATE
    public static void approvalonStatusChange (List<Opportunity> opportunities) {
        for (Opportunity opp : opportunities) {
            String aprProcessName;
            String toastmessage;
            String subComments;

            if ((opp.StageName == 'S3 - Evaluate Options/Proposals' && !opp.Deal_Team_Approval__c) || ((opp.StageName == 'S4 - Resolve Concerns' || opp.StageName == 'S5 - Negotiate/Purchase Solution') && !opp.Deal_Team_Approval__c && opp.SE_Approval__c)) {
                aprProcessName = 'S1_S2_Approval_Sales';
                subComments = 'Opportunity Deal Team Submitted for Approval';
                toastmessage = 'Opportunity Deal Team Submitted for Approval';
                submitforApproval(opp.Id, aprProcessName, subComments, toastmessage);
                //publishplaformEvent (newOpp.Id,toastmessage);
            } else if ((opp.StageName == 'S4 - Resolve Concerns' && opp.Primary_SE__c != null && !opp.SE_Approval__c && opp.Deal_Team_Approval__c) || (opp.StageName == 'S5 - Negotiate/Purchase Solution' && opp.Primary_SE__c != null && !opp.SE_Approval__c && opp.Deal_Team_Approval__c)) {
                aprProcessName = 'S1_S2_Approval_Sales_EVP';
                toastmessage = 'Opportunity SE Submitted for Approval';
                subComments = 'Opportunity SE Submitted for Approval';
                submitforApproval(opp.Id, aprProcessName, subComments, toastmessage);
                //publishplaformEvent (newOpp.Id,toastmessage);
            } else if ((opp.StageName == 'S4 - Resolve Concerns' || opp.StageName == 'S5 - Negotiate/Purchase Solution') && !opp.Deal_Team_Approval__c && !opp.SE_Approval__c) {
                if (opp.Primary_SE__c == null) {
                    aprProcessName = 'S1_S2_Approval_Sales';
                    subComments = 'Opportunity Deal Team Submitted for Approval';
                    toastmessage = 'Opportunity Deal Team Submitted for Approval';
                    submitforApproval(opp.Id, aprProcessName, subComments, toastmessage);
                    //publishplaformEvent (newOpp.Id,toastmessage);
                } else {
                    aprProcessName = 'S1_S2_Approval_Sales_DT_SE';
                    //aprProcessName = 'S1_S2_Approval_Sales';
                    toastmessage = 'Opportunity Deal Team and SE Submitted for Approval';
                    subComments = 'Opportunity Deal Team Submitted for Approval';
                    submitforApproval(opp.Id, aprProcessName, subComments, toastmessage);
                    //publishplaformEvent (newOpp.Id,toastmessage);
                }
            }

        } // END FOR
    }

    public static void submitforApproval (Id optyId, String aprProcessName, String approvalComments, String toastmessage) {

        Boolean hasCustomPermission = FeatureManagement.checkPermission('ITS_Data_Manager');
        if (!hasCustomPermission) {

            if (optyId != null && aprProcessName != null) {
                List<ProcessInstance> existingApprovals = [
                    SELECT Id, TargetObjectId
                    FROM ProcessInstance
                    WHERE TargetObjectId = :optyId
                    AND Status = 'Pending'
                ];
                if (existingApprovals.size() == 0) {

                    List<Approval.ProcessSubmitRequest> requests = new List<Approval.ProcessSubmitRequest> ();
                    Approval.ProcessSubmitRequest req1 = new Approval.ProcessSubmitRequest();
                    req1.setComments(approvalComments);
                    req1.setObjectId(optyId);
                    req1.setSubmitterId(UserInfo.getUserId());
                    req1.setProcessDefinitionNameOrId(aprProcessName);
                    requests.add(req1);
                    if (requests.size() > 0) {
                        Approval.ProcessResult[] processResults = null;
                        processResults = Approval.process(requests, true);
                        Approval.unlock(optyId, false);
                        OpportunityTriggerHandler.publishplaformEvent (optyId, toastmessage);
                    }
                }
            }

            // Id jobID = System.enqueueJob(new QueueableSubmitForApproval(optyId, aprProcessName, approvalComments, toastmessage));
        }
    }

    //-------------Generate STd entitlement when Cloud support SKU exists during Ren S6-----------------
    public static void CLDRenentitlement (List<Opportunity> oppList, Map<Id, Opportunity> oldMap) {
        Set<String> quoteIds = new Set<String>();
        for (Opportunity opp : oppList) {
            if ((opp.SBQQ__PrimaryQuote__c != null && opp.StageName == 'S6 - Closed Won' && opp.StageName != oldMap.get(opp.Id).StageName) || (Test.isRunningTest() && opp.Name == 'CLDRenentitlementTest')) {
                quoteIds.add(opp.SBQQ__PrimaryQuote__c);
            }
        }

        if (!quoteIds.isEmpty()) {
            Set<Id> dsiRecIds = new Set<Id>();
            List<SBQQ__QuoteLine__c> quoteLines = new List<SBQQ__QuoteLine__c>();
            Map<String, Date> maxEndDateMap = new Map<String, Date>();
            Set<String> accIds = new Set<String>();
            Set<String> skuIds = new Set<String>();
            List<SBQQ__QuoteLine__c> qlines = new List<SBQQ__QuoteLine__c>();
            for (
                SBQQ__QuoteLine__c qLineRec : [
                    SELECT
                    Id, SBQQ__Quote__r.SBQQ__Opportunity2__r.AccountId, SBQQ__Product__r.Name, SBQQ__StartDate__c, SBQQ__EndDate__c, SBQQ__Quote__c, SBQQ__Quote__r.SBQQ__Opportunity2__c, SBQQ__EffectiveEndDate__c, SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__r.Account__r.District__r.Business_Hours__c, SBQQ__Quote__r.SBQQ__Type__c, SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__r.Name, SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c, SBQQ__Product__c, SBQQ__ProductCode__c, Opportunity__c, SBCF_DSI__c, SBCF_DSI__r.Name, SBCF_DSI__r.Account__r.District__r.Business_Hours__c, Interval__c, SBQQ__DefaultSubscriptionTerm__c, SBQQ__ProrateMultiplier__c, SBQQ__SubscriptionTerm__c
                    FROM SBQQ__QuoteLine__c
                    WHERE
                    SBQQ__Quote__c IN :quoteIds
                    AND SBQQ__EndDate__c != NULL
                    //AND SBQQ__SubscriptionTerm__c != NULL AND
                    AND SBQQ__Quote__r.SBQQ__Type__c = 'Renewal'
                    AND SBQQ__ProductCode__c = '89690'
                    ORDER BY SBQQ__EndDate__c DESC
                ]
            ) {
                String key = qLineRec.SBQQ__Quote__c + qLineRec.SBQQ__ProductCode__c;
                if (!skuIds.contains(key)) {
                    dsiRecIds.add(qLineRec.SBCF_DSI__c);
                    if (qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c != null) {
                        dsiRecIds.add(qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c);
                    }
                    quoteLines.add(qLineRec);

                    if (!maxEndDateMap.containsKey(qLineRec.SBQQ__Quote__c)) {
                        maxEndDateMap.put(qLineRec.SBQQ__Quote__c, qLineRec.SBQQ__EndDate__c);
                    } else {
                        Date maxDt = qLineRec.SBQQ__EndDate__c > maxEndDateMap.get(qLineRec.SBQQ__Quote__c) ? qLineRec.SBQQ__EndDate__c : maxEndDateMap.get(qLineRec.SBQQ__Quote__c);
                        maxEndDateMap.put(qLineRec.SBQQ__Quote__c, maxDt);
                    }

                    if (qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.AccountId != null) {
                        accIds.add(qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.AccountId);
                    }
                    skuIds.add(key);
                }
            }

            if (!quoteLines.isEmpty()) {
                Map<String, Entitlement> entMap = getExistingEntitlements(dsiRecIds);
                Map<String, SlaProcess> entProcessmap = getEntitlementProcess();
                List<User> usrlst = [SELECT Id FROM User WHERE Name = 'SVC_APP_INTEGRATION'];

                List<Entitlement> entitlements = new List<Entitlement>();
                List<Entitlement> newEntitlements = new List<Entitlement>();
                Set<String> avoidDup = new Set<String>();
                for (SBQQ__QuoteLine__c qLineRec : quoteLines) {
                    String dsi_name = (qLineRec.SBCF_DSI__r.Name != null && qLineRec.SBCF_DSI__r.Name != '' ? qLineRec.SBCF_DSI__r.Name : qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__r.Name);
                    Id dsi = (qLineRec.SBCF_DSI__c != null) ? qLineRec.SBCF_DSI__c : qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c;
                    String StandardEntKey = String.valueOf(dsi) + 'Standard';

                    if (!entMap.containsKey(StandardEntKey)) {
                        Entitlement ent = new Entitlement();
                        ent.DSI__c = dsi;
                        ent.AccountId = qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.AccountId;
                        ent.Support_Level__c = 'Standard';
                        ent.SKU__c = '30089';
                        ent.Product__c = '01t4400000Bsx9F';
                        ent.StartDate = System.today();
                        ent.EndDate = maxEndDateMap.get(qLineRec.SBQQ__Quote__c); //qlineRec.SBQQ__EndDate__c;
                        ent.Name = dsi_name + ' - ' + 'Standard';
                        if (entProcessmap != null && entProcessmap.get('Standard') != null) {
                            ent.SlaProcessId = entProcessmap.get('Standard').Id;
                        }

                        ent.BusinessHoursId = qLineRec.SBCF_DSI__c != null ? qLineRec.SBCF_DSI__r.Account__r.District__r.Business_Hours__c : qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__r.Account__r.District__r.Business_Hours__c;
                        ent.Opportunity__c = qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c;
                        ent.LastUpdateddate__c = System.now();
                        if (usrlst != null && !usrlst.isEmpty()) {
                            ent.LastUpdatedBy__c = usrlst[0].Id;
                        }
                        entitlements.add(ent);
                        newEntitlements.add(ent);
                    } else {
                        Entitlement ent = entMap.get(StandardEntKey);

                        if (!avoidDup.contains(ent.Id)) {
                            ent.Opportunity__c = qLineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c;
                            ent.LastUpdateddate__c = System.now();
                            if (usrlst != null && !usrlst.isEmpty()) {
                                ent.LastUpdatedBy__c = usrlst[0].Id;
                            }
                            ent.EndDate = maxEndDateMap.get(qLineRec.SBQQ__Quote__c); //(ent.EndDate != null ? (ent.EndDate > qlineRec.SBQQ__EndDate__c ? ent.EndDate : qlineRec.SBQQ__EndDate__c ): qlineRec.SBQQ__EndDate__c );
                            entitlements.add(ent);
                            avoidDup.add(ent.Id);
                        }
                    }
                }
                if (!entitlements.isEmpty() && !Test.isRunningTest()) {
                    upsert entitlements;

                    Map<String, Set<String>> supportConMap = getSupportContact(accIds);
                    List<EntitlementContact> entContacts = new List<EntitlementContact>();
                    for (Entitlement ent : newEntitlements) {
                        if (supportConMap.containsKey(ent.AccountId)) {
                            for (String conId : supportConMap.get(ent.AccountId)) {
                                entContacts.add(new EntitlementContact(EntitlementId = ent.Id, ContactId = conId));
                            }
                        }
                    }

                    if (!entContacts.isEmpty()) {
                        insert entContacts;
                    }
                }
            }
        }
    }

    @TestVisible
    private static Map<String, Entitlement> getExistingEntitlements (Set<Id> dsiRecIds) {
        Map<String, Entitlement> temp = new Map<String, Entitlement>();
        for (Entitlement e : [SELECT Id, Name, Support_Level__c, DSI__c, EndDate FROM Entitlement WHERE DSI__c IN :dsiRecIds]) {
            temp.put((String.valueOf(e.DSI__c) + e.Support_Level__c), e);
        }
        return temp;
    }

    @TestVisible
    private static Map<String, SlaProcess> getEntitlementProcess () {
        Map<String, SlaProcess> epmap = new Map<String, SlaProcess>();
        for (SlaProcess ep : [SELECT Id, Name, BusinessHoursId FROM SlaProcess WHERE Name = 'Standard']) {
            epmap.put(ep.Name, ep);
        }
        return epmap;
    }

    @TestVisible
    private static Map<String, Set<String>> getSupportContact (Set<String> accIds) {
        Map<String, Set<String>> retMap = new Map<String, Set<String>>();
        for (Contact con : [SELECT Id, AccountId FROM Contact WHERE AccountId IN :accIds AND Support_Liaison__c = TRUE]) {
            Set<String> temp = retMap.containsKey(con.AccountId) ? retMap.get(con.AccountId) : new Set<String>();
            temp.add(con.Id);
            retMap.put(con.AccountId, temp);
        }
        return retMap;
    }

    public static void CreateDSILineItemsInContext (List<Opportunity> newOppList, Set<Id> oppIdSet, Set<Id> DSIidSet, Map<Id, Opportunity> oppsToUpdate) {

        Set<Id> oppIds = oppIdSet;
        Set<Id> DSIids = DSIidSet;
        Id oppSORtypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Sales Opportunity (New)').getRecordTypeId();
        Id oppRORtypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Renewal Opportunity (New)').getRecordTypeId();
        List<User> usrlst = [SELECT Id FROM User WHERE Name = 'SVC_APP_INTEGRATION'];

        if (!oppIds.isEmpty()) {
            Map<String, List<OpportunityLineItem>> oppLITMmap = OLICreateDSILineItemsHelper.getAllOppLineItems(oppIds);
            Map<String, Map<String, DSI_Line_Item__c>> DSiLIntItemsmap = OLICreateDSILineItemsHelper.getExistingDSIs(DSIids);

            List<DSI_Line_Item__c> DSiLineItemList = new List<DSI_Line_Item__c> ();

            Map<Id, DSI_Line_Item__c> duplicateDSImap = new Map<Id, DSI_Line_Item__c> ();

            Map<Id, Opportunity> loadedOpportunities = new Map<Id, Opportunity> ();
            for (Opportunity opp : newOppList) {
                if (oppLITMmap.containsKey(opp.Id)) {
                    for (OpportunityLineItem oplt : oppLITMmap.get(opp.Id)) {

                        Id oliDSI = (oplt.SBQQ__QuoteLine__c != null && oplt.SBQQ__QuoteLine__r.SBCF_DSI__c != null) ? oplt.SBQQ__QuoteLine__r.SBCF_DSI__c : opp.DSI__c; //AK
                        if (oliDSI != null) {
                            if (duplicateDSImap.containsKey(oplt.Product2Id) || (DSiLIntItemsmap.containsKey(oliDSI) && DSiLIntItemsmap.get(oliDSI).containsKey(oplt.Product2Id))) {

                                DSI_Line_Item__c dsi = duplicateDSImap.containsKey(oplt.Product2Id) ? duplicateDSImap.get(oplt.Product2Id) : DSiLIntItemsmap.get(oliDSI).get(oplt.Product2Id);
                                dsi.Name = oplt.Product_Name__c;
                                dsi.Quantity__c = (dsi.Quantity__c != null ? dsi.Quantity__c : 0) + (oplt.Quantity__c != null ? oplt.Quantity__c : 0);
                                dsi.Amount__c = (dsi.Amount__c != null ? dsi.Amount__c : 0) + oplt.TotalPrice;
                                dsi.End_Date__c = (dsi.End_Date__c != null ? (dsi.End_Date__c > oplt.End_Date__c ? dsi.End_Date__c : oplt.End_Date__c) : dsi.End_Date__c);

                                if (duplicateDSImap.containsKey(oplt.Product2Id)) {
                                    duplicateDSImap.put(oplt.Product2Id, dsi);
                                } else {
                                    dsi.Opportunity__c = opp.Id;
                                    dsi.LastUpdateddate__c = System.now();
                                    if (usrlst != null && !usrlst.isEmpty()) {
                                        dsi.LastUpdatedBy__c = usrlst[0].Id;
                                    }
                                    DSiLineItemList.add(dsi);
                                    duplicateDSImap.put(oplt.Product2Id, dsi);
                                }
                            } else {
                                DSI_Line_Item__c dsi = new DSI_Line_Item__c();
                                dsi.Name = oplt.Product_Name__c;
                                dsi.Amount__c = oplt.TotalPrice;
                                dsi.End_Date__c = oplt.End_Date__c;
                                dsi.Quantity__c = oplt.Quantity__c;
                                dsi.SKU__c = oplt.ProductCode;
                                dsi.Start_Date__c = oplt.Start_Date__c != null ? System.today() : null;
                                dsi.Interval__c = oplt.Interval__c;
                                dsi.Product__c = oplt.Product2Id;
                                dsi.DSI_Product_Name__c = oplt.Product_Name__c;
                                //dsi.DSI__c = opp.DSI__c;
                                dsi.DSI__c = oliDSI ; //AK
                                dsi.Opportunity__c = opp.Id;
                                dsi.LastUpdateddate__c = System.now();
                                if (usrlst != null && !usrlst.isEmpty()) {
                                    dsi.LastUpdatedBy__c = usrlst[0].Id;
                                }
                                if (oplt.Opportunity_Service__c != null) {
                                    dsi.DSI_Line_Item_Service__c = oliDSI ;

                                } else if (oplt.Opportunity_Product__c != null) {
                                    dsi.DSI_Line_Item_Software__c = oliDSI ;
                                }
                                duplicateDSImap.put(oplt.Product2Id, dsi);
                                DSiLineItemList.add(dsi);
                            }
                        }
                        if (!loadedOpportunities.containsKey(opp.Id)) {
                            if (oppsToUpdate.containsKey(opp.Id)) {
                                oppsToUpdate.get(opp.Id).DSI_Loaded__c = true;
                            } else {
                                Opportunity tempObj = opp.clone();
                                tempObj.DSI_Loaded__c = true;
                                tempObj.Id = opp.Id;
                                oppsToUpdate.put(opp.Id, tempObj);
                            }
                            //Opportunity oppNew = new Opportunity(Id = opp.Id, DSI_Loaded__c = true);
                            //loadedOpportunities.put(opp.Id, oppNew);
                        }
                    }
                }
            }
            if (!DSiLineItemList.isEmpty()) {
                upsert DSiLineItemList;
            }

            //            if (!loadedOpportunities.isEmpty()) {
            //                upsert loadedOpportunities.values();
            //            }
            //createEntitlements(oppIds, DSIids, usrlst, null);

        }
    }

    public static void updateMCIOptyAccountId (List<Opportunity> OptyUpdateList, Map<Id, Entitlement> entsToUpdate, Map<Id, SBQQ__Subscription__c> subsToUpdate, Map<Id, DSI__c> dsiToUpdate, Map<Id, SBQQ__Quote__c> quotesToUpdate) {
        List<Entitlement> toUpdateEntList = new List<Entitlement> ();
        List<DSI__c> toUpdateDSIList = new List<DSI__c> ();
        List<SBQQ__Quote__c> toUpdateQuoteList = new List<SBQQ__Quote__c>();
        List<SBQQ__Subscription__c> toUpdateSubList = new List<SBQQ__Subscription__c>();
        Map<Id, Id > optyToDSIIDsMap = new Map<Id, Id > ();

        for (Opportunity opty : [SELECT Id, AccountId, DSI__c FROM Opportunity WHERE Id IN :OptyUpdateList AND Platform__c = 'MCI']) {
            optyToDSIIDsMap.put(opty.DSI__c, opty.AccountId);
        }

        for (SBQQ__Quote__c qte : [SELECT Id, SBQQ__Opportunity2__r.AccountId, SBQQ__Account__c FROM SBQQ__Quote__c WHERE SBQQ__Opportunity2__c IN :OptyUpdateList AND SBQQ__Status__c = 'Q01 SAE' AND Platform__c = 'MCI']) {
            if (quotesToUpdate.containsKey(qte.Id)) {
                quotesToUpdate.get(qte.Id).SBQQ__Account__c = qte.SBQQ__Opportunity2__r.AccountId;
            } else {
                qte.SBQQ__Account__c = qte.SBQQ__Opportunity2__r.AccountId;
                quotesToUpdate.put(qte.Id, qte);
            }
            // toUpdateQuoteList.add(qte);
        }

        //        if (toUpdateQuoteList.size() > 0) {
        //            update toUpdateQuoteList;
        //        }

        for (DSI__c dsiRec : [SELECT Id, Account__c FROM DSI__c WHERE Id IN (SELECT DSI__c FROM Opportunity WHERE Id IN :OptyUpdateList) AND Status__c = 'Active']) {
            if (dsiToUpdate.containsKey(dsiRec.Id)) {
                dsiToUpdate.get(dsiRec.Id).Account__c = optyToDSIIDsMap.get(dsiRec.Id);
            } else {
                dsiRec.Account__c = optyToDSIIDsMap.get(dsiRec.Id);
                dsiToUpdate.put(dsiRec.Id, dsiRec);
            }
            toUpdateDSIList.add(dsiRec);
        }

        if (toUpdateDSIList.size() > 0) {
            // update toUpdateDSIList;
            for (SBQQ__Subscription__c sub : [SELECT Id, SBQQ__Account__c, SBCF_DSI__r.Account__c FROM SBQQ__Subscription__c WHERE SBCF_DSI__c IN :toUpdateDSIList]) {
                if (subsToUpdate.containsKey(sub.Id)) {
                    subsToUpdate.get(sub.Id).SBQQ__Account__c = sub.SBCF_DSI__r.Account__c;
                } else {
                    sub.SBQQ__Account__c = sub.SBCF_DSI__r.Account__c;
                    subsToUpdate.put(sub.Id, sub);
                }
                // toUpdateSubList.add(sub);
            }

            for (Entitlement ent : [SELECT Id, AccountId, DSI__r.Account__c FROM Entitlement WHERE DSI__c IN :toUpdateDSIList]) {
                if (entsToUpdate.containsKey(ent.Id)) {
                    entsToUpdate.get(ent.Id).AccountId = ent.DSI__r.Account__c;
                } else {
                    ent.AccountId = ent.DSI__r.Account__c;
                    entsToUpdate.put(ent.Id, ent);
                }
                // toUpdateEntList.add(ent);
            }
        }
        //        if (toUpdateSubList.size() > 0) {
        //            update toUpdateSubList;
        //        }
        //        if (toUpdateEntList.size() > 0) {
        //            update toUpdateSubList;
        //        }

    }

    public static void insertPSDandCSMTeamMembersFromAccount (Map<Id, Account> accounts, List<Opportunity> opportunities) {
        if (!accounts.isEmpty()) {
            for (Opportunity opp : opportunities) {
                if (opp.AccountId != null && accounts.containsKey(opp.AccountId)){ 
                    if(accounts.get(opp.AccountId).Primary_Services_Director__c != null) {
                    opp.Primary_Services_Director__c = accounts.get(opp.AccountId).Primary_Services_Director__c;
                    }
                    if(accounts.get(opp.AccountId).Customer_Success_Manager__c != null) {
                        opp.Customer_Success_Manager__c = accounts.get(opp.AccountId).Customer_Success_Manager__c;
                    }
                }
            }
        }
    }

    public static void insertCSMTeamMembersFromAccount (Map<Id, Account> accounts, Map<Id, Opportunity> opportunitiesByIds, List<OpportunityTeamMember> membersToInsert) {
        if (!accounts.isEmpty()) {
            for (Opportunity opp : opportunitiesByIds.values()) {
                if (opp.AccountId != null && accounts.containsKey(opp.AccountId)) {
                    Account acc = accounts.get(opp.AccountId);
                    //Old Customer Success Manager --> New Customer Renewals Manager
                    for (AccountTeamMember atm : acc.AccountTeamMembers) {
                        if (atm.TeamMemberRole == 'Customer Success Manager') {
                            membersToInsert.add(new OpportunityTeamMember(UserId = atm.UserId, TeamMemberRole = atm.TeamMemberRole, OpportunityAccessLevel = atm.OpportunityAccessLevel, Split_Percentage__c = 100, OpportunityId = opp.Id, Primary__c = true));
                        }
                    }
                }
            }
        }
    }

    public static void insertCSMTeamMembersFromOpp(List<Opportunity> opps, List<OpportunityTeamMember> membersToInsert) {
        for (Opportunity opp : opps) {
            membersToInsert.add(new OpportunityTeamMember(UserId = opp.Customer_Success_Manager__c, TeamMemberRole = 'Customer Success Manager New', OpportunityAccessLevel = 'Edit', Split_Percentage__c = 100, OpportunityId = opp.Id, Primary__c = true));
        }
    }

    public static void unlockRecords(Set<Id> oppsToUnlock) {
        Approval.unlock(new List<Id> (oppsToUnlock), false);
    }

    public static void updateProjectStatusOnOppClose (Set<Id> oppsToUpdateProjectStatus) {
        List<pse__Proj__c> projToUpdate = new List<pse__Proj__c>();
        List<pse__Proj__c> relatedProjects = [
            SELECT Id, pse__Stage__c
            FROM pse__Proj__c
            WHERE pse__Opportunity__c IN :oppsToUpdateProjectStatus AND
            pse__Stage__c != 'Finance Review' AND
            Stage__c != 'E1 - Planning' AND
            Stage__c != 'E0 - Cancelled' AND
            Stage__c != 'E7 - Closed'
        ];

        for (pse__Proj__c proj : relatedProjects) {
            proj.pse__Stage__c = 'Finance Review';
            projToUpdate.add(proj);
        }
        if (!projToUpdate.isEmpty()) {
            update projToUpdate;
        }
    }

    public static void createOpportunityInfluence (List<Id> oppIdsToSync, Map<Id, Id> mapCampaignsToRemove, Boolean isUpdate) {
        Map<String, Id> keyToInfluence = new Map<String, Id>();
        List<CampaignInfluence> influenceToInsert = new List<CampaignInfluence>();
        List<CampaignInfluence> toDelete = new List<CampaignInfluence>();

        List<MSTR_Global_Configuation__mdt> config = [SELECT Id,Type__c,Value__c FROM MSTR_Global_Configuation__mdt WHERE Type__c = 'CampaignInfluenceModel'];

        List<Opportunity> oppsToSync = [SELECT Id,CampaignId,Business_Executive__c FROM Opportunity WHERE Id IN:oppIdsToSync];

        for (CampaignInfluence campInf : [SELECT CampaignId,OpportunityId,Id FROM CampaignInfluence WHERE ModelId = :config[0].Value__c AND OpportunityId IN:oppIdsToSync]) {
            keyToInfluence.put(campInf.OpportunityId + '' + campInf.CampaignId, campInf.Id);
        }

        for (Opportunity opp : oppsToSync) {
            if (opp.CampaignId != null && !keyToInfluence.containsKey(opp.Id + '' + opp.CampaignId)) {
                CampaignInfluence tempInfluence = new CampaignInfluence(OpportunityId = opp.Id, CampaignId = opp.CampaignId, Influence = 100, ModelId = config[0].Value__c, ContactId = opp.Business_Executive__c);
                influenceToInsert.add(tempInfluence);
            }
        }

        if (!mapCampaignsToRemove.isEmpty()) {

            for (Id oppId : mapCampaignsToRemove.keySet()) {
                if (keyToInfluence.containsKey(oppId + '' + mapCampaignsToRemove.get(oppId))) {
                    toDelete.add(new CampaignInfluence(Id = keyToInfluence.get(oppId + '' + mapCampaignsToRemove.get(oppId)))) ;
                }
            }
        }

        if (!influenceToInsert.isEmpty()) {
            insert(influenceToInsert);
        }

        if (!toDelete.isEmpty()) {
            delete(toDelete);
        }
    }

    public static List<OpportunityTeamMember> primaryOpportunityOwners (Map<Id, Opportunity> newOpps) {
        List<OpportunityTeamMember> toUpdateOTMList = new List<OpportunityTeamMember>();
        for(OpportunityTeamMember optyOwnerOTMRec : [SELECT Id,OpportunityId, Primary__c, Split_Percentage__c, TeamMemberRole FROM OpportunityTeamMember WHERE OpportunityId IN :newOpps.keySet() AND TeamMemberRole = 'Opportunity Owner']){
            optyOwnerOTMRec.Split_Percentage__c = 100;
            optyOwnerOTMRec.Primary__c = true;
            toUpdateOTMList.add(optyOwnerOTMRec);
        }
        return toUpdateOTMList;
    }


    public static void extendSEProjects (List<Id> oppIds) {
        List<SObject> recordsToUpdate = new List<SObject>();
        Map<Id, pse__Proj__c> projects = new Map<Id, pse__Proj__c>([SELECT Id,pse__End_Date__c,pse__Opportunity__c,pse__Opportunity__r.CloseDate FROM pse__Proj__c WHERE pse__Opportunity__c IN:oppIds AND Project_Classification__c = :Constants.PROJ_CLASSIFICATION_SE]);
        if (projects.isEmpty()) {
            return;
        }
        for (pse__Proj__c proj : projects.values()) {
            proj.pse__End_Date__c = proj.pse__Opportunity__r.CloseDate.addMonths(1);
            recordsToUpdate.add(proj);
        }

        List<pse__Assignment__c> assignments = [SELECT Id,pse__Project__c, pse__Schedule__c,pse__Schedule__r.pse__End_Date__c FROM pse__Assignment__c WHERE pse__Project__c IN:projects.keySet()];

        for (pse__Assignment__c assign : assignments) {
            pse__Schedule__c tempSched = new pse__Schedule__c();
            tempSched.Id = assign.pse__Schedule__c;
            tempSched.pse__End_Date__c = projects.get(assign.pse__Project__c).pse__Opportunity__r.CloseDate.addMonths(1);
            recordsToUpdate.add(tempSched);
        }
        if (!recordsToUpdate.isEmpty()) {
            update recordsToUpdate;
        }
    }

    public static void setOppProbability (Opportunity opp) {
        if (String.isNotBlank(opp.StageName)) {
            Map<String, Integer> probabilityByStage = new Map<String, Integer>();
            List<Opportunity_Probability__mdt> types;
            if (String.isNotBlank(opp.Type) && TriggersHelper.oppProbabilityMap.containsKey(opp.Type)) {
                types = TriggersHelper.oppProbabilityMap.get(opp.Type);
            } else {
                types = TriggersHelper.oppProbabilityMap.get('Default');
            }

            List <String> args;
            for (Opportunity_Probability__mdt oppMtd : types) {
                args = oppMtd.Stage_Probability__c.split('; ');
                probabilityByStage.put(args.get(0), Integer.valueOf(args.get(1)));
            }

            if (probabilityByStage.containsKey(opp.StageName) && opp.Probability != probabilityByStage.get(opp.StageName)) {
                opp.Probability = probabilityByStage.get(opp.StageName);
            }
        }
    }

/*
    public static void uncheckDateSync (List<SBQQ__Quote__c>quotes) {
        update quotes;
    }
    public static void rcQuotes (Map<Id, SBQQ__Quote__c> quotesToUpdateMap, Set<Id>quotesNewPrimary) {
        for (SBQQ__Quote__c quote : [SELECT Id, RC__c FROM SBQQ__Quote__c WHERE Id IN :quotesNewPrimary]) {
            if (quotesToUpdateMap.containsKey(quote.Id)) {
                quotesToUpdateMap.get(quote.Id).RC__c = !quote.RC__c;
            } else {
                quote.RC__c = !quote.RC__c;
                quotesToUpdateMap.put(quote.Id, quote);
            }
        }
    }
*/
    public static void updateRepresentativesOnQuote (Map<Id, SBQQ__Quote__c> quotesToUpdateMap, Set<Id>quotesToUpdateReps) {

        for (SBQQ__Quote__c quote : [
            SELECT Id, Rev_Rec_Representative__c, Legal_Representative__c,
            SBQQ__Opportunity2__r.Rev_Rec_Representative__c, SBQQ__Opportunity2__r.Legal_Representative__c
            FROM SBQQ__Quote__c
            WHERE Id IN :quotesToUpdateReps
        ]) {
            if (quotesToUpdateMap.containsKey(quote.Id)) {
                quotesToUpdateMap.get(quote.Id).Legal_Representative__c = quote.SBQQ__Opportunity2__r.Legal_Representative__c;
                quotesToUpdateMap.get(quote.Id).Rev_Rec_Representative__c = quote.SBQQ__Opportunity2__r.Rev_Rec_Representative__c;

            } else {
                quote.Legal_Representative__c = quote.SBQQ__Opportunity2__r.Legal_Representative__c;
                quote.Rev_Rec_Representative__c = quote.SBQQ__Opportunity2__r.Rev_Rec_Representative__c;
                quotesToUpdateMap.put(quote.Id, quote);
            }
        }
    }

    //
    public static void updateAccountCategoryOpportunity (List<Opportunity> opportunitiesToProcess, List<Account> queriedAccounts, Boolean accCategoryUpdate) {
        // Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id, Email, FirstName, LastName FROM Contact WHERE Id IN :contactIds]);
        Map<Id, Account> AccountMap = new Map<Id, Account>();

        if (opportunitiesToProcess.size() > 0) {
            // if (accountIDSet.size() > 0) {
            //  for (Account acc : [SELECT Id, Owner.RVP_Approver__c, Owner.RVP_Approver__r.IsActive, District__c FROM Account WHERE Id IN :accountIDSet]) {
            for (Account acc : queriedAccounts) {
                AccountMap.put(acc.Id, acc);
            }
            for(Opportunity opp : opportunitiesToProcess){
                if(accCategoryUpdate){
                    opp.Account_Category__c = AccountMap.get(opp.AccountId).Account_Category__c;
                    opp.Customer_Status__c = AccountMap.get(opp.AccountId).Account_Category__c;
                }
                else if(opp.SBCF_Legal_Entity__c != null && AccountMap.get(opp.SBCF_Legal_Entity__c) != null){
                    opp.Legal_Entity_ID__c = AccountMap.get(opp.SBCF_Legal_Entity__c).Address_Number__c != null? Decimal.valueOf(AccountMap.get(opp.SBCF_Legal_Entity__c).Address_Number__c): null;
                    opp.Legal_Entity_Name__c = AccountMap.get(opp.SBCF_Legal_Entity__c).Name;
                }
                
            }
        }
    }
    
     public static void updateRepsOnQuote (Map<Id, SBQQ__Quote__c> quotesToUpdateMap, Set<Id> oppIds, Boolean isRepsUpdate) {

        for (SBQQ__Quote__c quote : [
            SELECT Id, Rev_Rec_Representative__c, Legal_Representative__c, Sales_RVP__c, Services_RVP__c, SBQQ__Opportunity2__r.Services_RVP_Approver__c,
            SBQQ__Opportunity2__r.Rev_Rec_Representative__c, SBQQ__Opportunity2__r.Legal_Representative__c,SBQQ__Opportunity2__r.RVP_Approver__c,SBQQ__Opportunity2__r.End_User_Customer__c
            FROM SBQQ__Quote__c
            WHERE SBQQ__Opportunity2__c IN :oppIds
        ]) {
            if (quotesToUpdateMap.containsKey(quote.Id)) {
                if(isRepsUpdate){
                    quotesToUpdateMap.get(quote.Id).Legal_Representative__c = quote.SBQQ__Opportunity2__r.Legal_Representative__c;
                    quotesToUpdateMap.get(quote.Id).Rev_Rec_Representative__c = quote.SBQQ__Opportunity2__r.Rev_Rec_Representative__c;
                    quotesToUpdateMap.get(quote.Id).Sales_RVP__c = quote.SBQQ__Opportunity2__r.RVP_Approver__c;
                    quotesToUpdateMap.get(quote.Id).Services_RVP__c = quote.SBQQ__Opportunity2__r.Services_RVP_Approver__c;
                }
                else{
                    //quotesToUpdateMap.get(quote.Id).SBQQ__Partner__c = quote.SBQQ__Opportunity2__r.End_User_Customer__c;
                    }
             } else {
                 if(isRepsUpdate){
                     quote.Legal_Representative__c = quote.SBQQ__Opportunity2__r.Legal_Representative__c;
                     quote.Rev_Rec_Representative__c = quote.SBQQ__Opportunity2__r.Rev_Rec_Representative__c;
                     quote.Sales_RVP__c = quote.SBQQ__Opportunity2__r.RVP_Approver__c;
                     quote.Services_RVP__c = quote.SBQQ__Opportunity2__r.Services_RVP_Approver__c;
                 }
                 else{
                     //quote.SBQQ__Partner__c = quote.SBQQ__Opportunity2__r.End_User_Customer__c;
                 }
                quotesToUpdateMap.put(quote.Id, quote);
            }
        }
     }
    
    public static void activateAllContracts (List<Contract> contractsToUpdate, Set<Opportunity> oppsToUpdateContracts) { //Sync PB Opportunity - Activate All Contracts
        Map<Id,Id> contractsToUpdateMap = new Map<Id,Id>();
        if(oppsToUpdateContracts != null && oppsToUpdateContracts.size() > 0){
            //516685 - Nogueira Matias Added 'Cancelled' filter to make sure no Cancelled contracts get renewed or reactivated.
            for(Contract con : [SELECT Id, SBQQ__Opportunity__c FROM Contract 
                                WHERE SBQQ__Opportunity__c IN: oppsToUpdateContracts
                                AND Status != 'Cancelled'])
                                {
                                    contractsToUpdateMap.put(con.SBQQ__Opportunity__c, con.Id);
                                }
            
            for (Opportunity opp : oppsToUpdateContracts) {
                Contract contractRec = new Contract();
                contractRec.Id = contractsToUpdateMap.get(opp.Id);
                contractRec.ActivatedDate2__c = System.now() - (58/24/60);
                contractRec.Renewal_Forecast_Time_Flow__c = true;
                contractRec.Status  = 'Activated';
                contractsToUpdate.add(contractRec); 
            } 
        }
    }
    
    //Sync PB opportunity- Ship date & Ship Date Updated
    public static void updateShipDateQuote(Map<Id, SBQQ__Quote__c> quotesToUpdateMap,List<Opportunity> oppsToProcess,Boolean oppWithLicense,Boolean oppWOLicense,Boolean oppShipDateUpdatedPB){
        if(oppsToProcess != null && oppsToProcess.size() > 0){
            for (Opportunity opp : oppsToProcess) {
                if(opp.SBQQ__PrimaryQuote__c != null){
                    if (quotesToUpdateMap.containsKey(opp.SBQQ__PrimaryQuote__c)) {
                        if(oppWithLicense){
                            quotesToUpdateMap.get(opp.SBQQ__PrimaryQuote__c).SBQQ__StartDate__c = opp.Ship_Date__c;
                        }
                        if(oppWOLicense){
                            quotesToUpdateMap.get(opp.SBQQ__PrimaryQuote__c).SBQQ__StartDate__c = opp.CloseDate;
                        }
                        if(oppShipDateUpdatedPB){
                            quotesToUpdateMap.get(opp.SBQQ__PrimaryQuote__c).Date_Sync__c = false;
                        }
                    } else {
                        SBQQ__Quote__c quote = new SBQQ__Quote__c();
                        quote.Id = opp.SBQQ__PrimaryQuote__c;
                        if(oppWithLicense){
                            quote.SBQQ__StartDate__c = opp.Ship_Date__c;
                        }
                        if(oppWOLicense){
                            quote.SBQQ__StartDate__c = opp.CloseDate;
                        }
                        if(oppShipDateUpdatedPB){
                            quote.Date_Sync__c = false;
                        }
                        quotesToUpdateMap.put(quote.Id, quote);
                    }
                }
            }
        }
    }
    
    //532514 - Gómez Gisela
    public static void updateMYCFlag(List<Opportunity> newOpps, Map<Id, SBQQ__Quote__c> queriedQuotes){ 
        for(Opportunity opp: newOpps){
            opp.Multi_Year_Contract_Opp__c = queriedQuotes.get(opp.SBQQ__PrimaryQuote__c) != null? queriedQuotes.get(opp.SBQQ__PrimaryQuote__c).Multi_Year_Contract_Quote__c : false;
        }
    }

    public static void processOppsWithChangedStatus (List<Opportunity> opps,Map<Id,Opportunity> oldOppsMap) {
        for (Opportunity opp : opps) {
            Date today = Date.today();
            String currentStage = opp.StageName?.left(2).right(1);
            String previousStage = oldOppsMap?.get(opp.Id)?.StageName.left(2).right(1);

            switch on currentStage {
                when '0' {
                    opp.S0_Date__c = today;
                    opp.CloseDate = Date.today();
                }when '1' {
                    opp.S1_Date__c = today;
                }
                when '2' {
                    opp.S2_Date__c = today;
                }
                when '3' {
                    opp.S3_Date__c = today;
                }
                when '4' {
                    opp.S4_Date__c = today;
                }
                when '5' {
                    opp.S5_Date__c = today;
                }
                when '6' {
                    opp.S6_Date__c = today;
                }
                when else {
                    //new statuses
                }
            }
            opp.Stage_Last_Changed_Date__c = today;

            //StageName,"S9 - Co-Termed"
            if (currentStage == '9') {
                continue;
            }
            if (currentStage > previousStage) {
                opp.Last_Stage_Change_Type__c = 'Upgrade';
            } else {
                opp.Last_Stage_Change_Type__c = 'Downgrade';
            }
        }
    }

    public static void processOppsWithChangedOwner (List<Opportunity> opps, Set<Id> newOwnersIds) {
        Map<Id, User> userMap = new Map<Id, User>([SELECT Id, Manager.Email FROM User WHERE Id IN :newOwnersIds]);
        for (Opportunity opp : opps) {
            System.debug('YC Debug--' + userMap.get(opp.OwnerId));
            opp.Opp_Owner_Manager_Email__c = userMap.get(opp.OwnerId)?.Manager?.Email;
        }

    }
    public static void setRenewalOppName (List<Opportunity> oppsWithPlatformChanged, Map<Id, Contract> relatedContracts) {
        //CPQ-MYC Case 541866 EDU Renewal Naming Fix
        for (Opportunity opp : oppsWithPlatformChanged) {
            if (opp.Platform__c == 'MCG' && opp.Type != 'Education Renewal') {
                opp.Name =  'MCG-'+opp.SBCF_QuoteNumber__c+'-'+relatedContracts.get(opp.SBQQ__RenewedContract__c).QuoteNumber__c;
            }
            else if (opp.Type == 'Education Renewal') {
                opp.Name =  'EDU-'+opp.SBCF_QuoteNumber__c+'-'+relatedContracts.get(opp.SBQQ__RenewedContract__c).QuoteNumber__c;
            }
            else if (opp.Type != 'Education Renewal' && !opp.Name.startsWith('EDU-')){
                opp.Name = opp.SBCF_OpportunityNameFormula__c;
                System.debug('YC Debug-2-' +opp.Name);
            }
            System.debug('setRenewalOppName NameDebug ->' +opp.Name);
        }
    }
    
    //542249 - Yogesh Sharma
    public static void updateNewBusiness(List<Opportunity> opportunitiesToCheckNewBusiness, Map<Id, Account> PAccountCategoryAccMap) {
        List<String> PAccountCategoryValues = OpportunityTriggerExecutionController.PAccountCategoryValues;
        for (Opportunity opp : opportunitiesToCheckNewBusiness) {
            if (PAccountCategoryAccMap.containsKey(opp.AccountId) && PAccountCategoryAccMap.get(opp.AccountId).AccountCategoryShort__c != null) {
                if (PAccountCategoryAccMap.get(opp.AccountId).AccountCategoryShort__c.contains(',')) {
                    List<String> categories = PAccountCategoryAccMap.get(opp.AccountId).AccountCategoryShort__c.split(',');
                    for (String c : categories) {
                        if (PAccountCategoryAccMap.containsKey(opp.AccountId) && PAccountCategoryValues.contains(c)) {
                            opp.New_Business__c = true;
                        }
                    }
                } else if (PAccountCategoryValues.contains(PAccountCategoryAccMap.get(opp.AccountId).AccountCategoryShort__c)) {
                    opp.New_Business__c = true;
                }
            }
        }
    }
    
    public static void createPartnerLine(Map<Id,Opportunity> oppMap){
        List<Partner_Line__c> pl2insert = new List<Partner_Line__c>();
        for(Opportunity opp : oppMap.values()){
            Partner_Line__c pl = new Partner_Line__c(Account__c = opp.End_User_Customer__c, Opportunity__c = opp.Id, Partner_Type__c = 'Sales Partner');
            pl2insert.add(pl);
        }
        if(!pl2insert.isEmpty())
            insert pl2insert;
    }

    public static void evaluateContactsRTM(Map<Id, Contact> contactsToUpdate, Set<Id> contactsToEvaluateRTM, Map<Id,Opportunity> queriedOpps){
        Set<Id> activeOppContacts = new Set<Id>();
        Set<Id> closedOppContacts = new Set<Id>();
        Map<Id, Contact> oppContacts = new Map<Id, Contact>();
        for(Opportunity opp: queriedOpps.values()){
            oppContacts.put(opp.Billing_Contact__c, opp.Billing_Contact__r);
            oppContacts.put(opp.Business_Executive__c, opp.Business_Executive__r);
            oppContacts.put(opp.Champion__c, opp.Champion__r);
            oppContacts.put(opp.Coach__c, opp.Coach__r);
            oppContacts.put(opp.ContactId, null);
            oppContacts.put(opp.Economic_Buyer__c, opp.Economic_Buyer__r);
            oppContacts.put(opp.Partner_Contact__c, opp.Partner_Contact__r);
            oppContacts.put(opp.SalesLoft1__Primary_Contact__c, opp.SalesLoft1__Primary_Contact__r);
            oppContacts.put(opp.Technical_Executive__c, opp.Technical_Executive__r);
            if(opp.StageName != Constants.OPPORTUNITY_STAGE_CLOSED){
				activeOppContacts.addAll(oppContacts.keySet());
            }
            else{
                closedOppContacts.addAll(oppContacts.keySet());
            }
        }
        if(!contactsToEvaluateRTM.isEmpty()){
            Contact contactToUpdate;
            for(Id cont: contactsToEvaluateRTM){
                if (contactsToUpdate.get(cont) != null) {
                    contactToUpdate = contactsToUpdate.get(cont);
                }
                else{
                    contactToUpdate = new Contact(Id = cont);
                }
                //Contact/Opp relationship:
                //It is related to any Opp -> Status = OPP 									(1)
                //It was removed from all Opps -> Revert to previous Status, RTMR = blank 	(2)
                //It is only related to Closed-Lost Opps -> RTMR = Closed-Lost 				(3)
                if(oppContacts.keySet().contains(cont)){ //(1)
                    if(oppContacts.get(cont) == null || oppContacts.get(cont).L_Status__c != 'L5 - Opportunity'){ 
                        contactToUpdate.L_Status__c = 'L5 - Opportunity';
                        contactsToUpdate.put(contactToUpdate.Id, contactToUpdate);
                    }
                }
                else{ //(2)
                    for(ContactHistory ch: [SELECT OldValue, NewValue FROM ContactHistory WHERE ContactId =: cont and Field ='L_Status__c' ORDER BY CreatedDate DESC]){
                        if((String)ch.NewValue == 'L5 - Opportunity'){
                            contactToUpdate.L_Status__c = (String)ch.OldValue;
                            contactToUpdate.Return_to_Marketing__c = false;
                            contactToUpdate.Return_to_Marketing_Reason__c = '';
                            contactsToUpdate.put(contactToUpdate.Id, contactToUpdate);
                            break;
                        }
                    }
                }
                if(!activeOppContacts.contains(cont) && closedOppContacts.contains(cont)){ //(3)
                	contactToUpdate.Return_to_Marketing__c = true;
                	contactToUpdate.Return_to_Marketing_Reason__c = 'Closed Lost';
                    contactsToUpdate.put(contactToUpdate.Id, contactToUpdate);
            	} 
            }
        }
    }

    public static void carryPartnerLinesOver(Map<Id, Opportunity> opps, Map<Id,Opportunity> queriedOpps){
        Set<Id> renewedOppsIds = new Set<Id>();
        List<Partner_Line__c> partnerLines = new List<Partner_Line__c>();
        List<Partner_Line__c> partnerLines2insert = new List<Partner_Line__c>();
        if(!queriedOpps.isEmpty()){
            for(Opportunity opp : queriedOpps.values()){
                if(opp.SBQQ__RenewedContract__c != null) {
                    renewedOppsIds.add(opp.SBQQ__RenewedContract__r.SBQQ__Opportunity__c);
                } else {
                    renewedOppsIds.add(opp.SBQQ__AmendedContract__r.SBQQ__Opportunity__c);
                }
            }
        }
        if(!renewedOppsIds.isEmpty()){
            partnerLines = [SELECT Id, Account__c, Opportunity__c, Partner_Type__c, Motion__c, Sourced__c FROM Partner_Line__c WHERE Opportunity__c in : renewedOppsIds];
        }
        if(!partnerLines.isEmpty()){
            for(Opportunity opp : opps.values()){
                for(Partner_Line__c pl : partnerLines){
                    if(pl.Opportunity__c == queriedOpps.get(opp.Id).SBQQ__RenewedContract__r.SBQQ__Opportunity__c || pl.Opportunity__c == queriedOpps.get(opp.Id).SBQQ__AmendedContract__r.SBQQ__Opportunity__c){
                        partnerLines2insert.add(new Partner_Line__c(
                                Account__c = pl.Account__c,
                                Opportunity__c = opp.Id,
                                Partner_Type__c = pl.Partner_Type__c,
                                Motion__c = pl.Motion__c,
                                Sourced__c = pl.Sourced__c
                        ));
                    }
                }
            }
        }
        if(!partnerLines2insert.isEmpty()){
            insert partnerLines2insert;
        }
    }
}