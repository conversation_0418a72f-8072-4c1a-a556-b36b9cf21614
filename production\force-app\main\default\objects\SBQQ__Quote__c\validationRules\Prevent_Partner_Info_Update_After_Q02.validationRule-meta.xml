<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Prevent_Partner_Info_Update_After_Q02</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
        NOT(ISPICKVAL(SBQQ__Status__c, &quot;Q01 SAE&quot;)),
        NOT(ISPICKVAL(SBQQ__Status__c, &quot;Q02 CSM&quot;)),
        NOT(ISPICKVAL(SBQQ__Status__c, &quot;Q02 OPS&quot;)),
        NOT(ISPICKVAL(SBQQ__Status__c, &quot;Q02 RVP&quot;)),
        NOT($Profile.Name = &quot;System Administrator&quot;),
        OR(
        ISCHANGED(SBQQ__Partner__c),
        ISCHANGED(SBQQ__Distributor__c),
        ISCHANGED(Reseller__c)
        )
        )</errorConditionFormula>
    <errorMessage>Partner fields are locked after Q02</errorMessage>
</ValidationRule>