public class GenericSObjectQueueable implements Queueable {
    private Map<Id,SObject> oldRecords;
    private List<SObject> newRecords;
    private Map<Id, SObject> newRecordsInitial;
    private List<SObject> retryList;
    private List<SObject> recordsToUpdate;
    private List<SObject> updatedRecords;
    private Integer batchSize;
    private Boolean isInsert;
	private Boolean isUpdate;
    
    public GenericSObjectQueueable(List<SObject> newRecords, Map<Id, SObject> oldRecords, Integer batchSize) {
        this.newRecords = new List<SObject>(newRecords);
        this.newRecordsInitial = new Map<Id, SObject>(newRecords.deepClone(true, true, true));  
        this.oldRecords = oldRecords;
        this.retryList = new List<SObject>();
        this.recordsToUpdate = new List<SObject>();
        this.updatedRecords = new List<SObject>();
        this.batchSize = batchSize;
        this.isInsert = (oldRecords == null);
        this.isUpdate = (oldRecords != null);
    }
    
    public void execute(QueueableContext context) {
        //Split the newRecords list into smaller batches
        Integer i = 0;
        List<SObject> batch = new List<SObject>();
        Integer originalSize = newRecords.size();
        Schema.SObjectType sObjectType = newRecords.get(0).getSObjectType();
        
        for (i = 0; i < (originalSize <= batchSize? originalSize: batchSize); i++) {
            batch.add(newRecords.remove(0));
        }
		
        //Trigger the DML operations
        if(sObjectType == Campaign.SObjectType){
            TriggerHandlerFactory.logTriggerHandler(Campaign.SObjectType, CampaignTriggerExecutionController.class);
            TriggerHandlerFactory.dispatchHandler(sObjectType, batch, oldRecords);
    	}
        else if(sObjectType == CampaignMember.SObjectType){
            TriggerHandlerFactory.dispatchHandler(sObjectType, batch, oldRecords);
        }
        else if(sObjectType == Account.SObjectType){
            accountBeforeMethods(new Map<Id, Account>((List<Account>)batch), (Map<Id, Account>)oldRecords);
            updateRecords(batch);
            accountAfterMethods(new Map<Id, Account>((List<Account>)updatedRecords), (Map<Id, Account>)oldRecords);
        }
        else if(sObjectType == Contact.SObjectType){
            contactBeforeMethods(new Map<Id, Contact>((List<Contact>)batch), (Map<Id, Contact>)oldRecords);
            updateRecords(batch);
            contactAfterMethods(new Map<Id, Contact>((List<Contact>)updatedRecords), (Map<Id, Contact>)oldRecords);
        }
        else if(sObjectType == Lead.SObjectType){
            leadBeforeMethods(new Map<Id, Lead>((List<Lead>)batch), (Map<Id, Lead>)oldRecords);
            updateRecords(batch);
            leadAfterMethods(new Map<Id, Lead>((List<Lead>)updatedRecords), (Map<Id, Lead>)oldRecords);
        }
        
        if(!newRecords.isEmpty()){
        	System.enqueueJob(new GenericSObjectQueueable(newRecords, oldRecords, batchSize));
    	}
    }
    
    private void accountBeforeMethods(Map<Id, Account> newRecords, Map<Id, Account> oldRecords){
        AccountTriggerHandler.updateAccountCategory(newRecords.values());
        AccountTriggerHandler.populatePlan(newRecords);
        if (isUpdate) {
            AccountTriggerHandler.populateClearExpiredSupportExceptionApprovedByField(oldRecords, newRecords);
            AccountTriggerHandler.restrictDowngrading(oldRecords, newRecords);
            AccountTriggerHandler.stampABBillingModeAndPOCategory(oldRecords, newRecords);
            AccountTriggerHandler.populatePlan(oldRecords, newRecords);
            AccountTriggerHandler.populateChurnDate(oldRecords, newRecords);  
        }
	}
    private void accountAfterMethods(Map<Id, Account> newRecords, Map<Id, Account> oldRecords){
        if ((UserInfo.getLastName() != 'SVC_APP_INTEGRATION' && UserInfo.getName() != 'Acumen Admin' 
             && UserInfo.getName() != 'Data.com Clean') && oldRecords == null) {
            AccountTriggerHelper.createMessageInQueue(newRecords.values(), isInsert);
        }
        AccountOwnerHistoryHelper.updateOwnerHistory(newRecords, oldRecords, isInsert);
	}
      
    private void contactBeforeMethods(Map<Id, Contact> newRecords, Map<Id, Contact> oldRecords){
        Map<Id, Contact> newContactsInitial = new Map<Id, Contact>(newRecords.values().deepClone(true, true, true));
        List<SObject> contactsToUpdate = new List<SObject>();
        if(isInsert) {
            ContactMappingController.populateAccountByDomain(newRecords.values());
            ContactTriggerHelper.updateMarketingFields(newRecords.values(), new Map<Id, Contact>());
        }
        if (isUpdate) {
            PersonDuplicateController.preventDuplicatesContacts(PersonDuplicateController.getRecordsWithEmailChange(newRecords.values(), oldRecords));
            ContactTriggerHelper.updateEDUActive(newRecords.values());
            ContactTriggerHelper.updateMarketingFields(newRecords.values(), oldRecords);
            ContactTriggerHelper.updateIsStatusTQLField(newRecords.values(), oldRecords);
        } 
	}
    private void contactAfterMethods(Map<Id, Contact> newRecords, Map<Id, Contact> oldRecords){
        if (isUpdate) {
            LeanDataTriggerHelper.createLead (newRecords.values(), oldRecords);
            if (UserInfo.getName() == 'LeanData Integration (BD)' || Test.isRunningTest()) {
                LeanDataTriggerHelper.isAccountChange(newRecords.values(), oldRecords);
            } 
        }
        
        MSTRSettings__c settings = MSTRSettings__c.getInstance();
        Id userId = UserInfo.getUserId();
        List<String> integrationUserList = new List<String>();
        for(MSTR_Global_Configuation__mdt metadata : MSTR_Global_Configuation__mdt.getAll().values()){
            if(metadata.Type__c == 'Integration User'){
                integrationUserList.add(metadata.value__c);
            }
        }
        
        if (!integrationUserList.contains(userId)) {
            if (isInsert) {
                ContactTriggerHelper.OnAfterInsert(newRecords.values());
            }
        }
        LeanDataTriggerHelper.checkSyncToMarketoIfCampaign(newRecords.values());
        
        if (isInsert) {
            LeanDataTriggerHelper.skipZoomInfo(newRecords.values());
            ContactTriggerHelper.createContactGeographyUCCode(newRecords);
            Set<Id> contactIds = new Set<Id>(newRecords.keyset());
            Set<Id> MQLcontactIds = new Set<Id>();
            for (contact con : newRecords.values()) {
                if (con.Converted_From_Lead__c != null)
                    contactIds.remove(con.Id);
                else if(con.L_Status__c == 'L3 - Engaged')
                    MQLcontactIds.add(con.Id);
            }
            if (!contactIds.isEmpty()){
                PersonRouting.processRecordsContactChange(newRecords.keySet(), MQLcontactIds);
            }
            LeanDataTriggerHelper.processRedReportCM(newRecords);
        }
        
        if (isUpdate) {
            Set<Id> conIds = new Set<Id>();
            for (Contact con : newRecords.values()) {
                if (con.pse__Utilization_Last_Update_Date__c != oldRecords.get(con.Id).pse__Utilization_Last_Update_Date__c) {
                    conIds.add(con.Id);
                }
            }
            if (!conIds.isEmpty()) {
                ContactTriggerHelperRollup.updateTargeHours(conIds);
            }
            ContactTriggerHelper.updateContactGeographyUCCode(oldRecords, newRecords);
            if (CheckRecursive.runOnce()) {
                ContactTriggerHelper.routingFieldChange(oldRecords, newRecords);
            }
        }
        ContactTriggerHelperRollup.HealthCheckCount(newRecords.values(), oldRecords, isInsert, isUpdate, false, false);
        ContactTriggerHelperRollup.contactRollup(newRecords.values(), oldRecords, isInsert, isUpdate, false, false);
        ContactTriggerHelperRollup.studentCount(newRecords.values(), oldRecords, isInsert, isUpdate, false, false);  
	}
    
    private void leadBeforeMethods(Map<Id, Lead> newRecords, Map<Id, Lead> oldRecords){      
        if(isInsert){
            Set<Id> leadIds = new Set<Id>();
            for (Lead ld : newRecords.values()) {
                if (ld.LeadSource != 'Community') {
                    leadIds.add(ld.Id);
                }
            }
            if (!leadIds.isEmpty()) {
                LeadTriggerHelper.AutopopulateDistrict(newRecords.values(), false);
            }
            LeadTriggerHelper.updateMarketingFields(newRecords.values(), new Map<Id, Lead>());
        }
        if(isUpdate){
            LeadTriggerHelper.setConvertedContactStatus(oldRecords, newRecords);
            LeadTriggerHelper.preventUpdateOfConvertedLead(newRecords.values());
            PersonDuplicateController.preventDuplicatesLeads(PersonDuplicateController.getRecordsWithEmailChange(newRecords.values(), oldRecords));
            LeadTriggerHelper.updateMarketingFields(newRecords.values(), oldRecords);
            LeadTriggerHelper.updateIsStatusTQLField(newRecords.values(), oldRecords);
        }
	}
    
    private void leadAfterMethods(Map<Id, Lead> newRecords, Map<Id, Lead> oldRecords){  
        if (isInsert){
            Set<Id> leadIds = new Set<Id>();
            for (Lead ld : newRecords.values()) {
                if (ld.LeadSource != 'Community') {
                    leadIds.add(ld.Id);
                }
            }
            if (!leadIds.isEmpty()) {
                LeadTriggerHelper.AutoPopAccountDomain(newRecords.values(), false);
            }
        }
        LeanDataTriggerHelper.checkSyncToMarketoIfCampaign(newRecords.values());
        if (isUpdate) {
            if (CheckRecursive.runOnce()) {
                LeadTriggerHelper.routingFieldChange(oldRecords, newRecords);
            }
        }
        if (isInsert) {
            Set<Id> MQLleadIds = new Set<Id>();
            for (Lead newLead : newRecords.values()) {
                if(newLead.Status == 'L3 - Engaged'){
                    MQLleadIds.add(newLead.Id);
                }
            }
            PersonRouting.processRecordsLeadChange(newRecords.keySet(), MQLleadIds);
            LeanDataTriggerHelper.processRedReportCM(newRecords);
        }
    }
    
    private void updateRecords(List<SObject> newRecords){
        String errors ='';
        recordsToUpdate = removeUnchangedFields(newRecordsInitial, newRecords);
        if(!recordsToUpdate.isEmpty()){
            List<Database.SaveResult> saveResults = Database.update(recordsToUpdate, false);
            for (Integer i = 0; i < saveResults.size(); ++i) {
                if (!saveResults.get(i).isSuccess()){
                    retryList.add(newRecordsInitial.values()[i]);
                    errors += 'ID: '+recordsToUpdate[i].Id+' : '+recordsToUpdate[i]+'\n';
                    errors += saveResults.get(i).getErrors();
                    errors += '\n\n';
                }
                else{
                    updatedRecords.add(newRecords[i]);
                }
            }
            //Email Failed Records
            if(!retryList.isEmpty()){
                List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
                Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                mail.setToAddresses(new List<String>{'<EMAIL>'});
                mail.setSubject('Failed Records: '+retryList.size());
                mail.setPlainTextBody(errors);
                emails.add(mail);
                Messaging.SendEmail(emails);
            }
        }   
    }
    
    public List<SObject> removeUnchangedFields(Map<ID, SObject> newRecordsInitial, List<SObject> newRecords){
        List<SObject> recordsToUpdate = new List<SObject>();
        Map<String, Object> fieldsToValue = new Map<String, Object>();
        SObject initialRecord;
        SObject recordToUpdate;
        ID currentId;
        for(SObject updatedRecord: newRecords){
            recordToUpdate = updatedRecord.getSObjectType().newSObject();
            currentId = updatedRecord.Id;
            initialRecord = newRecordsInitial.get(currentId);
            if(initialRecord != null){
                fieldsToValue = updatedRecord.getPopulatedFieldsAsMap();
                for (String fieldName : fieldsToValue.keySet()) {
                    if(updatedRecord.get(fieldName) != initialRecord.get(fieldName)){
                        recordToUpdate.put('Id', currentId);
                        recordToUpdate.put(fieldName, updatedRecord.get(fieldName));
                    }
                }
                if(recordToUpdate.get('Id') != null){
                   recordsToUpdate.add(recordToUpdate); 
                }
            }
        }
        return recordsToUpdate;
    }
}