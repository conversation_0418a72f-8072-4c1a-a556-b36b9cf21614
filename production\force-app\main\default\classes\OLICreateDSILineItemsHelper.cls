/************************************* MODIFICATION LOG ********************************************************************************************
 * CreateDSILineItems -->OLICreateDSILineItemsHelper
 *
 * DESCRIPTION : Handles all trigger logic related to DSI; Creates DSIlineitems from OppProducts
 *                   and Creates Entitlements from Support SKU's added when quote status is Agreement Executed(sale opp(new)) or Stage = s6(renewal opp(new))
 *
 *---------------------------------------------------------------------------------------------------------------------------------------------------
 * DEVELOPER                     DATE                                   REASON
 *---------------------------------------------------------------------------------------------------------------------------------------------------
 * Amritha Buddharaju           11/17/2016                         -- original Version
 * Amritha Buddharaju           02/28/2017                         -- Updated oppline object reference from custom to standard object
 * Alla Kiyashko                10/09/2018                         -- Updated oppline to check with quote line items DSI in case DSI missing on the opportunity level - renewal scenario
 * Alla Kiyashko                02/22/2019                         -- Updated to check correct dates for the Entitilement and DSI on quote execution
 * Amritha Buddharaju           06/22/2020                         -- Automatically create Cloud Entitlements for Cloud Support SKU's(Case 397848)
 * Priyank            03/15/2021              - Case 416490 - Service Cloud - Entitlements - create an Entitlement process record for CLOUD
 * ---------------------------------------------------------------------------------------------------------------------------------------------------
 * Test Class:OLICreateDSILineItemsTest
 */

public class OLICreateDSILineItemsHelper implements Database.AllowsCallouts {

    public static void CreateDSILineItems (List<Opportunity> newOppList, Set<Id> oppIdSet, Set<Id> DSIidSet) {
        System.debug('oppIds ' + oppIdSet);
        System.debug('DSIidSet ' + DSIidSet);
        Set<Id> oppIds = oppIdSet;
        Set<Id> DSIids = DSIidSet;
        Id oppSORtypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Sales Opportunity (New)').getRecordTypeId();
        Id oppRORtypId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Renewal Opportunity (New)').getRecordTypeId();
        List<User> usrlst = [SELECT Id FROM User WHERE Name = 'SVC_APP_INTEGRATION'];

        System.debug('###--oppIds === ' + oppIds);
        if (!oppIds.isEmpty()) {
            Map<String, List<OpportunityLineItem>> oppLITMmap = getAllOppLineItems(oppIds);
            Map<String, Map<String, DSI_Line_Item__c>> DSiLIntItemsmap = getExistingDSIs(DSIids);

            List<DSI_Line_Item__c> DSiLineItemList = new List<DSI_Line_Item__c> ();
            System.debug(oppLITMmap.size() + ' ###--oppLITMmap-=== ' + oppLITMmap);
            System.debug('DSiLIntItemsmap === ' + DSiLIntItemsmap);

            Map<Id, DSI_Line_Item__c> duplicateDSImap = new Map<Id, DSI_Line_Item__c> ();

            Map<Id, Opportunity> loadedOpportunities = new Map<Id, Opportunity> ();
            for (Opportunity opp : newOppList) {
                if (oppLITMmap.containsKey(opp.Id)) {
                    System.debug('inside if oppLITMmap contains opp === ');
                    for (OpportunityLineItem oplt : oppLITMmap.get(opp.Id)) {

                        Id oliDSI = (oplt.SBQQ__QuoteLine__c != null && oplt.SBQQ__QuoteLine__r.SBCF_DSI__c != null) ? oplt.SBQQ__QuoteLine__r.SBCF_DSI__c : opp.DSI__c; //AK
                        if (oliDSI != null) {
                            System.debug(' found specific DSI ===  ' + oliDSI);
                            if (duplicateDSImap.containsKey(oplt.Product2Id) || (DSiLIntItemsmap.containsKey(oliDSI) && DSiLIntItemsmap.get(oliDSI).containsKey(oplt.Product2Id))) {
                                System.debug('DSiLIntItemsmap === ' + DSiLIntItemsmap);

                                DSI_Line_Item__c dsi = duplicateDSImap.containsKey(oplt.Product2Id) ? duplicateDSImap.get(oplt.Product2Id) : DSiLIntItemsmap.get(oliDSI).get(oplt.Product2Id);
                                dsi.Name = oplt.Product_Name__c;
                                dsi.Quantity__c = (dsi.Quantity__c != null ? dsi.Quantity__c : 0) + (oplt.Quantity__c != null ? oplt.Quantity__c : 0);
                                dsi.Amount__c = (dsi.Amount__c != null ? dsi.Amount__c : 0) + oplt.TotalPrice;
                                dsi.End_Date__c = (dsi.End_Date__c != null ? (dsi.End_Date__c > oplt.End_Date__c ? dsi.End_Date__c : oplt.End_Date__c) : dsi.End_Date__c);

                                if (duplicateDSImap.containsKey(oplt.Product2Id)) {
                                    duplicateDSImap.put(oplt.Product2Id, dsi);
                                } else {
                                    dsi.Opportunity__c = opp.Id;
                                    dsi.LastUpdateddate__c = System.now();
                                    if (usrlst != null && !usrlst.isEmpty()) {
                                        dsi.LastUpdatedBy__c = usrlst[0].Id;
                                    }
                                    DSiLineItemList.add(dsi);
                                    System.debug(dsi.Id + ' ###--Adding to List 1 ' + oplt.Product2.Name);
                                    duplicateDSImap.put(oplt.Product2Id, dsi);
                                }
                            } else {
                                DSI_Line_Item__c dsi = new DSI_Line_Item__c();
                                dsi.Name = oplt.Product_Name__c;
                                dsi.Amount__c = oplt.TotalPrice;
                                dsi.End_Date__c = oplt.End_Date__c;
                                dsi.Quantity__c = oplt.Quantity__c;
                                dsi.SKU__c = oplt.ProductCode;
                                dsi.Start_Date__c = oplt.Start_Date__c != null ? System.today() : null;
                                dsi.Interval__c = oplt.Interval__c;
                                dsi.Product__c = oplt.Product2Id;
                                dsi.DSI_Product_Name__c = oplt.Product_Name__c;
                                //dsi.DSI__c = opp.DSI__c;
                                dsi.DSI__c = oliDSI ; //AK
                                dsi.Opportunity__c = opp.Id;
                                dsi.LastUpdateddate__c = System.now();
                                if (usrlst != null && !usrlst.isEmpty())
                                    dsi.LastUpdatedBy__c = usrlst[0].Id;
                                if (oplt.Opportunity_Service__c != null) {
                                    dsi.DSI_Line_Item_Service__c = oliDSI ;

                                } else if (oplt.Opportunity_Product__c != null) {
                                    dsi.DSI_Line_Item_Software__c = oliDSI ;
                                }
                                duplicateDSImap.put(oplt.Product2Id, dsi);
                                DSiLineItemList.add(dsi);
                                System.debug('###--Adding to List 2 ' + oplt.Product2.Name);
                            }
                        }
                        if (!loadedOpportunities.containsKey(opp.Id)) {
                            Opportunity oppNew = new Opportunity(Id = opp.Id, DSI_Loaded__c = true);
                            loadedOpportunities.put(opp.Id, oppNew);
                        }
                    }
                }
            }
            System.debug('###--DSiLineItemList-' + DSiLineItemList);
            if (!DSiLineItemList.isEmpty()) {
                upsert DSiLineItemList;
            }

            if (!loadedOpportunities.isEmpty()) {
                upsert loadedOpportunities.values();
            }
            //createEntitlements(oppIds, DSIids, usrlst, null);

        }
    }

    // getting all the OpportunityLineItem from opportunity ...
    public static Map<String, List<OpportunityLineItem>> getAllOppLineItems (Set<Id> opIds) {
        Map<String, List<OpportunityLineItem>> tempmap = new Map<String, List<OpportunityLineItem>> ();
        // AK added SBQQ__QuoteLine__c to the query
        for (OpportunityLineItem olt : [
                SELECT Id, Name, OpportunityId, Product_Name__c, Opportunity_Product__c, Opportunity_Service__c, Interval__c, ProductCode, Product2Id, Product2.Name, TotalPrice, End_Date__c, Quantity__c, Start_Date__c, Num_Periods__c, SBQQ__QuoteLine__c, SBQQ__QuoteLine__r.SBCF_DSI__c, Opportunity.DSI__c
                FROM OpportunityLineItem
                WHERE OpportunityId IN :opIds
        ]) {
            List<OpportunityLineItem> lst = tempmap.containsKey(olt.OpportunityId) ? tempmap.get(olt.OpportunityId) : new List<OpportunityLineItem> ();
            lst.add(olt);
            tempmap.put(olt.OpportunityId, lst);
        }
        return tempmap;
    }

    // getting all the DSI_Line_Item of DSI ...
    public static Map<String, Map<String, DSI_Line_Item__c>> getExistingDSIs (Set<Id> dsi_Ids) {
        Map<String, Map<String, DSI_Line_Item__c>> tempmap = new Map<String, Map<String, DSI_Line_Item__c>> ();
        for (DSI_Line_Item__c d : [SELECT Id, Name, Product_ID__c, Product__c, DSI__c, End_Date__c, Amount__c, Quantity__c, DSI_Line_Item_Service__c, DSI_Line_Item_Software__c FROM DSI_Line_Item__c WHERE DSI__c IN :dsi_Ids]) {
            Map<String, DSI_Line_Item__c> mp = tempmap.containsKey(d.DSI__c) ? tempmap.get(d.DSI__c) : new Map<String, DSI_Line_Item__c> ();
            mp.put(d.Product__c, d);
            tempmap.put(d.DSI__c, mp);
        }
        return tempmap;
    }

    // copying the entitlements ...
    public static void createEntitlements (/*Set<Id> opIds, Set<Id> dsiRecIdsFromOpty, */List<User> usrlst, SBQQ__Quote__c[] quoteScope) {
        Map<String, Entitlement> entMap = new Map<String, Entitlement> ();
        Map<String, SlaProcess> entProcessmap = getEntitlementProcess();
        Boolean updateDsi = false;
        Map<Id, DSI__c> dsisByIds = new Map<Id, DSI__c>();
        System.debug('###--opIds-===-');
        /*System.debug('###--opIds-===-' + opIds);
        System.debug('###--dsiRecIdsFromOpty===--' + dsiRecIdsFromOpty);*/

        // AK updated mapping
        Map<String, String> prdNms = new Map<String, String> {
            'Standard Support' => 'Standard',
            'Standard Support (Renewal)' => 'Standard',
            'Standard Support -1st Renewal' => 'Standard',
            'Elite Support' => 'Elite',
            'Cloud Elite Support' => 'Cloud Elite',
            'Cloud Elite Support (Renewal)' => 'Cloud Elite',
            'Elite Support (Renewal)' => 'Elite',
            'Extended Support' => 'Extended',
            'Extended Support (Renewal)' => 'Extended',
            'Premier Support' => 'Premier',
            'Premier Support (Renewal)' => 'Premier',
            'Cloud Support' => 'Cloud Standard',
            'SaaS - Hyper Intelligence' => 'Cloud Standard',
            'Cloud Architecture - AWS' => 'Cloud Standard',
            'Cloud Architecture - AWS Add\'l Production Node' => 'Cloud Standard',
            'Cloud Architecture - AWS Add\'l Non-Production Node' => 'Cloud Standard',
            'Cloud Architecture - Azure' => 'Cloud Standard',
            'Cloud Architecture - Azure Add\'l Production Node' => 'Cloud Standard',
            'Cloud Architecture - Azure Add\'l Non-Production Node' => 'Cloud Standard',
            'Cloud Architecture - Other' => 'Cloud Standard',
            'Cloud Architecture - MCG - AWS' => 'Cloud Standard',
            'Cloud Architecture - AWS Small' => 'Cloud Standard',
            'Cloud Architecture - Azure Small' => 'Cloud Standard',
            'Cloud Consumer User' => 'Cloud Standard',
            'Cloud Power User' => 'Cloud Standard',
            'Cloud Reporter User' => 'Cloud Standard',
            'Cloud Transaction User' => 'Cloud Standard',
            'Cloud Architect User' => 'Cloud Standard',
            'AI Consumer User' => 'Cloud Standard',
            'AI Power User' => 'Cloud Standard',
            'AI Architect User' => 'Cloud Standard',
            'Cloud Architecture - GCP' => 'Cloud Standard',
            'Cloud Architecture - GCP Add\'l Node' => 'Cloud Standard', 
            'Cloud Architecture - GCP Add\'l Non-Production Node' => 'Cloud Standard', 
            'Cloud Architecture - GCP Small' => 'Cloud Standard',
            'Cloud Architecture - StackIT' => 'Cloud Standard',
            'Cloud Architecture - StackIT Add’l Production Node' => 'Cloud Standard',
            'Cloud Architecture - StackIT Add’l Non-Production Node' => 'Cloud Standard',
            'Cloud Architecture - StackIT Small' => 'Cloud Standard',
            'Cloud Platform for AWS - Tier 1 - MCG' => 'Cloud Standard',
            'Cloud Platform for AWS - Tier 2 - MCG' => 'Cloud Standard',
            'Cloud Platform for AWS - Tier 3 - MCG' => 'Cloud Standard',
            'Cloud Platform for AWS - Tier 4 - MCG' => 'Cloud Standard',
            'Mosaic Consumer User' => 'Cloud Standard',
            'Standard User' => 'Cloud Standard',
            'Standard User (Addtl.)' => 'Cloud Standard',
            'Cloud Platform - MCS' => 'Cloud Standard'
        };

        if (quoteScope != null && !quoteScope.isEmpty()) {
            Set<Id> dSIidFromQuoteLines = new Set<Id>();
            Set<Id> allDsiIds = new Set<Id>();
            Map<String, Entitlement> oldEntmapOfQline = new Map<String, Entitlement>();
            List<SBQQ__QuoteLine__c> toProcessQLineList = [
                    SELECT Id, SBQQ__Group__c, SBQQ__Quote__r.SBQQ__Opportunity2__r.AccountId, SBQQ__Product__r.Name, SBQQ__StartDate__c, SBQQ__EndDate__c, SBQQ__Quote__c, SBQQ__Quote__r.SBQQ__Opportunity2__c, SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__r.Account__r.District__r.Business_Hours__c, SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__r.Name, SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c, SBQQ__Product__c, SBQQ__ProductCode__c, Opportunity__c, SBCF_DSI__c, SBCF_DSI__r.Name, SBCF_DSI__r.Account__r.District__r.Business_Hours__c, Interval__c, SBQQ__DefaultSubscriptionTerm__c, SBQQ__ProrateMultiplier__c, SBQQ__SubscriptionTerm__c, SBQQ__Quote__r.SBQQ__Opportunity2__r.Cloud_Opportunity__c,SBQQ__Quote__r.SBQQ__Opportunity2__r.SBQQ__PrimaryQuote__r.Platform__c, SBQQ__Quote__r.SBQQ__Opportunity2__r.Ship_Date__c
                    FROM SBQQ__QuoteLine__c
                    WHERE SBQQ__Quote__c IN :quoteScope
                    AND SBQQ__SubscriptionTerm__c != NULL
                    AND (SBQQ__Product__r.Name IN :prdNms.keySet()
                    OR Interval__c = 'Term')
            ];

            if (!toProcessQLineList.isEmpty()) {
                Map<Id, date> qlEndDateByGroup = new Map<Id, date>() ;
                qlEndDateByGroup = getEntEndDateByGroup(toProcessQLineList);
                for (SBQQ__QuoteLine__c qlineRec : toProcessQLineList) {
                    if (qlineRec.SBCF_DSI__c != null) {
                        dSIidFromQuoteLines.add(qlineRec.SBCF_DSI__c);
                        allDsiIds.add(qlineRec.SBCF_DSI__c);
                    }

                    if (qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c != null) {
                        allDsiIds.add(qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c);
                    }
                }

                if (dSIidFromQuoteLines.size() > 0) {
                    oldEntmapOfQline = getExistingEntitlements(dSIidFromQuoteLines);
                }

                dsisByIds = new Map<Id, DSI__c>([
                        SELECT Id, Account__r.District__r.Business_Hours__c, Platform__c, Entitlement_End_Date__c
                        FROM DSI__c
                        WHERE Id IN :allDsiIds
                ]);

                System.debug('toProcessQLineList === ' + toProcessQLineList);

                for (SBQQ__QuoteLine__c qlineRec : toProcessQLineList) {
                    System.debug('inside QLine FOR Loop === ');
                    system.debug('prdnms full' + prdNms.get(qlineRec.SBQQ__Product__r.Name));
                    String productName = '';
                    if (qlineRec.SBQQ__Product__r.Name != null && prdNms.containsKey(qlineRec.SBQQ__Product__r.Name) && String.isNotBlank(prdNms.get(qlineRec.SBQQ__Product__r.Name))) {
                        if (prdNms.get(qlineRec.SBQQ__Product__r.Name).equalsIgnoreCase('Cloud Elite')) {
                            productName = prdNms.get(qlineRec.SBQQ__Product__r.Name);
                        } else {
                            productName = prdNms.get(qlineRec.SBQQ__Product__r.Name).split(' ').get(0);
                        }
                    }
                    //String productName =  ? qlineRec.SBQQ__Product__r.Name.replace('Support', '').replace('support', '').replace('Renewal', '').replace('-1st', '').replace('(', '').replace(')', '').trim() : '';
                    Id dsiId = (qlineRec.SBCF_DSI__c != null) ? qlineRec.SBCF_DSI__c : qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c;
                    String dsi_name = (qlineRec.SBCF_DSI__r.Name != null && qlineRec.SBCF_DSI__r.Name != '' ? qlineRec.SBCF_DSI__r.Name : qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__r.Name);
                    String entKey = String.valueOf(dsiId) + productName;
                    System.debug('prdtName ===  ' + productName);
                    System.debug('dsi ===  ' + dsiId);
                    System.debug('dsi_name === ' + dsi_name);
                    System.debug('EntKey === ' + entKey);
                    Entitlement ent;
                    Datetime entitlementStartDateTime = qlineRec.SBQQ__StartDate__c != null ?
                            qlineRec.SBQQ__StartDate__c : System.now();
                    Date entitlementStartDate = Date.valueOf(entitlementStartDateTime);
                    Date newEndDate;
                    if(qlineRec.SBQQ__Group__c != null && qlEndDateByGroup != null && qlEndDateByGroup.containsKey(qlineRec.SBQQ__Quote__c)){
                        newEndDate = qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c);
                    } else {
                        newEndDate = qlineRec.SBQQ__EndDate__c != null ?
                                qlineRec.SBQQ__EndDate__c :
                                qlineRec.SBQQ__SubscriptionTerm__c != null ? entitlementStartDate.addMonths(Integer.valueOf(qlineRec.SBQQ__SubscriptionTerm__c)) : null;
                    }

                    if (prdNms.containsKey(qlineRec.SBQQ__Product__r.Name)) {
                        System.debug('inside if NonInterval, prdNms contains support ===');
                        if (!oldEntmapOfQline.containsKey(entKey) && !entMap.containsKey(entKey)) {// Create new Entitlement
                            ent = new Entitlement();
                            System.debug('inside if NONsInterval entitlement DOESnt exist ===');
                            ent.DSI__c = dsiId;
                            ent.AccountId = qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.AccountId;
                            ent.Support_Level__c = productName;
                            if (productName == 'Cloud') {
                                ent.Support_Level__c = 'Cloud Standard';
                            }
                            ent.SKU__c = qlineRec.SBQQ__ProductCode__c;
                            ent.Product__c = qlineRec.SBQQ__Product__c;
                            ent.StartDate = entitlementStartDate;

                            if(qlineRec.SBQQ__Group__c != null && qlEndDateByGroup != null && qlEndDateByGroup.containsKey(qlineRec.SBQQ__Quote__c)){
                                ent.EndDate = qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c);
                            } else {
                                ent.EndDate = newEndDate;
                            }
                            ent.Name = dsi_name + ' - ' + productName;

                            if (prdNms.containsKey(qlineRec.SBQQ__Product__r.Name) && entProcessmap.containsKey(prdNms.get(qlineRec.SBQQ__Product__r.Name))) {
                                ent.SlaProcessId = entProcessmap.get(prdNms.get(qlineRec.SBQQ__Product__r.Name)).Id;
                            }

                            ent.BusinessHoursId = qlineRec.SBCF_DSI__c != null && dsisByIds.containsKey(qlineRec.SBCF_DSI__c) ?
                                    dsisByIds.get(qlineRec.SBCF_DSI__c).Account__r.District__r.Business_Hours__c :
                                    qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c != null && dsisByIds.containsKey(qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c) ?
                                            dsisByIds.get(qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c).Account__r.District__r.Business_Hours__c :
                                            null
                                    ;

                            ent.Opportunity__c = qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c;
                            ent.LastUpdateddate__c = System.now();
                            if (usrlst != null && !usrlst.isEmpty()) {
                                ent.LastUpdatedBy__c = usrlst[0].Id;
                            }

                        } else if (oldEntmapOfQline.containsKey(entKey)) {// Update an existing Entitlement
                            System.debug('inside if NON Interval Entile EXISTS === ');
                            ent = oldEntmapOfQline.get(entKey);
                            System.debug('old entitl ent === ' + ent);
                            System.debug('qlineRec.SBQQ__Quote__r === ' + qlineRec.SBQQ__Quote__c);
                            System.debug('qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c === ' + qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c);

                            ent.Opportunity__c = qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c;
                            ent.LastUpdateddate__c = System.now();
                            if (usrlst != null && !usrlst.isEmpty()) {
                                ent.LastUpdatedBy__c = usrlst[0].Id;
                            }
                            // below is commented coz Premium support, Elite Support etc get end Date Comparision only if there r Duplicates.
                            if(qlineRec.SBQQ__Group__c != null && qlEndDateByGroup != null && qlEndDateByGroup.containsKey(qlineRec.SBQQ__Quote__c)){
                                ent.EndDate = (ent.EndDate != null ? (ent.EndDate > qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c) ? ent.EndDate : qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c)) : qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c));
                            } else {
                                ent.EndDate = (ent.EndDate != null ? (ent.EndDate > newEndDate ? ent.EndDate : newEndDate) : newEndDate);
                            }
                        }// END else
                        for (Entitlement objEnt : oldEntmapOfQline.values()) {
                            //case 485481: S6 Opportunities on MCI DSIs will automatically extend the entitlement end dates upon opportunity execution.
                            if (objEnt.DSI__c != null && qlineRec.SBCF_DSI__c != null && dsisByIds != null && !dsisByIds.isEmpty() && dsisByIds.containsKey(qlineRec.SBCF_DSI__c) && objEnt.DSI__c == qlineRec.SBCF_DSI__c && dsisByIds.get(qlineRec.SBCF_DSI__c).Platform__c != null && dsisByIds.get(qlineRec.SBCF_DSI__c).Platform__c.containsIgnoreCase('mci') && qlineRec.SBQQ__EndDate__c == null) {

                                ent.EndDate = qlineRec.SBQQ__Group__c != null && qlEndDateByGroup.containsKey(qlineRec.SBQQ__Quote__c) ? qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c) : ent.EndDate.addYears(1);

                                // ent.EndDate = ent.EndDate.addYears(1);
                                dsisByIds.get(qlineRec.SBCF_DSI__c).Entitlement_End_Date__c = ent.EndDate;
                                updateDsi = true;
                            }
                        }

                        //update the bigger date as End date
                        if (entMap.containsKey(entKey) && entMap.get(entKey) != null) {
                            System.debug('inside if check dates NONStan === ');
                            if(qlineRec.SBQQ__Group__c != null && qlEndDateByGroup != null && qlEndDateByGroup.containsKey(qlineRec.SBQQ__Quote__c)){
                                entMap.get(entKey).EndDate = (entMap.get(entKey).EndDate != null) ? (entMap.get(entKey).EndDate > qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c) ? entMap.get(entKey).EndDate : qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c)) : qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c) ;
                            }else {
                                entMap.get(entKey).EndDate = (entMap.get(entKey).EndDate != null) ? (entMap.get(entKey).EndDate > qlineRec.SBQQ__EndDate__c ? entMap.get(entKey).EndDate : qlineRec.SBQQ__EndDate__c) : qlineRec.SBQQ__EndDate__c ;
                            }
                        }

                        if (ent != null) {
                            entMap.put(entKey, ent);
                        }

                        // END if other than Standard MX
                    } else if (qlineRec.Interval__c == 'Term' && !prdNms.containsKey(qlineRec.SBQQ__Product__r.Name) && !qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.Cloud_Opportunity__c && qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.SBQQ__PrimaryQuote__r.Platform__c != 'MCG') { // This runs for NON MX QLines-to create Standard Entitle
                        System.debug('inside if interval = term ===');
                        String StandardEntKey = String.valueOf(dsiId) + 'Standard';
                        System.debug('StandardEntKey === ' + StandardEntKey);
                        System.debug('entMap === ' + entMap);
                        System.debug('entMap.ContainsKey(StandardEntKey) === ' + entMap.containsKey(StandardEntKey));

                        if (!oldEntmapOfQline.containsKey(StandardEntKey) && !entMap.containsKey(StandardEntKey)) {// Create New STANDARD Entitle

                            System.debug('inside if STANDARD entitlement DOESnt exist ===');
                            ent = new Entitlement();
                            ent.DSI__c = dsiId;
                            ent.AccountId = qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.AccountId;
                            ent.Support_Level__c = 'Standard';
                            ent.SKU__c = '30089';
                            ent.Product__c = '01t4400000Bsx9F';
                            ent.StartDate = entitlementStartDate;
                            if(qlineRec.SBQQ__Group__c != null && qlEndDateByGroup != null && qlEndDateByGroup.containsKey(qlineRec.SBQQ__Quote__c)){
                                ent.EndDate = qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c);
                            } else {
                                ent.EndDate = newEndDate;
                            }
                            ent.Name = dsi_name + ' - ' + 'Standard';
                            if (entProcessmap != null && entProcessmap.get('Standard') != null) {
                                ent.SlaProcessId = entProcessmap.get('Standard').Id;
                            }

                            ent.BusinessHoursId = qlineRec.SBCF_DSI__c != null && dsisByIds.containsKey(qlineRec.SBCF_DSI__c) ?
                                    dsisByIds.get(qlineRec.SBCF_DSI__c).Account__r.District__r.Business_Hours__c :
                                    qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c != null && dsisByIds.containsKey(qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c) ?
                                            dsisByIds.get(qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__r.DSI__c).Account__r.District__r.Business_Hours__c :
                                            null
                                    ;
                            ent.Opportunity__c = qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c;
                            ent.LastUpdateddate__c = System.now();
                            if (usrlst != null && !usrlst.isEmpty()) {
                                ent.LastUpdatedBy__c = usrlst[0].Id;
                            }
                            // system.debug()

                        } else if (oldEntmapOfQline.containsKey(StandardEntKey)) {// UPDATE existing STANDARD Entitle
                            System.debug('inside if Entile EXISTS === ');
                            ent = oldEntmapOfQline.get(StandardEntKey);
                            System.debug('old entitl ent === ' + ent);
                            System.debug('qlineRec.SBQQ__Quote__r === ' + qlineRec.SBQQ__Quote__c);
                            System.debug('qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c === ' + qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c);

                            ent.Opportunity__c = qlineRec.SBQQ__Quote__r.SBQQ__Opportunity2__c;
                            ent.LastUpdateddate__c = System.now();
                            if (usrlst != null && !usrlst.isEmpty()) {
                                ent.LastUpdatedBy__c = usrlst[0].Id;
                            }
                            System.debug(' Standard ent.EndDate b4 update === ' + ent.EndDate);
                            System.debug('qlineRec.SBQQ__EndDate__c === ' + qlineRec.SBQQ__EndDate__c);
                            System.debug('ent.EndDate > qlineRec.SBQQ__EndDate__c === ' + (ent.EndDate > qlineRec.SBQQ__EndDate__c));
                            System.debug('big expression === ' + (ent.EndDate != null ? (ent.EndDate > qlineRec.SBQQ__EndDate__c ? ent.EndDate : qlineRec.SBQQ__EndDate__c) : qlineRec.SBQQ__EndDate__c));
                            if(qlineRec.SBQQ__Group__c != null && qlEndDateByGroup != null && qlEndDateByGroup.containsKey(qlineRec.SBQQ__Quote__c)){
                                ent.EndDate = (ent.EndDate != null ? (ent.EndDate > qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c) ? ent.EndDate : qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c)) : qlEndDateByGroup.get(qlineRec.SBQQ__Quote__c));

                            } else {
                                ent.EndDate = (ent.EndDate != null ? (ent.EndDate > newEndDate ? ent.EndDate : newEndDate) : newEndDate);
                            }
                            System.debug(' Standard ent.EndDate AFTER update === ' + ent.EndDate);

                        }

                        //update the bigger date as End date
                        /*if(entMap.containsKey(StandardEntKey)  && entMap.get(StandardEntKey) != null  ){
                            system.debug('inside ifcheckdates STAND === ');
                            entMap.get(StandardEntKey).EndDate =   (entMap.get(StandardEntKey).EndDate != null)  ? (entMap.get(StandardEntKey).EndDate > qlineRec.SBQQ__EndDate__c ?entMap.get(StandardEntKey).EndDate : qlineRec.SBQQ__EndDate__c ):qlineRec.SBQQ__EndDate__c ;
                         }*/

                        if (ent != null) {
                            entMap.put(StandardEntKey, ent);
                        }

                    }// END IF term = Interval

                    System.debug('ent === ' + ent);
                    System.debug('entMap size === ' + entMap.size());

                }//END Quote Line FOR Loop
            }// END IF toProcessQLineList size > 0
        }// END IF QuoteScope

        //******Pavan QuoteLineItems END **********
        System.debug('###--entMap to b4Upsert ===' + entMap.size());
        if (!entMap.isEmpty()) {
            System.debug ('ready to upsertentMap  ==== ' + entMap);
            upsert entMap.values();
        }
        if (updateDsi && !dsisByIds.isEmpty()) {
            update dsisByIds.values();
        }

    }
/*
    public static Map<String, Entitlement> getExistingEntitlements (Set<Id> dsiRecIds) {
        Map<String, Entitlement> temp = new Map<String, Entitlement> ();
        for (Entitlement e : [SELECT Id, Name, Support_Level__c, DSI__c, EndDate FROM Entitlement WHERE DSI__c IN:dsiRecIds]) {
            temp.put((String.valueOf(e.DSI__c) + e.Support_Level__c), e);
            System.debug(e.DSI__c + e.Support_Level__c);
        }
        return temp;
    }
    */
    public static Map<String, Entitlement> getExistingEntitlements (Set<Id> dsiRecIds) {
        Map<String, Entitlement> temp = new Map<String, Entitlement> ();
        for (Entitlement e : [SELECT Id, Name, Support_Level__c, DSI__c, EndDate FROM Entitlement WHERE DSI__c IN :dsiRecIds]) {
            String suppLevel = e.Support_Level__c != null ? e.Support_Level__c.replace('Support', '').replace('support', '').replace('Renewal', '').replace('-1st', '').replace('(', '').replace(')', '').trim() : '';

            if (e.Support_Level__c.contains('Cloud') & !e.Support_Level__c.contains('Elite')) {
                temp.put((String.valueOf(e.DSI__c) + 'Cloud'), e);
            } else {
                temp.put((String.valueOf(e.DSI__c) + suppLevel), e);
                System.debug(e.DSI__c + e.Support_Level__c);
            }
        }
        return temp;
    }

    public static Map<String, SlaProcess> getEntitlementProcess () {
        Map<String, SlaProcess> epmap = new Map<String, SlaProcess> ();
        for (SlaProcess ep : [
                SELECT Id, Name, BusinessHoursId
                FROM SlaProcess
                WHERE Name = 'Standard'
                OR Name = 'Extended'
                OR Name = 'Elite'
                OR Name = 'Premier'
                OR Name = 'Cloud'
                OR Name = 'Cloud Standard'
                OR Name = 'Cloud Elite'
        ]) {
            epmap.put(ep.Name, ep);
        }
        return epmap;
    }

    public static Map<Id, Date> getEntEndDateByGroup(List<SBQQ__QuoteLine__c> quoteLines) {
        Map<Id, Date> result = new Map<Id, Date>();
        Map<Id, List<Date>> qlMap = new Map<Id, List<Date>>();
        for (SBQQ__QuoteLine__c line : quoteLines) {
            if (line.SBQQ__Group__c != null) {
                if (qlMap.containsKey(line.SBQQ__Quote__c)) {
                    qlMap.get(line.SBQQ__Quote__c).add(line.SBQQ__EndDate__c);
                } else {
                    qlMap.put(line.SBQQ__Quote__c, new List<date>{
                            line.SBQQ__EndDate__c
                    });
                }
            }
        }
        if (!qlMap.isEmpty()) {
            for (Id quote : qlMap.keySet()) {
                List<date> sortedList = new List<Date>();
                sortedList = qlMap.get(quote);
                sortedList.sort();
                result.put(quote, sortedList[qlMap.get(quote).size() - 1]);
            }
        }
        return result;
    }

    public static void createServiceNowCase (SBQQ__Quote__c quote) {

        CaseWrapper caseObj = new CaseWrapper();
        caseObj.u_status = 'C5 - New Key Confirmation';
        caseObj.category = 'Term Renewal Key';

        caseObj.description = 'Automated Case generated to have Support confirm that the Term Key has been applied by the Customer. The key should be shipped to [' + quote.SBCF_Ship_to_Contact__r.Name + ']';

        caseObj.short_description = 'Confirm with Customer that new Term Key has been applied';
        caseObj.u_related_opportunity = quote.SBQQ__Opportunity2__c;
        caseObj.account = quote.SBQQ__Opportunity2__r.Account.ServiceNow_Id__c;
        caseObj.contact = quote.SBCF_Ship_to_Contact__r.ServiceNow_Id__c;
        caseObj.u_dsi = quote.SBQQ__Opportunity2__r.DSI__r.ServiceNow_Id__c;
        caseObj.u_term_key_renewal_case = true;

        Http h = new Http();
        Httprequest caseCreation = new Httprequest();
        caseCreation.setEndpoint(SN_IntegrationHelper.snEndpoint + '/api/sn_customerservice/case');

        caseCreation.setHeader('Content-Type', 'application/json; charset=UTF-8');
        caseCreation.setBody(JSON.serialize(caseObj));
        caseCreation.setHeader('Authorization', '{!$Credential.Password}');
        caseCreation.setMethod('POST');
        HttpResponse response = new HttpResponse();
        if (!test.isRunningTest()) {
            response = h.send(caseCreation);
        }
        if (response.getStatusCode() != 201) {
            System.debug(response.getStatusCode());
            System.debug(response.getBody());
            System.debug(response.getStatus());
            FF_Integration_Log__c ffIntegrationLog = new FF_Integration_Log__c();
            ffIntegrationLog.Debug_Message__c = 'Service Now - Failed to create case: \n ' + response.getStatusCode() + '\n' + response.getBody() + '\n' + JSON.serialize(caseObj);
            ffIntegrationLog.Type__c = 'Error';
            insert ffIntegrationLog;
        }

    }

    public class CaseWrapper {

        public string u_status;
        public string category;
        public string description;
        public string short_description;
        public string u_related_opportunity;
        public string account;
        public string contact;
        public string u_dsi;
        public Boolean u_term_key_renewal_case;
    }
}