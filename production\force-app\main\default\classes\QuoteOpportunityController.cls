/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 06/01/2021.
 */

 public without sharing class QuoteOpportunityController {

    private static Map<Id, Opportunity> oppMapToUpdate {
        get {
            if (oppMapToUpdate == null) {
                oppMapToUpdate = new Map<Id, Opportunity>();
            }
            return oppMapToUpdate;
        }
        set;
    }

    public static void updateOppPlatformFlag (List<SBQQ__Quote__c> quotes) {
        Opportunity relatedOpp;
        for (SBQQ__Quote__c quote : quotes) {
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            relatedOpp.Platform__c = quote.Platform__c;
        }
    }

    public static void updateOppApprovalStatusFields (List<SBQQ__Quote__c> quotes) {
        Opportunity relatedOpp;
        for (SBQQ__Quote__c quote : quotes) {
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);

            relatedOpp.Last_Status_Changed_Quote__c = quote.Id;
            relatedOpp.Last_Status_Changed_Quote_Status__c = quote.SBCF_Approval_Status__c;
            relatedOpp.Last_Status_Changed_Quote_AP__c = quote.AP__c;
        }
    }

    public static void updateOppForecasts (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps) {
        Opportunity relatedOpp, queriedOpp;

        for (SBQQ__Quote__c quote : quotes) {
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);

            if (!queriedOpp.Prevent_Forecast_Override__c) {
                relatedOpp.License_Forecast__c = quote.LF2__c;
                relatedOpp.Bizible_Opportunity_Amount__c = quote.LF2__c;
            }
            if (!queriedOpp.Cloud_Migration_Forecast_Lock__c) {
                relatedOpp.Cloud_Migration_Forecast__c = quote.Cloud_Migration_Forecast__c;
            }
        }
    }

    public static void updateOppJDECorporation (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps, Map<Id, pse__Grp__c> groupMap) {
        Opportunity relatedOpp, queriedOpp;
        Country_Map__mdt countryMapRecord;
        Map<String, Country_Map__mdt> countryNameMap = new Map<String, Country_Map__mdt>();
        for (Country_Map__mdt metaRec : Country_Map__mdt.getAll().values()) {
            if(metaRec.Country_Code__c != null){
                countryNameMap.put(metaRec.Country_Code__c, metaRec);
            }
        }

        for (SBQQ__Quote__c quote : quotes) {
            queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            relatedOpp.SBCF_Legal_Entity__c = null;
            relatedOpp.Consulting_JDE_BU_ID__c = null;
            relatedOpp.Education_JDE_BU_ID__c = null;
            relatedOpp.Maintenance_JDE_BU_ID__c = null;
            relatedOpp.License_JDE_BU_ID__c = null;
            if (queriedOpp.SBQQ__PrimaryQuote__r.Ship_To_Book__c == null ||
                !countryNameMap.containsKey(queriedOpp.SBQQ__PrimaryQuote__r.Ship_To_Book__r.Country__c)
            ) {
                continue;
            }
            countryMapRecord = countryNameMap.get(queriedOpp.SBQQ__PrimaryQuote__r.Ship_To_Book__r.Country__c);
            if (countryMapRecord.Corporation_ID__c != null) {
                relatedOpp.SBCF_Legal_Entity__c = countryMapRecord.Corporation_ID__c;
            }
            if (countryMapRecord.JDE_BU_Consulting__c != null && groupMap.get(countryMapRecord.JDE_BU_Consulting__c) != null && groupMap.get(countryMapRecord.JDE_BU_Consulting__c).JDE_ID__c != null) {
                relatedOpp.Consulting_JDE_BU_ID__c = Integer.valueOf(groupMap.get(countryMapRecord.JDE_BU_Consulting__c).JDE_ID__c);
            }
            if (countryMapRecord.JDE_BU_Education__c != null && groupMap.get(countryMapRecord.JDE_BU_Education__c) != null && groupMap.get(countryMapRecord.JDE_BU_Education__c).JDE_ID__c != null) {
                relatedOpp.Education_JDE_BU_ID__c = Integer.valueOf(groupMap.get(countryMapRecord.JDE_BU_Education__c).JDE_ID__c);
            }
            if (countryMapRecord.JDE_BU_Maintenance__c != null && groupMap.get(countryMapRecord.JDE_BU_Maintenance__c) != null && groupMap.get(countryMapRecord.JDE_BU_Maintenance__c).JDE_ID__c != null) {
                relatedOpp.Maintenance_JDE_BU_ID__c = Integer.valueOf(groupMap.get(countryMapRecord.JDE_BU_Maintenance__c).JDE_ID__c);
            }
            if (countryMapRecord.JDE_BU_Sales__c != null && groupMap.get(countryMapRecord.JDE_BU_Sales__c) != null && groupMap.get(countryMapRecord.JDE_BU_Sales__c).JDE_ID__c != null) {
                relatedOpp.License_JDE_BU_ID__c = Integer.valueOf(groupMap.get(countryMapRecord.JDE_BU_Sales__c).JDE_ID__c);
            }
        }
    }

    public static void updateOppType (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps) {
        Opportunity relatedOpp, queriedOpp;
        for (SBQQ__Quote__c quote : quotes) {
            queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);

            if (String.isBlank(relatedOpp.Type)) {
                relatedOpp.Type = queriedOpp.Type;
            }

            if (String.isBlank(relatedOpp.Migration_Type__c)) {
                relatedOpp.Migration_Type__c =
                    (String.isBlank(queriedOpp.SBQQ__RenewedContract__r.SBQQ__Opportunity__r.Platform__c) ?
                        '' : queriedOpp.SBQQ__RenewedContract__r.SBQQ__Opportunity__r.Platform__c + ' ') +
                        (String.isBlank(queriedOpp.SBQQ__RenewedContract__r.SBQQ__Opportunity__r.LicenseType__c) ?
                            '' : queriedOpp.SBQQ__RenewedContract__r.SBQQ__Opportunity__r.LicenseType__c) +
                        ' to ' +
                        (String.isBlank(quote.Platform__c) ? '' : quote.Platform__c + ' ') +
                        (String.isBlank(quote.License_Type__c) ? '' : quote.License_Type__c)
                    ;
            }

            if (String.isNotBlank(quote.Quote_Offer_Type__c) && relatedOpp.Type != quote.Quote_Offer_Type__c) {
                if (quote.Quote_Offer_Type__c == 'Term Migration') {
                    relatedOpp.StageName = 'S1 - Recognize Needs';
                    relatedOpp.SBCF_Renewal_Type__c = null;
                    relatedOpp.Type = quote.Quote_Offer_Type__c;
                } else if (quote.Quote_Offer_Type__c == 'Term Renewal') {
                    relatedOpp.StageName = 'S5 - Negotiate/Purchase Solution';
                    relatedOpp.SBCF_Renewal_Type__c = null;
                    relatedOpp.Type = quote.Quote_Offer_Type__c;
                } else if (quote.Quote_Offer_Type__c == 'Maintenance Renewal') {
                    relatedOpp.StageName = 'S5 - Negotiate/Purchase Solution';
                    relatedOpp.SBCF_Renewal_Type__c = 'Standard';
                    relatedOpp.CPQ_UAT_Name__c = 'Maintenance Renewal';
                    relatedOpp.Type = quote.Quote_Offer_Type__c;
                }
            }
            relatedOpp.SBQQ__PrimaryQuote__c = quote.Id;

            if (relatedOpp.Type == 'Term Migration') {
                if (quote.SBCF_Sum_Of_Cloud__c > 0) {
                    relatedOpp.CPQ_UAT_Name__c = 'MCE Migration';
                } else if (quote.Sum_of_Term__c > 0) {
                    relatedOpp.CPQ_UAT_Name__c = 'Term Migration';
                }
            }
        }
    }

    public static void pushOppRollups (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps) {

        Map<String, String> customMetaMap = new Map<String, String>();
        for (Quote_Opportunity_Rollups__mdt metaDtRec : TriggersHelper.oppRollups) {
            customMetaMap.put(metaDtRec.Target_Field__c, metaDtRec.Target_Value__c);
        }

        Id salesOpportunityId = TriggersHelper.salesOppRecordTypeId;
        Id renewalOpportunityId = TriggersHelper.renewalOppRecordTypeId;
        Opportunity relatedOpp, queriedOpp;


        List<Id> relatedDSIs = new List<Id>();
        for (Opportunity currentOpportunity : queriedOpps.values()) {
            relatedDSIs.add(currentOpportunity.DSI__c);
        }

        Map<Id, DSI__c> assetsAndSubscriptionsByDSIId = new Map<Id, DSI__c>([
            SELECT Id, (SELECT Id FROM Assets__r LIMIT 1), (SELECT Id FROM Subscriptions__r LIMIT 1) //If DSI__c.Assets__r or DSI__c.Subscriptions__r returns at least one row, then the DSI is not new
            FROM DSI__c
            WHERE Id IN :relatedDSIs
        ]);

        for (SBQQ__Quote__c quote : quotes) {
            queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);

            for (String targetField : customMetaMap.keySet()) {
                if (customMetaMap.get(targetField) != null) {
                    relatedOpp.put(targetField, quote.get(customMetaMap.get(targetField)));
                }
            }

            if (!queriedOpp.Enterprise_Support__c) {
                if (quote.Net_Expert_Consulting__c != null && quote.Net_Other_Consulting__c != null) {
                    relatedOpp.Total_Cons_Pipe_Currency__c = quote.Net_Expert_Consulting__c + quote.Net_Other_Consulting__c;
                } else if (quote.Net_Expert_Consulting__c == null) {
                    relatedOpp.Total_Cons_Pipe_Currency__c = quote.Net_Other_Consulting__c;
                } else if (quote.Net_Other_Consulting__c == null) {
                    relatedOpp.Total_Cons_Pipe_Currency__c = quote.Net_Expert_Consulting__c;
                }
            }

            if (quote.MYC__c != null) {
                relatedOpp.MYC__c = quote.MYC__c;
            }

            String oppType = String.isNotBlank(relatedOpp.Type) ? relatedOpp.Type : queriedOpp.Type;
            //Case 454552 - Transaction Type Identifier on Opportunities
            // Logic to determine value of the Transaction Type field
            if (queriedOpp.StageName != Constants.OPPORTUNITY_STAGE_CLOSED &&
                queriedOpp.StageName != Constants.OPPORTUNITY_STAGE_WON &&
                queriedOpp.StageName != Constants.OPPORTUNITY_STAGE_CO_TERMED
            ) {
                if (quote.Account_Customer_Status__c <> null) {

                    // Add On: New Sales Opportunity for Account with Account Category is Customer and DSI Platform = Opportunity Platform
                    if (queriedOpp.Platform__c != null &&
                        queriedOpp.DSI__c != null &&
                        queriedOpp.DSI__r.Platform__c != null &&
                        quote.Account_Customer_Status__c.startsWith('C') &&
                        queriedOpp.RecordTypeId == salesOpportunityId &&
                        queriedOpp.DSI__r.Platform__c.split(';').contains(queriedOpp.Platform__c)) {
                        relatedOpp.Transaction_Type__c = 'Add On';

                    }
                    // New: (Account Category is Prospect) or (Account Category is Customer and Account Platform = Opportunity Platform and Opportunity DSI has no assets/subscriptions (opp created on new DSI) )
                    else if (quote.Account_Customer_Status__c.startsWith('P') ||
                        (queriedOpp.DSI__c != null &&
                            quote.SBQQ__Account__r != null &&
                            queriedOpp.Account.Platform__c != null &&
                            quote.Account_Customer_Status__c.startsWith('C') &&
                            queriedOpp.Account.Platform__c.split(';').contains(queriedOpp.Platform__c) &&
                            assetsAndSubscriptionsByDSIId.get(queriedOpp.DSI__c).Assets__r.size() == 0 &&
                            assetsAndSubscriptionsByDSIId.get(queriedOpp.DSI__c).Subscriptions__r.size() == 0)) {
                        relatedOpp.Transaction_Type__c = 'New';
                    }
                    //Migration: Renewal Opportunity Type 'Term Migration' or New Sales Opportunity Account with Account Category is Customer and Account Platform <> Opportunity Platform
                    else if (queriedOpp.Platform__c != null &&
                        queriedOpp.Account.Platform__c != null &&
                        ((queriedOpp.RecordTypeId == renewalOpportunityId && oppType == 'Term Migration') ||
                            (queriedOpp.RecordTypeId == salesOpportunityId && quote.Account_Customer_Status__c.startsWith('C') &&
                                (!queriedOpp.Account.Platform__c.split(';').contains(queriedOpp.Platform__c))))
                    ) {
                        relatedOpp.Transaction_Type__c = 'Migration';
                    }
                }
                if (queriedOpp.RecordTypeId == renewalOpportunityId && !oppType.contains('Migration')) {
                    if (quote.LF2__c == 0 || quote.LF2__c == null) {
                        //Renewal: Renewal Opportunity Type <> Migration and License Forecast = 0
                        relatedOpp.Transaction_Type__c = 'Renewal';
                    } else if (quote.LF2__c > 0) {
                        //Renewal with Incremental: Renewal Opportunity Type <> Migration and License Forecast > 0
                        relatedOpp.Transaction_Type__c = 'Renewal with Incremental';
                    }
                }
            }
        }
    }

    public static void updateOppRollups (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps) {
        Opportunity relatedOpp,queriedOpp;

        for (SBQQ__Quote__c quote : quotes) {
            queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            relatedOpp.NumQuotesQ4__c = 0;
            relatedOpp.NumQuotesQ5__c = 0;
            for (SBQQ__Quote__c relatedQuote2QuoteOpp : relatedOpp.SBQQ__Quotes2__r) {
                if (QuoteController.checkQuoteBucket('QuoteDraftStatuses', relatedQuote2QuoteOpp.SBQQ__Status__c)) {
                    relatedOpp.NumQuotesQ4__c ++;
                } else if (QuoteController.checkQuoteBucket('QuoteApprovedStatuses', relatedQuote2QuoteOpp.SBQQ__Status__c)) {
                    relatedOpp.NumQuotesQ5__c ++;
                }
            }
        }
    }

    public static void updateOppTotalLOE (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps) {
        Opportunity relatedOpp,queriedOpp;

        for (SBQQ__Quote__c quote : quotes) {
            queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            relatedOpp.Total_LC_LOE__c = 0;
            for (SBQQ__Quote__c relatedQuote2QuoteOpp : relatedOpp.SBQQ__Quotes2__r) {
                relatedOpp.Total_LC_LOE__c +=quote.LC_LOE__c;
            }
        }
    }

    public static void updateOppClosedWon (List<SBQQ__Quote__c> quotes,Map<Id, Opportunity> queriedOpps, Map<Id, List<SBQQ__QuoteLine__c>> qlByQuoteId) {
        List<SBQQ__QuoteLine__c> amendedQLs = new List<SBQQ__QuoteLine__c>();
        for (List<SBQQ__QuoteLine__c> qls : qlByQuoteId.values()) {
            for (SBQQ__QuoteLine__c ql : qls) {
                if (ql.Is_Amended__c) {
                    amendedQLs.add(ql);
                }
            }
        }
        if (!amendedQLs.isEmpty()) {
            QuoteController.updateAssetsForAmendedLines(amendedQLs,queriedOpps);
        }

        Opportunity relatedOpp;
        for (SBQQ__Quote__c quote : quotes) {
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            relatedOpp.CloseDate = Date.today();
            relatedOpp.StageName = Constants.OPPORTUNITY_STAGE_WON;
            relatedOpp.SBQQ__Contracted__c = true;
        }
    }

    public static void setCAFComments (SBQQ__Quote__c quote) {
        Opportunity relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
        relatedOpp.SBCF_CAFComments__c = String.isNotBlank(quote.SBCF_Legal_Notes__c) || String.isNotBlank(quote.SBCF_Rev_Rec_Notes__c);
    }

    public static void updateSalesOppMigrationType (List<SBQQ__Quote__c> quotes) {
        Opportunity relatedOpp;
        for (SBQQ__Quote__c quote : quotes) {
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            relatedOpp.Migration_Type__c = (String.isBlank(quote.Platform__c) ? '' : quote.Platform__c + ' ') +
                (String.isBlank(quote.License_Type__c) ? '' : quote.License_Type__c);
        }
    }

    public static void populatePrimaryOnOpp (Map<Id, SBQQ__Quote__c> quotesMap) {
        Opportunity relatedOpp;
        for (SBQQ__Quote__c quote : quotesMap.values()) {
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            relatedOpp.SBQQ__PrimaryQuote__c = quote.Id;
        }
    }

    public static void updateRelatedOpportunities () {
        System.debug(LoggingLevel.INFO, 'QuoteOpportunityController.updateRelatedOpportunities');
        if (!oppMapToUpdate.isEmpty()) {
            update oppMapToUpdate.values();
            oppMapToUpdate.clear();
        }
    }

    public static void updateOppMYCFlag (List<SBQQ__Quote__c> updatedMYCQuotes) {
        Opportunity relatedOpp;
        for (SBQQ__Quote__c quote : updatedMYCQuotes) {
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            relatedOpp.Multi_Year_Contract_Opp__c = quote.Multi_Year_Contract_Quote__c;
        }
    }

    public static void updateAmendmentOpps (List<SBQQ__Quote__c> quotes, Map<Id, Opportunity> queriedOpps, Map<Id, pse__Grp__c> groupMap) {
        String groupId;
        Opportunity relatedOpp,queriedOpp;
        for (SBQQ__Quote__c quote : quotes) {
            relatedOpp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
            queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);

            relatedOpp.RecordTypeId = TriggersHelper.amendmentOppRecordTypeId;
            relatedOpp.Amended_Quote__c = '<p><a href="/'+queriedOpp.SBQQ__AmendedContract__r.SBQQ__Quote__c+'" target="_blank">'+queriedOpp.SBQQ__AmendedContract__r.SBQQ__Quote__r.Name+'</a></p>';
            relatedOpp.Amended_Order__c = '<p><a href="/'+queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__c+'" target="_blank">'+queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.OrderNumber+'</a></p>';
            relatedOpp.DSI__c = queriedOpp.SBQQ__AmendedContract__r.DSI_SFID__c;
            relatedOpp.Partner_Contact__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Partner_Contact__c; //MYC BUG 329 Nogueira
            relatedOpp.Partner_AE__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Partner_AE__c; //MYC BUG 329 Nogueira
            relatedOpp.GA_Representative__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.GA_Representative__c; //MYC BUG 329 Nogueira
            relatedOpp.SBCF_Type_of_Sale__c = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.SBCF_Type_of_Sale__c; //MYC BUG 329 Nogueira
            
            //MYC BUG 324 Nogueira
            
            String jdeCorpBUId;
            Decimal consultingBUId;
            Decimal educationBUId;
            Decimal licenseBUId;
            Decimal maintenanceBUId;
            
            if(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.JDE_Corporation__c != null && String.isNotBlank(String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.JDE_Corporation__c))){
                jdeCorpBUId = String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.JDE_Corporation__c);
            }
            else{
                jdeCorpBUId = String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.SBCF_Legal_Entity__c);
            }
            
            if(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Consulting_JDE_BU__c != null && String.isNotBlank(String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Consulting_JDE_BU__c))){
                groupId = String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Consulting_JDE_BU__c);
                consultingBUId = (groupId != null && groupMap.get(groupId) != null && groupMap.get(groupId).JDE_ID__c != null)? Integer.valueOf(groupMap.get(groupId).JDE_ID__c): null;
            }
            else{
                consultingBUId = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Consulting_JDE_BU_ID__c;
            }
            
            if(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Education_JDE_BU__c != null && String.isNotBlank(String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Education_JDE_BU__c))){
                groupId = String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Education_JDE_BU__c);
                educationBUId = (groupId != null && groupMap.get(groupId) != null && groupMap.get(groupId).JDE_ID__c != null)? Integer.valueOf(groupMap.get(groupId).JDE_ID__c): null;
            }
            else{
                educationBUId = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Education_JDE_BU_ID__c;
            }
            
            if(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.License_JDE_BU__c != null && String.isNotBlank(String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.License_JDE_BU__c))){
                groupId = String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.License_JDE_BU__c);
                licenseBUId = (groupId != null && groupMap.get(groupId) != null && groupMap.get(groupId).JDE_ID__c != null)? Integer.valueOf(groupMap.get(groupId).JDE_ID__c): null;
            }
            else{
                licenseBUId = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.License_JDE_BU_ID__c;
            }
            
            if(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Maintenance_JDE_BU__c != null && String.isNotBlank(String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Maintenance_JDE_BU__c))){
                groupId = String.valueOf(queriedOpp.SBQQ__AmendedContract__r.SBQQ__Order__r.Maintenance_JDE_BU__c);
                maintenanceBUId = (groupId != null && groupMap.get(groupId) != null && groupMap.get(groupId).JDE_ID__c != null)? Integer.valueOf(groupMap.get(groupId).JDE_ID__c): null;
            }
            else{
                maintenanceBUId = queriedOpp.SBQQ__AmendedContract__r.SBQQ__Opportunity__r.Maintenance_JDE_BU_ID__c;
            }
            
            relatedOpp.SBCF_Legal_Entity__c = jdeCorpBUId; 
            relatedOpp.Consulting_JDE_BU_ID__c = consultingBUId;
            relatedOpp.Education_JDE_BU_ID__c = educationBUId;
            relatedOpp.License_JDE_BU_ID__c = licenseBUId;
            relatedOpp.Maintenance_JDE_BU_ID__c = maintenanceBUId;
            
            // MYC BUG 324 Nogueira
            
        }
    }


     public static void calculateCloudForecast(List<SBQQ__Quote__c> quotes,Map<Id, Opportunity> queriedOpps) {
         Opportunity opp, queriedOpp;
         if (!OpportunityTriggerExecutionController.cloudForcastLock) {
             for (SBQQ__Quote__c quote : quotes) {
                 opp = getRelatedOppFromOppMapToUpdate(quote.SBQQ__Opportunity2__c);
                 queriedOpp = queriedOpps.get(quote.SBQQ__Opportunity2__c);
                 opp.Cloud_Incremental_Forecast__c = queriedOpp.License_Forecast__c == null ? 0 : queriedOpp.License_Forecast__c;
                 Decimal cloudForecast = queriedOpp.Cloud_Incremental_Forecast__c == null ? 0 : queriedOpp.Cloud_Incremental_Forecast__c;
                 if (queriedOpp.CloseDate.year() < 2023 || queriedOpp.CloseDate > Date.newInstance(2024, 01, 01)) {
                     cloudForecast += queriedOpp.Cloud_Migration_Forecast__c == null ? 0 : queriedOpp.Cloud_Migration_Forecast__c;
                 }
                 opp.Cloud_Forecast__c = cloudForecast > 0 ? cloudForecast : 0;
                 opp.On_Prem_Forecast__c = 0;
             }
         }
     }

    private static Opportunity getRelatedOppFromOppMapToUpdate (Id oppId) {
        Opportunity relatedOpp;
        if (oppMapToUpdate.containsKey(oppId)) {
            relatedOpp = oppMapToUpdate.get(oppId);
        } else {
            relatedOpp = new Opportunity(Id = oppId);
            oppMapToUpdate.put(relatedOpp.Id, relatedOpp);
        }
        return relatedOpp;
    }

     public static void testCoverage(){
         Integer i = 0;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
     }
}