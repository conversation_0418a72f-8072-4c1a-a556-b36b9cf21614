/************************************* MODIFICATION LOG ********************************************************************************************
* AccountTrigger
*
* DESCRIPTION : Handles all trigger logic Account; Creates records in the queue for sync with Compass
*
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Public Method Name            Inputs                                  Description
*---------------------------------------------------------------------------------------------------------------------------------------------------
* N/A                           N/A                                     N/A
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                  REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Sunil Addepalli               02/24/2014          - Original Version
* John McGowan                  09/22/2014          - Updated the if(Trigger.isAfter && Trigger.idInsert) and if(Trigger.isAfter && Trigger.isUpdate) to
*                                                   include new code for updating the Account Owner History
* Deepa Palatty                 09/26/2014           - added data.com clean user to the filter for integration messgae queue
* Amritha Buddharaju            08/10/2016           - added saperate method for account owner history
* Amritha Buddharaju            01/19/2020           - added new method for updateAccountCategory
* Anton Katyrenchuk             03/17/2020           - added restriction for downgrading Account Category
* Archit Bansal                 01/02/2023           - added new method to populate/clear Expired Support Exception Approved By field
*/

trigger AccountTrigger on Account (before update, after insert, after update, before delete, after undelete) {
    List<String> triggerBypassUsers = MSTR_Global_Configuation__mdt.getInstance('Trigger_Bypass_Users').Value__c.split(';');
    if(!triggerBypassUsers.contains(UserInfo.getUserId())){
        if (Trigger.isAfter && (Trigger.isInsert || Trigger.isUpdate)) {
            if ((UserInfo.getLastName() != 'SVC_APP_INTEGRATION' && UserInfo.getName() != 'Acumen Admin' && UserInfo.getName() != 'Data.com Clean') && Trigger.isInsert) {
                AccountTriggerHelper.createMessageInQueue(Trigger.new, Trigger.isInsert);
            }
    
            AccountOwnerHistoryHelper.updateOwnerHistory(Trigger.newMap, Trigger.oldMap, Trigger.isInsert);
        }
    
        if (Trigger.isBefore && Trigger.isDelete) {
            //[TODO] : What needs to happen when an account is deleted?
        }
        if (Trigger.isAfter && Trigger.isUndelete) {
            //[TODO] : What needs to happen when an account is undeleted?
        }
        if (Trigger.isBefore && (Trigger.isInsert || Trigger.isUpdate)) {
            AccountTriggerHandler.updateAccountCategory(Trigger.new);
            AccountTriggerHandler.populateClearExpiredSupportExceptionApprovedByField(Trigger.oldMap, Trigger.newMap);
    
            AccountTriggerHandler.populatePlan(Trigger.newMap);  
            AccountTriggerHandler.criticalAccountEscalation(Trigger.oldMap, Trigger.newMap);
    
            if (Trigger.isUpdate) {
                AccountTriggerHandler.restrictDowngrading(Trigger.oldMap, Trigger.newMap);
    
                AccountTriggerHandler.stampABBillingModeAndPOCategory(Trigger.oldMap, Trigger.newMap);
    
                AccountTriggerHandler.populatePlan(Trigger.oldMap, Trigger.newMap);
                
                AccountTriggerHandler.populateChurnDate(Trigger.oldMap, Trigger.newMap);
                
            }
        }
    }
    else if(Trigger.isAfter && !System.isQueueable()){
    	System.enqueueJob(new GenericSObjectQueueable(new List<SObject>((List<SObject>)Trigger.new.deepClone(true, true, true)), Trigger.oldMap, Integer.valueOf(MSTR_Global_Configuation__mdt.getInstance('Trigger_Bypass_Batch_Size').Value__c)));
    }
}