/*
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER						DATE									REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* <PERSON>		        04/14/2020                       		- Original Version 
* Priyank						01/07/2021								- Case 435187 - SF - MCI - Update Environment Owner and Subscription Dates
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* Test Class:
*/

public with sharing class DSITriggerHelper {

    public static void updateName (Map<Id, DSI__c> oldDSIs, Map<Id, DSI__c> newDSIs) {
        for (DSI__c newDSI : newDSIs.values()) {
            DSI__c oldDSI = oldDSIs.get(newDSI.Id);
            if (oldDSI.Description_Name__c != newDSI.Description_Name__c || oldDSI.Instance_Number__c != newDSI.Instance_Number__c) {
                String dsiDescription = newDSI.Description_Name__c == null ? '' : newDSI.Description_Name__c;
                String newDsiName = newDSI.Instance_Number__c + ' - ' + dsiDescription;
                if (newDsiName.length() > 80) {
                    newDsiName = newDsiName.substring(0, 80);
                }
                newDSI.Name = newDsiName;
            }
        }
    }
    
    public static void validateMCIInfo(Map<Id, DSI__c> oldDSIs, Map<Id, DSI__c> newDSIs) {
        for (DSI__c newDSI : newDSIs.values()) {
            DSI__c oldDSI = oldDSIs.get(newDSI.Id);
            if(newDSI.Platform__c != null && newDSI.Platform__c.split(';').contains('MCI')) {
                if (oldDSI.Entitlement_End_Date__c != newDSI.Entitlement_End_Date__c && newDSI.Entitlement_End_Date__c < System.today()){
                    newDSI.addError('Entitlement End Date can not be past date');
                }
            }
        }
    }
    
    public static void updateMCIInfo(Map<Id, DSI__c> oldDSIs, Map<Id, DSI__c> newDSIs) {
        for (DSI__c newDSI : newDSIs.values()) {
            if(newDSI.Platform__c != null && newDSI.Platform__c.split(';').contains('MCI')) {
                if(newDSI.Entitlement_End_Date__c != oldDSIs.get(newDSI.Id).Entitlement_End_Date__c){   
                    newDSI.MCIExpirationDatetime__c = newDSI.Entitlement_End_Date__c;
                }
            }
        }
    }
    
    public static void validateMCSInfo(Map<Id, DSI__c> oldDSIs, Map<Id, DSI__c> newDSIs) {
        for (DSI__c newDSI : newDSIs.values()) {
            DSI__c oldDSI = oldDSIs.get(newDSI.Id);
            if(newDSI.Platform__c != null && newDSI.Platform__c.split(';').contains('MCS')) {
                if (oldDSI.Entitlement_End_Date__c != newDSI.Entitlement_End_Date__c && newDSI.Entitlement_End_Date__c < System.today()){
                    newDSI.addError('Entitlement End Date can not be past date');
                }
            }
        }
    }
    
    public static void updateMCSInfo(Map<Id, DSI__c> oldDSIs, Map<Id, DSI__c> newDSIs) {
        for (DSI__c newDSI : newDSIs.values()) {
            if(newDSI.Platform__c != null && newDSI.Platform__c.split(';').contains('MCS')) {
                if(newDSI.Entitlement_End_Date__c != oldDSIs.get(newDSI.Id).Entitlement_End_Date__c){   
                    newDSI.MCSExpirationDatetime__c = newDSI.Entitlement_End_Date__c;
                }
            }
        }
    }
    
        //Creates Platform Events to update MCI info if there are any
    public static void updateMCIAccountInfo(Map<Id, DSI__c> oldMap, Map<Id, DSI__c> newMap) {//Case 435187 - Priyank
        List<Id> dsiIdList = new List<ID>();
        List<MCI_Event__e> eventList = new List<MCI_Event__e>();
        
        for(DSI__c d : newMap.values()){ //Case 435187 - Priyank
            if(oldMap.containsKey(d.id)){
                DSI__c oldDSI = oldMap.get(d.Id);
                if(d.Account__c != oldDSI.Account__c){
                    //account changed on contact so notify MCI
                    MCI_Event__e mciDependencies = new MCI_Event__e();
                    mciDependencies.DSI__c = d.Id;
                    eventList.add(mciDependencies);
                }
                //Case 435187 - Priyank start
                if(d.Platform__c != null && d.Platform__c.split(';').contains('MCI')) {
                    if(d.Entitlement_End_Date__c != oldDSI.Entitlement_End_Date__c || d.Status__c != oldDSI.Status__c){                    
                        MCI_Event__e mciDependencies = new MCI_Event__e();
                        mciDependencies.DSI__c = d.Id;
                        mciDependencies.EndDate__c = d.Entitlement_End_Date__c;
                        eventList.add(mciDependencies);
                        
                        dsiIdList.add(d.Id);
                    }
                
                    if(d.MCIEnvironmentOwner__c != oldDSI.MCIEnvironmentOwner__c) {
                        MCI_Event__e mciDependencies = new MCI_Event__e();
                        mciDependencies.DSI__c = d.Id;
                        mciDependencies.Environment_Owner__c = d.MCIEnvironmentOwner__c;
                        eventList.add(mciDependencies);
                    }
                }
                //Case 435187 - Priyank end
            }
        }
        //Case 435187 - Priyank start
        /*if(dsiIdList.size() > 0) {
            List<SBQQ__Subscription__c> subscrList = [SELECT Id, SBCF_DSI__c, SBQQ__EndDate__c 
                                                      FROM SBQQ__Subscription__c 
                                                      WHERE SBCF_DSI__c IN :dsiIdList];
            
            if(subscrList.size() > 0) {
                Map<Id, List<SBQQ__Subscription__c>> dsiIdToSubscrMap = new Map<Id, List<SBQQ__Subscription__c>>();
                for(SBQQ__Subscription__c subscr : subscrList) {
                    if(!dsiIdToSubscrMap.containsKey(subscr.SBCF_DSI__c)) {
                        dsiIdToSubscrMap.put(subscr.SBCF_DSI__c, new List<SBQQ__Subscription__c>());
                    }
                    dsiIdToSubscrMap.get(subscr.SBCF_DSI__c).add(subscr);
                }
                List<SBQQ__Subscription__c> sbscrUpdateList = new List<SBQQ__Subscription__c>();
                for(DSI__c olddsi : oldMap.values()) {
                    if(dsiIdList.contains(olddsi.Id) && dsiIdToSubscrMap.containsKey(olddsi.Id)) {                      
                        for(SBQQ__Subscription__c subscr : dsiIdToSubscrMap.get(olddsi.Id)) {
                            if(olddsi.Entitlement_End_Date__c == subscr.SBQQ__EndDate__c) {
                                subscr.SBQQ__SubscriptionEndDate__c = newMap.get(olddsi.Id).Entitlement_End_Date__c;
                                sbscrUpdateList.add(subscr);
                            }
                        }   
                    }                    
                }
                if(sbscrUpdateList.size() > 0) {
                    update sbscrUpdateList;
                }
            }
        }*/
        //Case 435187 - Priyank end
        if(!eventList.isEmpty() && RestContext.request == null){
            // Case 453913 -> 'RestContext.request == null' prevents API callbacks to Global Controller when DSI changes were triggered by a call from the Global Controllet
            Database.SaveResult[] results = EventBus.publish(eventList);
        }
    }

    //Creates Platform Events to update MCS info if there are any
    public static void updateMCSAccountInfo(Map<Id, DSI__c> oldMap, Map<Id, DSI__c> newMap) {
        List<Id> dsiIdList = new List<ID>();
        List<MCS_Event__e> eventList = new List<MCS_Event__e>();
        
        for(DSI__c d : newMap.values()) {
            if(oldMap.containsKey(d.id)){
                DSI__c oldDSI = oldMap.get(d.Id);
                if(d.Account__c != oldDSI.Account__c){
                    //account changed on contact so notify MCS
                    MCS_Event__e mcsDependencies = new MCS_Event__e();
                    mcsDependencies.DSI__c = d.Id;
                    eventList.add(mcsDependencies);
                }

                if(d.Platform__c != null && d.Platform__c.split(';').contains('MCS')) {
                    if(d.Entitlement_End_Date__c != oldDSI.Entitlement_End_Date__c || d.Status__c != oldDSI.Status__c){                    
                        MCS_Event__e mcsDependencies = new MCS_Event__e();
                        mcsDependencies.DSI__c = d.Id;
                        mcsDependencies.EndDate__c = d.Entitlement_End_Date__c;
                        eventList.add(mcsDependencies);
                        dsiIdList.add(d.Id);
                    }
                
                    if(d.MCSEnvironmentOwner__c != oldDSI.MCSEnvironmentOwner__c) {
                        MCS_Event__e mcsDependencies = new MCS_Event__e();
                        mcsDependencies.DSI__c = d.Id;
                        mcsDependencies.Environment_Owner__c = d.MCSEnvironmentOwner__c;
                        eventList.add(mcsDependencies);
                    }
                }
            }
        }
        if(!eventList.isEmpty() && RestContext.request == null){
            Database.SaveResult[] results = EventBus.publish(eventList);
        }
    }

    public static void checkUncheckNeedAdditionalKeyShipment(Map<Id, DSI__c> oldDSIs, Map<Id, DSI__c> newDSIs){
        for (DSI__c newDSI : newDSIs.values()) {
            if(newDSI.Next_Key_Shipment_Date__c != null) {
                newDSI.KeysNeedShiping__c = true;
            } else{
                newDSI.KeysNeedShiping__c = false;
            }
        }
    }
}
