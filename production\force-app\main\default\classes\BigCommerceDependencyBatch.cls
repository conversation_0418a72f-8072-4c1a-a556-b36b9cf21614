/**
* Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 25/11/2020. 
*/

public without sharing class BigCommerceDependencyBatch implements Database.Batchable<SObject>, Database.AllowsCallouts {
    
    private Map<Id, Opportunity> oppByStagingOrderId;
    private Map<Id, Opportunity> oppById;
    private Map<String, BC_Staging_Order__c> stagingOrderByNumber;
    private Map<String, Contact> contactByEmail;
    private Map<String, SBQQ__Quote__c> quoteByName;
    private Map<String, SBQQ__Subscription__c> subByName;
    private Map<String, SBQQ__Subscription__c> subByEduCode;
    private Map<Id, Set<String>> productCodesByStagingId;
    private Map<String, Integer> productQuantityByKey;
    private Set<String> allProductCodes;
    private Set<String> allEmails;
    private Set<String> allSubscriptions;
    private Set<String> eduCodes;
    private Set<String> allQuotes;
    private String queryId;
    private Pattern bigCommercePattern;
    
    public BigCommerceDependencyBatch () {
        initialiseFields();
    }
    
    public BigCommerceDependencyBatch (String query) {
        queryId = query;
        initialiseFields();
    }
    
    public void initialiseFields () {
        oppByStagingOrderId = new Map<Id, Opportunity>();
        oppById = new Map<Id, Opportunity>();
        productQuantityByKey = new Map<String, Integer>();
        stagingOrderByNumber = new Map<String, BC_Staging_Order__c>();
        contactByEmail = new Map<String, Contact>();
        quoteByName = new Map<String,SBQQ__Quote__c>();
        subByName = new Map<String,SBQQ__Subscription__c>();
        subByEduCode = new Map<String,SBQQ__Subscription__c>();
        productCodesByStagingId = new Map<Id, Set<String>>();
        allProductCodes = new Set<String>();
        allEmails = new Set<String>();
        allSubscriptions = new Set<String>();
        eduCodes = new Set<String>();
        allQuotes = new Set<String>();
    }
    
    public Database.QueryLocator start (Database.BatchableContext param1) {
        String query = 'SELECT Id, UserEmail__c, UserFirstName__c, UserLastName__c, BillingState__c, Type__c, hasError__c, ErrorMessage__c, OrderNumber__c, OrderStatus__c, Products__c, ' +
            'StaffNotes__c, BCUnique_ID__c, CurrencyIsoCode, StoreEndpoint__c, StoreId__c, OpportunityCreated__c, ' +
            'QuoteCreated__c, QuoteLinesCreated__c, CampaignMemberCreated__c, ContactUpdated__c, DiscountAmount__c, ' +
            'Country__c, State__c, City__c, Address1__c, Address2__c, PostalCode__c,InvoiceMeOrder__c, BillingCountry__c, ' +
            'IsNewMiniBCOpp__c, SubscriptionId__c, PaymentCount__c,CouponCode__c, OrderTotal__c, CountryCode__c, ' +
            'CurrencyExchangeRate__c ' +
            'FROM BC_Staging_Order__c ' +
            'WHERE (((OpportunityCreated__c = FALSE OR QuoteCreated__c = FALSE OR QuoteLinesCreated__c = FALSE OR ContactUpdated__c = FALSE) AND IsZeroDollarDeal__c = FALSE) OR (IsZeroDollarDeal__c = TRUE AND ContactUpdated__c = FALSE)) ';
        if (String.isNotEmpty(queryId)) {
           query += ' AND Id = :queryId';
        }
        return Database.getQueryLocator(query);
    }

    public void execute (Database.BatchableContext param1, List<BC_Staging_Order__c> stagingOrders) {
        SBQQ.TriggerControl.disable();
        Map<String, Id> typeToCampaignMap = new Map<String, Id>();
        
        for(MSTR_Global_Configuation__mdt metadata : [
            SELECT Id, Type__c, Value__c
            FROM MSTR_Global_Configuation__mdt
            WHERE DeveloperName IN ('World_Pass_Campaign','MCI_Trial_Campaign_Id','CE_Education_Architect','CE_Education_Analyst')
        ]) {
            typeToCampaignMap.put(metadata.Type__c, metadata.Value__c);
        }
        
        String eduRegex = [
            SELECT Value__c
            FROM MSTR_Global_Configuation__mdt
            WHERE DeveloperName = 'BigCommerce_EDU_Regex'
            LIMIT 1
        ].Value__c;
        bigCommercePattern = Pattern.compile(eduRegex); 
        
        Map<Id, SBQQ__Quote__c> quotesByIds = new Map<Id, SBQQ__Quote__c>();
        List<SBQQ__Quote__c> quotesToInsert = new List<SBQQ__Quote__c>();
        List<SBQQ__QuoteLine__c> quoteLinesToInsert = new List<SBQQ__QuoteLine__c>();
        List<Contact> contactsToUpdate = new List<Contact>();
        Map<Id, Contact> contactMapToUpdate = new Map<Id, Contact>();
        Map<String, Contact> contactMapByEmail = new Map<String, Contact>();
        Map<String, String> stagingOrderErrorByEmail = new Map<String, String>();
        List<Opportunity> opportunitiesToUpsert = new List<Opportunity>();
        
        for (BC_Staging_Order__c stagingOrder : stagingOrders) {
            try {
                stagingOrder.hasError__c = false;
                stagingOrder.ErrorMessage__c = '';
                getOrderDetailsAPI(stagingOrder);
                getProductsAPI(stagingOrder);
            } catch (Exception ex) {
                stagingOrder.hasError__c = true;
                stagingOrder.ErrorMessage__c = 'Unexpected error : ' + ex.getMessage() + ' ' + ex.getStackTraceString();
            }
        }
        fetchOpportunities();
        fetchContacts();
        fetchSubscriptionAndQuotes();
        
        Map<String, Product2> productByProductCode = new Map<String, Product2>();
        //select non-renewable architect pass or product returned
        for (Product2 tmpProduct : [SELECT Id, ProductCode,SBQQ__SubscriptionType__c FROM Product2 WHERE ProductCode IN :allProductCodes and SBQQ__SubscriptionType__c = 'One-time']) {
            productByProductCode.put(tmpProduct.ProductCode, tmpProduct);
        }
        Map<String, Country_Map__mdt> countryNameMap = new Map<String, Country_Map__mdt>();
        for (Country_Map__mdt metaRec : [SELECT Id, MasterLabel, Region__c FROM Country_Map__mdt]) {
            countryNameMap.put(metaRec.MasterLabel, metaRec);
        }
        
        for (BC_Staging_Order__c stagingOrder : stagingOrders) {
         if (!contactByEmail.containsKey(stagingOrder.UserEmail__c)) {
            if (string.isNotBlank(stagingOrder.UserLastName__c)) {
                contact relatedContact = new contact();
                relatedContact.firstName = stagingOrder.UserFirstName__c;
                relatedContact.lastName = stagingOrder.UserLastName__c;
                relatedContact.email = stagingOrder.UserEmail__c;
                relatedContact.AccountId = TriggersHelper.resourceCentreAccountId;
                relatedContact.mailingState = stagingOrder.BillingState__c;
                relatedContact.mailingCountry = stagingOrder.BillingCountry__c;
                contactMapByEmail.put(relatedContact.email,relatedContact);
             }
             else {
                stagingOrder.hasError__c = true;
                stagingOrder.ErrorMessage__c = 'Blank user last name.';
             }
         }
        }
        
        if (!contactMapByEmail.Values().isEmpty()) {
            List<Database.SaveResult> saveResults = Database.insert(contactMapByEmail.Values(), false);
            for (Integer i = 0; i < saveResults.size(); ++i) {
                if (!saveResults.get(i).isSuccess()) { 
                  contact relatedContact = contactMapByEmail.Values().get(i);  
                  string error = 'Contact Insert failed :';
                  for (Database.Error err : saveResults.get(i).getErrors()) {
                    error += '\n' + err.getFields() + '-' + err.getMessage();
                  }
                  stagingOrderErrorByEmail.put(relatedContact.email,error);
                }
            }
        }

        for (BC_Staging_Order__c stagingOrder : stagingOrders) {
            Decimal orderTotal;
            try {
                orderTotal = Decimal.valueOf(stagingOrder.OrderTotal__c);
            } catch (Exception ex) {
                stagingOrder.hasError__c = true;
                stagingOrder.ErrorMessage__c = 'Unable to identify order total: ' + ex.getMessage();
            }

            Contact relatedContact;
            system.debug('contactByEmail' + ' ' + stagingOrder.UserEmail__c + ' ' + contactByEmail);
            if (contactByEmail.containsKey(stagingOrder.UserEmail__c)) { 
                system.debug('contactByEmail found');
                relatedContact = contactByEmail.get(stagingOrder.UserEmail__c);
            } 
            else if (contactMapByEmail.containsKey(stagingOrder.UserEmail__c) 
            && !stagingOrderErrorByEmail.containsKey(stagingOrder.UserEmail__c)) {
                system.debug('contactMapByEmail found');
                relatedContact = contactMapByEmail.get(stagingOrder.UserEmail__c);
            } else if (stagingOrderErrorByEmail.containsKey(stagingOrder.UserEmail__c)) {
                stagingOrder.hasError__c = true;
                stagingOrder.ErrorMessage__c = stagingOrderErrorByEmail.get(stagingOrder.UserEmail__c);
            }

            //process edu coupon code if any
            updateContactEduFields(stagingOrder, relatedContact, contactMapToUpdate);

            if (!stagingOrder.hasError__c && orderTotal > 0 &&
                (stagingOrder.OrderStatus__c == 10 || stagingOrder.OrderStatus__c == 11 || stagingOrder.OrderStatus__c == 7)
               ) {

                   //check for MiniBC monthly orders that do not need a new opp and mark them to remove them from the batch
                   if(!stagingOrder.IsNewMiniBCOpp__c && String.isNotBlank(stagingOrder.SubscriptionId__c)){
                       stagingOrder.OpportunityCreated__c = true;
                       stagingOrder.QuoteCreated__c = true;
                       stagingOrder.QuoteLinesCreated__c = true;
                       stagingOrder.ContactUpdated__c = true;
                       stagingOrder.OpportunityId__c = oppByStagingOrderId.containsKey(stagingOrder.Id) ? oppByStagingOrderId.get(stagingOrder.Id).Id : null;
                       continue;
                   }

                   if (relatedContact != null) {

                       Opportunity relatedOpportunity;
                       if (oppByStagingOrderId.containsKey(stagingOrder.Id)) {
                           relatedOpportunity = oppByStagingOrderId.get(stagingOrder.Id);
                       } else {
                           relatedOpportunity = new Opportunity();
                       }

                       if (!stagingOrder.OpportunityCreated__c) {
                           relatedOpportunity.Name = 'NotUsedName';
                           relatedOpportunity.BCUnique_ID__c = stagingOrder.BCUnique_ID__c;
                           relatedOpportunity.RecordTypeId = TriggersHelper.salesOppRecordTypeId;
                           if (stagingOrder.InvoiceMeOrder__c) {
                               relatedOpportunity.SBCF_Type_of_Sale__c = 'Invoice Me Sale';
                               relatedOpportunity.CPQ_UAT_Name__c = stagingOrder.Type__c + ' Invoice Me Purchase - ' + relatedContact.Name;

                           } else {
                               relatedOpportunity.SBCF_Type_of_Sale__c = 'Credit Card Sale';
                               relatedOpportunity.CPQ_UAT_Name__c = stagingOrder.Type__c + ' Credit Card Purchase - ' + relatedContact.Name;

                           }
                           relatedOpportunity.StageName = 'S2 - Define Requirements';
                           relatedOpportunity.CloseDate = Date.today();
                           relatedOpportunity.Due_Date__c = Date.today();
                           relatedOpportunity.Business_Executive__c = relatedContact.Id;
                           relatedOpportunity.AccountId = relatedContact.AccountId;
                           relatedOpportunity.District__c = relatedContact.Account.District__c;
                           relatedOpportunity.OwnerId = UserInfo.getUserId();

                           //******** Priyank start
                           /*if (stagingOrder.Type__c.equals('World Premium Pass')) {
                               relatedOpportunity.CampaignId = wordPassCampaignId;
                           } else if (stagingOrder.Type__c.equals('Hyper.Now')) {
                               relatedOpportunity.CampaignId = hyperNowCampaignId;
                           }*/
                           if(String.isNotBlank(stagingOrder.Type__c) && typeToCampaignMap.containsKey(stagingOrder.Type__c)) {
                               relatedOpportunity.CampaignId = typeToCampaignMap.get(stagingOrder.Type__c);
                           }
                           //******** Priyank end

                           //figure out if this is a renewal from MiniBC
                           if(stagingOrder.PaymentCount__c != null && ((Integer.valueOf(stagingOrder.PaymentCount__c)-1)/12 > 0)){
                               relatedOpportunity.recordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Renewal Opportunity (New)').getRecordTypeId();
                           }

                           //TODO - be accurate about country + currency selection
                           Country_Map__mdt countryMapRecord = countryNameMap.get(stagingOrder.BillingCountry__c);
                           if (countryMapRecord != null && countryMapRecord.Region__c != null &&
                               (stagingOrder.BillingCountry__c + ' - ' + stagingOrder.CurrencyIsoCode)==countryMapRecord.Region__c) {
                                   relatedOpportunity.RegionPricebook__c = countryMapRecord.Region__c;
                           } else {
                               relatedOpportunity.RegionPricebook__c = 'United States - USD';
                           }
                       }

                       //Update Contact BC Purchases
                       if (stagingOrder.Type__c.equals('World Premium Pass')) {
                           if (String.isNotBlank(stagingOrder.Country__c) && String.isNotBlank(stagingOrder.City__c) &&
                               String.isNotBlank(stagingOrder.Address1__c) && String.isNotBlank(stagingOrder.PostalCode__c)
                              ) {
                                  relatedContact.OtherCity = stagingOrder.Country__c;
                                  relatedContact.OtherState = stagingOrder.State__c;
                                  relatedContact.OtherCity = stagingOrder.City__c;
                                  relatedContact.OtherStreet = stagingOrder.Address1__c + (String.isNotBlank(stagingOrder.Address2__c) ? ' ' + stagingOrder.Address2__c : '');
                                  relatedContact.OtherPostalCode = stagingOrder.PostalCode__c;
                              }

                           relatedContact.CS_Pass_Type__c = 'Analyst';
                           //TODO: think about edge case with 2 different addresses for same contact
                           contactMapToUpdate.put(relatedContact.Id, relatedContact);
                       }else if (stagingOrder.Type__c.equals('Analyst Pass') || stagingOrder.Type__c.equals('Architect Pass')){
                           relatedContact.CS_Pass_Type__c = stagingOrder.Type__c.equals('Analyst Pass') ? 'Analyst' : 'Architect';
                           relatedContact.CS_PEP_Start_Date__c = DateTime.now();
                           relatedContact.CS_PEP_End_Date__c = DateTime.now().addYears(1).addDays(-1);
                           relatedContact.CS_Quote__c = 'Online';
                           relatedContact.CS_Active__c = true;
                           contactMapToUpdate.put(relatedContact.Id, relatedContact);
                       }else{
                           stagingOrder.ContactUpdated__c = true;
                       }

                       oppByStagingOrderId.put(stagingOrder.Id, relatedOpportunity);
                   }
               }
        }
        system.debug('oppByStagingOrderId ' + oppByStagingOrderId);
        List<Database.UpsertResult> upsertResults = Database.upsert(oppByStagingOrderId.values(), false);
        fetchOpportunities();
        for (Integer i = 0; i < upsertResults.size(); ++i) {
            Opportunity relatedOpportunity = oppByStagingOrderId.values().get(i);
            system.debug('relatedOpportunity ' + upsertResults.get(i).isSuccess() + ' ' +  relatedOpportunity);
            String bcUniqueId = relatedOpportunity.BCUnique_ID__c;
            if (upsertResults.get(i).isSuccess()) {
                SBQQ__Quote__c quote = new SBQQ__Quote__c();
                quote.SBQQ__Account__c = relatedOpportunity.AccountId;
                quote.SBQQ__Opportunity2__c = relatedOpportunity.Id;
                quote.SBQQ__StartDate__c = relatedOpportunity.CloseDate;
                quote.SBQQ__Primary__c = false;
                quote.Is_BC_Quote__c = true;
                quote.SBCF_Bill_to_Contact__c = relatedOpportunity.Business_Executive__c;
                quote.SBCF_Ship_to_Contact__c = relatedOpportunity.Business_Executive__c;
                quote.SBQQ__SubscriptionTerm__c = 12;
                quote.RecordTypeId = TriggersHelper.draftQuoteRecordTypeId;
                quote.AccountOwner__c = relatedOpportunity.OwnerId;
                quote.OwnerId = relatedOpportunity.OwnerId;
                quote.CurrencyIsoCode = relatedOpportunity.CurrencyIsoCode;
                quote.SBQQ__PriceBook__c = relatedOpportunity.Pricebook2Id;
                quote.SBQQ__PricebookId__c = relatedOpportunity.Pricebook2Id;
                quote.SBQQ__ContractingMethod__c = 'Single Contract'; //HDR 745172 -  Nogueira Ordering of autoexecuted Quotes
                quotesToInsert.add(quote);
                stagingOrderByNumber.get(bcUniqueId).OpportunityCreated__c = true;
                stagingOrderByNumber.get(bcUniqueId).OpportunityId__c = relatedOpportunity.Id;
            } else {
                stagingOrderByNumber.get(bcUniqueId).OpportunityCreated__c = false;
                stagingOrderByNumber.get(bcUniqueId).hasError__c = true;
                stagingOrderByNumber.get(bcUniqueId).ErrorMessage__c = 'Opportunity Upsert failed :';
                for (Database.Error err : upsertResults.get(i).getErrors()) {
                    stagingOrderByNumber.get(bcUniqueId).ErrorMessage__c += '\n' + err.getFields() + '-' + err.getMessage();
                }
            }
        }
        
        if (!quotesToInsert.isEmpty()) {
            List<Database.SaveResult> saveResults = Database.insert(quotesToInsert, false);
            for (Integer i = 0; i < saveResults.size(); ++i) {
                SBQQ__Quote__c relatedQuote = quotesToInsert.get(i);
                String bcUniqueId = oppById.get(relatedQuote.SBQQ__Opportunity2__c).BCUnique_ID__c;
                if (saveResults.get(i).isSuccess()) {
                    quotesByIds.put(relatedQuote.Id, relatedQuote);
                    for (String productCode : productCodesByStagingId.get(stagingOrderByNumber.get(bcUniqueId).Id)) {
                        SBQQ__QuoteLine__c quoteLine = new SBQQ__QuoteLine__c();
                        quoteLine.SBQQ__Qt__c = productByProductCode.get(productCode).Id;
                        quoteLine.SBQQ__Quauote__c = relatedQuote.Id;
                        quoteLine.SBQQ__Producntity__c = productQuantityByKey.get(bcUniqueId + ';' + productCode);
                        quoteLine.SBCF_DSI__c = oppById.get(relatedQuote.SBQQ__Opportunity2__c).DSI__c;
                        quoteLine.SBCF_Account__c = relatedQuote.SBQQ__Account__c;
                        quoteLine.SBQQ__SubscriptionTerm__c = 12;
                        //HDR 745172 Nogueira, Added default Dates from Quote
                        quoteLine.SBQQ__StartDate__c = relatedQuote.SBQQ__StartDate__c;
                        quoteLine.SBQQ__EndDate__c = Date.isLeapYear(quoteLine.SBQQ__StartDate__c.addMonths((Integer)quoteLine.SBQQ__SubscriptionTerm__c).year()) ? quoteLine.SBQQ__StartDate__c.addMonths((Integer)quoteLine.SBQQ__SubscriptionTerm__c).addDays(-1) : quoteLine.SBQQ__StartDate__c.addMonths((Integer)quoteLine.SBQQ__SubscriptionTerm__c);
                        if(stagingOrderByNumber.get(bcUniqueId).CurrencyIsoCode == relatedQuote.CurrencyIsoCode){
                            //if matching currency iso code, assume that it is okay to multiply by exchange rate to account for different values
                            quoteLine.SBQQ__AdditionalDiscountAmount__c = stagingOrderByNumber.get(bcUniqueId).DiscountAmount__c * stagingOrderByNumber.get(bcUniqueId).CurrencyExchangeRate__c;
                        }else{
                            //assume default USD/EUR, so use BigCommerce transaction amount rather than currency conversion
                            quoteLine.SBQQ__AdditionalDiscountAmount__c = stagingOrderByNumber.get(bcUniqueId).DiscountAmount__c;
                        }

                        quoteLinesToInsert.add(quoteLine);
                    }
                    stagingOrderByNumber.get(bcUniqueId).QuoteCreated__c = true;
                } else {
                    stagingOrderByNumber.get(bcUniqueId).QuoteCreated__c = false;
                    stagingOrderByNumber.get(bcUniqueId).hasError__c = true;
                    stagingOrderByNumber.get(bcUniqueId).ErrorMessage__c = 'Quote Insert failed :';
                    for (Database.Error err : saveResults.get(i).getErrors()) {
                        stagingOrderByNumber.get(bcUniqueId).ErrorMessage__c += '\n' + err.getFields() + '-' + err.getMessage();
                    }
                }
            }
        }
        
        if (!quoteLinesToInsert.isEmpty()) {
            List<Database.SaveResult> saveResults = Database.insert(quoteLinesToInsert, false);
            for (Integer i = 0; i < saveResults.size(); ++i) {
                String bcUniqueId = oppById.get(quotesByIds.get(quoteLinesToInsert.get(i).SBQQ__Quote__c).SBQQ__Opportunity2__c).BCUnique_ID__c;
                if (!saveResults.get(i).isSuccess()) {
                    stagingOrderByNumber.get(bcUniqueId).QuoteLinesCreated__c = false;
                    stagingOrderByNumber.get(bcUniqueId).hasError__c = true;
                    stagingOrderByNumber.get(bcUniqueId).ErrorMessage__c = 'Quote Line Insert failed :';
                    for (Database.Error err : saveResults.get(i).getErrors()) {
                        stagingOrderByNumber.get(bcUniqueId).ErrorMessage__c += '\n' + err.getFields() + '-' + err.getMessage();
                    }
                } else {
                    stagingOrderByNumber.get(bcUniqueId).QuoteLinesCreated__c = true;
                }
            }
        }
        
        contactsToUpdate = contactMapToUpdate.values();
        if (!contactsToUpdate.isEmpty()) {
            List<Database.SaveResult> saveResults = Database.update(contactsToUpdate, false);
            for (Integer i = 0; i < saveResults.size(); ++i) {
                BC_Staging_Order__c relatedOrder;
                for (Integer j = 0; j < stagingOrders.size() && relatedOrder == null; ++j) {
                    if (contactsToUpdate.get(i).Email == stagingOrders.get(j).UserEmail__c) {
                        relatedOrder = stagingOrders.get(j);
                    }
                }
                
                if (saveResults.get(i).isSuccess()) {
                    relatedOrder.ContactUpdated__c = true;
                } else {
                    relatedOrder.ContactUpdated__c = false;
                    relatedOrder.hasError__c = true;
                    relatedOrder.ErrorMessage__c = 'Contact update failed : ';
                    for (Database.Error err : upsertResults.get(i).getErrors()) {
                        relatedOrder.ErrorMessage__c += '\n' + err.getFields() + '-' + err.getMessage();
                    }
                }
            }
        }
        
        update stagingOrders;
        SBQQ.TriggerControl.enable();
        List<SBQQ__Quote__c> quoteToUpdate = new List<SBQQ__Quote__c>();
        for (SBQQ__Quote__c quote : quotesToInsert) {
            quoteToUpdate.add(new SBQQ__Quote__c(
                Id = quote.Id,
                RC__c = true
            ));
        }
        update quoteToUpdate;
    }
    
    public void finish (Database.BatchableContext param1) {
    }
    
    private void getOrderDetailsAPI (BC_Staging_Order__c stagingOrder) {
        if (String.isBlank(stagingOrder.CurrencyIsoCode) || String.isBlank(stagingOrder.UserEmail__c) || String.isBlank(stagingOrder.BCUnique_ID__c)) {
            BigCommerceRestController.getOrderDetails(stagingOrder);
        }
        
        stagingOrderByNumber.put(stagingOrder.BCUnique_ID__c, stagingOrder);
        system.debug('stagingOrder.BCUnique_ID__c'+stagingOrder.BCUnique_ID__c);
        if (String.isBlank(stagingOrder.UserEmail__c)) {
            stagingOrder.hasError__c = true;
            stagingOrder.ErrorMessage__c = 'Blank user email.';
        } else {
            allEmails.add(stagingOrder.UserEmail__c);
        }
        
        //REGEX logic to find Quote or Subscription
        if(String.isNotBlank(stagingOrder.CouponCode__c)){
            Matcher regexMatch = bigCommercePattern.matcher(stagingOrder.CouponCode__c);
            if(regexMatch.find()){
                //found regex
                String matchString = regexMatch.group(0).replaceAll(';','');
                system.debug('matchString ' + matchString);
                if(matchString.startsWithIgnoreCase('s')){
                    allSubscriptions.add(matchString.left(1).toUpperCase() + 'UB-' + matchString.right(matchString.length()-1));
                    eduCodes.add(stagingOrder.CouponCode__c.replaceAll(';',''));
                }else{
                    allQuotes.add(matchString.left(1).toUpperCase() + '-' + matchString.right(matchString.length()-1));
                }
            }
            
        }    
    }
    
    private void getProductsAPI (BC_Staging_Order__c stagingOrder) {
        if (String.isBlank(stagingOrder.Products__c) || String.isBlank(stagingOrder.Type__c)) {
            BigCommerceRestController.getProducts(stagingOrder);
        }
        
        BigCommerceRestController.BigCommerceOrder bigCommerceOrder = (BigCommerceRestController.BigCommerceOrder) JSON.deserialize(stagingOrder.Products__c, BigCommerceRestController.BigCommerceOrder.class);
        for (BigCommerceRestController.BigCommerceProduct bigCommerceProduct : bigCommerceOrder.orderProducts) {
            String productCode = String.valueOf(Integer.valueOf(bigCommerceProduct.upc));
            if (productCodesByStagingId.containsKey(stagingOrder.Id)) {
                productCodesByStagingId.get(stagingOrder.Id).add(productCode);
            } else {
                productCodesByStagingId.put(stagingOrder.Id, new Set<String> {
                    productCode
                        });
            }
            allProductCodes.add(productCode);
            productQuantityByKey.put(stagingOrder.BCUnique_ID__c + ';' + productCode, bigCommerceProduct.quantity);
        }
    }
    
    private void fetchSubscriptionAndQuotes (){
        if(!allSubscriptions.isEmpty()){
            for(SBQQ__Subscription__c sub : [
                SELECT Id, Name, SBQQ__StartDate__c, SBQQ__EndDate__c, SBQQ__QuoteLine__r.SBQQ__Quote__r.Name, SBQQ__QuoteLine__c, 
                    SBQQ__QuoteLine__r.SBQQ__Quote__c, SingleCouponCode__c, Edu_CouponCode__c 
                FROM SBQQ__Subscription__c
                WHERE Name in: allSubscriptions
                OR SingleCouponCode__c IN: eduCodes
                ORDER BY SBQQ__StartDate__c
            ]) {
                if(sub.SingleCouponCode__c == null){
                    subByName.put(sub.Name, sub);
                }
                else{
                    subByEduCode.put(sub.Edu_CouponCode__c, sub);
                }
            }
        }
        
        if(!allQuotes.isEmpty()){
            for(SBQQ__Quote__c quote : [
                SELECT Id, Name, CTR__c, SBQQ__Opportunity2__r.Ship_Date__c, SBQQ__Opportunity2__r.CloseDate
                FROM SBQQ__Quote__c
                WHERE Name in: allQuotes
            ]) {
                quoteByName.put(quote.Name, quote);
            }
        }
    }
    
    private void fetchOpportunities () {
        if (!stagingOrderByNumber.isEmpty()) {
            for (Opportunity opp : [
                SELECT Id, SBQQ__PrimaryQuote__c, Pricebook2Id, CurrencyIsoCode, AccountId, CloseDate, Business_Executive__c, OwnerId, BCUnique_ID__c, Name, CPQ_UAT_Name__c, RecordTypeId, SBCF_Type_of_Sale__c, Due_Date__c, District__c, DSI__c
                FROM Opportunity
                WHERE BCUnique_ID__c IN :stagingOrderByNumber.keySet()
            ]) {
                oppByStagingOrderId.put(stagingOrderByNumber.get(opp.BCUnique_ID__c).Id, opp);
                oppById.put(opp.Id, opp);
            }
        }
    }
    
    private void fetchContacts () {
        if (!stagingOrderByNumber.isEmpty()) {
            for (Contact relatedContact : [
                SELECT Id, Email, Name, AccountId, Account.District__c, MailingCountry, OtherCity, OtherState, OtherStreet, OtherPostalCode, OtherCountry, CS_Active__c, CS_PEP_Start_Date__c, CS_PEP_End_Date__c,
                CS_Pass_Type__c, CS_Quote__c
                FROM Contact
                WHERE Email IN :allEmails
            ]) {
                contactByEmail.put(relatedContact.Email, relatedContact);
            }
        }
    }
    
    private void updateContactEduFields(BC_Staging_Order__c stagingOrder, Contact relatedContact, Map<Id,Contact> contactMapToUpdate){
        SBQQ__Subscription__c sub;
        SBQQ__Subscription__c relatedSub;
        if(String.isNotBlank(stagingOrder.CouponCode__c) && relatedContact != null){
            Matcher regexMatch = bigCommercePattern.matcher(stagingOrder.CouponCode__c);
            if(regexMatch.find()){
                String matchString = regexMatch.group(0).replaceAll(';','');
                
                //populate contact edu fields with subscription or case
                if(matchString.startsWithIgnoreCase('s') && subByName.containsKey(matchString.left(1).toUpperCase() + 'UB-' + matchString.right(matchString.length()-1))){
                    sub = subByName.get(matchString.left(1).toUpperCase() + 'UB-' + matchString.right(matchString.length()-1));
                    relatedSub = subByEduCode.get(sub.Edu_CouponCode__c) != null? subByEduCode.get(sub.Edu_CouponCode__c): sub;
                    relatedContact.CS_Active__c = Date.today() < sub.SBQQ__EndDate__c;
                    relatedContact.CS_PEP_Start_Date__c = DateTime.newInstanceGMT(sub.SBQQ__StartDate__c.year(), sub.SBQQ__StartDate__c.month(), sub.SBQQ__StartDate__c.day());
                    relatedContact.CS_PEP_End_Date__c = DateTime.newInstanceGMT(relatedSub.SBQQ__EndDate__c.year(), relatedSub.SBQQ__EndDate__c.month(), relatedSub.SBQQ__EndDate__c.day());
                    if (String.isNotBlank(sub.SBQQ__QuoteLine__r.SBQQ__Quote__r.Name)) {
                        relatedContact.CS_Quote__c = sub.SBQQ__QuoteLine__r.SBQQ__Quote__r.Name;
                    }
                    if(stagingOrder.Type__c=='Architect Pass'){
                        relatedContact.CS_Pass_Type__c = 'Architect';
                    }else if (stagingOrder.Type__c=='Analyst Pass'){
                        relatedContact.CS_Pass_Type__c = 'Analyst';
                    }
                    
                    stagingOrder.ContactUpdated__c = true;
                    contactMapToUpdate.put(relatedContact.Id, relatedContact);
               
                }else if (matchString.startsWithIgnoreCase('q') && quoteByName.containsKey(matchString.left(1).toUpperCase() + '-' + matchString.right(matchString.length()-1))){
                    SBQQ__Quote__c quote = quoteByName.get(matchString.left(1).toUpperCase() + '-' + matchString.right(matchString.length()-1));
                    Integer defaultDateLength = quote.CTR__c != null ? Integer.valueOf(quote.CTR__c) : 12;
                    Date oppCloseDate = quote.SBQQ__Opportunity2__r.Ship_Date__c != null ? quote.SBQQ__Opportunity2__r.Ship_Date__c : quote.SBQQ__Opportunity2__r.CloseDate;
                    
                    relatedContact.CS_Active__c = Date.today() < oppCloseDate.addMonths(defaultDateLength).addDays(-1);
                    relatedContact.CS_PEP_Start_Date__c = oppCloseDate;
                    relatedContact.CS_PEP_End_Date__c = oppCloseDate.addMonths(defaultDateLength).addDays(-1);
                    relatedContact.CS_Quote__c = quote.Name.right(quote.Name.length()-2);
                    if(stagingOrder.Type__c=='Architect Pass'){
                        relatedContact.CS_Pass_Type__c = 'Architect';
                    }else if (stagingOrder.Type__c=='Analyst Pass'){
                        relatedContact.CS_Pass_Type__c = 'Analyst';
                    }
                    
                    stagingOrder.ContactUpdated__c = true;
                    contactMapToUpdate.put(relatedContact.Id, relatedContact);
               }
                
            }
            
        }
        
    }
}