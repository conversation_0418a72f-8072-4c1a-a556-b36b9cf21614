[{"configId": "RpzM2UnAKfj033JK8x", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "id", "sf_fieldLabel": "Record ID", "sf_childRelationshipName": "", "cp_fieldName": "integrationId", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "VudzYo6APXne0if3pX", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "clientDetails", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "WUmQajncWznrwgycCr", "isConstant": true, "cp_fieldName": "clientTypeId", "cp_fieldLabel": "Standard : Partner Type", "sf_fieldName": "Customer", "sf_fieldLabel": "Customer"}, {"configId": "IZuSacUbVwudXkLdwZ", "isConstant": false, "cp_fieldName": "customerName", "cp_fieldLabel": "Standard : Partner Name", "sf_fieldName": "SBQQ__Account__r.name", "sf_fieldLabel": "SBQQ__Account__r.Account Name"}, {"configId": "nxQbihgaZ4nMV3MlYY", "isConstant": false, "cp_fieldName": "addressLine1", "cp_fieldLabel": "Standard : Address", "sf_fieldName": "SBQQ__Account__r.billingstreet", "sf_fieldLabel": "SBQQ__Account__r.<PERSON>"}, {"configId": "ihb2vwZYz1aYX7KWSb", "isConstant": false, "cp_fieldName": "countryId", "cp_fieldLabel": "Standard : Country", "sf_fieldName": "SBQQ__Account__r.billingcountry", "sf_fieldLabel": "SBQQ__Account__r.<PERSON>"}, {"configId": "RYfombzJhFsJqzPatl", "isConstant": false, "cp_fieldName": "city", "cp_fieldLabel": "Standard : City", "sf_fieldName": "SBQQ__Account__r.billingcity", "sf_fieldLabel": "SBQQ__Account__r.Billing City"}, {"configId": "sOGoe43MO6aewhDSjA", "isConstant": false, "cp_fieldName": "state", "cp_fieldLabel": "Standard : State/Province", "sf_fieldName": "SBQQ__Account__r.billingstate", "sf_fieldLabel": "SBQQ__Account__r.Billing State/Province"}, {"configId": "vihVezj6gLqdNecABR", "isConstant": false, "cp_fieldName": "pincode", "cp_fieldLabel": "Standard : Pincode/Postal Code", "sf_fieldName": "SBQQ__Account__r.billingpostalcode", "sf_fieldLabel": "SBQQ__Account__r.Billing Zip/Postal Code"}, {"configId": "woalEiWQYdilNUhzcB", "isConstant": false, "cp_fieldName": "contactDetailsId", "cp_fieldLabel": "Standard : Contact", "sf_fieldName": "SBQQ__Account__r.phone", "sf_fieldLabel": "SBQQ__Account__r.Account Phone"}], "configId": "fjp48PE0mprjVBtecQ"}], "showFilter": false}, {"configId": "y1S7bkecLWAMJdpcEo", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "assignees", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "b2D0CqCgYazgwY5kpF", "isConstant": true, "cp_fieldName": "assigntoUsername", "cp_fieldLabel": "Standard : Assignee", "sf_fieldName": "ContractPod", "sf_fieldLabel": "ContractPod"}, {"configId": "iWG4OTAKSA1AyRMv8V", "isConstant": true, "cp_fieldName": "departmentId", "cp_fieldLabel": "Standard : Department", "sf_fieldName": "CPAi Support", "sf_fieldLabel": "CPAi Support"}], "configId": "srDU2axnlDffWSOi7Y"}], "showFilter": false}, {"configId": "9oB7obQXdBpx1f0n3M", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "SBQQ__Opportunity2__r.SBCF_Legal_Entity__r.name", "sf_fieldLabel": "SBQQ__Opportunity2__r.SBCF_Legal_Entity__r.Account Name", "sf_childRelationshipName": "", "cp_fieldName": "contractingPartyId", "cp_fieldType": "Standard", "isCompositeMap": false, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "b2D0CqCgYazgwY5kpF", "isConstant": true, "cp_fieldName": "assigntoUsername", "cp_fieldLabel": "Standard : Assignee", "sf_fieldName": "ContractPod", "sf_fieldLabel": "ContractPod"}, {"configId": "iWG4OTAKSA1AyRMv8V", "isConstant": true, "cp_fieldName": "departmentId", "cp_fieldLabel": "Standard : Department", "sf_fieldName": "CPAi Support", "sf_fieldLabel": "CPAi Support"}], "configId": "srDU2axnlDffWSOi7Y"}], "showFilter": false}, {"configId": "mteLUfUrrR5eYJJrHQ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "footer_so__c", "sf_fieldLabel": "Footer SO", "sf_childRelationshipName": "", "cp_fieldName": "OrderForm", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "uytyoFhlXV5bCLEGyQ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_company__c", "sf_fieldLabel": "Billing Company", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Customer", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "HLCVRihTyD5AuLloLW", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_name__c", "sf_fieldLabel": "<PERSON> Contact Name", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Contact", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "lXBDtQtWeX8k8sMWtW", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_email__c", "sf_fieldLabel": "Billing Email", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Email", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "TDmIcn14Ebp8KgOhEk", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_bill_to_contact_address__c", "sf_fieldLabel": "Billing Address", "sf_childRelationshipName": "", "cp_fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "eWiNWVMyNSjk1NHJPz", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__billingcountry__c", "sf_fieldLabel": "<PERSON>", "sf_childRelationshipName": "", "cp_fieldName": "Bill2Country", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "em2EE4rpPIgJMiz6Tp", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_name__c", "sf_fieldLabel": "Ship To Contact Name", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Contact", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "IkbiYjXynzdUcpUaHr", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_email__c", "sf_fieldLabel": "Shipping Email", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Email", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "W5EVPzeNSbAmAfPmPa", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_address__c", "sf_fieldLabel": "Shipping Address", "sf_childRelationshipName": "", "cp_fieldName": "ShiptoAddress", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "m2ZskM1DmrGBX5nOCs", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbqq__shippingcountry__c", "sf_fieldLabel": "Ship To Country", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Country", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "JcddaJAxxhiSgGhtuX", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_ship_to_contact_company__c", "sf_fieldLabel": "Shipping Company", "sf_childRelationshipName": "", "cp_fieldName": "Ship2Customer", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "dXKnzzUrdvS1iuzhAw", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "output_expiration_date_full_month__c", "sf_fieldLabel": "Output Expiration Date - Full Month", "sf_childRelationshipName": "", "cp_fieldName": "QuoteExpDateNew", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "kONkTHXk7S3PgvtjxZ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_document_language__c", "sf_fieldLabel": "Territory of Sale", "sf_childRelationshipName": "", "cp_fieldName": "TerritoryOfSale", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "t6A9hfBMK7TTabX7gX", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "ma_ref__c", "sf_fieldLabel": "Reference to Negotiated Master Agreement", "sf_childRelationshipName": "", "cp_fieldName": "Ref2NegotiatedMANewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "z9hwOAhgzuv7Z4XfbA", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "send_to_rev_rec__c", "sf_fieldLabel": "Send to Rev Rec", "sf_childRelationshipName": "", "cp_fieldName": "RevRec", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "zwm6HXmRpBrZ0mk5et", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "mstr_signs_first__c", "sf_fieldLabel": "MSTR Signs First", "sf_childRelationshipName": "", "cp_fieldName": "MSTRSignsFirst", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "L6vtecgWFuaLXh0G4Z", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "internal_comments__c", "sf_fieldLabel": "Internal Comments", "sf_childRelationshipName": "", "cp_fieldName": "InternalComments", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "NvGux3MLuQRWNeRUh1", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "customer_signer_email__c", "sf_fieldLabel": "Customer Signer <PERSON><PERSON>", "sf_childRelationshipName": "", "cp_fieldName": "CustomerSignerEmail", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "XNdKnixMi2RGCNaiTR", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "legal_clarification_notes__c", "sf_fieldLabel": "Legal Clarification Notes", "sf_childRelationshipName": "", "cp_fieldName": "LegalClarificationNotes", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "ocjDKd8ne46TRkILHJ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "opportunity_close_date__c", "sf_fieldLabel": "Opportunity Close Date", "sf_childRelationshipName": "", "cp_fieldName": "OpportunityCloseDate", "cp_fieldType": "Custom", "isCompositeMap": false}, {"configId": "t7V3p2pAcLzeFUwQv9", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "opportunity_close_date_addition_year__c", "sf_fieldLabel": "Opportunity Close Date Addition Year", "sf_childRelationshipName": "", "cp_fieldName": "OpportunityCloseDateAdditionYear", "cp_fieldType": "Custom", "isCompositeMap": false}]