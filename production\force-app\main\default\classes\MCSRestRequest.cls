/*
* MCSRestRequest
* 
* DESCRIPTION : Called by Internally by Create<PERSON>SDSI.apxc to perform DML Actions before KeyGen HTTP Callout
* Creates a DSI, Subscriptions 
* 
* Test Class:CreateMCSDSITest
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Mahipal Jat		             13/05/2025                       		- Original Version - 557760 - SF/CPQ - MCS - Auto Opp Creation
* --------------------------------------------------------------------------------------------------------------------------------------------------- 
*/

@RestResource (UrlMapping='/sfdcInternalMCS/*')

global class MCSRestRequest {
    global class WSException extends Exception {
    }

    static final String CLASS_NAME = 'MCSRestRequest';

    @HttpPost
    global static CreateMCSDSI.ResponseWrapper createDSI () {
        final String METHOD_NAME = 'createDSI';
        DSI__c dsi;
        CreateMCSDSI.ResponseWrapper Resp;
        String requestString = null;
        String sErrorMsg= '';

        IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Internal createDSI endpoint entered.');

        try {

            RestRequest req = RestContext.request;
            Blob body = req.requestBody;
            requestString = body.toString();
            IntegrationErrorLogger.logDebug(CLASS_NAME, METHOD_NAME, 'Raw internal request body.', requestString, null, null);

            CreateMCSDSI.RequestWrapper item = (CreateMCSDSI.RequestWrapper) JSON.deserialize(requestString, CreateMCSDSI.RequestWrapper.class);
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Internal request deserialized for environment: ' + item.environmentId);

            dsi = generateDSI(item);
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Internal DSI generation successful. DSI ID: ' + dsi.Id);
        } catch (Exception ex) {
            sErrorMsg =  'DSI was not generated '+ex.getMessage();
            RestRequest rstRequest = RestContext.request;
            String requestBodyString = (rstRequest != null && rstRequest.requestBody != null) ? rstRequest.requestBody.toString() : null;
            String requestHeadersString = (rstRequest != null && rstRequest.headers != null) ? JSON.serialize(rstRequest.headers) : null;
            String requestUri = rstRequest != null ? rstRequest.requestURI : null;
            String httpMethod = rstRequest != null ? rstRequest.httpMethod : null;

            // Log the error using IntegrationErrorLogger
            IntegrationErrorLogger.logError(
                'SFDCInternalMCS',        // integrationName: Name of your service/integration
                'createDSI_Endpoint',     // integrationPoint: Specific operation
                'MCSRestRequest',         // apexClassName
                'createDSI',              // apexMethodName
                null,                     // severity (let logger use default 'Error')
                ex.getTypeName(),         // errorCode (e.g., 'System.NullPointerException')
                ex.getMessage(),          // errorMessage
                ex.getStackTraceString(), // stackTrace
                requestUri,               // requestEndpoint (URI of the incoming REST request)
                httpMethod,               // requestMethod (e.g., 'POST')
                requestHeadersString,     // requestHeaders (Incoming request headers)
                requestBodyString,        // requestBody (Incoming request payload)
                null,                     // responseStatusCode (Not applicable here)
                null,                     // responseHeaders (Not applicable here)
                null,                     // responseBody (Not applicable here)
                null,                     // relatedRecordId (If you have a relevant ID at this point)
                null                      // relatedRecordObject (SObject type of relatedRecordId)
            );

            IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_ERROR);
            throw new WSException('Something went Wrong: ' + ex.getMessage());
        }
        
        Resp = new CreateMCSDSI.ResponseWrapper (dsi, sErrorMsg);
        IntegrationErrorLogger.logDebug(CLASS_NAME, METHOD_NAME, 'Response body.', JSON.serialize(Resp), null, null);
        IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_ERROR);
        return Resp;
    }

    //inserting DSI
    private static DSI__c generateDSI (CreateMCSDSI.RequestWrapper request) {
        final String METHOD_NAME = 'generateDSI';

        Boolean hasError = false;
        Savepoint sp = Database.setSavepoint();
        DSI__c d;
        MSTR_Placeholder_Values__c CS;
        List<Contact> contactList = new List<Contact>();

        if (request != null && request.user != null) {
            contactList = [SELECT Id, AccountId FROM Contact WHERE Email = :request.user.email];
        }

        String accountId = contactList.isEmpty() ? TriggersHelper.unmappedAccountId : contactList[0].AccountId;
        MSTR_Global_Configuation__mdt productCode = [SELECT Value__c, Type__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = :request.productPackage LIMIT 1];
        List<String> productCodeList = productCode.Value__c.split(';');

        try {
            d = new DSI__c(Name = 'tempName', Description_Name__c = request.environmentId, Description__c = 'Environment via MCS', Version__c = request.mstrVersion, Account__c = accountId,
                RecordTypeId = Schema.SObjectType.DSI__c.getRecordTypeInfosByName().get('Archived').getRecordTypeId(), Platform__c = 'MCS',
                MCSEnvironmentName__c = request.environmentId, Status__c = 'Archived', MCSEnvironmentUrl__c = request.environmentUrl);
            if (request.subscription != null) {
                d.MCSActivationDateTime__c = request.subscription.activationDate;
                d.MCSExpirationDatetime__c = request.subscription.expirationDate;
            } else {
                d.MCSActivationDateTime__c = System.now();
                d.MCSExpirationDatetime__c = System.now().addDays(30);
            }
            if(contactList != null && contactList.size() > 0 && contactList[0].Id != null) {
                d.MCSEnvironmentOwner__c = contactList[0].Id;
            }
            d.Status__c = 'Trial';
            d.Support_Level__c = 'Cloud mcs';
            d.MCSBatchStatus__c = 'Pending';
            d.Next_Key_Shipment_Date__c = null;
            
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'DSI SObject prepared for insertion for env: ' + request.environmentId);
            insert d;
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'DSI inserted successfully with ID: ' + d.Id);

            List<SBQQ__Subscription__c> lSubs = new List<SBQQ__Subscription__c>();
            Map<String, Id> mSKUs = new Map<String, Id>();
            
            if (!productCodeList.isEmpty()) {
                for (Product2 p : [SELECT Id, ProductCode FROM Product2 WHERE ProductCode IN :productCodeList AND IsActive = TRUE]) {
                    SBQQ__Subscription__c s = new SBQQ__Subscription__c(
                        SBCF_DSI__c = d.Id, 
                        SBQQ__Quantity__c =  p.productCode == '89412' ? 50 : 1,
                        SBQQ__Product__c = p.Id, 
                        Key_Group_multi__c = 'Prod Key 1', 
                        SBQQ__Account__c = d.Account__c, 
                        SBQQ__ChargeType__c = 'Trial'
                    );
                    if(request.subscription != null) {
                        s.SBQQ__SubscriptionStartDate__c = Date.valueOf(request.subscription.activationDate);
                        s.SBQQ__SubscriptionEndDate__c = Date.valueOf(request.subscription.expirationDate);
                    } else {
                        s.SBQQ__SubscriptionStartDate__c = System.today();
                        s.SBQQ__SubscriptionEndDate__c = System.today().addDays(30);
                    }

                    lSubs.add(s);
                }
            }
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Preparing to insert ' + lSubs.size() + ' subscription records.');
            insert lSubs;
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Subscriptions inserted successfully.');

            List<DSI__c> lstDSI = [SELECT Id, DSI_ID__c, DSIID__c, Account__c, Account__r.Compass_Id_F__c, Version__c, Operating_System__c, Elastic_Cloud_Console__c, MCSActivationDateTime__c, MCSExpirationDatetime__c FROM DSI__c WHERE Id = :d.Id];
            d = lstDSI[0];
        } catch (Exception ex) {
            Database.rollback(sp);
            Id dsiIdOnError = (d != null && d.Id != null) ? d.Id : null;
            IntegrationErrorLogger.logError(
                ex, 'SFDCInternalMCS_DML', 'DSI_Subscription_DML_Error', CLASS_NAME, METHOD_NAME, dsiIdOnError
            );

            IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_ERROR);

            throw new WSException('Internal Error Occurred: ' + ex.getMessage());
        }

        return d;
    }

}