[{"configId": "2RXl80CNCpCtwBSO7J", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "id", "sf_fieldLabel": "Record ID", "sf_childRelationshipName": "", "cp_fieldName": "integrationId", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "skHQQzteWDLb8qVeFj", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "internal_comments__c", "sf_fieldLabel": "Internal Comments", "sf_childRelationshipName": "", "cp_fieldName": "contractDescription", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "JOpWndBrPmQniamDsX", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "clientDetails", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "aWDKvDYpERteAwZMwr", "isConstant": false, "cp_fieldName": "clientTypeId", "cp_fieldLabel": "Standard : Partner Type", "sf_fieldName": "Account__r.type", "sf_fieldLabel": "Account__r.Account Type"}, {"configId": "SoHX5uSgHUHXB7ObNO", "isConstant": false, "cp_fieldName": "customerName", "cp_fieldLabel": "Standard : Partner Name", "sf_fieldName": "Account__r.name", "sf_fieldLabel": "Account__r.Account Name"}, {"configId": "GbGqInCENg96lY6pmT", "isConstant": false, "cp_fieldName": "addressLine1", "cp_fieldLabel": "Standard : Address", "sf_fieldName": "Account__r.billings<PERSON>t", "sf_fieldLabel": "Account__r.<PERSON>"}, {"configId": "Honu1L0SY9CTcl7gnI", "isConstant": false, "cp_fieldName": "city", "cp_fieldLabel": "Standard : City", "sf_fieldName": "Account__r.billingcity", "sf_fieldLabel": "Account__r.Billing <PERSON>"}, {"configId": "rEiB2YLuw99ZwgHCpn", "isConstant": false, "cp_fieldName": "pincode", "cp_fieldLabel": "Standard : Pincode/Postal Code", "sf_fieldName": "Account__r.billingpostalcode", "sf_fieldLabel": "Account__r.Billing Zip/Postal Code"}, {"configId": "mJPMu6S94iQiUnDOTt", "isConstant": false, "cp_fieldName": "countryId", "cp_fieldLabel": "Standard : Country", "sf_fieldName": "Account__r.billingcountry", "sf_fieldLabel": "Account__r.<PERSON>"}, {"configId": "v0uBtQVLKXTNCgFimv", "isConstant": false, "cp_fieldName": "state", "cp_fieldLabel": "Standard : State/Province", "sf_fieldName": "Account__r.billingstate", "sf_fieldLabel": "Account__r.Billing State/Province"}], "configId": "sy3YpqjBAI5gEaT8nk"}], "showFilter": false}, {"configId": "2UqoaUMg4g12oguRbt", "mappingType": "compositeMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "", "sf_fieldLabel": "", "sf_childRelationshipName": "", "cp_fieldName": "assignees", "cp_fieldType": "composite", "isCompositeMap": true, "compositeMapping": [{"sf_fieldName": "", "sf_childRelationshipName": "", "relatedDataMapping": [{"configId": "2dspa0l2LsmduKjDHw", "isConstant": true, "cp_fieldName": "assigntoUsername", "cp_fieldLabel": "Standard : Assignee", "sf_fieldName": "ContractPod", "sf_fieldLabel": "ContractPod"}], "configId": "u81Vou6fd2DMcu4YjE"}], "showFilter": false}, {"configId": "T3mzKbYKhXhN2GW3fi", "mappingType": "fieldMapping", "isConstant": true, "isRelatedMap": false, "isReferenceMapping": false, "sf_fieldName": "MicroStrategy Ltd.", "sf_fieldLabel": "MicroStrategy Ltd.", "sf_childRelationshipName": "", "cp_fieldName": "contractingPartyId", "cp_fieldType": "Standard", "isCompositeMap": false, "showFilter": false}, {"configId": "VnnV8Seo4UZpgVDR1g", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "name", "sf_fieldLabel": "Legal Document ID", "sf_childRelationshipName": "", "cp_fieldName": "OrderForm", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "pcv0DiF4tA183Exyfw", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_broaden_license_grant__c", "sf_fieldLabel": "License for Internal & External Bus Ops", "sf_childRelationshipName": "", "cp_fieldName": "LicenseIntExtBusOpsNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "BjpWJq5scYk0Yw6NyO", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_reimbursement_not_required__c", "sf_fieldLabel": "No Reimbursement of Travel Expenses", "sf_childRelationshipName": "", "cp_fieldName": "NoReimbursementTravelExpensesNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "DQEzV3D1lqZfI0xmC5", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_no_name_logo_rights__c", "sf_fieldLabel": "No Name/Logo Rights", "sf_childRelationshipName": "", "cp_fieldName": "NoNameLogoRightsNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "rbv8XAmEWAtsWsdXMf", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_no_press_rights__c", "sf_fieldLabel": "No Press Release", "sf_childRelationshipName": "", "cp_fieldName": "NoPressReleaseNewSF", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "V1We6icWl58EK0YLRG", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "license_terms__c", "sf_fieldLabel": "License Terms", "sf_childRelationshipName": "", "cp_fieldName": "LicenseTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "DuZUP2pFzbE5nut5Xt", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "other_enterprise_terms__c", "sf_fieldLabel": "Other Enterprise Terms", "sf_childRelationshipName": "", "cp_fieldName": "OtherEnterpriseTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "0ld09Mchbm71orl57f", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "pricing_payment_and_invoicing_terms__c", "sf_fieldLabel": "Pricing, Payment and Invoicing Terms", "sf_childRelationshipName": "", "cp_fieldName": "PricingAndInvoicingTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "R324oTIWDUKk7fFX1f", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "technical_support_terms__c", "sf_fieldLabel": "Technical Support Terms", "sf_childRelationshipName": "", "cp_fieldName": "TechnicalSupportTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "tdVV0d0GEhdLOeuBv2", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "services_terms__c", "sf_fieldLabel": "Services Terms", "sf_childRelationshipName": "", "cp_fieldName": "ServicesTerms", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "APWLhr5q1yg8PpNm0n", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "legal_internal_notes__c", "sf_fieldLabel": "Legal Internal Notes", "sf_childRelationshipName": "", "cp_fieldName": "LegalNotes", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "JQgm5ro2djCkDtRK2G", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "right_to_terminate_and_refund__c", "sf_fieldLabel": "Right to Terminate Support and Refund", "sf_childRelationshipName": "", "cp_fieldName": "RighttoTerminateandRefund", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "Hcr4qgV3ykxzhxEGej", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_no_technical_support_auto_renew__c", "sf_fieldLabel": "No Technical Support Auto-Renew", "sf_childRelationshipName": "", "cp_fieldName": "NoTechnicalSupportAR", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "O9CpkVWIaYJQIqretK", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "sbcf_no_pep_auto_renew__c", "sf_fieldLabel": "No PEP Auto-Renew", "sf_childRelationshipName": "", "cp_fieldName": "NoPEPAR", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "8H7CJTXRubC7x2zdST", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "internal_comments__c", "sf_fieldLabel": "Internal Comments", "sf_childRelationshipName": "", "cp_fieldName": "InternalComments", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "cK6qoFmNDUtJEuGOFb", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "upload_comments__c", "sf_fieldLabel": "Upload Comments", "sf_childRelationshipName": "", "cp_fieldName": "UploadComments", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "hKiJsBUwoUIHZz1FZt", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "legal_clarification_notes__c", "sf_fieldLabel": "Legal Clarification Notes", "sf_childRelationshipName": "", "cp_fieldName": "LegalClarificationNotes", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "NUuFg0ZXYrUcKw0WEq", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "standard__c", "sf_fieldLabel": "Standard", "sf_childRelationshipName": "", "cp_fieldName": "Standard", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "l6x2NAj0D7DIUYleO6", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "legal_caf_comments__c", "sf_fieldLabel": "Legal CAF Comments", "sf_childRelationshipName": "", "cp_fieldName": "LegalCAFComments", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}, {"configId": "mhDAYNmdMQpCfISadJ", "mappingType": "fieldMapping", "isConstant": false, "isRelatedMap": false, "isReferenceMapping": true, "sf_fieldName": "document_language__c", "sf_fieldLabel": "Document Language", "sf_childRelationshipName": "", "cp_fieldName": "DocumentLanguage", "cp_fieldType": "Custom", "isCompositeMap": false, "showFilter": false}]