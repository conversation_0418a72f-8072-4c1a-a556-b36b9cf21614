<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Calling_batch_to_update_ARR</name>
        <label>Calling batch to update ARR</label>
        <locationX>110</locationX>
        <locationY>792</locationY>
        <actionName>SubscriptionRenewalStatusIndicatorBatch</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>accId</name>
            <value>
                <elementReference>$Record.Account.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SubscriptionRenewalStatusIndicatorBatch</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Update_MCS_Environment</name>
        <label>Update MCS Environment</label>
        <locationX>374</locationX>
        <locationY>384</locationY>
        <actionName>MCSEnvironmentController</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_Renewal_Date_on_Subs</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>opportunityId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCSEnvironmentController</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Update_Migration_Date_on_Subs</name>
        <label>Update Migration Date on Subs</label>
        <locationX>374</locationX>
        <locationY>900</locationY>
        <actionName>UnstampingOverrideCheckBatch</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>closeDate</name>
            <value>
                <elementReference>$Record.CloseDate</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>oppId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>UnstampingOverrideCheckBatch</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Update_Renewal_Date_on_Subs</name>
        <label>Update Renewal Date on Subs</label>
        <locationX>506</locationX>
        <locationY>576</locationY>
        <actionName>AccountMetricBatch</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Record_Type</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>oppId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>AccountMetricBatch</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <decisions>
        <name>Is_MCS_Opp</name>
        <label>Is MCS Opp?</label>
        <locationX>506</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>Update_Renewal_Date_on_Subs</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_MSC_Opp</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Platform__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MCS</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_MCS_Environment</targetReference>
            </connector>
            <label>Yes, MSC Opp</label>
        </rules>
    </decisions>
    <decisions>
        <name>Opportunity_Type</name>
        <label>Opportunity Type</label>
        <locationX>506</locationX>
        <locationY>792</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Term_Migration</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Type</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Term Migration</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Migration_Date_on_Subs</targetReference>
            </connector>
            <label>Term Migration</label>
        </rules>
    </decisions>
    <decisions>
        <name>Record_Type</name>
        <label>Record Type</label>
        <locationX>506</locationX>
        <locationY>684</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Amendment_Opp</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Record_Type_F__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Amendment Opportunity</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Calling_batch_to_update_ARR</targetReference>
            </connector>
            <label>Amendment Opp</label>
        </rules>
        <rules>
            <name>Renewal_Opp</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Record_Type_F__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renewal Opportunity (New)</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Opportunity_Type</targetReference>
            </connector>
            <label>Renewal Opp</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Call Subscription Batch {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Call Subscription Batch</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>StageName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>S6 - Closed Won</stringValue>
            </value>
        </filters>
        <object>Opportunity</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <name>X5_minute_after_opp_closed</name>
            <connector>
                <targetReference>Is_MCS_Opp</targetReference>
            </connector>
            <label>5 minute after opp closed</label>
            <offsetNumber>5</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <timeSource>RecordTriggerEvent</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
