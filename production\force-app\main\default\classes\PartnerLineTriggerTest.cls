/**
 * Created by <PERSON><PERSON><PERSON> on 11/06/2024.
 * test for PartnerLineController, PartnerLineTrigger, PartnerOppController
 */

@IsTest
private class PartnerLineTriggerTest {
    @TestSetup
    static void initData() {
        List<Opportunity> opportunities = DataFactory.createOpportunities(0, 1, true, null);
        List<Account> accounts = DataFactory.createAccounts(1, 4, true, null);
        Partner_Line__c pl = new Partner_Line__c(
                Opportunity__c = opportunities[0].Id,
                Account__c = accounts[0].Id,
                Partner_Type__c = 'Sales Partner',
                Motion__c = 'Referral'
        );
        insert pl;
        Partner_Line__c pl1 = new Partner_Line__c(
                Opportunity__c = opportunities[0].Id,
                Account__c = accounts[0].Id,
                Partner_Type__c = 'Sales Partner',
                Motion__c = 'Pass-Through Resell'
        );
        insert pl1;
        Partner_Line__c pl2 = new Partner_Line__c(
                Opportunity__c = opportunities[0].Id,
                Account__c = accounts[0].Id,
                Partner_Type__c = 'Distributor'
        );
        insert pl2;
        List<SBQQ__Quote__c> quotes = CPQTestDataFactory.CreateQuoteforOpptys(true, opportunities);

    }

    @IsTest
    static void test1() {
        Partner_Line__c pl2del = [SELECT Id, Motion__c, Partner_Type__c, Account__c FROM Partner_Line__c WHERE Partner_Type__c = 'Distributor'];
        Opportunity opportunity = [SELECT Id, AccountId, RecordTypeId FROM Opportunity LIMIT 1];
        Partner_Line__c pl1 = [SELECT Id, Motion__c, Partner_Type__c, Account__c FROM Partner_Line__c LIMIT 1];
        SBQQ__Quote__c quote = [SELECT Id, SBQQ__Partner__c FROM SBQQ__Quote__c WHERE SBQQ__Opportunity2__c = :opportunity.Id];
        List<Account> accounts = [SELECT Id, Name FROM Account];

        System.assertEquals(quote.SBQQ__Partner__c, pl2del.Account__c);

        Test.startTest();
        delete pl2del;
        quote = [SELECT Id, SBQQ__Partner__c FROM SBQQ__Quote__c WHERE Id = :quote.Id];
        System.assertEquals(pl1.Account__c, quote.SBQQ__Partner__c);

        pl1.Partner_Type__c = 'Distributor';
        pl1.Motion__c = null;
        update pl1;
        quote = [SELECT Id, SBQQ__Distributor__c, SBQQ__Partner__c FROM SBQQ__Quote__c WHERE Id = :quote.Id];
        System.assertEquals(quote.SBQQ__Partner__c, quote.SBQQ__Distributor__c);

        delete pl1;
        quote = [SELECT Id, Reseller__c, SBQQ__Partner__c FROM SBQQ__Quote__c WHERE Id = :quote.Id];
        System.assertEquals(quote.Reseller__c, quote.SBQQ__Partner__c);

        List<Partner_Line__c> pls= [SELECT Id FROM Partner_Line__c];
        delete pls;

        quote = [SELECT Id, SBQQ__Partner__c FROM SBQQ__Quote__c WHERE Id = :quote.Id];
        System.assertEquals(null, quote.SBQQ__Partner__c);

        pl1 = new Partner_Line__c(
                Opportunity__c = opportunity.Id,
                Account__c = accounts[0].Id,
                Partner_Type__c = 'Distributor'
        );
        insert pl1;

        Partner_Line__c pl2 = new Partner_Line__c(
                Opportunity__c = opportunity.Id,
                Account__c = accounts[0].Id,
                Partner_Type__c = 'Sales Partner',
                Motion__c = 'Pass-Through Resell'
        );
        insert pl2;

        delete pl2;

        Test.stopTest();
    }

    @IsTest
    static void testPartnerOppController() {
        Opportunity opportunity = [SELECT Id FROM Opportunity LIMIT 1];
        List<Partner_Line__c> partnerLines = PartnerOppController.getPartnerLines(opportunity.Id);
        Partner_Line__c pl = PartnerOppController.getRecordById(partnerLines.get(0).Id);
        Map<String, String> motionValues = PartnerOppController.getPicklistValuesOfMotion();
        System.assert(!motionValues.isEmpty());
        System.assertEquals(pl, new Partner_Line__c(Id = partnerLines.get(0).Id, Sourced__c = partnerLines.get(0).Sourced__c));
    }
}