/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 06/01/2021.
 */

 public without sharing class OpportunityTriggerExecutionController implements TriggerHandler {

    public static Boolean useOldOpportunityTrigger {
        
        get {
            if (useOldOpportunityTrigger == null) {
                useOldOpportunityTrigger = Boolean.valueOf(MSTR_Global_Configuation__mdt.getInstance('Use_Old_Opportunity_Trigger').Value__c);
            }
            return useOldOpportunityTrigger;
        }
        set;
    }
    
    public static List<String> PAccountCategoryValues {
        
        get {
            if (PAccountCategoryValues == null) {
                PAccountCategoryValues = MSTR_Global_Configuation__mdt.getInstance('P_Account_Category_Values').Value__c.split(',');
            }
            return PAccountCategoryValues;
        }
        set;
    }

    public static Boolean cloudForcastLock = false;

    Set<Id> partnerAccountIds;
    Set<Id> oppIdsForDsiLineItems;
    Set<Id> dsiIds;
    Set<Id> pqIds;
    Set<Id> oppIdsToUpdateOriginalCampaign;
    Map<Id, Opportunity> oppIdsToUpdateContact;
    Map<Id, Opportunity> oppIdsToCreateInI;
    Set<Id> psaCreateResourceOppIds;
    Set<Id> psaUpdateResourceOppIds;
    Set<Id> psaCancelResourceOppIds;
    Set<Id> recallQuotesForClosedOppIds;
    List<Id> checkCampaignInfluenceoppIdsToSync;
    Map<Id, Id> checkCampaignInfluencemapCampaignsToRemove;
    Boolean isUpdate;
    Boolean isAccountUpdate;
    Boolean isBeforeUpdate;
    Boolean isAfterUpdate;
    Boolean processExpertNow;
    Boolean createExpertNowQuote;
    Map<Id, Opportunity> newOpportunities;
    Map<Id, Opportunity> oldOpportunities;
    List<Id> opportunitiesChangedCloseDate;
    Set<Id> oppsToUnlock;
    List<Case> newOnBoardingCases;
    List<Opportunity> opportunitiesNewStage;
    Set<Id> oppIdsUpdateAccountCustomer;
    List<Opportunity> toProcessOptyList;
    List<Opportunity> populateApproversOppList;
    List<Opportunity> populateDistrictSalesApproversOppList;
    List<Opportunity> oppListToSetBillingContactFields;
    List<Opportunity> oppListToMatchCurrency;
    List<Opportunity> oppsForCPQRenewal;
    List<Opportunity> oppsToSetRpg;
    List<Opportunity> renewalOpps;
    List<Opportunity> oppsPSD;
    List<Opportunity> oppUpdateAccCategory; // Sync PB Opportunity - Account Category
    List<Opportunity> oppLegalEntitySync; // Sync PB Opportunity - Legal Entity Sync
    Set<Id> accIdsAccCategory;// Sync PB Opportunity - Account Category
    Map<Id, Opportunity> oppsCSM;
    Map<Id, Opportunity> newOppsPrimaryOwner;
    Set<Id> accIdsPSD;
    Set<Id> accIdsCSM;
    Set<Id> contactIdsForBillingFields;
    Set<Id> accountIDSetForDistrictSalesApprover;
    Set<Id> districtIDSet;
    Set <String> usrIdsToSetRPGs;
    Map <String, String> accUserMaptoSetRPGs;
    Set<Id> parentContractIdSetForCPQRenewal;
    Set<Id> parentAccIDSetForCPQRenewal;
    Set<Id> renewalContractIDSet;
    Set<Id> mciDsiIds;
    Set<Id> dsiIdsToTerminate;
    Set<Id> oppIdsForOCM;
    Set<Id> oldRelatedContactIdsForOCM;
    Set<Id> newRelatedContactIdsForOCM;
    Set<Id> oppsToUpdateProjectStatus;
    List<Opportunity> opportunitiesOldOCM;
    List<Opportunity> opportunitiesNewOCM;
    List<Opportunity> oppListToInsertOCMRecords;
    List<Opportunity> accChangedOptyListLeanData;
    List<Opportunity> reopenedOptyList;
    List<Opportunity> oppsForKeyGeneration;
    List<Opportunity> oppsToUpdateOnAccountChange;
    List<Opportunity> oppsForApprovalOnStatusChange;
    List<Opportunity> oppsForaccountIdsForManagedCloudAndPO;
    Set<Id> opportunitiesWithNewStatus;
    Set<Id> accountIdsForManagedCloudAndPO;
        List<SBQQ__Quote__c> pQuotesShipDate;
    Set<Id>quotesToUpdateReps;
    private Boolean campaignRollupsExecuted = false;
    Set<Id> oppIdsToUpdateQuotes; //Sync PB Opportunity - Legal Rev Rec Changed
    Set<Id> oppIdsToUpdatePartnerDealQuote; //Sync PB Opportunity - Update Partner On Quote
    Set<Opportunity> oppIdsToActivateContracts; //Sync PB Opportunity - Activate All Contracts
    List<Opportunity> oppsShipDateUpdateSync; //Sync PB opportunity- Ship date & Ship Date Updated
    Boolean oppWithLicense; //Sync PB opportunity- Ship date & Ship Date Updated
    Boolean oppShipDateUpdatedPB; //Sync PB opportunity- Ship date & Ship Date Updated
    Boolean oppWOLicense; //Sync PB opportunity- Ship date & Ship Date Updated
    Map<String, Object> params; // Sync PB Opportunity - Primary SD
    Map<String, Object> params1; // Sync PB Opportunity - Primary SD
    List<Opportunity> replacementOpportunities;
    List<Opportunity> replacementQuotedOpps;
    List<Opportunity> amendmentOpps;
    List<Id> amendmentOppsId;
    Date MYCReleaseDate;
    List<Opportunity> primaryQuoteChanged;
    Set<Id> newPrimaryQuoteIds;
    List<Opportunity> oppsWithStageChanged;
    List<Opportunity> oppsWithOwnerChanged;
    Set<Id> newOwnersIds;
    List<Opportunity> oppsWithPlatformChanged;
    Set<Id> queriedContractIds;
    List<Opportunity> opportunitiesToCheckNewBusiness;
    Set<Id> PAccountCategoryAccs;
    List<Opportunity> csmChanged;
    Map<Id,Opportunity>partnerOpps;
    Set<Id> contactsToEvaluateRTM;
     Map<Id, Opportunity> opps2carryOverPartnerLines;
     Set<Id> opps2carryOverPartnerLinesIds;

    public Integer getRecursionDepth () {
        return 0; 
    }

    public Integer getMaxRecursionDepthAllowed () {
        return 0;
    }

    public Boolean isEnabled () {
        return true;
    }

    public void bulkBefore () {
        toProcessOptyList = new List<Opportunity>();
        newOpportunities = new Map<Id, Opportunity>();
        oldOpportunities = new Map<Id, Opportunity>();
        populateApproversOppList = new List<Opportunity>();
        populateDistrictSalesApproversOppList = new List<Opportunity>();
        oppListToSetBillingContactFields = new List<Opportunity>();
        renewalOpps = new List<Opportunity>();
        contactIdsForBillingFields = new Set<Id>();
        accountIDSetForDistrictSalesApprover = new Set<Id>();
        oppListToMatchCurrency = new List<Opportunity>();
        oppsForCPQRenewal = new List<Opportunity>();
        parentContractIdSetForCPQRenewal = new Set<Id>();
        parentAccIDSetForCPQRenewal = new Set<Id>();
        districtIDSet = new Set<Id>();
        usrIdsToSetRPGs = new Set<String>();
        renewalContractIDSet = new Set<Id>();
        accUserMaptoSetRPGs = new Map<String, String>();
        mciDsiIds = new Set<Id>();
        dsiIdsToTerminate = new Set<Id>();
        oppsToSetRpg = new List<Opportunity>();
        accountIdsForManagedCloudAndPO = new Set<Id>();
        oppsForaccountIdsForManagedCloudAndPO = new List<Opportunity>();
        processExpertNow = false;
        isBeforeUpdate = false;
        isAccountUpdate = false;
        oppsPSD = new List<Opportunity>();
        accIdsPSD = new Set<Id>();
        oppUpdateAccCategory = new List<Opportunity>(); // Sync PB Opportunity - Account Category
        accIdsAccCategory = new Set<Id>(); // Sync PB Opportunity - Account Category
        oppLegalEntitySync = new List<Opportunity>(); // Sync PB Opportunity - Legal Entity Sync
        //oppIdsToInsertTeamMembersFromAccount = new Set<Id>();
        // accountIdsToInsertTeamMembersFromAccount = new Set<Id>();
        replacementOpportunities = new List<Opportunity>();
        amendmentOpps = new List<Opportunity>();
        amendmentOppsId = new List<Id>();
        MYCReleaseDate = !Test.isRunningTest()? MSTRSettings__c.getInstance().MYC_Release_Date__c : Date.today()-1;
    	primaryQuoteChanged = new List<Opportunity>();
        newPrimaryQuoteIds = new Set<Id>();
        oppsWithStageChanged = new List<Opportunity>();
        oppsWithOwnerChanged = new List<Opportunity>();
        newOwnersIds = new Set<Id>();
        oppsWithPlatformChanged = new List<Opportunity>();
        queriedContractIds = new Set<Id>();
        opportunitiesToCheckNewBusiness = new List<Opportunity>();
        PAccountCategoryAccs = new Set<Id>();
    }

    public void bulkAfter () {
        partnerAccountIds = new Set<Id>();
        createExpertNowQuote = false;
        oppsToSetRpg = new List<Opportunity>();
        oppIdsToUpdateOriginalCampaign = new Set<Id>();
        oppIdsToUpdateContact = new Map<Id, Opportunity>();
        oppIdsToCreateInI = new Map<Id, Opportunity>();
        psaCreateResourceOppIds = new Set<Id>();
        psaUpdateResourceOppIds = new Set<Id>();
        psaCancelResourceOppIds = new Set<Id>();
        recallQuotesForClosedOppIds = new Set<Id>();
        checkCampaignInfluenceoppIdsToSync = new List<Id>();
        checkCampaignInfluencemapCampaignsToRemove = new Map<Id, Id>();
        oppIdsForDsiLineItems = new Set<Id>();
        dsiIds = new Set<Id>();
        newOpportunities = new Map<Id, Opportunity>();
        oldOpportunities = new Map<Id, Opportunity>();
        opportunitiesChangedCloseDate = new List<Id>();
        pqIds = new Set<Id>();
        isAfterUpdate = false;
        oppsToUnlock = new Set<Id>();
        newOnBoardingCases = new List<Case>();
        opportunitiesNewStage = new List<Opportunity>();
        oppIdsUpdateAccountCustomer = new Set<Id>();
        accUserMaptoSetRPGs = new Map<String, String>();
        oppIdsForOCM = new Set<Id>();
        oldRelatedContactIdsForOCM = new Set<Id>();
        newRelatedContactIdsForOCM = new Set<Id>();
        opportunitiesOldOCM = new List<Opportunity>();
        opportunitiesNewOCM = new List<Opportunity>();
        oppListToInsertOCMRecords = new List<Opportunity>();
        accChangedOptyListLeanData = new List<Opportunity>();
        reopenedOptyList = new List<Opportunity>();
        oppsForKeyGeneration = new List<Opportunity>();
        opportunitiesWithNewStatus = new Set<Id>();
        oppsToUpdateProjectStatus = new Set<Id>();
        oppsToUpdateOnAccountChange = new List<Opportunity>();
        oppsForApprovalOnStatusChange = new List<Opportunity>();
        accIdsCSM = new Set<Id>();
        oppsCSM = new Map<Id, Opportunity>();
        newOppsPrimaryOwner = new Map<Id, Opportunity>();
                pQuotesShipDate = new List<SBQQ__Quote__c>();
        quotesToUpdateReps = new Set<Id>();
        oppIdsToUpdateQuotes = new Set<Id>(); //Sync PB Opportunity - Legal Rev Rec Changed
        oppIdsToUpdatePartnerDealQuote = new Set<Id>(); //Sync PB Opportunity - Update Partner On Quote
        oppIdsToActivateContracts = new Set<Opportunity>(); // Sync PB Opportunity - Activate All Contracts
        oppsShipDateUpdateSync = new List<Opportunity>(); //Sync PB opportunity- Ship date & Ship Date Updated
        oppWithLicense = false; //Sync PB opportunity- Ship date & Ship Date Updated
        oppWOLicense = false; //Sync PB opportunity- Ship date & Ship Date Updated
        oppShipDateUpdatedPB = false; //Sync PB opportunity- Ship date & Ship Date Updated
        params = new Map<String, Object>(); // Sync PB Opportunity - Primary SD
        params1 = new Map<String, Object>(); // Sync PB Opportunity - Primary SD
        replacementQuotedOpps = new List<Opportunity>();
        csmChanged = new List<Opportunity>();
        partnerOpps = new Map<Id, Opportunity>();
        contactsToEvaluateRTM = new Set<Id>();
        opps2carryOverPartnerLines = new Map<Id, Opportunity>();
        opps2carryOverPartnerLinesIds = new Set<Id>();
    }

    public void beforeInsert (SObject so) {
        Opportunity opp = (Opportunity) so;
        newOpportunities.put(opp.Id, opp);
        processExpertNow = true;
        if(String.isNotBlank(opp.RegionPricebook__c) || opp.SBQQ__RenewedContract__c != null) {
            oppListToMatchCurrency.add(opp);
        }
        if (opp.RecordTypeId == TriggersHelper.salesOppRecordTypeId || opp.Type == 'Term Migration') {
            opp.Deal_Team_Approval__c = false;
            opp.Approval_Lock__c = false;
            opp.Approval_Lock_SE__c = false;
            opp.SE_Approval__c = false;
        }

        //populateApproversOppList.add(opp);
        populateDistrictSalesApproversOppList.add(opp);
        
        //to populate approvers
        if(opp.District__c != null) {
            districtIDSet.add(opp.District__c);
        }
        //to populate districtSalesApprover
        accountIDSetForDistrictSalesApprover.add(opp.AccountId);
        if (String.isNotBlank(opp.Billing_Contact__c) && (
            String.isBlank(opp.BCEmail__c) ||
            String.isBlank(opp.BCFName__c) ||
            String.isBlank(opp.BCLName__c)
        )) {
            oppListToSetBillingContactFields.add(opp);
            contactIdsForBillingFields.add(opp.Billing_Contact__c);
        }
        // if (String.isNotBlank(opp.RegionPricebook__c)) {
        // }

        //SetRPG (populateJDEID)
        usrIdsToSetRPGs.add(opp.OwnerId);
        if (opp.Primary_Services_Director__c != null) {
            usrIdsToSetRPGs.add(opp.Primary_Services_Director__c);
        }
        if (opp.AccountId != null) {
            accIdsPSD.add(opp.AccountId);
            oppsPSD.add(opp);
            accUserMaptoSetRPGs.put(opp.AccountId, null);
            oppsToSetRpg.add(opp);
        }

        if (opp.SBQQ__RenewedContract__c != null) {
            oppsForCPQRenewal.add(opp);
            parentContractIdSetForCPQRenewal.add(opp.SBQQ__RenewedContract__c);
            parentAccIDSetForCPQRenewal.add(opp.AccountId);
        }
        if (String.isNotEmpty(opp.SBQQ__RenewedContract__c)) {
            renewalOpps.add(opp);
            renewalContractIDSet.add(opp.SBQQ__RenewedContract__c);
        }
        if (String.isNotBlank(opp.DSI__c) && (opp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId || opp.RecordTypeId == TriggersHelper.salesOppRecordTypeId)) {
            accountIdsForManagedCloudAndPO.add(opp.DSI__c);
            oppsForaccountIdsForManagedCloudAndPO.add(opp);
        }
        
        //Process Builder Opportunity - Sync Primary SE
        if(opp.Primary_SE__c != null) {
            Opp.Primary_SE_ID__c = Opp.Primary_SE__c;
        }
        
        if(opp.AccountId != null){  // Sync PB Opportunity - Account Category
            oppUpdateAccCategory.add(opp);
            accIdsAccCategory.add(opp.AccountId);
        }

        //Sync Opportunity - Late Close Date Changed
        if (opp.Late_Close_Date__c != null) {
            String userFullName = UserInfo.getFirstName() + ' ' + UserInfo.getLastName();
            Opp.Late_Close_Date_Modified_By__c = userFullName;
            Opp.Late_Close_Date_Modified_Date__c = Date.today();
        }
        
        // Sync PB Opportunity - Legal Entity Sync
        if (opp.SBCF_Legal_Entity__c != null) {
            oppLegalEntitySync.add(opp);
            accIdsAccCategory.add(opp.SBCF_Legal_Entity__c);
        }
        
        OpportunityTriggerController.setOppProbability(opp);
        
        if(opp.CreatedDate >= MYCReleaseDate && !String.isEmpty(opp.Originating_Quote__c)){
            opp.Originating_Quote__c = null;
        }
        if (String.isNotBlank(opp.StageName)) {
            oppsWithStageChanged.add(opp);
        }

/*
        Transferred workflows:
        Opportunity - Renewal Do not Ship Key
        if (opp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId) {
            opp.Do_Not_Ship_Key__c = true;
        }
*/
/*
        Transferred workflows:
        Opportunity Billing Type Updated
*/
        if (String.isBlank(opp.Consulting_Billing_Type__c)) {
            opp.Consulting_Billing_Type__c = 'Time and Materials';
        }
/*
        Transferred workflows:
        Eng Last Modified Date
*/
        if (String.isNotBlank(opp.Eng_Status__c) ||
            String.isNotBlank(opp.Eng_Scope__c)) {
            opp.Eng_Last_Modified_Date__c = Date.today();
        }
        if (opp.OwnerId != null) {
            oppsWithOwnerChanged.add(opp);
            newOwnersIds.add(opp.OwnerId);
        }
                
        //Check new business
        if(opp.RecordTypeId == TriggersHelper.salesOppRecordTypeId && opp.AccountId != null) {
            PAccountCategoryAccs.add(opp.AccountId);
            opportunitiesToCheckNewBusiness.add(opp);
        }

        if (opp.License_Forecast__c != null) {
        	opp.Bizible_Opportunity_Amount__c = opp.License_Forecast__c;
        }
    }

    public void beforeUpdate (SObject oldObject, SObject newObject) {
        if(OrderProductsRelatedListController.firedFromOI() != null && OrderProductsRelatedListController.firedFromOI()){
    		return;
    	}
        if(ContractTriggerExecutionController.firedFromOrderInsert() != null && ContractTriggerExecutionController.firedFromOrderInsert()){
            return; 
        }
        Opportunity newOpp = (Opportunity) newObject;
        Opportunity oldOpp = (Opportunity) oldObject;
        isBeforeUpdate = true;
        oldOpportunities.put(oldOpp.Id, oldOpp);
        newOpportunities.put(newOpp.Id, newOpp);

         if (newOpp.RegionPricebook__c != oldOpp.RegionPricebook__c && String.isNotBlank(newOpp.RegionPricebook__c) ) {
             oppListToMatchCurrency.add(newOpp);
         }
        //Sync Opportunity - Late Close Date Changed
        if (newOpp.Late_Close_Date__c != null && newOpp.Late_Close_Date__c != oldOpp.Late_Close_Date__c) {
            String userFullName = UserInfo.getFirstName() + ' ' + UserInfo.getLastName();
            newOpp.Late_Close_Date_Modified_By__c = userFullName;
            newOpp.Late_Close_Date_Modified_Date__c = Date.today();
        }
        
        if (newOpp.SBQQ__RenewedContract__c != null && newOpp.Platform__c != oldOpp.Platform__c) {
            oppsWithPlatformChanged.add(newOpp);
            queriedContractIds.add(newOpp.SBQQ__RenewedContract__c);
        }
        if (newOpp.District__c != null && (newOpp.District__c != oldOpp.District__c || (newOpp.SBQQ__PrimaryQuote__c != oldOpp.SBQQ__PrimaryQuote__c 
            && newOpp.SBQQ__PrimaryQuote__c != null && oldOpp.SBQQ__PrimaryQuote__c != null))) {
            populateApproversOppList.add(newOpp);
            districtIDSet.add(newOpp.District__c);
        }
        if (newOpp.AccountId != oldOpp.AccountId) {
            isAccountUpdate = true;
            accountIDSetForDistrictSalesApprover.add(newOpp.AccountId);
            populateDistrictSalesApproversOppList.add(newOpp);
            if (newOpp.AccountId != null) {
                accIdsPSD.add(newOpp.AccountId);
                oppsPSD.add(newOpp);
            }
        }
        if (String.isNotBlank(newOpp.Billing_Contact__c) && newOpp.Billing_Contact__c != oldOpp.Billing_Contact__c) {
            oppListToSetBillingContactFields.add(newOpp);
            contactIdsForBillingFields.add(newOpp.Billing_Contact__c);
        }

        if ((newOpp.Primary_Services_Director__c != null && newOpp.Primary_Services_Director__c != oldOpp.Primary_Services_Director__c)
            || newOpp.SBQQ__PrimaryQuote__c == null) {
            usrIdsToSetRPGs.add(newOpp.Primary_Services_Director__c);
            usrIdsToSetRPGs.add(newOpp.OwnerId);
            oppsToSetRpg.add(newOpp);
            accUserMaptoSetRPGs.put(newOpp.AccountId, null);
        }
        
        //Process Builder Opportunity - Sync Primary SE
        if(newOpp.Primary_SE__c != oldOpp.Primary_SE__c){
            newOpp.Primary_SE_ID__c = newOpp.Primary_SE__c;
        }
        
        // Sync PB Opportunity - Legal Entity Sync - start
		// MYC BUGFix 233 Nogueira added null check for Legal Name and ID
        if (newOpp.SBCF_Legal_Entity__c != null && (newOpp.SBCF_Legal_Entity__c != oldOpp.SBCF_Legal_Entity__c || newOpp.Legal_Entity_Name__c == null || newOpp.Legal_Entity_ID__c == null)) {
            oppLegalEntitySync.add(newOpp);
            accIdsAccCategory.add(newOpp.SBCF_Legal_Entity__c);
        }
        
        if (newOpp.SBCF_Legal_Entity__c == null && newOpp.SBCF_Legal_Entity__c != oldOpp.SBCF_Legal_Entity__c){
            newOpp.Legal_Entity_ID__c = null;
            newOpp.Legal_Entity_Name__c = null;
        } // Sync PB Opportunity - Legal Entity Sync - end
        
        if(newOpp.StageName == 'S6 - Closed Won' && newOpp.StageName != oldOpp.StageName){  // Sync PB Opportunity - Account Category
            oppUpdateAccCategory.add(newOpp);
            accIdsAccCategory.add(newOpp.AccountId);
        }

        if(newOpp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId && newOpp.StageName == 'S0 - Closed Lost' && newOpp.StageName != oldOpp.StageName){
            newOpp.CloseDate = System.today();
        }

        if (newOpp.RecordTypeId == TriggersHelper.salesOppRecordTypeId && newOpp.StageName != 'S6 - Closed Won') {
            if (!FeatureManagement.checkPermission('ITS_Data_Manager')) {
                if (newOpp.Deal_Team_Approval__c && (newOpp.OwnerId != oldOpp.OwnerId || newOpp.GA_Representative__c != oldOpp.GA_Representative__c || newOpp.BDRUser__c != oldOpp.BDRUser__c)) {
                    newOpp.StageName = 'S2 - Define Requirements';
                    //newOpp.Deal_Team_Approval__c=FALSE;
                } else if (newOpp.SE_Approval__c && newOpp.Primary_SE__c != oldOpp.Primary_SE__c) {
                    newOpp.StageName = 'S3 - Evaluate Options/Proposals';
                    //newOpp.SE_Approval__c=FALSE;
                }
            }
        }
        if ((newOpp.RecordTypeId == TriggersHelper.salesOppRecordTypeId || newOpp.Type == 'Term Migration') && newOpp.Reject_Recall_Stage__c != null && newOpp.StageName != 'S6 - Closed Won') {
            newOpp.StageName = newOpp.Reject_Recall_Stage__c;
            newOpp.Reject_Recall_Stage__c = null;
        }
        if (oldOpp.StageName != newOpp.StageName && newOpp.DSI__c != null && (newOpp.StageName.containsIgnoreCase('S3 - Evaluate Options/Proposals')
            || newOpp.StageName.containsIgnoreCase('S4 - Resolve Concerns')
            || newOpp.StageName.containsIgnoreCase('S5 - Negotiate/Purchase Solution'))) {
            mciDsiIds.add(newOpp.DSI__c);
        }
        if (!OpportunityTriggerController.isMciTrialDeactivated && !System.isBatch() && oldOpp.StageName != newOpp.StageName && newOpp.DSI__c != null && newOpp.StageName.containsIgnoreCase('S0 - Closed Lost')) {
            dsiIdsToTerminate.add(newOpp.DSI__c);
        }

        if (oldOpp.StageName != newOpp.StageName || oldOpp.Type != newOpp.Type || oldOpp.RecordTypeId != newOpp.RecordTypeId) {
            OpportunityTriggerController.setOppProbability(newOpp);
        }
        if (String.isNotBlank(newOpp.Platform__c) && (newOpp.Platform__c.contains('MCE') || newOpp.Platform__c.contains('MCG') || newOpp.Platform__c.contains('CMC')
            || newOpp.Platform__c.contains('MCS'))) {
            newOpp.Cloud_Incremental_Forecast__c = newOpp.License_Forecast__c == null ? 0 : newOpp.License_Forecast__c;
            Decimal cloudForecast = newOpp.Cloud_Incremental_Forecast__c == null ? 0 : newOpp.Cloud_Incremental_Forecast__c;
            if (newOpp.CloseDate.year() < 2023 || newOpp.CloseDate > Date.newInstance(2024,01,01)) {
                cloudForecast += newOpp.Cloud_Migration_Forecast__c == null ? 0 : newOpp.Cloud_Migration_Forecast__c;
            }
            newOpp.Cloud_Forecast__c = cloudForecast > 0 ? cloudForecast : 0;
            newOpp.On_Prem_Forecast__c = 0;
            cloudForcastLock = true;
        }else{
            newOpp.Cloud_Incremental_Forecast__c = 0;
            newOpp.Cloud_Forecast__c = 0;
            newOpp.On_Prem_Forecast__c = newOpp.License_Forecast__c;
        }
        if (newOpp.RecordTypeId == TriggersHelper.salesOppRecordTypeId && (
            oldOpp.Platform__c != newOpp.Platform__c ||
            oldOpp.LicenseType__c != newOpp.LicenseType__c)
        ) {
            newOpp.Migration_Type__c =  (String.isBlank(newOpp.Platform__c) ? '' : newOpp.Platform__c + ' ') +
                (String.isBlank(newOpp.LicenseType__c) ? '' : newOpp.LicenseType__c);
        }

        //516152 - Nogueira Matias
        OpportunityTriggerHandler.getAmendmentOpps(amendmentOpps,newOpp);
        amendmentOppsId.add(newOpp.Id);
        
       if(newOpp.CreatedDate >= MYCReleaseDate && newOpp.Originating_Quote__c != oldOpp.Originating_Quote__c && !String.isEmpty(newOpp.Originating_Quote__c)){
            newOpp.Originating_Quote__c = null;
        }

       if(newOpp.SBQQ__PrimaryQuote__c != oldOpp.SBQQ__PrimaryQuote__c){
            if(newOpp.SBQQ__PrimaryQuote__c == null){
                newOpp.Multi_Year_Contract_Opp__c = false;
            }
            else{
                primaryQuoteChanged.add(newOpp);
                newPrimaryQuoteIds.add(newOpp.SBQQ__PrimaryQuote__c);
            }
       }
/*
        Transferred workflows:
        Update S0-6 Date
        Stage Last Changed Date
        Last Stage Change Downgrade
        Last Stage Change Upgrade
*/
        if (newOpp.StageName != oldOpp.StageName) {
            oppsWithStageChanged.add(newOpp);
        }

/*
        Transferred workflows:
        Opportunity - Renewal with Software
        Opportunity - Renewal without Software
        Opportunity - Renewal Do not Ship Key
        if (newOpp.Software__c != oldOpp.Software__c &&
            newOpp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId
        ) {
            //no software = no key shipment
            if (oldOpp.Software__c == null && newOpp.Software__c > 0 && newOpp.Do_Not_Ship_Key__c) {
                newOpp.Do_Not_Ship_Key__c = false;
            } else if (newOpp.Software__c == null && oldOpp.Software__c > 0 && !newOpp.Do_Not_Ship_Key__c) {
                newOpp.Do_Not_Ship_Key__c = true;
            }
        }
 */
/*
        Transferred workflows:
        Update Last Qtrs Checkbox T/F
*/
        if (newOpp.Is_Last_Quarter__c != newOpp.Last_4_Qtrs_F__c) {
            newOpp.Is_Last_Quarter__c = newOpp.Last_4_Qtrs_F__c;
        }
/*
        Transferred workflows:
        Opportunity - Check Contains Education checkbox
*/
        if (String.isNotBlank(newOpp.Agreement_Description__c) &&
            newOpp.Contains_Education__c != newOpp.Agreement_Description__c.contains('Education')) {
            newOpp.Contains_Education__c = newOpp.Agreement_Description__c.contains('Education');
        }
/*
        Transferred workflows:
        Update Next 4 Qtrs Checkbox T/F
*/
        if (newOpp.Next_4_Qtrs__c != newOpp.Next_4_Qtrs_F__c) {
            newOpp.Next_4_Qtrs__c = newOpp.Next_4_Qtrs_F__c;
        }
/*
        Transferred workflows:
        Update This and 3 Prev Qrtrs T/F
*/
        if (newOpp.This_and_3_Prev_Qrtrs__c != newOpp.This_and_3_Prev_Qrtrs_F__c) {
            newOpp.This_and_3_Prev_Qrtrs__c = newOpp.This_and_3_Prev_Qrtrs_F__c;
        }
/*
        Transferred workflows:
        Eng Last Modified Date
*/
        if (newOpp.Eng_Status__c != oldOpp.Eng_Status__c ||
            newOpp.Eng_Scope__c != oldOpp.Eng_Scope__c) {
            newOpp.Eng_Last_Modified_Date__c = Date.today();
        }

        if (newOpp.OwnerId != oldOpp.OwnerId) {
            oppsWithOwnerChanged.add(newOpp);
            newOwnersIds.add(newOpp.OwnerId);
        }
        
        if (newOpp.License_Forecast__c != oldOpp.License_Forecast__c) {
        	newOpp.Bizible_Opportunity_Amount__c = newOpp.License_Forecast__c;
        }
    }

    public void beforeDelete (SObject so) {
        Opportunity opp = (Opportunity) so;
        if (opp.StageName == 'S6 - Closed Won') {
            opp.addError('Opportunity with stage \'S6 - Closed Won\' cannot be deleted');
        }

    }
    public void afterUndelete (SObject so) {
        Opportunity opp = (Opportunity) so;
        if (opp.End_User_Customer__c != null) {
            partnerAccountIds.add(opp.End_User_Customer__c);
        }
        oppIdsToUpdateOriginalCampaign.add(opp.Id);
    }

    public void afterInsert (SObject so) {
        Opportunity opp = (Opportunity) so;
        newOpportunities.put(opp.Id, opp);
        createExpertNowQuote = true;
        oppIdsToUpdateOriginalCampaign.add(opp.Id);
        String rrStage = PSA_Project_Creation_Settings__c.getInstance().Resource_Request_Stage__c;

        if (opp.End_User_Customer__c != null) {
            partnerAccountIds.add(opp.End_User_Customer__c);
        }
        
        if (opp.SBQQ__PrimaryQuote__c == null && opp.Tx_Incentive__c != null) {
            oppIdsToCreateInI.put(opp.Id, opp);
        }
        if (opp.RecordTypeId != TriggersHelper.renewalOppRecordTypeId && opp.Technical_Executive__c != null || opp.Business_Executive__c != null) {
            oppIdsToUpdateContact.put(opp.Id, opp);
        }
        //Added Null check for failin TestHelper Opp creation - Nogueira
        if (rrStage != null && rrStage.contains(opp.StageName.left(2))) {
            psaCreateResourceOppIds.add(opp.Id);
        }
        if (opp.CampaignId != null) {
            checkCampaignInfluenceoppIdsToSync.add(opp.Id);
        }
        
        if ((opp.DSI__c == null && opp.SBQQ__PrimaryQuote__c != null && opp.Num_Opp_Line_Items__c > 0) &&
            opp.DSI_Loaded__c == false && opp.StageName == 'S6 - Closed Won') {
            oppIdsForDsiLineItems.add(opp.Id);
            if (opp.DSI__c != null) {
                dsiIds.add(opp.DSI__c);
            } else {
                pqIds.add(opp.SBQQ__PrimaryQuote__c);
            }
        }
        if (String.isNotEmpty(opp.Business_Executive__c) || String.isNotEmpty(opp.Technical_Executive__c)) {
            oppListToInsertOCMRecords.add(opp);
            oppIdsForOCM.add(opp.Id);
        }
        if (opp.AccountId != null) {
            if (opp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId) {
                if (opp.Type == 'Cloud Renewal' || opp.Type == 'Term Renewal') {
                    accIdsCSM.add(opp.AccountId);
                    oppsCSM.put(opp.Id, opp);
                }
            } else {
                accIdsCSM.add(opp.AccountId);
                oppsCSM.put(opp.Id, opp);
            }
        }
        newOppsPrimaryOwner.put(opp.Id, opp);
                if (opp.Legal_Representative__c != null || opp.Rev_Rec_Representative__c != null ||
            opp.RVP_Approver__c != null || opp.Services_RVP_Approver__c != null) {
                oppIdsToUpdateQuotes.add(opp.Id); //Sync PB Opportunity - Legal Rev Rec Changed
            }
        if(opp.End_User_Customer__c != null){ //Sync PB Opportunity - Update Partner On Quote
            oppIdsToUpdatePartnerDealQuote.add(opp.Id);
        }
        if(opp.Primary_Services_Director__c != null){ // Sync PB Opportunity - Primary SD - start
            //run flow branch
            params.put('Opportunity_ID',opp.Id);
            Flow.Interview.Opportunity_Primary_SD flow = new Flow.Interview.Opportunity_Primary_SD (params);
            flow.start();

            //update Splits branch
            if(opp.RecordTypeId == TriggersHelper.salesOppRecordTypeId || opp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId){
                params1.put('Opportunity_ID',opp.Id);
                params1.put('New_User_ID', opp.Primary_Services_Director__c);
                params1.put('Old_Old_ID', null);
                Flow.Interview.Manage_Services_Splits flow1 = new Flow.Interview.Manage_Services_Splits (params1);
                flow1.start();
            }
        } // Sync PB Opportunity - Primary SD - end
        if (opp.Customer_Success_Manager__c != null) {
            csmChanged.add(opp);
        }

        if(opp.SBCF_Type_of_Sale__c != null && opp.End_User_Customer__c != null && opp.SBQQ__RenewedContract__c == null && opp.SBQQ__AmendedContract__c == null){
            partnerOpps.put(opp.Id, opp);
        }

        if(opp.SBQQ__AmendedContract__c != null || opp.SBQQ__RenewedContract__c != null){
            opps2carryOverPartnerLinesIds.add(opp.Id);
            opps2carryOverPartnerLines.put(opp.Id, opp);
        }
    }

    public void afterUpdate (SObject oldSo, SObject so) {
        if(OrderProductsRelatedListController.firedFromOI() != null && OrderProductsRelatedListController.firedFromOI()){
    		return;
    	}
        if(ContractTriggerExecutionController.firedFromOrderInsert() != null && ContractTriggerExecutionController.firedFromOrderInsert()){
            return; 
        }
        Opportunity newOpp = (Opportunity) so;
        Opportunity oldOpp = (Opportunity) oldSo;
        isAfterUpdate = true;
        newOpportunities.put(newOpp.Id, newOpp);
        oldOpportunities.put(oldOpp.Id, oldOpp);
        String rrStage = PSA_Project_Creation_Settings__c.getInstance().Resource_Request_Stage__c;

        if (newOpp.End_User_Customer__c != oldOpp.End_User_Customer__c || newOpp.Pipe_K__c != oldOpp.Pipe_K__c
            && oldOpp.End_User_Customer__c != null) {
            partnerAccountIds.add(newOpp.End_User_Customer__c);
        }
        if(newOpp.End_User_Customer__c != null && newOpp.End_User_Customer__c != oldOpp.End_User_Customer__c ){ //Sync PB Opportunity - Update Partner On Quote
            oppIdsToUpdatePartnerDealQuote.add(newOpp.Id); 
        }
        if (newOpp.SBQQ__PrimaryQuote__c == null && newOpp.Tx_Incentive__c != oldOpp.Tx_Incentive__c) {
            oppIdsToCreateInI.put(newOpp.Id, newOpp);
        }
        if (newOpp.RecordTypeId != TriggersHelper.renewalOppRecordTypeId && ((newOpp.Business_Executive__c != null && oldOpp.Business_Executive__c != newOpp.Business_Executive__c) || (newOpp.Technical_Executive__c != null && oldOpp.Technical_Executive__c != newOpp.Technical_Executive__c))) {
            oppIdsToUpdateContact.put(newOpp.Id, newOpp);
        }
        
        if (newOpp.StageName != Constants.OPPORTUNITY_STAGE_CLOSED &&
            newOpp.StageName != Constants.OPPORTUNITY_STAGE_WON &&
            (newOpp.pse__Region__c != oldOpp.pse__Region__c ||
                newOpp.pse__Practice__c != oldOpp.pse__Practice__c ||
                newOpp.pse__Group__c != oldOpp.pse__Group__c)
            ) {
            psaUpdateResourceOppIds.add(newOpp.Id);
        }

        //if stage name switches to match custom setting then create/update resource requests
        //Added Null check for failin TestHelper Opp creation - Nogueira
        if(rrStage != null){
            if ((rrStage.contains(newOpp.StageName.left(2)) && !rrStage.contains(oldOpp.StageName.left(2)) ||
                (rrStage.contains(newOpp.StageName.left(2)) && newOpp.CloseDate != oldOpp.CloseDate) ||
                (rrStage.contains(newOpp.StageName.left(2)) && newOpp.pse__Region__c != oldOpp.pse__Region__c) ||
                (rrStage.contains(newOpp.StageName.left(2)) && newOpp.pse__Practice__c != oldOpp.pse__Practice__c) ||
                (rrStage.contains(newOpp.StageName.left(2)) && newOpp.pse__Group__c != oldOpp.pse__Group__c))
                ) {
                psaCreateResourceOppIds.add(newOpp.Id);
            }
        }

        if (newOpp.StageName.containsIgnoreCase(Constants.OPPORTUNITY_STAGE_CLOSED) && !oldOpp.StageName.containsIgnoreCase(Constants.OPPORTUNITY_STAGE_CLOSED)) {
            psaCancelResourceOppIds.add(newOpp.Id);
        }
        if (newOpp.StageName != oldOpp.StageName && (newOpp.StageName == 'S0 - Closed Lost' || newOpp.StageName == 'S6 - Closed Won' || newOpp.StageName == 'S9 - Co-Termed')) {
            recallQuotesForClosedOppIds.add(newOpp.Id);
        }
        if (newOpp.CampaignId != oldOpp.CampaignId) {
            isUpdate = true;
            if (oldOpp.CampaignId != null) {
                checkCampaignInfluencemapCampaignsToRemove.put(newOpp.Id, oldOpp.CampaignId);
            }
            checkCampaignInfluenceoppIdsToSync.add(newOpp.Id);
        }
        if ((newOpp.DSI__c != null || (newOpp.DSI__c == null && newOpp.SBQQ__PrimaryQuote__c != null && newOpp.Num_Opp_Line_Items__c > 0)) &&
            newOpp.DSI_Loaded__c == false && newOpp.StageName == 'S6 - Closed Won' && newOpp.StageName != oldOpp.StageName
            ) {
            oppIdsForDsiLineItems.add(newOpp.Id);

            if (newOpp.DSI__c != null) {
                dsiIds.add(newOpp.DSI__c);
            } else {
                pqIds.add(newOpp.SBQQ__PrimaryQuote__c);
            }
        }

        if (newOpp.CloseDate != oldOpp.CloseDate) {
            opportunitiesChangedCloseDate.add(newOpp.Id);
        }
        if (Approval.isLocked(newOpp.Id)) {
            oppsToUnlock.add(newOpp.Id);
        }
        if (oldOpp.StageName != newOpp.StageName && newOpp.StageName == 'S6 - Closed Won' &&
            ((newOpp.Cloud_Opportunity__c && (newOpp.Platform__c == 'MCE' || newOpp.Platform__c == 'CMC' || newOpp.Platform__c == 'MCS')) || newOpp.Platform__c == 'MCG')
            ) {
            newOnBoardingCases.add(new Case(
                RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('On-boarding').getRecordTypeId(), Affected_Opp__c = newOpp.Id,
                Subject = 'On-boarding Case: ' + newOpp.Name, OwnerId = OpportunityTriggerHelper.cloudOnBoardingQueueId
            ));
        }
        if (oldOpp.StageName != newOpp.StageName) {
            opportunitiesNewStage.add(newOpp);
        }
        if (newOpp.StageName.containsIgnoreCase('S6 - Closed Won') && newOpp.StageName != oldOpp.StageName) {
            oppIdsUpdateAccountCustomer.add(newOpp.AccountId);
        }
        if (oldOpp.AccountId != newOpp.AccountId && newOpp.Platform__c == 'MCI') {
            accChangedOptyListLeanData.add(newOpp);
        }
        if (oldOpp.StageName.containsIgnoreCase('S6 - Closed Won') && !newOpp.StageName.equalsIgnoreCase(oldOpp.StageName)) {
            //Stagename changed from S6 to sth Else
            reopenedOptyList.add(newOpp);
        }

        if (newOpp.StageName == 'S6 - Closed Won' && !newOpp.StageName.equalsIgnoreCase(oldOpp.StageName)) {
            opportunitiesWithNewStatus.add(newOpp.Id);
        }
        //ke=y generation
        if ((newOpp.Contracts_Generated__c && oldOpp.Contracts_Generated__c != newOpp.Contracts_Generated__c) && (newOpp.RecordTypeId == TriggersHelper.salesOppRecordTypeId && newOpp.Software__c > 0 && newOpp.Do_Not_Ship_Key__c == false) || Test.isRunningTest()) {
            oppsForKeyGeneration.add(newOpp);
        }
        if (newOpp.AccountId != oldOpp.AccountId) {
            oppsToUpdateOnAccountChange.add(newOpp);
        }
        if (newOpp.StageName != oldOpp.StageName && (newOpp.RecordTypeId == TriggersHelper.salesOppRecordTypeId || newOpp.Type == 'Term Migration')
            && (newOpp.StageName == 'S3 - Evaluate Options/Proposals' || newOpp.StageName == 'S4 - Resolve Concerns' ||
            newOpp.StageName == 'S5 - Negotiate/Purchase Solution')) {
            oppsForApprovalOnStatusChange.add(newOpp);
        }
        if (newOpp.AccountId != null && oldOpp.AccountId != newOpp.AccountId) {
            if (newOpp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId) {
                if (newOpp.Type == 'Cloud Renewal' || newOpp.Type == 'Term Renewal') {
                    accIdsCSM.add(newOpp.AccountId);
                    oppsCSM.put(newOpp.Id, newOpp);
                }
            } else {
                accIdsCSM.add(newOpp.AccountId);
                oppsCSM.put(newOpp.Id, newOpp);
            }
        }
        if (!OpportunityTeamTriggerHandler.firedFromOTM && newOpp.Customer_Success_Manager__c != null && newOpp.Customer_Success_Manager__c != oldOpp.Customer_Success_Manager__c) {
            csmChanged.add(newOpp);
        }

        //Compare old and new records to check if Business_Executive__c changed and update OCM
        OpportunityTriggerController.checkForOCMChange(oldOpp, newOpp, oppIdsForOCM, oldRelatedContactIdsForOCM, newRelatedContactIdsForOCM, opportunitiesOldOCM, opportunitiesNewOCM);

                if(oldOpp.StageName != newOpp.StageName && newOpp.StageName == 'S6 - Closed Won') {
            oppsToUpdateProjectStatus.add(newOpp.Id);
        }
            if (newOpp.SBQQ__PrimaryQuote__c != null && oldOpp.Ship_Date__c != newOpp.Ship_Date__c && newOpp.Ship_Date__c != null) {
            pQuotesShipDate.add(new SBQQ__Quote__c (Id = newOpp.SBQQ__PrimaryQuote__c, Date_Sync__c = false));
        }
        if (newOpp.SBQQ__PrimaryQuote__c != null && oldOpp.SBQQ__PrimaryQuote__c != newOpp.SBQQ__PrimaryQuote__c) {
            quotesToUpdateReps.add(newOpp.SBQQ__PrimaryQuote__c);
        }
        
        if ((newOpp.Legal_Representative__c != null && oldOpp.Legal_Representative__c != newOpp.Legal_Representative__c) ||
           (newOpp.Rev_Rec_Representative__c != null && oldOpp.Rev_Rec_Representative__c != newOpp.Rev_Rec_Representative__c) ||
           (newOpp.RVP_Approver__c != null && oldOpp.RVP_Approver__c != newOpp.RVP_Approver__c) ||
           (newOpp.Services_RVP_Approver__c != null && oldOpp.Services_RVP_Approver__c != newOpp.Services_RVP_Approver__c)) {
            oppIdsToUpdateQuotes.add(newOpp.Id); //Sync PB Opportunity - Legal Rev Rec Changed
        }
        
        // Sync PB Opportunity - Activate All Contracts
        if (newOpp.Contracts_Generated__c == true && newOpp.Contracts_Generated__c != oldOpp.Contracts_Generated__c) {
            oppIdsToActivateContracts.add(newOpp);
        }
        
        //Sync PB opportunity- Ship date & Ship Date Updated - start
        if (newOpp.key_manually_generated__c == true && newOpp.key_manually_generated__c != oldOpp.key_manually_generated__c
            && newOpp.Software__c > 0) {
                oppsShipDateUpdateSync.add(newOpp);
                oppWithLicense = true;    
            }
        else if (newOpp.StageName == 'S6 - Closed Won' && newOpp.StageName != oldOpp.StageName
                 && (newOpp.Software__c == null || newOpp.Software__c == 0)){
                     oppsShipDateUpdateSync.add(newOpp);
                     oppWOLicense = true;   
                 }
        else if (newOpp.Ship_Date__c != null && newOpp.Ship_Date__c != oldOpp.Ship_Date__c && oldOpp.Ship_Date__c != null){
            oppsShipDateUpdateSync.add(newOpp);
            oppShipDateUpdatedPB = true;   
        } //Sync PB opportunity- Ship date & Ship Date Updated - end
        
        if(newOpp.Primary_Services_Director__c != null && newOpp.Primary_Services_Director__c != oldOpp.Primary_Services_Director__c){ // Sync PB Opportunity - Primary SD - start
            //run flow branch
            params.put('Opportunity_ID',newOpp.Id);
            Flow.Interview.Opportunity_Primary_SD flow = new Flow.Interview.Opportunity_Primary_SD (params);
            flow.start();
            
            //Modify Splits branch
            if(newOpp.RecordTypeId == TriggersHelper.salesOppRecordTypeId || newOpp.RecordTypeId == TriggersHelper.renewalOppRecordTypeId){
                params1.put('Opportunity_ID',newOpp.Id);
                params1.put('New_User_ID', newOpp.Primary_Services_Director__c);
                params1.put('Old_Old_ID', oldOpp.Primary_Services_Director__c);
                Flow.Interview.Manage_Services_Splits flow1 = new Flow.Interview.Manage_Services_Splits (params1);
                flow1.start();
            }
        } // Sync PB Opportunity - Primary SD - end

        /*Case#548825: If the Opp moves to Closed-Lost, re-evaluate Contacts RTM fields
        				When changing related Contacts, evaluate if the old ones still belong to active Opps*/
        if((newOpp.StageName != oldOpp.StageName && newOpp.StageName == Constants.OPPORTUNITY_STAGE_CLOSED)
			|| (newOpp.Billing_Contact__c != oldOpp.Billing_Contact__c) || (newOpp.Business_Executive__c != oldOpp.Business_Executive__c)
            || (newOpp.Champion__c != oldOpp.Champion__c) || (newOpp.Coach__c != oldOpp.Coach__c)
            || (newOpp.ContactId != oldOpp.ContactId) || (newOpp.Economic_Buyer__c != oldOpp.Economic_Buyer__c)
            || (newOpp.Partner_Contact__c != oldOpp.Partner_Contact__c) || (newOpp.SalesLoft1__Primary_Contact__c != oldOpp.SalesLoft1__Primary_Contact__c)
            || (newOpp.Technical_Executive__c != oldOpp.Technical_Executive__c)){
            contactsToEvaluateRTM.add(newOpp.Billing_Contact__c);
            contactsToEvaluateRTM.add(newOpp.Business_Executive__c);
            contactsToEvaluateRTM.add(newOpp.Champion__c);
            contactsToEvaluateRTM.add(newOpp.Coach__c);
            contactsToEvaluateRTM.add(newOpp.ContactId);
            contactsToEvaluateRTM.add(newOpp.Economic_Buyer__c);
            contactsToEvaluateRTM.add(newOpp.Partner_Contact__c);
            contactsToEvaluateRTM.add(newOpp.SalesLoft1__Primary_Contact__c);
            contactsToEvaluateRTM.add(newOpp.Technical_Executive__c);
            contactsToEvaluateRTM.add(oldOpp.Billing_Contact__c);
            contactsToEvaluateRTM.add(oldOpp.Business_Executive__c);
            contactsToEvaluateRTM.add(oldOpp.Champion__c);
            contactsToEvaluateRTM.add(oldOpp.Coach__c);
            contactsToEvaluateRTM.add(oldOpp.ContactId);
            contactsToEvaluateRTM.add(oldOpp.Economic_Buyer__c);
            contactsToEvaluateRTM.add(oldOpp.Partner_Contact__c);
            contactsToEvaluateRTM.add(oldOpp.SalesLoft1__Primary_Contact__c);
            contactsToEvaluateRTM.add(oldOpp.Technical_Executive__c);
            contactsToEvaluateRTM.remove(null);
        }
    }

    public void afterDelete (SObject so) {

    }

    public void andFinally () {
        if(OrderProductsRelatedListController.firedFromOI() != null && OrderProductsRelatedListController.firedFromOI()){
    		return;
    	}
        if(ContractTriggerExecutionController.firedFromOrderInsert() != null && ContractTriggerExecutionController.firedFromOrderInsert()){
            return; 
        }
        List<OpportunityTeamMember> membersToDelete = new List<OpportunityTeamMember>();
        List<OpportunityTeamMember> membersToInsert = new List<OpportunityTeamMember>();
        List<OpportunityTeamMember> membersToUpdate = new List<OpportunityTeamMember>();
        List<Contract> contractsToUpdate = new List<Contract>(); //Sync PB Opportunity - Activate All Contracts
        Map<Id, Account> accountsToUpdate = new Map<Id, Account>();
        //Map<Id, Contact> contactsToUpdate = new Map<Id, Contact>();
        Map<Id, Opportunity> opportunitiesToUpdate = new Map<Id, Opportunity>();
        String currentUserId = UserInfo.getUserId();
        Map<Id, Entitlement> entitlementsToUpdate = new Map<Id, Entitlement>();
        Map<Id, SBQQ__Subscription__c> subscriptionsToUpdate = new Map<Id, SBQQ__Subscription__c>();
        Map<Id, Contact> contactsToUpdate = new Map<Id, Contact>();
        Map<Id, DSI__c> dsisToUpdate = new Map<Id, DSI__c>();
        Map<Id, SBQQ__Quote__c> quotesToUpdate = new Map<Id, SBQQ__Quote__c>();
        Map<Id, Opportunity_Contact_Member__c> ocmToUpdate = new Map<Id, Opportunity_Contact_Member__c>();
        List<Account> queriedAccounts = new List<Account>();
        String accountQueryFields = 'Select Id ';
        String accountQueryCondition = 'FROM ACCOUNT WHERE ';
        Map<Id, Contact> queriedContacts = new Map<Id, Contact>();
        String contactQueryFields = 'Select Id ';
        String contactQueryCondition = 'FROM Contact WHERE';
        Integer contactQueryConditioncounter = 0;
        Map<Id, SBQQ__Quote__c> queriedQuotes = new Map<Id, SBQQ__Quote__c>();
        String quoteQueryFields = 'SELECT Id';
        String quoteQueryCondition = 'FROM SBQQ__Quote__c WHERE ';
        Map<Id, Opportunity> queriedOpps = new Map<Id, Opportunity>();
        String oppQueryFields = 'SELECT Id';
        String oppQueryCondition = 'FROM Opportunity WHERE';

        if(contactsToEvaluateRTM != null && !contactsToEvaluateRTM.isEmpty()){
            oppQueryFields += ', StageName, ContactId, Billing_Contact__c, Billing_Contact__r.L_Status__c, Business_Executive__c, Business_Executive__r.L_Status__c';
            oppQueryFields += ', Champion__c, Champion__r.L_Status__c, Coach__c, Coach__r.L_Status__c, Economic_Buyer__c, Economic_Buyer__r.L_Status__c';
            oppQueryFields += ', Partner_Contact__c, Partner_Contact__r.L_Status__c, SalesLoft1__Primary_Contact__c, SalesLoft1__Primary_Contact__r.L_Status__c';
            oppQueryFields += ', Technical_Executive__c, Technical_Executive__r.L_Status__c ';
            oppQueryCondition += ' ((Billing_Contact__c IN: contactsToEvaluateRTM ';
            oppQueryCondition += ' OR Business_Executive__c IN: contactsToEvaluateRTM ';
            oppQueryCondition += ' OR Champion__c IN: contactsToEvaluateRTM ';
            oppQueryCondition += ' OR Coach__c IN: contactsToEvaluateRTM ';
            oppQueryCondition += ' OR ContactId IN: contactsToEvaluateRTM ';
            oppQueryCondition += ' OR Economic_Buyer__c IN: contactsToEvaluateRTM ';
            oppQueryCondition += ' OR Partner_Contact__c IN: contactsToEvaluateRTM ';
            oppQueryCondition += ' OR SalesLoft1__Primary_Contact__c IN: contactsToEvaluateRTM ';
            oppQueryCondition += ' OR Technical_Executive__c IN: contactsToEvaluateRTM)) ';
        }


        if(opps2carryOverPartnerLines != null && !opps2carryOverPartnerLines.isEmpty()){
            oppQueryFields += ', SBQQ__RenewedContract__r.SBQQ__Opportunity__c, SBQQ__AmendedContract__r.SBQQ__Opportunity__c, SBQQ__RenewedContract__c, SBQQ__AmendedContract__c';
            oppQueryCondition += ' OR Id IN :opps2carryOverPartnerLinesIds';
        }
        
        if ((oppQueryFields + oppQueryCondition).containsIgnoreCase('WHERE OR') || (oppQueryFields + oppQueryCondition).containsIgnoreCase('WHERE (')) {
            String tempQuery = oppQueryFields +' '+ oppQueryCondition;
            tempQuery = tempQuery.replace('WHERE OR', 'WHERE');
            for (Opportunity obj : (List<Opportunity>) Database.query(tempQuery)) {
                queriedOpps.put(obj.Id, obj);
            }
        }

        if (partnerAccountIds != null && !partnerAccountIds.isEmpty()) {
            accountQueryFields += ',Partner_Bookings_L4Q__c, Partner_Bookings_N4Q__c ';
            accountQueryCondition += 'OR Id IN:partnerAccountIds ';
        }
        if (oppIdsUpdateAccountCustomer != null && !oppIdsUpdateAccountCustomer.isEmpty()) {
            // List<Account> accList = [SELECT Id, CustomerSince__c FROM Account WHERE Id IN :accIdSet AND CustomerSince__c = NULL];
            accountQueryFields += ',CustomerSince__c, ';
            accountQueryCondition += 'OR (Id IN:oppIdsUpdateAccountCustomer AND CustomerSince__c = NULL)';
        }
        // for (Account acc : [SELECT Id, Owner.RVP_Approver__c, Owner.RVP_Approver__r.IsActive, District__c FROM Account WHERE Id IN :accountIDSet]) {
        if (accountIDSetForDistrictSalesApprover != null && !accountIDSetForDistrictSalesApprover.isEmpty()) {
            accountQueryFields += ',Owner.RVP_Approver__c, Owner.RVP_Approver__r.IsActive, District__c, OwnerId';
            if(!accountQueryFields.containsIgnoreCase('Name')){
                accountQueryFields += ' ,Name ';
            }
            accountQueryCondition += 'OR Id IN:accountIDSetForDistrictSalesApprover ';
        }
        // for (Account acc: [select Id, Primary_Services_Director__c from Account Where Id In: accUserMap.KeySet() AND Primary_Services_Director__c != null
        if (accUserMaptoSetRPGs != null && !accUserMaptoSetRPGs.isEmpty()) {
            Set<String> filterList = accUserMaptoSetRPGs.keySet();
            accountQueryFields += ',Primary_Services_Director__c ';
            accountQueryCondition += 'OR (Id IN:filterList AND Primary_Services_Director__c != null) ';
        }
        if(oppLegalEntitySync != null && !oppLegalEntitySync.isEmpty()){
            // Sync PB Opportunity - Legal Entity Sync
            // //for (Account acc : [SELECT Id, Address_Number__c, Name  FROM Account WHERE Id IN :accIdsAccCategory]) {
            accountQueryFields += ',Address_Number__c ';
            if(!accountQueryFields.containsIgnoreCase('Name')){
                accountQueryFields += ' ,Name ';
            }
            accountQueryCondition += 'OR Id IN:accIdsAccCategory ';
        } 
        if(oppUpdateAccCategory != null && !oppUpdateAccCategory.isEmpty()){
            // PB Opportunity - Account Category
            // //for (Account acc : [SELECT Id, Account_Category_F__c, Customer_Status1__c  FROM Account WHERE Id IN :accIdsAccCategory]) {
            //516685 added missing field in query, because it was failing to execute 'Account_Category__c'
            accountQueryFields += ',Account_Category_F__c, Account_Category__c, Customer_Status1__c ';
            accountQueryCondition += 'OR Id IN:accIdsAccCategory ';
        }    
        if (parentAccIDSetForCPQRenewal != null && !parentAccIDSetForCPQRenewal.isEmpty()) {
            //for (Account acc : [SELECT Id, SBCF_PO_Required__c FROM Account WHERE Id IN :parentAccIDSet]) {
            accountQueryFields += ',SBCF_PO_Required__c ';
            accountQueryCondition += 'OR Id IN:parentAccIDSetForCPQRenewal ';
        }
        if (accountIdsForManagedCloudAndPO != null && !accountIdsForManagedCloudAndPO.isEmpty()) {
            //[SELECT Id, PO_required__c FROM Account WHERE Id IN :accountIds]
            accountQueryFields += accountQueryFields.containsIgnoreCase('Id') ? ',PO_required__c ' : ',PO_required__c, Id ';
            accountQueryCondition += 'OR Id IN:accountIdsForManagedCloudAndPO ';
        }
        if (accIdsPSD != null && !accIdsPSD.isEmpty()){ 
            if(!accountQueryFields.contains('Primary_Services_Director__c')) {
            accountQueryFields += ',Primary_Services_Director__c ';
            }
            accountQueryFields += ',Customer_Success_Manager__c ';
            accountQueryCondition += 'OR Id IN:accIdsPSD ';
        }
        if (accIdsCSM != null && !accIdsCSM.isEmpty()) {
            accountQueryFields += ',(SELECT Id, UserId, TeamMemberRole, OpportunityAccessLevel FROM AccountTeamMembers WHERE TeamMemberRole = \'Customer Success Manager\') ';
            accountQueryCondition += 'OR Id IN:accIdsCSM ';
        }
        if (PAccountCategoryAccs != null && !PAccountCategoryAccs.isEmpty()) {
            accountQueryFields += ',AccountCategoryShort__c ';
            accountQueryCondition += 'OR (Id IN:PAccountCategoryAccs AND AccountCategoryShort__c IN:PAccountCategoryValues) ';
        }
        if ((accountQueryFields + accountQueryCondition).containsIgnoreCase('WHERE OR Id') || (accountQueryFields + accountQueryCondition).containsIgnoreCase('WHERE OR (Id')) {
            String tempQuery = accountQueryFields.removeEnd(', ') + ' ' + accountQueryCondition;
            tempQuery = tempQuery.replace('WHERE OR Id', 'WHERE Id');
            tempQuery = tempQuery.replace('WHERE OR (Id', 'WHERE (Id');
                queriedAccounts = Database.query(tempQuery);
        }

        if (newOnBoardingCases != null && !newOnBoardingCases.isEmpty()) {
            //                Id runningUserContactId = [SELECT Id FROM Contact WHERE pse__Salesforce_User__c = :UserInfo.getUserId()].Id;
            contactQueryCondition += ' OR pse__Salesforce_User__c = :currentUserId ';
            contactQueryConditioncounter++ ;
        }
        if (contactIdsForBillingFields != null && !contactIdsForBillingFields.isEmpty()) {
            // Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id, Email, FirstName, LastName FROM Contact WHERE Id IN :contactIds]);
            contactQueryFields += ', Email, FirstName, LastName';
            contactQueryCondition += ' OR Id IN:contactIdsForBillingFields ';
            contactQueryConditioncounter++ ;
        }

        if (contactQueryConditioncounter > 0) {
            if (contactQueryConditioncounter == 1) {
                contactQueryCondition = contactQueryCondition.removeStart(' OR');
            }
            String tempQuery = contactQueryFields +' '+ contactQueryCondition;
            tempQuery = tempQuery.replace('WHERE OR', 'WHERE');
            for (Contact obj : (List<Contact>) Database.query(tempQuery)) {
                queriedContacts.put(obj.Id, obj);
            }
        }
        //Related Quotes
        if (newPrimaryQuoteIds != null && !newPrimaryQuoteIds.isEmpty()) {
            // Map<Id, SBQQ__Quote__c> quoteMap = new Map<Id, SBQQ__Quote__c>([SELECT Id, Multi_Year_Contract_Quote__c FROM SBQQ__Quote__c WHERE Id IN : newPrimaryQuoteIds]);
            quoteQueryFields += ',Multi_Year_Contract_Quote__c';
            quoteQueryCondition += ' OR Id IN: newPrimaryQuoteIds';
        }
        if ((quoteQueryFields + quoteQueryCondition).containsIgnoreCase('WHERE OR')) {
            String tempQuery = quoteQueryFields + ' ' + quoteQueryCondition;
            tempQuery = tempQuery.replace('WHERE OR', 'WHERE');
            queriedQuotes = new Map<Id, SBQQ__Quote__c>((List<SBQQ__Quote__c>)Database.query(tempQuery)); 
        }

        Map<Id, Contract> relatedContracts = queryRelatedContracts();

        //querying account
        //for (Account acc : [SELECT Id, SBCF_PO_Required__c FROM Account WHERE Id IN :parentAccIDSet]) {
        if (oppsForCPQRenewal != null && !oppsForCPQRenewal.isEmpty()) {
            OpportunityTriggerController.OpportunityCPQRenewal(oppsForCPQRenewal, parentContractIdSetForCPQRenewal, parentAccIDSetForCPQRenewal, queriedAccounts);
        }
        if (oppListToMatchCurrency != null && !oppListToMatchCurrency.isEmpty()) {
            OpportunityTriggerController.regionCurrencyMatcher(oppListToMatchCurrency);
            OpportunityTriggerController.updateConversionRate(oppListToMatchCurrency);
        }
        // query and update account
        //  for (Account acc : [SELECT Id, Partner_Bookings_L4Q__c, Partner_Bookings_N4Q__c FROM Account WHERE Id IN:partnerAccounts]) {
        if (partnerAccountIds != null && !partnerAccountIds.isEmpty()) {
            OpportunityTriggerController.RollupPartnerAccounts(partnerAccountIds, accountsToUpdate, queriedAccounts);
        }
        //update opportunity and SSO_Contact__c
        if (oppIdsToUpdateOriginalCampaign != null && !oppIdsToUpdateOriginalCampaign.isEmpty()) {
            OpportunityTriggerController.UpdateOriginalCampaign(oppIdsToUpdateOriginalCampaign, opportunitiesToUpdate);
        }
        if (oppIdsToCreateInI != null && !oppIdsToCreateInI.isEmpty()) {
            OpportunityTriggerController.CreateTNI(oppIdsToCreateInI);
        }
        //insert SSO_Contact__c
        if (oppIdsToUpdateContact != null && !oppIdsToUpdateContact.isEmpty()) {
            OpportunityTriggerController.updateOppContactsonExecChange(oppIdsToUpdateContact);
        }
        if (oppsWithOwnerChanged != null && !oppsWithOwnerChanged.isEmpty()) {
            OpportunityTriggerController.processOppsWithChangedOwner(oppsWithOwnerChanged,newOwnersIds);
        }
        if (psaCreateResourceOppIds != null && !psaCreateResourceOppIds.isEmpty()) {
            PSAOpportunityTriggerHelper.CreateResourceRequests(psaCreateResourceOppIds, false); //insert and update RRs
        }
        if (psaUpdateResourceOppIds != null && !psaUpdateResourceOppIds.isEmpty()) {
            PSAOpportunityTriggerHelper.CreateResourceRequests(psaUpdateResourceOppIds, false); //insert and update RRs
        }
        if (oppsWithStageChanged != null && !oppsWithStageChanged.isEmpty()) {
            OpportunityTriggerController.processOppsWithChangedStatus(oppsWithStageChanged,oldOpportunities);
        }
        if (psaCancelResourceOppIds != null && !psaCancelResourceOppIds.isEmpty()) {
            PSAOpportunityTriggerHelper.CancelResourceRequests(psaCancelResourceOppIds);
        }
        if (recallQuotesForClosedOppIds != null && !recallQuotesForClosedOppIds.isEmpty()) {
            OpportunityTriggerController.recallQuotesForClosedOpp(recallQuotesForClosedOppIds);
        }
        if (checkCampaignInfluenceoppIdsToSync != null && !checkCampaignInfluenceoppIdsToSync.isEmpty() && checkCampaignInfluencemapCampaignsToRemove != null && !checkCampaignInfluencemapCampaignsToRemove.isEmpty() && !OpportunityTriggerHandler.firstRunInfluence) {
            OpportunityTriggerHandler.firstRunInfluence = true;
            OpportunityTriggerController.createOpportunityInfluence(checkCampaignInfluenceoppIdsToSync, checkCampaignInfluencemapCampaignsToRemove, isUpdate);
            //ID jobID = System.enqueueJob(new OpportunityInfluenceQueuableJob(oppIdsToSync,mapCampaignsToRemove));
           // Id jobID = System.enqueueJob(new OpportunityInfluenceQueuableJob(checkCampaignInfluenceoppIdsToSync, checkCampaignInfluencemapCampaignsToRemove, isUpdate));
        }
        //upsert opportunities
        if (oppIdsForDsiLineItems != null && !oppIdsForDsiLineItems.isEmpty()) {
            OpportunityTriggerController.createDSILineItems(dsiIds, pqIds, oppIdsForDsiLineItems, newOpportunities, opportunitiesToUpdate);
        }
        if (opportunitiesChangedCloseDate != null && !opportunitiesChangedCloseDate.isEmpty() && !System.isBatch()) {
            OpportunityTriggerController.extendSEProjects(opportunitiesChangedCloseDate);
        }
        if (oppsToUnlock != null && !oppsToUnlock.isEmpty()) {
            OpportunityTriggerController.unlockRecords(oppsToUnlock);
        }
        //query contact
        //                Id runningUserContactId = [SELECT Id FROM Contact WHERE pse__Salesforce_User__c = :UserInfo.getUserId()].Id;
        if (newOnBoardingCases != null && !newOnBoardingCases.isEmpty()) {
            OpportunityTriggerController.createOnBoardingCases(newOnBoardingCases, queriedContacts.values());
        }
        //query and update opp contact member
        if (opportunitiesNewStage != null && !opportunitiesNewStage.isEmpty()) {
            OpportunityTriggerController.syncOppStage(opportunitiesNewStage, ocmToUpdate);
        }
        //query and update account
        //        List<Account> accList = [SELECT Id, CustomerSince__c FROM Account WHERE Id IN :accIdSet AND CustomerSince__c = NULL];
        if (oppIdsUpdateAccountCustomer != null && !oppIdsUpdateAccountCustomer.isEmpty()) {
            OpportunityTriggerController.updateAccountCustomerSince(accountsToUpdate, queriedAccounts);
        }
        if (populateApproversOppList != null && !populateApproversOppList.isEmpty()) {
            // this method can be combined into one to save querries
            OpportunityTriggerController.populateApproversAndGeographyFields(populateApproversOppList, districtIDSet);
            //OpportunityTriggerController.setGeographyFields(populateApproversOppList, districtIDSet);
        }
        if (oppUpdateAccCategory != null && !oppUpdateAccCategory.isEmpty()){// Sync PB Opportunity - Account Category
            OpportunityTriggerController.updateAccountCategoryOpportunity(oppUpdateAccCategory, queriedAccounts, true);
        }
        if (oppLegalEntitySync != null && !oppLegalEntitySync.isEmpty()){// Sync PB Opportunity - Legal Entity Sync
            OpportunityTriggerController.updateAccountCategoryOpportunity(oppLegalEntitySync, queriedAccounts, false);
        }
        // querying account
        // for (Account acc : [SELECT Id, Owner.RVP_Approver__c, Owner.RVP_Approver__r.IsActive, District__c FROM Account WHERE Id IN :accountIDSet]) {vv
        if (populateDistrictSalesApproversOppList != null && !populateDistrictSalesApproversOppList.isEmpty()) {
            OpportunityTriggerController.populateDistrictSalesApprover(populateDistrictSalesApproversOppList, queriedAccounts, isAccountUpdate, districtIDSet);
        }

        //querying contacts
        // Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id, Email, FirstName, LastName FROM Contact WHERE Id IN :contactIds]);
        if (oppListToSetBillingContactFields != null && oppListToSetBillingContactFields.isEmpty() && contactIdsForBillingFields != null && !contactIdsForBillingFields.isEmpty()) {
            OpportunityTriggerController.setBillingContactFields(oppListToSetBillingContactFields, contactIdsForBillingFields, queriedContacts);
        }

        // querying contact and Account
        // for (Account acc: [select Id, Primary_Services_Director__c from Account Where Id In: accUserMap.KeySet() AND Primary_Services_Director__c != null
        if (usrIdsToSetRPGs != null && !usrIdsToSetRPGs.isEmpty()) {
            OpportunityTriggerController.SetRPGs(oppsToSetRpg, usrIdsToSetRPGs, accUserMaptoSetRPGs, isUpdate, oldOpportunities, queriedAccounts);
        }
        if (renewalOpps != null && !renewalOpps.isEmpty()) {
            OpportunityTriggerController.namingMaintenanceRenewalOptys(renewalOpps, renewalContractIDSet);
        }
        //update sub, dsi and entitlement
//        if (mciDsiIds != null && !mciDsiIds.isEmpty()) {
//            OpportunityTriggerController.updateEntitlementEndDateFOrTrialMCIDSI(mciDsiIds, entitlementsToUpdate, subscriptionsToUpdate, dsisToUpdate);
//        }
//        if (dsiIdsToTerminate != null && !dsiIdsToTerminate.isEmpty()) {
//            OpportunityTriggerController.terminateMCIDSITrials(dsiIdsToTerminate);
//        }
        if (oppIdsForOCM != null && !oppIdsForOCM.isEmpty() && opportunitiesOldOCM != null && !opportunitiesOldOCM.isEmpty() && oldRelatedContactIdsForOCM != null && !oldRelatedContactIdsForOCM.isEmpty()) {
            OpportunityTriggerController.removeOCMRecords(opportunitiesOldOCM, oppIdsForOCM, oldRelatedContactIdsForOCM);
        }
        if (oppIdsForOCM != null && !oppIdsForOCM.isEmpty() && opportunitiesNewOCM != null && !opportunitiesNewOCM.isEmpty() && newRelatedContactIdsForOCM != null && !newRelatedContactIdsForOCM.isEmpty()) {
            OpportunityTriggerController.createOCMRecords(opportunitiesNewOCM, oppIdsForOCM, newRelatedContactIdsForOCM);
        }
        if (oppListToInsertOCMRecords != null && !oppListToInsertOCMRecords.isEmpty() && oppIdsForOCM != null && !oppIdsForOCM.isEmpty()) {
            OpportunityTriggerController.createOCMRecords(oppListToInsertOCMRecords, null, null);
        }
        //update sub and entitlement
        if (accChangedOptyListLeanData != null && !accChangedOptyListLeanData.isEmpty() && OpportunityTriggerController.executionFirstRun == true) {
            OpportunityTriggerController.executionFirstRun = false;
            OpportunityTriggerController.updateMCIOptyAccountId(accChangedOptyListLeanData, entitlementsToUpdate, subscriptionsToUpdate, dsisToUpdate, quotesToUpdate);
        }
        if (reopenedOptyList != null && !reopenedOptyList.isEmpty() && OpportunityTriggerHandler.unexecutionFirstRun) {
            OpportunityTriggerController.optyReopenedAlert(reopenedOptyList);
        }
        if (opportunitiesWithNewStatus != null && !opportunitiesWithNewStatus.isEmpty()) {
            OpportunityTriggerController.updateRelatedOpportunityTeamMembers(opportunitiesWithNewStatus);
        }
        if (oppsForKeyGeneration != null && !oppsForKeyGeneration.isEmpty()) {
            OpportunityTriggerController.keyGeneration(oppsForKeyGeneration);
        }
        //update quote dsi entitlemet
        if (oppsToUpdateOnAccountChange != null && !oppsToUpdateOnAccountChange.isEmpty()) {
            OpportunityTriggerController.updateRelatedObjectsOnAccountChange(oppsToUpdateOnAccountChange, entitlementsToUpdate, dsisToUpdate, quotesToUpdate);
        }
        if (oppsForApprovalOnStatusChange != null && !oppsForApprovalOnStatusChange.isEmpty() && !FeatureManagement.checkPermission('ITS_Data_Manager')) {
            OpportunityTriggerController.approvalonStatusChange(oppsForApprovalOnStatusChange);
        }
        if (oppsForaccountIdsForManagedCloudAndPO != null && !oppsForaccountIdsForManagedCloudAndPO.isEmpty()) {
            OpportunityTriggerController.setMSTRManagedCloudAndPO(oppsForaccountIdsForManagedCloudAndPO, accountIdsForManagedCloudAndPO, queriedAccounts);
        }
        if (oppsPSD != null && !oppsPSD.isEmpty()) {
            OpportunityTriggerController.insertPSDandCSMTeamMembersFromAccount(new Map<Id, Account>(queriedAccounts), oppsPSD);
        }
        if (accIdsCSM != null && !accIdsCSM.isEmpty() && oppsCSM != null && !oppsCSM.isEmpty()) {
            OpportunityTriggerController.insertCSMTeamMembersFromAccount(new Map<Id, Account>(queriedAccounts), oppsCSM, membersToInsert);
        }
        if (csmChanged != null && !csmChanged.isEmpty()){
            OpportunityTriggerController.insertCSMTeamMembersFromOpp(csmChanged, membersToInsert);
        }
        if (oppsToUpdateProjectStatus != null && !oppsToUpdateProjectStatus.isEmpty()) {
            OpportunityTriggerController.updateProjectStatusOnOppClose(oppsToUpdateProjectStatus);
        }

        ///query contact account
        if (newOpportunities != null && !newOpportunities.isEmpty()) {
            if (processExpertNow != null && processExpertNow) {
                ExpertNowOpportunityTriggerHandler.processExpertNowOpp(newOpportunities.values());
            }
            if (createExpertNowQuote != null && createExpertNowQuote) {
                ExpertNowOpportunityTriggerHandler.createExperNowQuote(newOpportunities.values());
            }
        }
        //update quote dsi entitlement , its future call and cn be ignored for now
        if (oldOpportunities != null && !oldOpportunities.isEmpty() && isAfterUpdate != null && isAfterUpdate) {
            OpportunityTriggerHandler.seChangeSubmitApproval(oldOpportunities, newOpportunities);
            OpportunityTriggerHandler.dealTeamChangeSubmitApproval(oldOpportunities, newOpportunities);
            OpportunityTriggerController.CLDRenentitlement(newOpportunities.values(), oldOpportunities);
        }
        if (quotesToUpdateReps != null && !quotesToUpdateReps.isEmpty()) {
            OpportunityTriggerController.updateRepresentativesOnQuote(quotesToUpdate, quotesToUpdateReps);
        }
        if (oppIdsToUpdateQuotes != null && !oppIdsToUpdateQuotes.isEmpty()) { //Sync PB Opportunity - Legal Rev Rec Changed
            OpportunityTriggerController.updateRepsOnQuote(quotesToUpdate, oppIdsToUpdateQuotes, true);
        }
        if (oppIdsToUpdatePartnerDealQuote != null && !oppIdsToUpdatePartnerDealQuote.isEmpty()) { //Sync PB Opportunity - Update Partner On Quote
            OpportunityTriggerController.updateRepsOnQuote(quotesToUpdate, oppIdsToUpdatePartnerDealQuote, false);
        }
        if(oppIdsToActivateContracts != null && !oppIdsToActivateContracts.isEmpty()){ //Sync PB Opportunity - Activate All Contracts
            OpportunityTriggerController.activateAllContracts(contractsToUpdate, oppIdsToActivateContracts);
        }
        if(oppsShipDateUpdateSync != null && !oppsShipDateUpdateSync.isEmpty()){//Sync PB opportunity- Ship date & Ship Date Updated
            OpportunityTriggerController.updateShipDateQuote(quotesToUpdate, oppsShipDateUpdateSync, oppWithLicense, oppWOLicense, oppShipDateUpdatedPB);
        }
        if (newOppsPrimaryOwner != null && !newOppsPrimaryOwner.isEmpty()) {
            membersToUpdate.addAll(OpportunityTriggerController.primaryOpportunityOwners(newOppsPrimaryOwner));
        }
                if (primaryQuoteChanged != null && !primaryQuoteChanged.isEmpty()) {
            OpportunityTriggerController.updateMYCFlag(primaryQuoteChanged, queriedQuotes);
        }

        if (oppsWithPlatformChanged != null && !oppsWithPlatformChanged.isEmpty()){
            OpportunityTriggerController.setRenewalOppName(oppsWithPlatformChanged, relatedContracts);
        }

        if (contactsToEvaluateRTM != null && !contactsToEvaluateRTM.isEmpty()) {
            OpportunityTriggerController.evaluateContactsRTM(contactsToUpdate, contactsToEvaluateRTM, queriedOpps);
        }

        if (ocmToUpdate != null && !ocmToUpdate.isEmpty()) {
            Database.update(ocmToUpdate.values());
        }
        if (accountsToUpdate != null && !accountsToUpdate.isEmpty()) {
            Database.update(accountsToUpdate.values());
        }
        if (dsisToUpdate != null && !dsisToUpdate.isEmpty()) {
            Database.update(dsisToUpdate.values());
        }
        if (subscriptionsToUpdate != null && !subscriptionsToUpdate.isEmpty()) {
            Database.update(subscriptionsToUpdate.values());
        }
        if (entitlementsToUpdate != null && !entitlementsToUpdate.isEmpty()) {
            Database.update(entitlementsToUpdate.values());
        }
        if (quotesToUpdate != null && !quotesToUpdate.isEmpty()) {
            Database.update(quotesToUpdate.values());
        }
        if (contactsToUpdate != null && !contactsToUpdate.isEmpty()) {
            Database.update(contactsToUpdate.values());
        }
        if (membersToDelete != null && !membersToDelete.isEmpty()) {
            Database.delete(membersToDelete);
        }
        if (membersToInsert != null && !membersToInsert.isEmpty()) {
            Database.insert(membersToInsert);
        }
        if (membersToUpdate != null && !membersToUpdate.isEmpty()) {
            Database.update(membersToUpdate);
        }
        if (contractsToUpdate != null && !contractsToUpdate.isEmpty()) { //Sync PB Opportunity - Activate All Contracts
            Database.update(contractsToUpdate);
        }
        if (opportunitiesToUpdate != null && !opportunitiesToUpdate.isEmpty()) {
            List<Opportunity> oppsList = new List<Opportunity>();
            for (Opportunity opp : opportunitiesToUpdate.values()) {
                Opportunity obj = new Opportunity();
                obj = opp.clone();
                obj.Id = opp.Id;
                oppsList.add(obj);
            }
            Database.update(oppsList);
        }
        /*if(pQuotesShipDate != null && !pQuotesShipDate.isEmpty()){
            OpportunityTriggerController.uncheckDateSync(pQuotesShipDate);
        }*/

        //516152 - Nogueira Matias
        if(amendmentOpps != null && !amendmentOpps.isEmpty()){
            OpportunityTriggerHandler.updateAmendmentName(amendmentOpps, amendmentOppsId);
        }
        //542249 - Yogesh Sharma
        if(opportunitiesToCheckNewBusiness != null && !opportunitiesToCheckNewBusiness.isEmpty()){
            OpportunityTriggerController.updateNewBusiness(opportunitiesToCheckNewBusiness,new Map<Id, Account>(queriedAccounts));
        }

        if(partnerOpps != null && !partnerOpps.isEmpty()){
            OpportunityTriggerController.createPartnerLine(partnerOpps);
        }
        if(opps2carryOverPartnerLines != null && !opps2carryOverPartnerLines.isEmpty()){
            OpportunityTriggerController.carryPartnerLinesOver(opps2carryOverPartnerLines, queriedOpps);
        }

    }

    private Map<Id, Contract> queryRelatedContracts () {
        String contractFields = '';

        if (queriedContractIds != null && !queriedContractIds.isEmpty()) {
            contractFields += 'QuoteNumber__c,';
        }

        Set<String> contractFieldsSet = new Set<String>(contractFields.split(','));
        contractFieldsSet.remove('Id');
        contractFieldsSet.remove(null);
        contractFields = String.join(new List<String>(contractFieldsSet), ',');

        if (String.isBlank(contractFields)) {
            return new Map<Id, Contract>();
        }

        return new Map<Id, Contract>((List<Contract>) Database.query('SELECT ' + contractFields + ' ' +
            'FROM Contract ' +
            'WHERE Id IN :queriedContractIds'
        ));
    }

}