/************************************* MODIFICATION LOG ********************************************************************************************
* TaskTrigger 
*
* DESCRIPTION : Trigger for TaskTriggerHelper class with casecommunication and taskmilestone logics
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Amritha Buddharaju            4/27/2016                       -- Original Version     
* Alla Kiyashko                 5/21/2018                       -- Added logic to update Lead and Contact (WhoId) with rollup of Engagement score                                      
*/

trigger TaskTrigger on Task (before insert, before update, after insert, after update, after delete, before delete) {

    if (Trigger.isBefore && Trigger.isDelete) {
        TaskTriggerHelper.preventDefectDeletion(Trigger.old);
    }

    if (Trigger.isBefore && Trigger.isInsert) {
        TaskTriggerHelper.CaseCommunication(Trigger.new);
        TaskTriggerHelper.assignDefectToDeveloper(Trigger.new);
        TaskTriggerHelper.updateTaskFields(Trigger.new, Trigger.oldMap, Trigger.isInsert, Trigger.isUpdate);
    }
    
    if (Trigger.isBefore && Trigger.isUpdate) {
        TaskTriggerHelper.updateTaskFields(Trigger.new, Trigger.oldMap, Trigger.isInsert, Trigger.isUpdate);
    }

    if (Trigger.isAfter && (Trigger.isInsert || Trigger.isUpdate)) {
        TaskTriggerHelper.TaskMilestone(Trigger.new, Trigger.oldMap, Trigger.isInsert, Trigger.isUpdate);
    }

    if (Trigger.isAfter && (Trigger.isInsert || Trigger.isDelete || Trigger.isUpdate)) {
        //Rollup task on a case helper
        TaskRollupHelper.rollup(Trigger.new, Trigger.oldMap, Trigger.isInsert, Trigger.isUpdate, Trigger.isDelete, Trigger.isUndelete);

        //Count Convers on a case
        TaskTriggerHelper.rollupConvs((Trigger.isInsert || Trigger.isUpdate ? Trigger.new : Trigger.old), Trigger.oldMap, Trigger.isUpdate, Trigger.isInsert, Trigger.isDelete);

        //Count Tasks on a lead
        TaskTriggerHelper.rollupLeads((Trigger.isInsert || Trigger.isUpdate ? Trigger.new : Trigger.old), Trigger.oldMap, Trigger.isUpdate, Trigger.isInsert, Trigger.isDelete);

        //update engagement score on qualified leads and contacts
        TaskTriggerHelper.updateEngagementScore((Trigger.isInsert || Trigger.isUpdate ? Trigger.new : Trigger.old), Trigger.oldMap, Trigger.isUpdate, Trigger.isInsert, Trigger.isDelete);
    }

}