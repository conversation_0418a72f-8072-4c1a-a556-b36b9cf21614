<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>SBCF_Brazil_Tax_Rate__c</fullName>
    <externalId>false</externalId>
    <formula>
    IF( AND(
            NOT(ISBLANK(SBQQ__Quote__r.SBQQ__PriceBook__c)),
            OR (SBQQ__Quote__r.SBQQ__PriceBook__r.Name = &apos;Standard 2019 Brazil&apos;,
                SBQQ__Quote__r.SBQQ__PriceBook__r.Name = &apos;2. Brazil&apos;, 
                SBQQ__Quote__r.SBQQ__PriceBook__r.Name = &apos;Legacy Brazil&apos;, 
                AND(ISPICKVAL(SBQQ__Quote__r.CurrencyIsoCode, &apos;BRL&apos;),
                    OR(SBQQ__Quote__r.SBQQ__PriceBook__r.Name = &apos;Standard Price Book&apos;,
                        SBQQ__Quote__r.SBQQ__PriceBook__r.Name = &apos;Legacy Standard&apos;,
                        SBQQ__Quote__r.SBQQ__PriceBook__r.Name = &apos;# Standard&apos;
                    )
                )
            ), 
            NOT(ISBLANK(SBQQ__Product__r.SBCF_Brazil_Tax_Rate__c))
        ),
        SBQQ__Product__r.SBCF_Brazil_Tax_Rate__c ,
        1
    )
    </formula>
    <label>Brazil Tax Rate</label>
    <precision>18</precision>
    <required>false</required>
    <scale>4</scale>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Number</type>
    <unique>false</unique>
</CustomField>
