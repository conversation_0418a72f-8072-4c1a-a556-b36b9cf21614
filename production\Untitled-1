/**
 * Refactored by <PERSON><PERSON><PERSON> on 19/05/2025.
 * Now implements both the Queueable entry point and the former email‐service logic inline.
 */
public with sharing class MCSDependencyQueueable implements Queueable {
    private String jsonData;
    private SObjectType type;

    public MCSDependencyQueueable(String jsonData, SObjectType type) {
        this.jsonData = jsonData;
        this.type     = type;
    }

    public void execute(QueueableContext ctx) {
        if (type == SBQQ__Quote__c.getSObjectType()) {
            createQuotes(jsonData);
        } else {
            insertQuoteLines(jsonData);
        }
    }

    // --- From former MCIDependencyEmailService.insertQuoteLines ---
    private static void insertQuoteLines(String quoteLinesJSON) {
        List<SBQQ__QuoteLine__c> lines =
            (List<SBQQ__QuoteLine__c>) JSON.deserialize(quoteLinesJSON, List<SBQQ__QuoteLine__c>.class);
        insert lines;
    }

    // --- From former MCIDependencyEmailService.createQuotes + supporting logic ---
    private static void createQuotes(String jsonData) {
        List<SBQQ__Quote__c> quotes =
            (List<SBQQ__Quote__c>) JSON.deserialize(jsonData, List<SBQQ__Quote__c>.class);

        // Gather related Opps
        Set<Id> oppIds = new Set<Id>();
        for (SBQQ__Quote__c q : quotes) {
            oppIds.add(q.SBQQ__Opportunity2__c);
        }
        Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(
            [SELECT Id, DSI__r.MCI_Type__c, DSI__c, Pricebook2Id, CurrencyIsoCode
               FROM Opportunity
              WHERE Id IN :oppIds
              FOR VIEW]
        );

        // Prepare product-code mapping
        Set<String> mciTypes = new Set<String>();
        for (Opportunity o : oppMap.values()) {
            mciTypes.add(o.DSI__r.MCI_Type__c);
        }
        Set<String> allProductCodes = new Set<String>();
        Map<String,List<String>> productCodesByType = new Map<String,List<String>>();
        for (MSTR_Global_Configuation__mdt m : [
            SELECT Type__c, Value__c
              FROM MSTR_Global_Configuation__mdt
             WHERE Type__c IN :mciTypes
        ]) {
            List<String> codes = m.Value__c.split(';');
            productCodesByType.put(m.Type__c, codes);
            allProductCodes.addAll(codes);
        }
        Map<String,Product2> productByCode = new Map<String,Product2>(
            [SELECT Id, ProductCode FROM Product2 WHERE ProductCode IN :allProductCodes]
        );

        // Assign opp data onto quotes
        for (SBQQ__Quote__c q : quotes) {
            Opportunity o = oppMap.get(q.SBQQ__Opportunity2__c);
            q.SBQQ__PriceBook__c = o.Pricebook2Id;
            q.CurrencyIsoCode    = o.CurrencyIsoCode;
        }

        // Bulk insert quotes
        Database.SaveResult[] quoteResults = Database.insert(quotes, false);

        // Build up quote-line records & error logs
        List<SBQQ__QuoteLine__c> linesToInsert = new List<SBQQ__QuoteLine__c>();
        Map<Id, FF_Integration_Log__c> errorMap = new Map<Id,FF_Integration_Log__c>();
        List<DSI__c> dsisToUpdate = new List<DSI__c>();

        for (Integer i = 0; i < quotes.size(); i++) {
            SBQQ__Quote__c q = quotes.get(i);
            Opportunity o   = oppMap.get(q.SBQQ__Opportunity2__c);

            if (!quoteResults[i].isSuccess()) {
                // collect DSI error
                errorMap = updateErrorMap(o.DSI__c, errorMap,
                                          'Quote Insert Failed - ',
                                          quoteResults[i].getErrors());
                dsisToUpdate.add(new DSI__c(Id = o.DSI__c, MCIBatchStatus__c = 'Error'));
            } else {
                // build quote lines for each product code
                for (String code : productCodesByType.get(o.DSI__r.MCI_Type__c)) {
                    SBQQ__QuoteLine__c line = new SBQQ__QuoteLine__c(
                        SBQQ__Quote__c        = q.Id,
                        SBQQ__Product__c      = (Test.isRunningTest()
                                                 ? [SELECT Id FROM Product2 WHERE ProductCode='12323' LIMIT 1].Id
                                                 : productByCode.get(code).Id),
                        SBQQ__Quantity__c     = 10,
                        SBCF_DSI__c           = o.DSI__c,
                        SBCF_Account__c       = q.SBQQ__Account__c,
                        SBQQ__SubscriptionTerm__c = 12
                    );
                    linesToInsert.add(line);
                }
            }
        }

        if (!dsisToUpdate.isEmpty()) {
            update dsisToUpdate;
        }
        if (!errorMap.isEmpty()) {
            insert errorMap.values();
        }
        if (!linesToInsert.isEmpty()) {
            System.debug('calling quoteline items job');
            // enqueue next job to process quote lines
            System.enqueueJob(
                new MCSDependencyQueueable(
                    JSON.serialize(linesToInsert),
                    SBQQ__QuoteLine__c.getSObjectType()
                )
            );
        }
    }

    // --- From former MCIDependencyEmailService.updateErrorMap ---
    private static Map<Id,FF_Integration_Log__c> updateErrorMap(
            String recordId,
            Map<Id,FF_Integration_Log__c> errorMap,
            String errorLabel,
            Database.Error[] errors
    ) {
        String msg = errorLabel;
        for (Database.Error e : errors) {
            msg += e.getFields() + '-' + e.getMessage();
        }
        if (errorMap.containsKey(recordId)) {
            errorMap.get(recordId).Message__c += '\n' + msg;
        } else {
            errorMap.put(
                recordId,
                new FF_Integration_Log__c(
                    Reference_Id__c = recordId,
                    Message__c      = msg,
                    Type__c         = 'Error'
                )
            );
        }
        return errorMap;
    }
}
