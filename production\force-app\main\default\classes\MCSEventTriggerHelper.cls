/*
* MCSEventTriggerHelper
* 
* DESCRIPTION : Platform Event Trigger to asynchronously create Entitlement, Opp, Quote
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Mahipal Jat		          	10/06/2025                       		-- Original Version
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Test Class:CreateMCSDSITest
*/

public without sharing class MCSEventTriggerHelper {
    
    @future (callout=true)
    public static void updateAccountOwner (String sObjectId){
        try{
            List<DSI__c> dsiList = [Select Id, Account__c,Account__r.Owner.FirstName, Account__r.Owner.LastName,
                                    Account__r.Owner.Email, Account__r.Name, Account__r.Owner.Department, DSIID__c from DSI__c
                                    where (Account__c =: sObjectId or Id =:sObjectId) and Platform__c includes ('MCS')];
            List<MSTR_Global_Configuation__mdt> defaultOwnerList = [Select value__c from MSTR_Global_Configuation__mdt where developerName = 'MCS_Default_Owner'];
            MSTR_Global_Configuation__mdt MCSApiActive = [Select value__c from MSTR_Global_Configuation__mdt where developerName = 'MCS_API_Active' limit 1];
            List<User> userDefault;
            if(!defaultOwnerList.isEmpty()){
                MSTR_Global_Configuation__mdt defaultUser = defaultOwnerList[0];
                userDefault = [Select Id, FirstName, LastName, email from User where id =: defaultUser.value__c];
            }
            List<FF_Integration_Log__c> failureLogList = new List<FF_Integration_Log__c>();
            system.debug('MCS API Active Flag - ' + MCSApiActive.value__c);
            if(Boolean.valueOf(MCSApiActive.value__c) || Test.isRunningTest()){
                for(DSI__c dsi : dsiList){
                    Http h = new Http();
                    Httprequest updateAccOwnerReq = new Httprequest();
                    CreateMCSDSI.PatchResponseWrapper mcsWrapper = new CreateMCSDSI.PatchResponseWrapper();
                    mcsWrapper.account.accountName = dsi.Account__r.Name;
                    
                    //supply SLS account owner or Default point of contact
                    if(dsi.Account__r.Owner.Department == 'SLS'){
                        mcsWrapper.account.accountExecutive.firstName = dsi.Account__r.Owner.FirstName;
                        mcsWrapper.account.accountExecutive.lastName = dsi.Account__r.Owner.LastName.substringBefore('(').normalizeSpace();
                        mcsWrapper.account.accountExecutive.email = dsi.Account__r.Owner.Email.substringBefore('.invalid');
                    }else{
                        if(!userDefault.isEmpty()){
                            mcsWrapper.account.accountExecutive.firstName = userDefault[0].FirstName.normalizeSpace();
                            mcsWrapper.account.accountExecutive.lastName = userDefault[0].LastName.substringBefore('(').normalizeSpace();
                            mcsWrapper.account.accountExecutive.email = userDefault[0].email.substringBefore('.invalid');
                        }
                    }
                    
                    updateAccOwnerReq.setEndpoint('callout:MCS/api/v1/_internal/environments/DSI/'+dsi.DSIID__c+'/account');
                    updateAccOwnerReq.setHeader('Content-Type','application/json; charset=UTF-8');
                    updateAccOwnerReq.setHeader('Authorization', '{!$Credential.Password}');
                    updateAccOwnerReq.setMethod('PATCH');
                    updateAccOwnerReq.setBody(JSON.serialize(mcsWrapper));
                    system.debug(JSON.serialize(mcsWrapper));
                    HttpResponse resp = h.send(updateAccOwnerReq);
                    
                    if(resp.getStatusCode() != 200){
                        //Failed call to MCs=S - Create Error Log for Investigation
                        FF_Integration_Log__c errorReport = new FF_Integration_Log__c();
                        errorReport.Debug_Message__c = 'MCS Account Update Failed: \n' + resp.getStatusCode() + '\n' + resp.getBody();
                        errorReport.Type__c = 'Error';
                        errorReport.Reference_Id__c = dsi.id;
                        failureLogList.add(errorReport);
                    }
                }
                
                if(!failureLogList.isEmpty()){
                    insert failureLogList;
                }
            }
            
            
        }catch (Exception e){
            system.debug(e.getMessage());
            system.debug(e.getStackTraceString());
            FF_Integration_Log__c errorReport = new FF_Integration_Log__c();
            errorReport.Debug_Message__c = 'MCS Account Update Failed: \n' +e.getMessage() + '\n' + e.getStackTraceString();
            errorReport.Type__c = 'Error';
            errorReport.Reference_Id__c = sObjectId;
            insert errorReport;
        }
    }
    
    @future (callout=true)
    public static void updateEntitlementEndDate (String sObjectId){
        try{
            List<DSI__c> dsiList = [Select Id, MCSActivationDateTime__c,Status__c, MCSExpirationDatetime__c,DSIID__c, Entitlement_End_Date__c   
                                    from DSI__c
                                    where (Account__c =: sObjectId or Id =:sObjectId) 
                                    and Platform__c includes ('MCS')];
            
            MSTR_Global_Configuation__mdt MCSApiActive = [Select value__c from MSTR_Global_Configuation__mdt where developerName = 'MCS_API_Active' limit 1];
            
            List<FF_Integration_Log__c> failureLogList = new List<FF_Integration_Log__c>();
            
            system.debug('MCS API Active Flag - ' + MCSApiActive.value__c);
            
            if(Boolean.valueOf(MCSApiActive.value__c) || Test.isRunningTest()){
                for(DSI__c dsi : dsiList){
                    Http h = new Http();
                    Httprequest updateAccOwnerReq = new Httprequest();
                    CreateMCSDSI.SubscriptionUpdateWrapper mcsWrapper = new CreateMCSDSI.SubscriptionUpdateWrapper();
                    
                    mcsWrapper.activationDate = dsi.MCSActivationDateTime__c != null ? dsi.MCSActivationDateTime__c.format('yyyy-MM-dd\'T\'hh:mm:ss.SSS\'Z\'') : null;
                    if(dsi.Entitlement_End_Date__c != null){
                        Datetime dt = DateTime.newInstance(dsi.Entitlement_End_Date__c.year(),dsi.Entitlement_End_Date__c.month(),dsi.Entitlement_End_Date__c.day());
                        mcsWrapper.expirationDate = dt.format('yyyy-MM-dd\'T\'hh:mm:ss.SSS\'Z\'');
                            if(dsi.Status__c == 'Trial'){
                                mcsWrapper.plan = 'trial';
                            }else if(dsi.Status__c == 'Active'){
                                mcsWrapper.plan = 'paid';
                            }else {
                                mcsWrapper.plan = 'trial';
                            }    
                    }
                    
                    updateAccOwnerReq.setEndpoint('callout:MCS/api/v1/_internal/environments/DSI/'+dsi.DSIID__c+'/subscription');
                    updateAccOwnerReq.setHeader('Content-Type','application/json; charset=UTF-8');
                    updateAccOwnerReq.setHeader('Authorization', '{!$Credential.Password}');
                    updateAccOwnerReq.setMethod('PATCH');
                    updateAccOwnerReq.setBody(JSON.serialize(mcsWrapper));
                    system.debug(JSON.serialize(mcsWrapper));
                    HttpResponse resp = h.send(updateAccOwnerReq);
                    
                    if(resp.getStatusCode() != 200 || test.isRunningTest()){
                        //Failed call to MCS - Create Error Log for Investigation
                        FF_Integration_Log__c errorReport = new FF_Integration_Log__c();
                        errorReport.Debug_Message__c = 'MCS Account Update Failed: \n' + resp.getStatusCode() + '\n' + resp.getBody();
                        errorReport.Type__c = 'Error';
                        errorReport.Reference_Id__c = dsi.id;
                        failureLogList.add(errorReport);
                    }
                }
                
                if(!failureLogList.isEmpty()){
                    insert failureLogList;
                }
            }
            
            
        }catch (Exception e){
            system.debug(e.getMessage());
            system.debug(e.getStackTraceString());
            FF_Integration_Log__c errorReport = new FF_Integration_Log__c();
            errorReport.Debug_Message__c = 'MCS Account Update Failed: \n' +e.getMessage() + '\n' + e.getStackTraceString();
            errorReport.Type__c = 'Error';
            errorReport.Reference_Id__c = sObjectId;
            insert errorReport;
        }
    }
    
    @future (callout=true)
    public static void updateEnvironmentOwner (String sObjectId){
        try{
            List<DSI__c> dsiList = [Select Id, MCSEnvironmentOwner__c,MCSEnvironmentOwner__r.FirstName, MCSEnvironmentOwner__r.LastName,
                                    MCSEnvironmentOwner__r.Email,DSIID__c
                                    from DSI__c
                                    where (Account__c =: sObjectId or Id =:sObjectId) 
                                    and Platform__c includes ('MCS')];
            
            MSTR_Global_Configuation__mdt MCSApiActive = [Select value__c from MSTR_Global_Configuation__mdt where developerName = 'MCS_API_Active' limit 1];
            
            List<FF_Integration_Log__c> failureLogList = new List<FF_Integration_Log__c>();
            
            system.debug('MCS API Active Flag - ' + MCSApiActive.value__c);
            
            if(Boolean.valueOf(MCSApiActive.value__c) || Test.isRunningTest()){
                for(DSI__c dsi : dsiList){
                    if(dsi.MCSEnvironmentOwner__c != null) {
                        Http h = new Http();
                        Httprequest updateAccOwnerReq = new Httprequest();
                        CreateMCSDSI.EnvironmentOwnerWrapper mcsWrapper = new CreateMCSDSI.EnvironmentOwnerWrapper();
                        
                        mcsWrapper.firstName = dsi.MCSEnvironmentOwner__r.FirstName;
                        mcsWrapper.lastName = dsi.MCSEnvironmentOwner__r.LastName;
                        mcsWrapper.email = dsi.MCSEnvironmentOwner__r.Email;
                        
                        updateAccOwnerReq.setEndpoint('callout:MCS/api/v1/_internal/environments/DSI/'+dsi.DSIID__c+'/owner');
                        updateAccOwnerReq.setHeader('Content-Type','application/json; charset=UTF-8');
                        updateAccOwnerReq.setHeader('Authorization', '{!$Credential.Password}');
                        updateAccOwnerReq.setMethod('PUT');
                        updateAccOwnerReq.setBody(JSON.serialize(mcsWrapper));
                        system.debug(JSON.serialize(mcsWrapper));
                        HttpResponse resp = h.send(updateAccOwnerReq);
                        
                        if(resp.getStatusCode() != 200 || test.isRunningTest()){
                            //Failed call to MCS - Create Error Log for Investigation
                            FF_Integration_Log__c errorReport = new FF_Integration_Log__c();
                            errorReport.Debug_Message__c = 'MCS Account Update Failed: \n' + resp.getStatusCode() + '\n' + resp.getBody();
                            errorReport.Type__c = 'Error';
                            errorReport.Reference_Id__c = dsi.id;
                            failureLogList.add(errorReport);
                        }
                    }
                }
                
                if(!failureLogList.isEmpty()){
                    insert failureLogList;
                }
            }
            
            
        }catch (Exception e){
            system.debug(e.getMessage());
            system.debug(e.getStackTraceString());
            FF_Integration_Log__c errorReport = new FF_Integration_Log__c();
            errorReport.Debug_Message__c = 'MCS Account Update Failed: \n' +e.getMessage() + '\n' + e.getStackTraceString();
            errorReport.Type__c = 'Error';
            errorReport.Reference_Id__c = sObjectId;
            insert errorReport;
        }
    }
}