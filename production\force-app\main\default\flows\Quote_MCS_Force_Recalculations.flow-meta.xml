<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>Quote - MCS Force Recalculations {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Quote - MCS Force Recalculations</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Quote</name>
        <label>Update Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>RC__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>SBQQ__Quote__c</object>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>X1min_after</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>quoteID</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <waits>
        <name>X1min_after</name>
        <elementSubtype>WaitDuration</elementSubtype>
        <label>1min after</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Path</defaultConnectorLabel>
        <waitEvents>
            <conditionLogic>and</conditionLogic>
            <connector>
                <targetReference>Update_Quote</targetReference>
            </connector>
            <label>el_0</label>
            <offset>1</offset>
            <offsetUnit>Minutes</offsetUnit>
        </waitEvents>
    </waits>
</Flow>
