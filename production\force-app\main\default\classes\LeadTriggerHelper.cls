/************************************* MODIFICATION LOG ********************************************************************************************
* LeadTriggerHelper
*
* DESCRIPTION : Handles Trigger Operations on Lead:
*               LeadCount on Campaign:Rollup to show count of leads based on originalcampaignsource
*               Autopopulate District on Lead: Autopopulate district on newly created leads
*               Autopopulate Account  on Lead: Autopopulate Account Name on newly created leads
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                  REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Amritha Buddharaju            10/10/2016          - Added Rollups for OriginalCampaignSource
* Amritha Buddharaju            10/28/2016          - Added Autopopulate District on Lead
* Amritha Buddharaju            11/14/2016          - Deleted LeadCount on Campaign converted to batch class
* Amritha Buddharaju            11/18/2016          - Added Autopopulate Account Name on Lead
* Amritha Buddharaju            03/05/2018          - Added Rollups for Leads created in L4Q for each account
* Himanshu Kalra                03/14/2018          - Updated total no. Leads Under Account Rollup Query as per Case - 233786
* Himanshu Kalra                04/27/2018          - Updated logic to get Total no. of Leads by adding more conditions:FirstRespondedDate,Lead CreatedDate. PLease refer report - https://mstr.my.salesforce.com/00O4400000349yv?pv0=001E000000rTQHw
* Alla Kiyashko                 05/24/2018          - Commented out LeadsL4QAccount per changed requirements
* Alla Kiyashko                 08/01/2018          - LeadsRedBoxAccount to populate number of leads in a red campaign - stages L1 - L6
*/

public without sharing class LeadTriggerHelper {

    public static Boolean stopLeadTrigger = true;

    //-----------------Autopopulate District on Lead----------------
    public static void AutopopulateDistrict(List<Lead> newLeads, Boolean isUpdate) {
        Map<String, String> keyDistrictMap = new Map<String, String>();
        for (Lead ld : newLeads) {
            if (ld.District__c == null) {
                keyDistrictMap.put(ld.District_Key__c, null);
            }
        }

        if (!keyDistrictMap.isEmpty()) {
            Map<String, District_Assignment__c> districtAssignmentMap = new Map<String, District_Assignment__c>();
            for (District_Assignment__c da : [SELECT Id, Key__c, District__c FROM District_Assignment__c WHERE Key__c IN:keyDistrictMap.keySet()]) {
                districtAssignmentMap.put(da.Key__c, da);
            }

            List<Lead> leadList = new List<Lead>();
            for (Lead ld : newLeads) {
                if (districtAssignmentMap.containsKey(ld.District_Key__c)) {
                    ld.District__c = districtAssignmentMap.get(ld.District_Key__c).District__c;
                    leadList.add(ld);
                }
            }

            if (isUpdate) {
                stopLeadTrigger = false;
                update leadList;
                stopLeadTrigger = true;
            }
        }
    }

    //-----------------Autopopulate Account Name on a lead based on its account domain --------------
    public static void AutoPopAccountDomain(List<Lead> newLeads, Boolean isUpdate) {
        Map<String, String> domainAccountMap = new Map<String, String>();
        for (Lead ld : newLeads) {
            if (ld.Account_2__c == null) {
                domainAccountMap.put(ld.Domain__c, null);
            }
        }
        if (!domainAccountMap.isEmpty()) {
            for (Account_Domain__c ad : [SELECT Id, Domain__c, Account__c FROM Account_Domain__c WHERE Domain__c IN:domainAccountMap.keySet()]) {
                domainAccountMap.put(ad.Domain__c, ad.Account__c);
            }

            List<Lead> leadList = new List<Lead>();
            Set<Id> leadIds = new Set<Id>();
            for (Lead ld : newLeads) {
                Lead newLeadObj = new Lead(Id = ld.Id);
                if (domainAccountMap.containsKey(ld.Domain__c)) {
                    newLeadObj.Account_2__c = domainAccountMap.get(ld.Domain__c);
                } else {
                    newLeadObj.Account_Apex_Run__c = false;
                }
                leadList.add(newLeadObj);
            }

            if (!leadList.isEmpty()) {
                update leadList;
            }
        }
    }

    /*public static void LeadsRedBoxAccount (List<Lead> newList, Map<Id, Lead> oldMap, Boolean isInsert, Boolean isUpdate, Boolean isDelete, Boolean isUndelete) {

        if (FeatureSwitchManager.isSwitchDisabled('LeadRollUpOnAccount') && !Test.isRunningTest()) {
            System.debug(' disabled');
            return;
        }
        Set<Id> accId = new Set<Id>();
        Set<String> stages = RedReportRollupsCalculate.getStages();
        if (isInsert || isUndelete || isUpdate) {
            for (Lead ld : newList) {
                if (isInsert || isUndelete) {
                    if (ld.Account_2__c != null && stages.contains(ld.BD_Status__c)) {
                        accId.add(ld.Account_2__c);
                    }
                }

                else if (isUpdate) {
                    if ((ld.Account_2__c != oldMap.get(ld.Id).Account_2__c && stages.contains(ld.BD_Status__c))
                        || (ld.Account_2__c != null && stages.contains(ld.BD_Status__c) != stages.contains(oldMap.get(ld.Id).BD_Status__c)))

                    {
                        if (oldMap.get(ld.Id).Account_2__c != null) {
                            accId.add(oldMap.get(ld.Id).Account_2__c);
                        }
                        if (ld.Account_2__c != null) {
                            accId.add(ld.Account_2__c);
                        }
                    }
                }
            }
        }

        //delete
        if (isDelete) {
            for (Lead ld : oldMap.values()) {
                if (ld.Account_2__c != null) {
                    accId.add(ld.Account_2__c);
                }
            }
        }

        if (!accId.isEmpty()) {

            Map<Id, Account> rollupMap = new Map<Id, Account>([SELECT Id, L4Q_Leads__c FROM Account WHERE Id IN :accId]);
            String retval = RedReportRollupsCalculate.CalculateAccountLeads(rollupMap);
            System.debug('retval ' + retval);
        }
    }*/

    public static void preventUpdateOfConvertedLead(List<Lead> newLeads) {
        //Set type to false to turn off the logic
        if (Boolean.valueOf(TriggersHelper.marketoUserConfiguration.Type__c) && UserInfo.getUserId() == TriggersHelper.marketoUserConfiguration.Value__c) {
            for (Lead lead : newLeads) {
                if (lead.Status == 'Converted' && String.isNotBlank(lead.ConvertedContactId)) {
                    lead.addError('This lead is converted, no changes from Marketo are allowed.');
                }
            }
        }
    }

    public static void setConvertedContactStatus(Map<Id, Lead> oldLeads, Map<Id, Lead> newLeads) {
        List<String> syncToMktBypassUsers = MSTR_Global_Configuation__mdt.getInstance('Sync_To_Marketo_Bypass_Users').Value__c.split(';');
        Map<Id, Contact> contactMap = new Map<Id, Contact>();
        List<Contact> contactsToUpdate = new List<Contact>();
        List<Lead> convertedLeads = new List<Lead>();

        for (Lead newLead : newLeads.values()) {
            Lead oldLead = oldLeads.get(newLead.Id);
            if (oldLead.ConvertedContactId != newLead.ConvertedContactId && String.isNotBlank(newLead.ConvertedContactId)) {
                contactMap.put(newLead.ConvertedContactId, null);
                convertedLeads.add(newLead);
            }
        }

        if (!convertedLeads.isEmpty()) {
            contactMap.remove(null);
            contactMap = new Map<Id, Contact>([
                SELECT Id, L_Status__c, Sync_to_Marketo__c, AccountId
                FROM Contact
                WHERE Id IN :contactMap.keySet()
            ]);

            if (!contactMap.isEmpty()) {
                Set<Id> placeholderAccounts = new Set<Id> {
                    TriggersHelper.unmappedAccountId, TriggersHelper.resourceCentreAccountId
                };

                for (Lead newLead : convertedLeads) {
                    if (contactMap.containsKey(newLead.ConvertedContactId)) {
                        Contact relatedContact = contactMap.get(newLead.ConvertedContactId);

                        relatedContact.L_Status__c = oldLeads.get(newLead.Id).Status;

                        if (newLead.Sync_to_Marketo__c && !syncToMktBypassUsers.contains(UserInfo.getUserId())) {
                            relatedContact.Sync_to_Marketo__c = true;
                        }

                        if (String.isNotBlank(newLead.Account_2__c) &&
                            newLead.Account_2__c != TriggersHelper.resourceCentreAccountId &&
                            placeholderAccounts.contains(relatedContact.AccountId)
                        ) {
                            relatedContact.AccountId = newLead.Account_2__c;
                        }
                        
                        //Marketing fields
                        relatedContact.MQL_Date__c = newLead.MQL_Date__c;
                        relatedContact.TAL_Date__c = newLead.TAL_Date__c;
                        relatedContact.TQL_Date__c = newLead.TQL_Date__c;
                        relatedContact.SAL_Date__c = newLead.SAL_Date__c;
                        relatedContact.SQL_Date__c = newLead.SQL_Date__c;
                        
                        contactsToUpdate.add(relatedContact);
                    }
                }
                update contactsToUpdate;
            }
        }
    }

    public static void routingFieldChange(Map<Id, Lead> oldLeads, Map<Id, Lead> newLeads) {
        List<String> triggerBypassUsers = MSTR_Global_Configuation__mdt.getInstance('Trigger_Bypass_Users').Value__c.split(';');
		Set<Id> MQLleadIds = new Set<Id>();
        List<Lead> MQLToChatter = new List<Lead>();
        Set<Id> newOwners = new Set<Id>();
        Set<Id> leadIds = new Set<Id>();
        Boolean validAccount = false;
        Boolean oldValidAccount = false;
        for (Lead newLead : newLeads.values()) {
            Lead oldLead = oldLeads.get(newLead.Id);
            validAccount = newLead.Account_2__c != null && newLead.Account_2__c != TriggersHelper.unmappedAccountId && newLead.Account_2__c != TriggersHelper.resourceCentreAccountId;
            oldValidAccount = oldLead.Account_2__c != null && oldLead.Account_2__c != TriggersHelper.unmappedAccountId && oldLead.Account_2__c != TriggersHelper.resourceCentreAccountId;
            if ((oldLead.Account_2__c != newLead.Account_2__c && validAccount && !oldValidAccount)
                || oldLead.Ownership_Rules__c != newLead.Ownership_Rules__c) {
                    leadIds.add(newLead.Id);
            }
            if(newLead.Status == 'L3 - Engaged'
               && (oldLead.Status != newLead.Status || (validAccount && !oldValidAccount))){ //Status changed to MQL/TQL or Account changed to a valid one
                        MQLleadIds.add(newLead.Id);
                        if(!leadIds.contains(newLead.Id) && newLead.OwnerId != null){ //No Owner reevaluation (only Status change): only send the notification
                            MQLToChatter.add(newLead); 
                            newOwners.add(newLead.OwnerId);
                        }
        	}
        }
        if(!MQLToChatter.isEmpty()){
        	CampaignMemberTriggerHelper.processMQLChatterNotifications(MQLToChatter, newOwners);
        }
        if (!leadIds.isEmpty()) {
            PersonRouting.processRecordsLeadChange(leadIds, MQLleadIds);
        }
    }
    
    public static void updateIsStatusTQLField(List<Lead> Leads, Map<Id, Lead> oldLeadMap) {
        for (Lead newLead : Leads) {
            Lead oldLead = oldLeadMap.get(newLead.Id);
            if (TriggersHelper.valueChanged(oldLead, newLead, Lead.Status) && newLead.Status == 'TQL')
                newLead.isStatusTQL__c = true;
        }
    }
    
    public static void updateMarketingFields (List<Lead> newLeads, Map<Id, Lead> oldLeads) {
        List<String> syncToMktBypassUsers = MSTR_Global_Configuation__mdt.getInstance('Sync_To_Marketo_Bypass_Users').Value__c.split(';');
        Map<String, Integer> statusOrder = new Map<String, Integer>{
            'L0 - Disqualified' => -3,
            'Prospect' => -2,
            'Inquiry' => -1,
            'L3 - Engaged' => 0,
            'L2 - Contacted' => 1,
            'TQL' => 2,
            'SAL' => 3,
            'SQL' => 4
        };
        List<String> statusAPIs = new List<String>{
            'MQL_Date__c',
            'TAL_Date__c',
            'TQL_Date__c', 
            'SAL_Date__c', 
            'SQL_Date__c'
        };
        Datetime now;
        Integer newStatusOrder;
        Integer oldStatusOrder;
        for (Lead l : newLeads) {
            if (!l.Return_to_Marketing__c && oldLeads.get(l.Id) != null && oldLeads.get(l.Id).Return_to_Marketing__c) {
                l.Return_to_Marketing_Reason__c = '';
            }
            if(oldLeads.get(l.Id) == null || l.Status != oldLeads.get(l.Id).Status){
                //Lead Before Update Flow
                if(oldLeads.get(l.Id) != null && l.Status != oldLeads.get(l.Id).Status && l.Status == 'L3 - Engaged' && !syncToMktBypassUsers.contains(UserInfo.getUserId())){
                    l.Sync_to_Marketo__c = true;
                }
                //New Status Date fields
                now = Datetime.now();
                newStatusOrder = statusOrder.get(l.Status);
                oldStatusOrder = oldLeads.get(l.Id) != null? (statusOrder.get(oldLeads.get(l.Id).Status) != null? statusOrder.get(oldLeads.get(l.Id).Status): -1): -1;
                if(newStatusOrder >= 0){
                    //If the new Status skips statuses, populate the date of all of them
                    if(newStatusOrder > oldStatusOrder+1){
                        for(Integer i = (oldStatusOrder >= 0? oldStatusOrder+1: 0); i <= newStatusOrder; i++){
                            l.put(statusAPIs.get(i), now);
                        }
                    }
                    else{
                        l.put(statusAPIs.get(newStatusOrder), now);  
                    }
                }
            }
        }
    }
}