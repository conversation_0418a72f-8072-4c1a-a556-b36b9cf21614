/*
* CreateMCSDSI
*
* DESCRIPTION : Called by External Cloud MCS Global Controller.
* POST: Creates a DSI, Subscriptions and Generates License Key
* PATCH: Updates EndDate, Assigns to Account, Creates Entitlement/Entitlement Contact, and Fires platform event to create Opp/Quote

* ---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER						DATE									REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Mahipal Jat		          	09/05/2025                       		- Original Version - 557760 - SF/CPQ - MCS - Auto Opp Creation
* ---------------------------------------------------------------------------------------------------------------------------------------------------
* Test Class:CreateMCSDSITest
*/
@RestResource (UrlMapping='/v1/mcs/dsi/*')
global class CreateMCSDSI {
    public static final String CLASS_NAME = 'CreateMCSDSI';
    

    global class WSException extends Exception {
    }

    @HttpPost
    global static ResponseWrapper doPost () {
        final String METHOD_NAME = 'doPost';
        Boolean hasError = false;
        String sErrorMsg = '';
        DSI__c dsi;
        RestResponse restResponse = RestContext.response;
        ResponseWrapper apexResp = new ResponseWrapper();
        RequestWrapper initRequest;

        HttpRequest createDSIReq;
		HttpResponse createDSIResp;
        String requestString = null;
        
        IntegrationErrorLogger.initializeTransactionLog(CLASS_NAME, METHOD_NAME, 'MCS DSI POST Request Received.');

        try {
            RestRequest req = RestContext.request;
            Blob body = req.requestBody;
            requestString = body.toString().removeStart('\'').removeEnd('\'');
            IntegrationErrorLogger.logDebug(CLASS_NAME, METHOD_NAME, 'Raw request body.', requestString, null, null);
            System.debug(requestString);
            initRequest = (RequestWrapper) JSON.deserialize(requestString, RequestWrapper.class);
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Request deserialized. Environment ID: ' + 
            (initRequest != null ? initRequest.environmentId : 'N/A'));

            if (!validateRequest(initRequest)) {
                restResponse.statusCode = 400;
                apexResp.errorMessage = 'Missing Required Fields';
                IntegrationErrorLogger.logError(
                    'MCS_DSI_Creation_Service', 'ValidationFailure', CLASS_NAME, METHOD_NAME,
                    IntegrationErrorLogger.DEFAULT_ERROR_SEVERITY, 'BAD_REQUEST', apexResp.errorMessage, null,
                    (req != null ? req.requestURI : null), (req != null ? req.httpMethod : null), 
                    (req != null ? JSON.serialize(req.headers) : null), requestString,
                    400, null, JSON.serialize(apexResp),
                    null, null
                );
                IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED_WITH_ERRORS);
                
                return apexResp;
            }

            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Request validated successfully.');

            //Contain DSI Creation in an hidden REST Request to prevent DML and HTTP callout in the same transaction
            Http h = new Http();
            createDSIReq = new HttpRequest();
            createDSIReq.setBody(requestString);
            createDSIReq.setTimeout(120000);
            createDSIReq.setEndpoint(Url.getOrgDomainUrl().toExternalForm() + '/services/apexrest/sfdcInternalMCS/createDSI');
            createDSIReq.setHeader('Content-Type', 'application/json; charset=UTF-8');
            createDSIReq.setHeader('Authorization', 'OAuth ' + UserInfo.getSessionId());
            System.debug(req.requestURI);
            createDSIReq.setMethod('POST');
            IntegrationErrorLogger.logDebug(CLASS_NAME, METHOD_NAME, 'Internal createDSI HttpRequest prepared.', createDSIReq.getBody(), null, null);

            createDSIResp = h.send(createDSIReq);
            System.debug(createDSIResp.getBody());

            String internalRespBody = createDSIResp.getBody();
            Integer internalRespStatusCode = createDSIResp.getStatusCode();
            IntegrationErrorLogger.logDebug(CLASS_NAME, METHOD_NAME, 
                'Internal createDSI HttpResponse. Status: ' + internalRespStatusCode, null, internalRespBody, null);


            ResponseWrapper createDSIRespObj = (ResponseWrapper) JSON.deserialize(createDSIResp.getBody(), ResponseWrapper.class);
            
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Internal DSI creation successful. DSI ID from internal call: ' + 
            (createDSIRespObj != null ? createDSIRespObj.dsiId : 'N/A'));


            //Generate Key - cap expiration at 3 years from today.
            apexResp = generateKey(initRequest, createDSIRespObj);

            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Key generation process completed.');
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Response -> ' + JSON.serialize(apexResp));
            
            restResponse.statusCode = 202;
            IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED);
            
        } catch (Exception e) {
            apexResp.errorMessage = e.getMessage();
            restResponse.statusCode = 500;
            // FF_Integration_Log__c errorReport = new FF_Integration_Log__c();
            // errorReport.Debug_Message__c = 'MCS POST FAILURE \n' + e.getMessage() + '\n' + e.getStackTraceString();
            // errorReport.Type__c = 'Error';
            // if (initRequest != null) {
            //     errorReport.Reference_Id__c = initRequest.dsiId;
            // }
            // insert errorReport;
           
            
            // Log the exception using IntegrationErrorLogger
            // Pass HttpRequest and HttpResponse if available and relevant to the exception context
            String reqBodyForLog = requestString != null ? requestString : (initRequest != null ? JSON.serialize(initRequest) : 'Request body not captured before error');
            String reqURIForLog = RestContext.request != null ? RestContext.request.requestURI : 'URI not captured';
            String reqMethodForLog = RestContext.request != null ? RestContext.request.httpMethod : 'Method not captured';
            
            // For the main exception, internalCalloutEndpoint and related fields might be more relevant if the exception is from the callout logic
            String endpointForLog = (createDSIReq != null) ? createDSIReq.getEndpoint() : reqURIForLog;
            String methodForLog = (createDSIReq != null) ? createDSIReq.getMethod() : reqMethodForLog;
            String reqPayloadForLog = (createDSIReq != null && createDSIReq.getBody() != null) ? createDSIReq.getBody() : reqBodyForLog;
            Integer respStatusForLog = (createDSIResp != null) ? createDSIResp.getStatusCode() : null;
            String respBodyForLog = (createDSIResp != null && createDSIResp.getBody() != null) ? createDSIResp.getBody() : null;


            IntegrationErrorLogger.logError(
                'MCS_DSI_Creation_Service', 'doPost_GlobalException', CLASS_NAME, METHOD_NAME,
                IntegrationErrorLogger.DEFAULT_ERROR_SEVERITY, e.getTypeName(), e.getMessage(), e.getStackTraceString(),
                endpointForLog, methodForLog, '[HEADERS_OMITTED]', reqPayloadForLog,
                respStatusForLog, null, respBodyForLog,
                (initRequest != null && initRequest.dsiId != null) ? Id.valueOf(initRequest.dsiId.left(15)) : null, // Attempt to get a related ID if possible
                (initRequest != null && initRequest.dsiId != null) ? 'ExternalDSI_ID_Reference' : null
            );
            IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_ERROR);
        }
        //Resp = new ResponseWrapper (dsi, sErrorMsg);
        return apexResp;
    }

    @HttpPatch
    global static PatchResponseWrapper updateDSI () {
        final String METHOD_NAME = 'updateDSI';

        RestRequest req = RestContext.request;
        RestResponse restResponse = RestContext.response;
        PatchResponseWrapper apexResp = new PatchResponseWrapper();
        Savepoint sp = null;
        RequestWrapper initRequest;
        String requestString;
        IntegrationErrorLogger.initializeTransactionLog(CLASS_NAME, METHOD_NAME, 'MCS DSI PATCH Request Received.');


        try {
            Blob body = req.requestBody;
            requestString = body.toString();
            IntegrationErrorLogger.logDebug(CLASS_NAME, METHOD_NAME, 'Raw PATCH request body.', requestString, null, null);
            initRequest = (RequestWrapper) JSON.deserialize(requestString, RequestWrapper.class);
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'PATCH Request deserialized. DSI ID: ' + (initRequest != null ? initRequest.dsiId : 'N/A'));

            if (!validatePatchRequest(initRequest)) {
                restResponse.statusCode = 400;
                apexResp.errorMessage = 'Missing Required Fields';
                IntegrationErrorLogger.logError(
                    'MCS_DSI_Update_Service', 'ValidationFailure', CLASS_NAME, METHOD_NAME,
                    IntegrationErrorLogger.DEFAULT_ERROR_SEVERITY, 'BAD_REQUEST', apexResp.errorMessage, null,
                    req.requestURI, req.httpMethod,
                    JSON.serialize(req.headers), requestString,
                    400, null, JSON.serialize(apexResp),
                    (initRequest != null && initRequest.dsiId != null) ? Id.valueOf(initRequest.dsiId.left(15)) : null,
                    'DSI__c'
                );
                IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED_WITH_ERRORS);
                return apexResp;
            }
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Request validated successfully.');

            List<Contact> contactList;

            List<DSI__c> dsiList = [SELECT Id, RecordTypeId, Name, Account__c, Entitlement_End_Date__c, DSI_ID__c, Version__c, Elastic_Cloud_Console__c, Operating_System__c, Account__r.Compass_Id_F__c, MCSActivationDateTime__c, MCSExpirationDatetime__c FROM DSI__c WHERE DSIID__c = :initRequest.dsiId];
            List<SBQQ__Subscription__c> subscriptionList = [SELECT Id, SBQQ__Account__c, SBQQ__SubscriptionStartDate__c, SBQQ__SubscriptionEndDate__c FROM SBQQ__Subscription__c WHERE SBCF_DSI__c IN:dsiList];

            if (dsiList.isEmpty()) {
                apexResp.errorMessage = 'No DSI Found matching provided DSI Id';
                restResponse.statusCode = 400;
                
                IntegrationErrorLogger.logError('MCS_DSI_Update_Service', 'DataValidation', CLASS_NAME, METHOD_NAME, IntegrationErrorLogger.DEFAULT_ERROR_SEVERITY, 'NOT_FOUND', apexResp.errorMessage, null, req.requestURI, req.httpMethod, JSON.serialize(req.headers), requestString, 400, null, JSON.serialize(apexResp), (initRequest != null && initRequest.dsiId != null) ? Id.valueOf(initRequest.dsiId.left(15)) : null, 'DSI__c');
                IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED_WITH_ERRORS);

                return apexResp;
            }

            if (initRequest.mstrVersion != null) {
                System.debug('test ' + initRequest.mstrVersion);
                for (DSI__c d : dsiList) {
                    /*Product_Key_Request__c keyRequest = new Product_Key_Request__c(Account__c = d.account__c, Employee_Number__c = null,
                                                                                   Version_List__c = initRequest.mstrVersion,
                                                                                   Manual_Key__c = false,
                                                                                   Employee_Key__c = false,
                                                                                   //Ship_Contact__c = contactList[0].Id,
                                                                                   QuoteId__c = 1,
                                                                                   DSI__c = d.Id);*/

                    Product_Key_Request__c keyRequest = new Product_Key_Request__c(Account__c = d.Account__c, Employee_Number__c = null,
                        Version_List__c = initRequest.mstrVersion, Manual_Key__c = false,
                        Employee_Key__c = false, QuoteID__c = 1, DSI__c = d.Id,
                        Disable_Registration__c = true, LockedKey__c = true);

                    keyRequest.putSObject('DSI__r', d);

                    System.debug('keyRequest: ' + keyRequest);
                    if (!Test.isRunningTest()) {
                        KeyGeneratorController.generateDSIKey(keyRequest);
                    } else {
                        keyRequest = [SELECT Id, Success__c, Error__c FROM Product_Key_Request__c LIMIT 1];
                    }
                    List<Product_Key_Request__c> res = [SELECT Id, Success__c, Error__c, Existing_Request__c, New_Key_Delivered__c FROM Product_Key_Request__c WHERE Id = :keyRequest.Id];

                    if (!res.isEmpty() && res[0].Success__c) {
                        String resultingPKR = res[0].New_Key_Delivered__c ? res[0].Id : res[0].Existing_Request__c;
                        apexResp.licenseKey = [SELECT Key__c FROM Product_Key__c WHERE Product_Key_Request__c = :resultingPKR ORDER BY CreatedDate DESC LIMIT 1].Key__c;
                        d.Version__c = initRequest.mstrVersion;
                    } else {
                        apexResp.errorMessage = res[0].Error__c ;
                    }
                }
            }

            //update DSI
            if (String.isNotBlank(initRequest.environmentId)) {
                for (DSI__c dsi : dsiList) {
                    dsi.Description_Name__c = initRequest.environmentId;
                    dsi.MCSEnvironmentName__c = initRequest.environmentId;
                }
            }
            //assign to a user
            if (initRequest.user != null) {
                contactList = [
                    SELECT Id, AccountId, Account.Name, Account.Owner.FirstName, Account.Owner.LastName, Account.Owner.IsActive,
                        Account.Owner.Email, Account.Owner.Department, Account.District__c, Account.District__r.Business_Hours__c,
                        MailingCountry, Account.OwnerId, Name
                    FROM Contact
                    WHERE Email = :initRequest.user.email
                ];

                if (!contactList.isEmpty()) {
                    //update account on dsi and subscription
                    for (DSI__c dsi : dsiList) {
                        dsi.Account__c = contactList[0].AccountId;
                        Id activeRecTypeId = Schema.SObjectType.DSI__c.getRecordTypeInfosByName().get('Standard').getRecordTypeId();
                        dsi.RecordTypeId = activeRecTypeId;
                        dsi.Status__c = 'Trial';
                        dsi.Support_Level__c = 'Cloud mcs';
                        dsi.MCSEnvironmentOwner__c = contactList[0].Id;
                        dsi.MCSBatchStatus__c = 'Pending';
                        if (initRequest.subscription != null) {
                            dsi.Entitlement_End_Date__c = Date.newInstance(initRequest.subscription.expirationDate.year(), initRequest.subscription.expirationDate.month(), initRequest.subscription.expirationDate.day());
                            dsi.MCSActivationDateTime__c = initRequest.subscription.activationDate;
                            dsi.MCSExpirationDatetime__c = initRequest.subscription.expirationDate;
                        }
                    }
                } else {
                    apexResp.errorMessage = 'No matching Salesforce Contact found';
                    restResponse.statusCode = 400;
                    return apexResp;
                }

                for (SBQQ__Subscription__c sub : subscriptionList) {
                    sub.SBQQ__Account__c = contactList[0].AccountId;
                }

                //set up response user info
                apexResp.account.accountName = contactList[0].Account.Name;
                if (contactList[0].Account.Owner.IsActive && contactList[0].Account.Owner.Department == 'SLS') {
                    apexResp.account.accountExecutive.firstName = contactList[0].Account.Owner.FirstName.normalizeSpace();
                    apexResp.account.accountExecutive.lastName = contactList[0].Account.Owner.LastName.substringBefore('(').normalizeSpace();
                    apexResp.account.accountExecutive.email = contactList[0].Account.Owner.Email.substringBefore('.invalid');
                } else {
                    //default to custom metadata owner id
                    String defaultOwnerId = MSTR_Global_Configuation__mdt.getInstance('MCS_Default_Owner').Value__c;
                    List<User> userDefault = [SELECT Id, FirstName, LastName, Email FROM User WHERE Id = :defaultOwnerId];
                    if (!userDefault.isEmpty()) {
                        apexResp.account.accountExecutive.firstName = userDefault[0].FirstName.normalizeSpace();
                        apexResp.account.accountExecutive.lastName = userDefault[0].LastName.substringBefore('(').normalizeSpace();
                        apexResp.account.accountExecutive.email = userDefault[0].Email.substringBefore('.invalid');
                    }
                }
            } else if (initRequest.user == null && initRequest.mstrVersion == null) {
                apexResp.errorMessage = 'No matching Salesforce Contact found';
                restResponse.statusCode = 400;
                return apexResp;

            }

            //update start/end dates
            if (initRequest.subscription != null) {
                for (SBQQ__Subscription__c sub : subscriptionList) {
                    sub.SBQQ__SubscriptionStartDate__c = Date.newInstance(initRequest.subscription.activationDate.year(), 
                                                                            initRequest.subscription.activationDate.month(), 
                                                                            initRequest.subscription.activationDate.day());
                    sub.SBQQ__SubscriptionEndDate__c = Date.newinstance(initRequest.subscription.expirationDate.year(),
                                                                            initRequest.subscription.expirationDate.month(),
                                                                            initRequest.subscription.expirationDate.day());
                    sub.SBQQ__ChargeType__c = 'Trial';
                }
            }

            sp = Database.setSavepoint();

            update dsiList;
            update subscriptionList;

            restResponse.statusCode = 200;
            IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED);

        } catch (Exception e) {
            if (sp != null) {
                Database.rollback(sp);
            }
            restResponse.statusCode = 500;
            apexResp.errorMessage = 'Something went wrong: ' + e.getMessage();
            //Id relatedId = (initRequest != null && String.isNotBlank(initRequest.dsiId)) ? Id.valueOf(initRequest.dsiId.left(15)) : null;
            IntegrationErrorLogger.logError(
                e, 'MCS_DSI_Update_Service', 'doPatch_GlobalException', CLASS_NAME, METHOD_NAME, null
            );
            IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_ERROR);
        }
        return apexResp;
    }

    @HttpDelete
        global static PatchResponseWrapper deleteMCSEnvironment () {
        final String METHOD_NAME = 'deleteMCSEnvironment';

        RestRequest req = RestContext.request;
        RestResponse restResponse = RestContext.response;
        PatchResponseWrapper apexResp = new PatchResponseWrapper();
        RequestWrapper initRequest;
        String dsiId;
        IntegrationErrorLogger.initializeTransactionLog(CLASS_NAME, METHOD_NAME, 'MCS DSI DELETE Request Received.');
		List<DSI__c> dsiList;
        
        try {
            // Getting the request URI
            String deleteReqURI = req.requestURI;
            // Getting the contact id from the request URI
            dsiId = String.valueOf(deleteReqURI.substring(deleteReqURI.lastIndexOf('/') + 1));
            IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Attempting to process termination for DSIID__c: ' + dsiId);


            if (String.isBlank(dsiId)) {
                restResponse.statusCode = 400;
                apexResp.errorMessage = 'No DSI Provided';
                IntegrationErrorLogger.logError(
                    'MCS_DSI_Termination_Service', 'ValidationFailure', CLASS_NAME, METHOD_NAME,
                    IntegrationErrorLogger.DEFAULT_ERROR_SEVERITY, 'BAD_REQUEST', apexResp.errorMessage, null,
                    req.requestURI, req.httpMethod, JSON.serialize(req.headers), null,
                    400, null, JSON.serialize(apexResp), null, null
                );
                IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED_WITH_ERRORS);
                return apexResp;
            }

            dsiList = [SELECT Id, RecordTypeId, Name, Account__c, Entitlement_End_Date__c FROM DSI__c WHERE DSIID__c = :dsiId];
            List<SBQQ__Subscription__c> subscriptionList = [SELECT Id, SBQQ__Account__c, SBQQ__SubscriptionStartDate__c, SBQQ__SubscriptionEndDate__c FROM SBQQ__Subscription__c WHERE SBCF_DSI__c IN:dsiList];
            List<Entitlement> entitlementList = [SELECT Id, EndDate FROM Entitlement WHERE DSI__c IN:dsiList];
            List<Product_Key__c> productKeyList = [SELECT Id, IsBlackListed__c, Blacklist_Reason__c FROM Product_Key__c WHERE DSI__c IN:dsiList];

            if (!dsiList.isEmpty()) {
                for (DSI__c dsi : dsiList) {
                    dsi.Entitlement_End_Date__c = System.today();
                    dsi.MCSExpirationDatetime__c = System.now();
                    dsi.Status__c = 'Expired';
                    dsi.RecordTypeId = Schema.SObjectType.DSI__c.getRecordTypeInfosByName().get('Archived').getRecordTypeId();
                }
                update dsiList;
            } else {
                restResponse.statusCode = 400;
                apexResp.errorMessage = 'No DSI Located with the ID Provided';
                IntegrationErrorLogger.logError(
                    'MCS_DSI_Termination_Service', 'DataValidation', CLASS_NAME, METHOD_NAME,
                    IntegrationErrorLogger.DEFAULT_ERROR_SEVERITY, 'NOT_FOUND', apexResp.errorMessage, null,
                    req.requestURI, req.httpMethod, JSON.serialize(req.headers), null,
                    400, null, JSON.serialize(apexResp), null, 'DSI__c'
                );
                IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED_WITH_ERRORS);
                return apexResp;
            }
            if (!subscriptionList.isEmpty()) {
                for (SBQQ__Subscription__c sub : subscriptionList) {
                    sub.SBQQ__SubscriptionEndDate__c = System.today();
                }
                update subscriptionList;
            }
            if (!entitlementList.isEmpty()) {
                for (Entitlement ent : entitlementList) {
                    ent.EndDate = System.today();
                }
                update entitlementList;
            }
            if (!productKeyList.isEmpty()) {
                for (Product_Key__c key : productKeyList) {
                    key.IsBlackListed__c = true;
                    key.Blacklist_Reason__c = 'MCS Environment Terminated via GC API Call';
                }
                update productKeyList;
                IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Successfully terminated environment for DSIID__c: ' + dsiId);
                IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_COMPLETED);
            }
        } catch (Exception e) {
            System.debug(e);
            restResponse.statusCode = 500;
            apexResp.errorMessage = 'Something went wrong: ' + e.getMessage();
            Id relatedId = ((dsiList != null && !dsiList.isEmpty())) ? dsiList[0].Id : null;
            IntegrationErrorLogger.logError(e, 'MCS_DSI_Termination_Service', 'doDelete_GlobalException', CLASS_NAME, METHOD_NAME, relatedId);
            if(relatedId == null && String.isNotBlank(dsiId)){
                IntegrationErrorLogger.logInfo(CLASS_NAME, METHOD_NAME, 'Exception occurred for external DSIID__c: ' + dsiId);
            }
            IntegrationErrorLogger.finalizeTransactionLog(IntegrationErrorLogger.TX_STATUS_ERROR);
        }
        return apexResp;
    }

    private static ResponseWrapper generateKey (RequestWrapper initReq, ResponseWrapper dsiWrapper) {
        //ResponseWrapper response = new ResponseWrapper();
        Product_Key_Request__c keyRequest;
        try {
            List<DSI__c> dsiList = [
                SELECT Id, DSI_ID__c, DSIID__c, Account__c, Account__r.Owner.FirstName, Account__r.Owner.LastName,
                    Account__r.Owner.Email, Account__r.Name, Account__r.Compass_Id_F__c, Version__c,
                    Operating_System__c, MCSEnvironmentOwner__c, Elastic_Cloud_Console__c
                FROM DSI__c
                WHERE DSIID__c = :dsiWrapper.dsiId
            ];
            DSI__c dsi = dsiList[0];
            //dsiWrapper.dsiId = dsi.DSIID__c;

            //set response AE info
            List<Contact> contactList;
            if (initReq.user != null) {
                contactList = [
                    SELECT Id, AccountId, Account.Name, Account.Owner.FirstName, Account.Owner.LastName, Account.Owner.IsActive, Account.Owner.Email, Account.Owner.Department
                    FROM Contact
                    WHERE Email = :initReq.user.email
                ];

                if (!contactList.isEmpty()) {
                    //set up response user info
                    dsiWrapper.account.accountName = contactList[0].Account.Name;
                    if (contactList[0].Account.Owner.IsActive && contactList[0].Account.Owner.Department == 'SLS') {
                        dsiWrapper.account.accountExecutive.firstName = contactList[0].Account.Owner.FirstName.normalizeSpace();
                        dsiWrapper.account.accountExecutive.lastName = contactList[0].Account.Owner.LastName.substringBefore('(').normalizeSpace();
                        dsiWrapper.account.accountExecutive.email = contactList[0].Account.Owner.Email.substringBefore('.invalid');
                    } else {
                        //default MCS Default Owner From metadata
                        String defaultOwnerId = MSTR_Global_Configuation__mdt.getInstance('MCS_Default_Owner').Value__c;
                        List<User> userDefault = [SELECT Id, FirstName, LastName, Email FROM User WHERE Id = :defaultOwnerId];
                        if (!userDefault.isEmpty()) {
                            dsiWrapper.account.accountExecutive.firstName = userDefault[0].FirstName.normalizeSpace();
                            dsiWrapper.account.accountExecutive.lastName = userDefault[0].LastName.substringBefore('(').normalizeSpace();
                            dsiWrapper.account.accountExecutive.email = userDefault[0].Email.substringBefore('.invalid');
                        }
                    }
                }
            }

            keyRequest = new Product_Key_Request__c(Account__c = dsi.Account__c, Employee_Number__c = null,
                Version_List__c = initReq.mstrVersion, Manual_Key__c = false,
                Employee_Key__c = false, QuoteID__c = 1, DSI__c = dsi.Id,
                Disable_Registration__c = true, LockedKey__c = true, Ship_Contact__c = dsi.MCSEnvironmentOwner__c);
            keyRequest.putSObject('DSI__r', dsi);
            System.debug('keyRequest: ' + keyRequest);
            KeyGeneratorController.generateDSIKey(keyRequest);
            List<Product_Key_Request__c> res = [SELECT Id, Success__c, Error__c FROM Product_Key_Request__c WHERE Id = :keyRequest.Id];
            if (!res.isEmpty() && res[0].Success__c) {
                dsiWrapper.licenseKey = [SELECT Key__c FROM Product_Key__c WHERE Product_Key_Request__c = :res[0].Id ORDER BY CreatedDate DESC LIMIT 1].Key__c;
            }

        } catch (Exception e) {
            System.debug(e.getMessage());
            keyRequest.Error__c = e.getMessage() + e.getStackTraceString();
            insert keyRequest;
            throw new WSException('Internal Error Occurred: ' + e.getMessage() + e.getStackTraceString());
        }
        return dsiWrapper;
    }

    //response
    global class ResponseWrapper {
        global String dsiId;
        global String errorMessage;
        global String licenseKey;
        global AccountObj account;
        global SubscriptionDates subscription;

        global ResponseWrapper (DSI__c dsiId, String ErrMsg) {
            this.dsiId = dsiId == null ? null : dsiId.DSIID__c;
            this.licenseKey = null;
            this.errorMessage = ErrMsg;
            this.account = new AccountObj();
            SubscriptionDates sdObj = new SubscriptionDates();
            if(dsiId != null && dsiId.MCSActivationDateTime__c != null) {
                sdObj.activationDate = dsiId.MCSActivationDateTime__c;
            }
            if(dsiId != null && dsiId.MCSExpirationDatetime__c != null) {
                sdObj.expirationDate = dsiId.MCSExpirationDatetime__c;
            }
            this.subscription = sdObj;
        }
        global ResponseWrapper () {
            this.account = new AccountObj();
        }

    }
    //update wrapper
    global class PatchResponseWrapper {
        global String licenseKey;
        global String errorMessage;
        global AccountObj account;
        global PatchResponseWrapper () {
            account = new AccountObj();
        }

    }

    global class EnvironmentOwnerWrapper {
        global String firstName;
        global String lastName;
        global String email;
    }

    global class SubscriptionUpdateWrapper {
        global String activationDate;
        global String expirationDate;
        global String plan;
    }
    
    //request
    global class RequestWrapper {
        global String mstrVersion;
        global String productPackage;
        global String environmentId;
        global String environmentUrl;
        global String dsiId;
        global ContactUser user;
        global SubscriptionDates subscription;

        global RequestWrapper parse (String json) {
            return (RequestWrapper) System.JSON.deserialize(json, RequestWrapper.class);
        }

    }
    global class ContactUser {
        global String email;
        global String firstName;
        global String lastName;
        global String scopusID;
        global List<UserEnvDetails> quotas;

        global ContactUser () {
            this.quotas = new List<UserEnvDetails>();
        }
    }

    global class UserEnvDetails {
        global String type;
        global Integer environmentsRemaining;

        global UserEnvDetails () {
        }
    }

    global class SubscriptionDates {
        global Datetime activationDate;
        global Datetime expirationDate;
        //global String plan;
        global String status;
        global String type;

        global SubscriptionDates () {
        }
    }

    //used as a return to provide account data based on contact user provided
    global class AccountObj {
        global String accountName;
        global ContactUser accountExecutive;

        global AccountObj () {
            this.accountExecutive = new ContactUser();
        }
    }

    //request validator
    private static Boolean validateRequest (RequestWrapper request) {
        if (String.isBlank(request.environmentId) || String.isBlank(request.mstrVersion)) {
            return false;
        }
        if (request.user != null && (String.isBlank(request.user.email))) {
            return false;
        }
        return true;
    }

    private static Boolean validatePatchRequest (RequestWrapper request) {
        if (String.isBlank(request.dsiId)) {
            return false;
        }
        if (request.user != null && (String.isBlank(request.user.email))) {
            return false;
        }
        if (request.subscription != null && (request.subscription.activationDate == null || request.subscription.expirationDate == null)) {
            if (request.subscription.activationDate > request.subscription.expirationDate) {
                throw new WSException('Expiration Date cannot be before Activation Date');
            }
            return false;
        }
        if (request.user == null && request.subscription == null && request.environmentId == null) {
            return false;
        }
        return true;
    }

}