<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>SBQQ__TestedField__c</fullName>
    <deprecated>false</deprecated>
    <description>Field on the Quote object that should be used in testing this condition.</description>
    <inlineHelpText>Field on the Quote object that should be used in testing this condition.</inlineHelpText>
    <label>Tested Field</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <controllingField>SBQQ__TestedObject__c</controllingField>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Group Line Items</fullName>
                <default>false</default>
                <label>Group Line Items</label>
            </value>
            <value>
                <fullName>Unit Price</fullName>
                <default>false</default>
                <label>Unit Price</label>
            </value>
            <value>
                <fullName>SBQQ__NetAmount__c</fullName>
                <default>false</default>
                <label>SBQQ__NetAmount__c</label>
            </value>
            <value>
                <fullName>SBQQ__Type__c</fullName>
                <default>false</default>
                <label>SBQQ__Type__c</label>
            </value>
            <value>
                <fullName>Available_Server_Telemetry_Named_User__c</fullName>
                <default>false</default>
                <label>Available_Server_Telemetry_Named_User__c</label>
            </value>
            <value>
                <fullName>SBCF_ES_Quote__c</fullName>
                <default>false</default>
                <label>SBCF_ES_Quote__c</label>
            </value>
            <value>
                <fullName>SBQQ__Discount__c</fullName>
                <default>false</default>
                <label>SBQQ__Discount__c</label>
            </value>
            <value>
                <fullName>SBQQ__Quantity__c</fullName>
                <default>false</default>
                <label>SBQQ__Quantity__c</label>
            </value>
            <value>
                <fullName>Gross_Education__c</fullName>
                <default>false</default>
                <label>Gross_Education__c</label>
            </value>
            <value>
                <fullName>ACN2__c</fullName>
                <default>false</default>
                <label>ACN2__c</label>
            </value>
            <value>
                <fullName>ACB__c</fullName>
                <default>false</default>
                <label>ACB__c</label>
            </value>
            <value>
                <fullName>ACM__c</fullName>
                <default>false</default>
                <label>ACM__c</label>
            </value>
            <value>
                <fullName>ACR__c</fullName>
                <default>false</default>
                <label>ACR__c</label>
            </value>
            <value>
                <fullName>ACU__c</fullName>
                <default>false</default>
                <label>ACU__c</label>
            </value>
            <value>
                <fullName>ACW__c</fullName>
                <default>false</default>
                <label>ACW__c</label>
            </value>
            <value>
                <fullName>ACN1__c</fullName>
                <default>false</default>
                <label>ACN1__c</label>
            </value>
            <value>
                <fullName>ADB__c</fullName>
                <default>false</default>
                <label>ADB__c</label>
            </value>
            <value>
                <fullName>ADO__c</fullName>
                <default>false</default>
                <label>ADO__c</label>
            </value>
            <value>
                <fullName>AEA__c</fullName>
                <default>false</default>
                <label>AEA__c</label>
            </value>
            <value>
                <fullName>AEN__c</fullName>
                <default>false</default>
                <label>AEN__c</label>
            </value>
            <value>
                <fullName>AEB__c</fullName>
                <default>false</default>
                <label>AEB__c</label>
            </value>
            <value>
                <fullName>AGL__c</fullName>
                <default>false</default>
                <label>AGL__c</label>
            </value>
            <value>
                <fullName>AGEM__c</fullName>
                <default>false</default>
                <label>AGEM__c</label>
            </value>
            <value>
                <fullName>APL__c</fullName>
                <default>false</default>
                <label>APL__c</label>
            </value>
            <value>
                <fullName>APP__c</fullName>
                <default>false</default>
                <label>APP__c</label>
            </value>
            <value>
                <fullName>APS__c</fullName>
                <default>false</default>
                <label>APS__c</label>
            </value>
            <value>
                <fullName>APT__c</fullName>
                <default>false</default>
                <label>APT__c</label>
            </value>
            <value>
                <fullName>ASA__c</fullName>
                <default>false</default>
                <label>ASA__c</label>
            </value>
            <value>
                <fullName>ASC__c</fullName>
                <default>false</default>
                <label>ASC__c</label>
            </value>
            <value>
                <fullName>ASI__c</fullName>
                <default>false</default>
                <label>ASI__c</label>
            </value>
            <value>
                <fullName>ASR__c</fullName>
                <default>false</default>
                <label>ASR__c</label>
            </value>
            <value>
                <fullName>AST1__c</fullName>
                <default>false</default>
                <label>AST1__c</label>
            </value>
            <value>
                <fullName>AST2__c</fullName>
                <default>false</default>
                <label>AST2__c</label>
            </value>
            <value>
                <fullName>ASD__c</fullName>
                <default>false</default>
                <label>ASD__c</label>
            </value>
            <value>
                <fullName>SBQQ__PricebookID__c</fullName>
                <default>false</default>
                <label>SBQQ__PricebookID__c</label>
            </value>
            <value>
                <fullName>SBQQ__CustomerAmount__c</fullName>
                <default>false</default>
                <label>SBQQ__CustomerAmount__c</label>
            </value>
            <value>
                <fullName>SBQQ__ProductFamily__c</fullName>
                <default>false</default>
                <label>SBQQ__ProductFamily__c</label>
            </value>
            <value>
                <fullName>Enterprise_Support__c</fullName>
                <default>false</default>
                <label>Enterprise_Support__c</label>
            </value>
            <value>
                <fullName>CTR__c</fullName>
                <default>false</default>
                <label>CTR__c</label>
            </value>
            <value>
                <fullName>SBQQ__SubscriptionTerm__c</fullName>
                <default>false</default>
                <label>SBQQ__SubscriptionTerm__c</label>
            </value>
            <value>
                <fullName>SBCF_Co_termed__c</fullName>
                <default>false</default>
                <label>SBCF_Co_termed__c</label>
            </value>
            <value>
                <fullName>SBQQ__EndDate__c</fullName>
                <default>false</default>
                <label>SBQQ__EndDate__c</label>
            </value>
            <value>
                <fullName>SBQQ__StartDate__c</fullName>
                <default>false</default>
                <label>SBQQ__StartDate__c</label>
            </value>
            <value>
                <fullName>SBQQ__SubscriptionPricing__c</fullName>
                <default>false</default>
                <label>SBQQ__SubscriptionPricing__c</label>
            </value>
            <value>
                <fullName>SBQQ__ProductName__c</fullName>
                <default>false</default>
                <label>SBQQ__ProductName__c</label>
            </value>
            <value>
                <fullName>SBQQ__ProductCode__c</fullName>
                <default>false</default>
                <label>SBQQ__ProductCode__c</label>
            </value>
            <value>
                <fullName>SBQQ__AdditionalDiscountAmount__c</fullName>
                <default>false</default>
                <label>SBQQ__AdditionalDiscountAmount__c</label>
            </value>
            <value>
                <fullName>SBQQ__TotalDiscountRate__c</fullName>
                <default>false</default>
                <label>SBQQ__TotalDiscountRate__c</label>
            </value>
        </valueSetDefinition>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>Group Line Items</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <controllingFieldValue>Quote Line Group</controllingFieldValue>
            <valueName>SBQQ__Type__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>SBCF_ES_Quote__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ACN2__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ACB__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ACM__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ACR__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ACU__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ACW__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ACN1__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ADB__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ADO__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>AEA__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>AEN__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>AEB__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>AGL__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>AGEM__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>APL__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>APP__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>APS__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>APT__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ASA__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ASC__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ASI__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ASR__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>AST1__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>AST2__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>ASD__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>SBQQ__PricebookID__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>SBQQ__CustomerAmount__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>CTR__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote</controllingFieldValue>
            <valueName>SBCF_Co_termed__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__Discount__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__Quantity__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__ProductFamily__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>Enterprise_Support__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__SubscriptionTerm__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__EndDate__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__StartDate__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__SubscriptionPricing__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__ProductName__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__ProductCode__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__AdditionalDiscountAmount__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Quote Line</controllingFieldValue>
            <valueName>SBQQ__TotalDiscountRate__c</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Product Option</controllingFieldValue>
            <controllingFieldValue>Configuration Attributes</controllingFieldValue>
            <controllingFieldValue>Upgraded Asset</controllingFieldValue>
            <valueName>Unit Price</valueName>
        </valueSettings>
    </valueSet>
</CustomField>
