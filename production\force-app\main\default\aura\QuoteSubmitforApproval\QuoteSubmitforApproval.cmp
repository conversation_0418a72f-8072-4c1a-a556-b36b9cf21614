<aura:component controller="QuoteApprovalController" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickActionWithoutHeader" access="global" >
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <aura:attribute name="quoteID" type="String" />
    <aura:attribute name="isValid" type="Boolean" default="true" />
    <aura:attribute name="showSpinner" type="Boolean" default="true" description="for Spinner" />
    <aura:attribute name="QuoteRecord" type="Object" description="A simplified view record object to be displayed"/>
    <aura:attribute name="recordError" type="String"  description="An error message bound to force:recordData"/>
        <!-- Loading Complete Contact record for Accessing -->
    <force:recordData aura:id="recordHandler"
                      recordId="{!v.recordId}"
                      fields="SBQQ__Status__c,Billing_Frequency__c,SBQQ__LineItemCount__c,SBQQ__Type__c,SBQQ__Primary__c,SBCF_ES_Quote__c,Software_Quote_Lines__c,Sum_of_Pkg_Services__c,Sum_of_Consulting__c,SOW_Uploaded__c,Quote_Type__c,Internal_Quote__c,SBCF_Agreement_Classification__c,Service_Line_Items__c,Sales_RVP__c,Rev_Rec_Representative__c,Services_RVP__c,Legal_Representative__c,SBCF_Bill_to_Contact__c,SBCF_Ship_to_Contact__c,SBQQ__ExpirationDate__c,SBQQ__Opportunity2__r.DSI__c,SBQQ__Opportunity2__r.RecordType.Name,SBCF_Pricing_Payment_and_Invoice_Terms__c,Has_Cloud_Architecture_or_Infrastructure__c,Maximum_Software_Discount__c,Software_Discount__c,Minimum_Software_ARR__c,Software_ARR__c,Pricing_Exception__c,Cloud_Tier__c,Reseller__c,SBQQ__Distributor__c,SBCF_Type_of_Sale__c"
                      targetFields="{!v.QuoteRecord}"
                      targetError="{!v.recordError}"
                      recordUpdated="{!c.ValidateRecord}"
                      mode="EDIT"
                      />
    <!-- Display Lightning Data Service errors, if any -->
    <aura:if isTrue="{!not(empty(v.recordError))}">
        <div class="recordError">
            {!v.recordError}</div>
    </aura:if>
    
    
    
    <aura:if isTrue="{!v.showSpinner}">
         <div class="slds-is-relative slds-spinner_container"> <lightning:spinner variant="brand" size="medium" /></div>
        <!-- <div class="slds-is-relative slds-spinner_container slds-align_absolute-center slds-grid-vertical-align-center"> <lightning:spinner variant="brand" size="medium" /></div> -->
    </aura:if> 
</aura:component>