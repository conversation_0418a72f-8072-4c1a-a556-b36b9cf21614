public class MCSTrialRequestPayload {
    public String region;
    public String environmentType;
    public String plan;
    public Integer numberOfUsers;
    public UserDetails user;
    public SubscriptionDetails subscription;

    public MCSTrialRequestPayload(String contactEmail, String contactFirstName, String contactLastName, DateTime trialStartDate, DateTime trialEndDate) {
        this.region = 'us-east-1'; 
        this.environmentType = 'MCS';
        this.plan = 'trial';
        this.numberOfUsers = 50; 
        this.user = new UserDetails(contactEmail, contactFirstName, contactLastName);
        
        Datetime startDt = trialStartDate;
        Datetime endDt = trialEndDate;
        
        this.subscription = new SubscriptionDetails(
            startDt.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''),
            endDt.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'')
        );
    }


    public MCSTrialRequestPayload(String contactEmail, String contactFirstName, String contactLastName, DateTime trialStartDate, DateTime trialEndDate, 
                                    String plan, String region, Integer numberOfUsers) {
        this.region = region;
        this.environmentType = 'MCS';
        this.plan = plan;
        this.numberOfUsers = numberOfUsers;
        this.user = new UserDetails(contactEmail, contactFirstName, contactLastName);
        
        Datetime startDt = trialStartDate;
        Datetime endDt = trialEndDate;
        
        this.subscription = new SubscriptionDetails(
            startDt.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''),
            endDt.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'')
        );
    }

    public class UserDetails {
        public String email;
        public String firstName;
        public String lastName;

        public UserDetails(String email, String firstName, String lastName) {
            this.email = email;
            this.firstName = firstName;
            this.lastName = lastName;
        }
    }

    public class SubscriptionDetails {
        public String activationDate;
        public String expirationDate;

        public SubscriptionDetails(String activationDate, String expirationDate) {
            this.activationDate = activationDate;
            this.expirationDate = expirationDate;
        }
    }
}