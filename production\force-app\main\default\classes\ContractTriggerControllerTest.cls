/*
<AUTHOR>
@date	15/05/2023
@description Test Class for ContractController and ContractTriggerExecutionController
@version API 57.0
*/
@isTest
public class ContractTriggerControllerTest {

    @testSetup static void setup() {
        Test.startTest();
        TestHelper.MYCSetup();
        Test.stopTest();
    }


    @isTest static void updateContractRelationships(){

        Opportunity opp = [SELECT ID from Opportunity Limit 1];
        Account acc = [SELECT ID from Account Limit 1];
        Order order = new Order();
        order.OpportunityId = opp.Id;
        order.AccountId = acc.Id;
        order.EffectiveDate = Date.today();
        order.Status = 'Draft';
        insert order;

        Test.startTest();
        //Order Insert --> Triggers nonSub Contract 
        List<Contract> nonSubCont = [SELECT ID from Contract WHERE SBQQ__Order__c =: order.Id Limit 1];
        System.assertEquals(1,nonSubCont.size());

        Contract cntr = new Contract();
        cntr.Name = 'Sample Contract';
        cntr.SBQQ__Opportunity__c = opp.Id;
        cntr.SBQQ__Order__c = order.Id;
        cntr.AccountId = acc.Id;
        cntr.StartDate = Date.today()-10;
        insert cntr;

        Test.stopTest();
    }

    @isTest static void replacedContracts(){
        
        Opportunity opp = [SELECT ID from Opportunity Limit 1];
        Contract cntr = [SELECT Id, ContractNumber FROM Contract LIMIT 1];
        Account acc = [SELECT ID from Account Limit 1];
        SBQQ__Quote__c sq = [SELECT ID from SBQQ__Quote__c Limit 1];
        update opp;

        Contract cntrc = new Contract();
        cntrc.SBQQ__Opportunity__c = opp.Id;
        cntrc.SBQQ__Quote__c = sq.Id;
        cntrc.AccountId = acc.Id;
        insert cntrc;

    }

    @isTest static void contractTermUpdated(){
        
        Contract cntr = [SELECT Id FROM Contract LIMIT 1];
        cntr.ContractTerm = 12;
        cntr.StartDate = Date.today();
        cntr.EndDate = Date.today().addDays(365);
        update cntr;

    }

    @isTest static void cancelContract(){

        Account acc = [SELECT ID from Account Limit 1];

        Contract cntrc2 = new Contract();
        cntrc2.AccountId = acc.Id;
        insert cntrc2;

        Contract cntr = [SELECT Id FROM Contract LIMIT 1];
        cntr.Status = 'Activated';
        update cntr;

    }
    
    @isTest static void renewalContracts(){
  
        Opportunity opp = [SELECT ID from Opportunity Limit 1];
        Account acc = [SELECT ID from Account Limit 1];
        SBQQ__Quote__c sq = [SELECT ID from SBQQ__Quote__c Limit 1];
        opp.SBQQ__PrimaryQuote__c = sq.Id;
        Id RecordTypeId = UtilityHelper.getRecordTypeId('Opportunity', 'Renewal Opportunity (New)');

        List<Opportunity> renewalOppsToInsert = new List<Opportunity>();
        List<Contract> contractsToInsert = new List<Contract>();

        Opportunity o = new Opportunity();
        o.CloseDate = System.today();
        o.Name = 'Unit Test Opportunity S5';
        o.StageName = 'S5 - Negotiate/Purchase Solution';
        o.RecordTypeId = RecordTypeId;
        o.AccountId = acc.Id;  
        renewalOppsToInsert.add(o);
        
        Opportunity o2 = new Opportunity();
        o2.CloseDate = System.today();
        o2.Name = 'Unit Test Opportunity S5 -2';
        o2.StageName = 'S5 - Negotiate/Purchase Solution';
        o2.RecordTypeId = RecordTypeId;
        o2.Type = 'Maintenance Renewal';
        o2.AccountId = acc.Id; 
        renewalOppsToInsert.add(o2);
        insert renewalOppsToInsert;
       
        Test.startTest();
        Contract cntrc = new Contract();
        cntrc.SBQQ__Opportunity__c = opp.Id;
        cntrc.SBQQ__Quote__c = sq.Id;
        cntrc.AccountId = acc.Id;
		cntrc.SBQQ__RenewalOpportunity__c = o.Id;
        cntrc.SBQQ__RenewalForecast__c = true;
        cntrc.SBQQ__RenewalQuoted__c = true;
        cntrc.EndDate = Date.today()+5;
        contractsToInsert.add(cntrc);
        
        Contract cntrc2 = new Contract();
        cntrc2.SBQQ__Opportunity__c = opp.Id;
        cntrc2.SBQQ__Quote__c = sq.Id;
        cntrc2.AccountId = acc.Id;
		cntrc2.SBQQ__RenewalOpportunity__c = o2.Id;
        contractsToInsert.add(cntrc2);

        insert contractsToInsert;
        
        cntrc.SBQQ__RenewalOpportunity__c=null;
        update cntrc;
        o.SBQQ__RenewedContract__c = cntrc.Id;
        update o;
        cntrc.SBQQ__RenewalOpportunity__c =  cntrc2.SBQQ__RenewalOpportunity__c;
        update cntrc;

        Test.stopTest();
        //RenewalOppChange
        opp = [SELECT ID, StageName from Opportunity WHERE Name = 'Unit Test Opportunity S5' Limit 1];
        System.assertEquals('S9 - Co-Termed', opp.StageName);
        
        //Prevent Renewal Forecast
        cntrc.SBQQ__RenewalForecast__c = false;
        update cntrc;
    }
    
    @isTest static void ContractControllerMethods(){
  
        Opportunity opp = [SELECT ID from Opportunity Limit 1];
        Account acc = [SELECT ID from Account Limit 1];
        SBQQ__Quote__c sq = [SELECT ID from SBQQ__Quote__c Limit 1];
        opp.SBQQ__PrimaryQuote__c = sq.Id;
        update opp;
        opp = [SELECT ID, SBQQ__PrimaryQuote__r.Ship_To_Book__c,SBQQ__PrimaryQuote__r.SBCF_Ship_to_Contact__c,SBQQ__PrimaryQuote__r.Bill_To_Book__c,SBQQ__PrimaryQuote__r.SBCF_Bill_to_Contact__c from Opportunity Limit 1];
        Id RecordTypeId = UtilityHelper.getRecordTypeId('Opportunity', 'Renewal Opportunity (New)');

        Contract cntrc = new Contract();
        cntrc.SBQQ__Opportunity__c = opp.Id;
        cntrc.SBQQ__Quote__c = sq.Id;
        cntrc.AccountId = acc.Id;
	    cntrc.SBQQ__RenewalForecast__c = true;
        cntrc.SBQQ__RenewalQuoted__c = true;
        cntrc.EndDate = Date.today()+5;
       
        Test.startTest();
        ContractController.stampPrevAddressBook(new List<Contract>{cntrc},new Map<Id, Opportunity>{opp.Id=>opp});
        Test.stopTest();
        //System.assertEquals('S9 - Co-Termed', opp.StageName);
    }

    @IsTest static void requestContractCancellation () {

        Account acc = [SELECT ID FROM Account LIMIT 1];

        Contract c = new Contract();
        c.AccountId = acc.Id;
        insert c;

        Approval.ProcessSubmitRequest req1 = new Approval.ProcessSubmitRequest();
        req1.setObjectId(c.Id);
        req1.setSubmitterId(UserInfo.getUserId());
        req1.setProcessDefinitionNameOrId('Request_Cancellation_v4');

        Test.startTest();
        Approval.ProcessResult resultApp = Approval.process(req1);
        Test.stopTest();
        c = [SELECT Id, Cancellation_Approval_Step__c FROM Contract WHERE Id = :c.Id];
        System.assertEquals('Enter', c.Cancellation_Approval_Step__c, 'Cancellation Approval Step is not Correct');
    }
}