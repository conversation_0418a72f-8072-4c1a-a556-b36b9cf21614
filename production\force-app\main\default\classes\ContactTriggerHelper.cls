public with sharing class Contact<PERSON>riggerHelper {
    public static void OnAfterInsert(List<Contact> newRecords) {
        createMessageQueue(newRecords, true); 
    }

    public static void OnAfterUpdate(List<Contact> updatedRecords) {
        createMessageQueue(updatedRecords, false); 
    }   

/*
    public void OnAfterUndelete(List<Contact> newRecords, Boolean isInsert) {
        createMessageQueue(newRecords, true);  
    }   
*/

    public static void updateContactGeographyUCCode (Map<Id, Contact> id2OldContact, Map<Id, Contact> id2NewContact) {
        Id resourceId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();
        Map<Id, Contact> id2ContactNewRegion = new Map<Id, Contact>();
        for (Contact newContact : id2NewContact.values()) {
            Contact oldContact = id2OldContact.get(newContact.Id);
            if (oldContact.pse__Region__c != newContact.pse__Region__c && newContact.RecordTypeId == resourceId) {
                id2ContactNewRegion.put(newContact.Id, newContact);
            }
        }

        if (!id2ContactNewRegion.isEmpty()) {
            deleteOldGeographyUcCodes(id2ContactNewRegion);
            createContactGeographyUCCode(id2ContactNewRegion);
        }
    }

    public static void createContactGeographyUCCode (Map<Id, Contact> id2Contacts) {
        Id resourceId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Resource').getRecordTypeId();
        Map<Id, Contact> id2ResourceContact = new Map<Id, Contact>();
        for (Contact c : id2Contacts.values()) {
            if (c.RecordTypeId == resourceId) {
                id2ResourceContact.put(c.Id, c);
            }
        }

        if (!id2ResourceContact.isEmpty()) {
            Map<Id, Contact> regionIds2Contact = new Map<Id, Contact>();
            for (Contact c : id2ResourceContact.values()) {
                regionIds2Contact.put(c.pse__Region__c, c);
            }

            List<pse__Region__c> regions = [SELECT Id, Name FROM pse__Region__c WHERE Id IN :regionIds2Contact.keySet()];
            Map<String, Contact> regionCode2Contact = new Map<String, Contact>();
            List<Universal_Code__c> geographyCodes = [SELECT Id, Name, Code__c FROM Universal_Code__c WHERE RecordType.Name = 'Geography'];
            Map<String, Universal_Code__c> geographyMap = new Map<String, Universal_Code__c>();

            Pattern p = Pattern.compile('L ([0-9])+');
            for (pse__Region__c r : regions) {
                Matcher pm = p.matcher(r.Name);
                if (pm.find()) {
                    regionCode2Contact.put(pm.group(), regionIds2Contact.get(r.Id));
                }
            }

            for(Universal_Code__c uc : geographyCodes) {
                Matcher pm = p.matcher(uc.Name);
                if (pm.find()) {
                    geographyMap.put(pm.group(), uc);
                }
            }

            List<UC_Contact__c> ucContactsToInsert = new List<UC_Contact__c>();
            for (String code : regionCode2Contact.keySet()) {
                if (geographyMap.containsKey(code)) {
                    ucContactsToInsert.add(
                        new UC_Contact__c (
                            Contact__c = regionCode2Contact.get(code).Id,
                            Universal_Code__c = geographyMap.get(code).Id
                        )
                    );
                }
            }

            if (!ucContactsToInsert.isEmpty()) {
                insert ucContactsToInsert;
            }
        }
    }

    public static void deleteOldGeographyUcCodes (Map<Id, Contact> contacts) {
        delete [
            SELECT Id
            FROM UC_Contact__c
            WHERE Contact__c IN :contacts.keySet()
                AND Universal_Code__r.RecordType.Name = 'Geography'
        ];
    }

    private static void createMessageQueue(List<Contact> newRecords, Boolean isInsert) {
        list<Contact> contacts = [SELECT Id, Scopus_ID__c, Title, FirstName, LastName, Email, Fax, MailingStreet, MailingCity, MailingState, MailingCountry, MailingPostalCode, Phone, MobilePhone, 
                                         CreatedBy.EmployeeNumber, CreatedDate, LastModifiedBy.EmployeeNumber, LastModifiedDate, AccountId, Compass_ID__c, Owner.EmployeeNumber, ReportsTo.Scopus_ID__c, 
                                         AssistantName, AssistantPhone, LeadSource, HasOptedOutOfEmail, DoNotCall, Department__c
                                  FROM Contact 
                                  WHERE Id IN: newRecords];
        map<String, MessageQueue__c> MessageQueueToCreate = new map<String, MessageQueue__c>();
        list<MessageQueue__c> messageQueueList = new list<MessageQueue__c>();  
        MessageQueue__c mq;
                

        JSONStructures.accountUpdateStructure contactUpdateStructure = new JSONStructures.accountUpdateStructure();
        for(Contact co : contacts) {
            mq = new MessageQueue__c();
            if(!MessageQueueToCreate.containsKey(mq.RecordID__c)) {
                if(co.Scopus_ID__c != null) {
                    mq.Action__c = 'Update';
                    mq.OutgoingMessage__c = getUpdatedContactJSON(co);
                } else if(isInsert) {
                    mq.Action__c = 'Create';
                    mq.OutgoingMessage__c = getInsertedContactJSON(co);
                } else if (!isInsert && co.Scopus_ID__c == null) {
                    mq.Action__c = 'Create';
                    mq.OutgoingMessage__c = getInsertedContactJSON(co);
                } else {
                    mq.Action__c = 'Update';
                    mq.OutgoingMessage__c = getUpdatedContactJSON(co);
                }
                mq.ObjectType__c = 'Contact';
                mq.RecordID__c = co.Id;
                mq.ParentID__c = co.AccountId;
                mq.Status__c = 'Queued';
                mq.RetryCounter__c = 0;
                MessageQueueToCreate.put(mq.RecordID__c, mq);
                //system.debug('MessageQueueToCreate: ' + MessageQueueToCreate);
            }
        }
        
        if(MessageQueueToCreate.size()>0) {
            try {
                insert MessageQueueToCreate.values();
            } catch (DMLException ex) {
                system.debug('UNABLE TO INSERT MESSAGE QUEUE RECORDS: ' + ex);
            }
        }        
    }
    
    private static String getInsertedContactJSON(Contact co) {
        JSONStructures.contactInsertStructure contactInsertStructure = new JSONStructures.contactInsertStructure();
        contactInsertStructure.ContactSalesforceId = co.Id;
        contactInsertStructure.ContactCompassId = Integer.valueOf(co.Compass_ID__c);
        contactInsertStructure.ContactTitle = co.Title;
        contactInsertStructure.ContactFirstName = co.FirstName;
        contactInsertStructure.ContactLastName = co.LastName;
        contactInsertStructure.ContactEmail = co.Email;
        contactInsertStructure.ContactFax = co.Fax;
        contactInsertStructure.ContactAddressLine1 = co.MailingStreet;
        contactInsertStructure.ContactCity = co.MailingCity;
        contactInsertStructure.ContactStateName = co.MailingState;
        contactInsertStructure.ContatCountryName = co.MailingCountry;
        contactInsertStructure.ContactPostalCode = co.MailingPostalCode;
        contactInsertStructure.ContactPhone = co.Phone;
        contactInsertStructure.ContactMobilePhone = co.MobilePhone;

        contactInsertStructure.ContactOwner = (co.Owner.EmployeeNumber!=null ? Integer.valueOf(co.Owner.EmployeeNumber) : Integer.valueOf('9999999'));
        //contactInsertStructure.ContactReportsTo = co.ReportsTo.Scopus_ID__c != null ? Integer.valueOf(co.ReportsTo.Scopus_ID__c) : null;
        //contactInsertStructure.ContactDepartment = co.Department__c;
        //contactInsertStructure.ContactAssistant = co.AssistantName;
        //contactInsertStructure.ContactAsstPhone = co.AssistantPhone;
        //contactInsertStructure.ContactLeadSource = co.LeadSource;
        contactInsertStructure.ContactEmailOptOut = co.HasOptedOutOfEmail;
        //contactInsertStructure.ContactDoNotCall = co.DoNotCall;       
        
        contactInsertStructure.ContactCreatedByEmployeeId = co.CreatedBy.EmployeeNumber != null ? Integer.valueOf(co.CreatedBy.EmployeeNumber) : null;
        contactInsertStructure.ContactCreatedDate = co.CreatedDate;
        contactInsertStructure.ContactLastModifiedByEmployeeId = co.LastModifiedBy.EmployeeNumber != null ? Integer.valueOf(co.LastModifiedBy.EmployeeNumber) : null;
        contactInsertStructure.ContactLastModifiedDate = co.LastModifiedDate;
        return JSON.serialize(contactInsertStructure);
    }
    
    private static String getUpdatedContactJSON(Contact co) {
        JSONStructures.contactUpdateStructure contactUpdateStructure = new JSONStructures.contactUpdateStructure();
        contactUpdateStructure.ContactSalesforceId = co.Id;
        //contactUpdateStructure.ContactScopusId = co.Scopus_ID__c != null ? Integer.valueOf(co.Scopus_ID__c) : null;
        contactUpdateStructure.ContactScopusId = Integer.valueOf(co.Scopus_ID__c);
        contactUpdateStructure.ContactCompassId = Integer.valueOf(co.Compass_ID__c);
        contactUpdateStructure.ContactTitle = co.Title;
        contactUpdateStructure.ContactFirstName = co.FirstName;
        contactUpdateStructure.ContactLastName = co.LastName;
        contactUpdateStructure.ContactEmail = co.Email;
        contactUpdateStructure.ContactFax = co.Fax;
        contactUpdateStructure.ContactAddressLine1 = co.MailingStreet;
        contactUpdateStructure.ContactCity = co.MailingCity;
        contactUpdateStructure.ContactStateName = co.MailingState;
        contactUpdateStructure.ContatCountryName = co.MailingCountry;
        contactUpdateStructure.ContactPostalCode = co.MailingPostalCode;
        contactUpdateStructure.ContactPhone = co.Phone;
        contactUpdateStructure.ContactMobilePhone = co.MobilePhone;
        
        contactUpdateStructure.ContactOwner = (co.Owner.EmployeeNumber!=null ? Integer.valueOf(co.Owner.EmployeeNumber) : Integer.valueOf('9999999'));
        //contactUpdateStructure.ContactReportsTo = co.ReportsTo.Scopus_ID__c != null ? Integer.valueOf(co.ReportsTo.Scopus_ID__c) : null;
        //contactUpdateStructure.ContactDepartment = co.Department__c;
        //contactUpdateStructure.ContactAssistant = co.AssistantName;
        //contactUpdateStructure.ContactAsstPhone = co.AssistantPhone;
        //contactUpdateStructure.ContactLeadSource = co.LeadSource;
        contactUpdateStructure.ContactEmailOptOut = co.HasOptedOutOfEmail;
        //contactUpdateStructure.ContactDoNotCall = co.DoNotCall;       
        
        contactUpdateStructure.ContactCreatedByEmployeeId = (co.CreatedBy.EmployeeNumber!=null ? Integer.valueOf(co.CreatedBy.EmployeeNumber) : Integer.valueOf('9999999'));
        contactUpdateStructure.ContactCreatedDate = co.CreatedDate;
        contactUpdateStructure.ContactLastModifiedByEmployeeId = (co.LastModifiedBy.EmployeeNumber!=null ? Integer.valueOf(co.LastModifiedBy.EmployeeNumber) : Integer.valueOf('9999999'));
        contactUpdateStructure.ContactLastModifiedDate = co.LastModifiedDate;
        return JSON.serialize(contactUpdateStructure);
    }

    public static void routingFieldChange (Map<Id, Contact> oldContacts, Map<Id, Contact> newContacts) {
        List<String> triggerBypassUsers = MSTR_Global_Configuation__mdt.getInstance('Trigger_Bypass_Users').Value__c.split(';');
        Set<Id> MQLcontactIds = new Set<Id>();
        List<Contact> MQLToChatter = new List<Contact>();
        Set<Id> newOwners = new Set<Id>();
        Set<Id> contactIds = new Set<Id>();
        Boolean validAccount = false;
        Boolean oldValidAccount = false;
        for (Contact newContact : newContacts.values()) {
            Contact oldContact = oldContacts.get(newContact.Id);
            validAccount = newContact.AccountId != null && newContact.AccountId != TriggersHelper.unmappedAccountId && newContact.AccountId != TriggersHelper.resourceCentreAccountId;
            oldValidAccount = oldContact.AccountId != null && oldContact.AccountId != TriggersHelper.unmappedAccountId && oldContact.AccountId != TriggersHelper.resourceCentreAccountId;
            if ((oldContact.AccountId != newContact.AccountId && validAccount && !oldValidAccount)
                || oldContact.Ownership_Rules__c != newContact.Ownership_Rules__c) {
            		contactIds.add(newContact.Id);
        	}
            if(newContact.L_Status__c == 'L3 - Engaged'
              	&& (oldContact.L_Status__c != newContact.L_Status__c || (validAccount && !oldValidAccount))){ //Status changed to MQL or Account changed to a valid one
                        MQLcontactIds.add(newContact.Id);
                        if(!contactIds.contains(newContact.Id) && newContact.OwnerId != null){ //No Owner reevaluation (only Status change): only send the notification
                            MQLToChatter.add(newContact); 
                            newOwners.add(newContact.OwnerId);
                        }
        	} 
        }
        if(!MQLToChatter.isEmpty()){
            CampaignMemberTriggerHelper.processMQLChatterNotifications(MQLToChatter, newOwners);
        }
        if (!contactIds.isEmpty()) { 
        	PersonRouting.processRecordsContactChange(contactIds, MQLcontactIds);    
        }
    }
    
    public static void updateIsStatusTQLField(List<Contact> Contacts, Map<Id, Contact> oldContactMap) {
        for (Contact newContact : Contacts) {
            Contact oldContact = oldContactMap.get(newContact.Id);
            if (TriggersHelper.valueChanged(oldContact, newContact, Contact.L_Status__c) && newContact.L_Status__c == 'TQL')
                newContact.isStatusTQL__c = true;
        }
    }
    
    public static void updateEDUActive (List<Contact> newContacts) {
        for (Contact c : newContacts) {
            if (c.CS_Active__c && c.CS_PEP_End_Date__c < System.now()) {
                c.CS_Active__c = false;
            }
        }
    }
    
    public static void updateMarketingFields (List<Contact> newContacts, Map<Id, Contact> oldContacts) {
        List<String> syncToMktBypassUsers = MSTR_Global_Configuation__mdt.getInstance('Sync_To_Marketo_Bypass_Users').Value__c.split(';');
        Map<String, Integer> statusOrder = new Map<String, Integer>{
            'L0 - Disqualified' => -3,
            'Prospect' => -2,
            'Inquiry' => -1,
            'L3 - Engaged' => 0,
            'L2 - Contacted' => 1,
            'TQL' => 2,
            'SAL' => 3,
            'SQL' => 4,
            'L5 - Opportunity' => 5
        };
        List<String> statusAPIs = new List<String>{
            'MQL_Date__c',
            'TAL_Date__c',
            'TQL_Date__c', 
            'SAL_Date__c', 
            'SQL_Date__c',
            'OPP_Date__c' 
        };
        Datetime now;
        Integer newStatusOrder;
        Integer oldStatusOrder;
        for (Contact c : newContacts) {
            if (!c.Return_to_Marketing__c && oldContacts.get(c.Id) != null && oldContacts.get(c.Id).Return_to_Marketing__c) {
                c.Return_to_Marketing_Reason__c = '';
            }
            if(oldContacts.get(c.Id) == null || c.L_Status__c != oldContacts.get(c.Id).L_Status__c){
                //Contact Before Update Flow
                if(oldContacts.get(c.Id) != null && c.L_Status__c != oldContacts.get(c.Id).L_Status__c && c.L_Status__c == 'L3 - Engaged' && !syncToMktBypassUsers.contains(UserInfo.getUserId())){
                    c.Sync_to_Marketo__c = true;
                }
                //New Status Date fields
                now = Datetime.now();
                newStatusOrder = statusOrder.get(c.L_Status__c);
                oldStatusOrder = oldContacts.get(c.Id) != null? (statusOrder.get(oldContacts.get(c.Id).L_Status__c) != null? statusOrder.get(oldContacts.get(c.Id).L_Status__c): -1): -1;
                if(newStatusOrder >= 0){
                    //If the new Status skips statuses, populate the date of all of them
                    if(newStatusOrder > oldStatusOrder+1){
                        for(Integer i = (oldStatusOrder >= 0? oldStatusOrder+1: 0); i <= newStatusOrder; i++){
                            c.put(statusAPIs.get(i), now);
                        }
                    }
                    else{
                    	c.put(statusAPIs.get(newStatusOrder), now);  
                    }
                }
            }
        }
    }
}